from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import Employee, ExternalTransfer, RetiredEmployee
from .forms import ExternalTransferForm, TransferEmployeeForm
from system_logs.models import SystemLog


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@login_required
def external_transfers_list(request):
    """View to display list of externally transferred employees"""
    search_query = request.GET.get('search', '')
    
    # Get externally transferred employees
    external_transfers = ExternalTransfer.objects.select_related('employee').all()
    
    # Apply search filter
    if search_query:
        external_transfers = external_transfers.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__national_id__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(destination_directorate__icontains=search_query) |
            Q(transfer_reason__icontains=search_query)
        )
    
    # Handle POST request for transferring an employee
    if request.method == 'POST':
        form = TransferEmployeeForm(request.POST)
        if form.is_valid():
            employee = form.cleaned_data['employee']
            
            # Check if employee is already transferred
            if hasattr(employee, 'external_transfer'):
                messages.error(request, 'هذا الموظف منقول خارجياً بالفعل.')
                return redirect('employees:external_transfers_list')
            
            # Check if employee is retired
            if hasattr(employee, 'retirement'):
                messages.error(request, 'لا يمكن نقل موظف متقاعد.')
                return redirect('employees:external_transfers_list')
            
            # Create external transfer record
            external_transfer = ExternalTransfer.objects.create(
                employee=employee,
                transfer_date=form.cleaned_data['transfer_date'],
                destination_directorate=form.cleaned_data['destination_directorate'],
                transfer_reason=form.cleaned_data['transfer_reason'],
                notes=form.cleaned_data['notes']
            )
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.EMPLOYEES,
                action=SystemLog.CREATE,
                page='external_transfers_list',
                object_id=str(external_transfer.id),
                object_repr=f'{employee.full_name} ({employee.ministry_number})',
                description=f'تم نقل الموظف خارجياً: {employee.full_name} - الرقم الوزاري: {employee.ministry_number} - إلى: {form.cleaned_data["destination_directorate"]}'
            )
            
            messages.success(request, f'تم نقل الموظف {employee.full_name} (الرقم الوزاري: {employee.ministry_number}) إلى {form.cleaned_data["destination_directorate"]} بنجاح.')
            return redirect('employees:external_transfers_list')
        else:
            # Display form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{form.fields[field].label}: {error}')
    else:
        form = TransferEmployeeForm()
    
    context = {
        'external_transfers': external_transfers,
        'search_query': search_query,
        'form': form,
    }
    
    return render(request, 'employees/external_transfers_list.html', context)


@login_required
def external_transfer_detail(request, pk):
    """View to display external transfer details"""
    external_transfer = get_object_or_404(ExternalTransfer, pk=pk)
    
    context = {
        'external_transfer': external_transfer,
    }
    
    return render(request, 'employees/external_transfer_detail.html', context)


@login_required
def external_transfer_update(request, pk):
    """View to update external transfer information"""
    external_transfer = get_object_or_404(ExternalTransfer, pk=pk)
    
    if request.method == 'POST':
        form = ExternalTransferForm(request.POST, instance=external_transfer)
        if form.is_valid():
            form.save()
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.EMPLOYEES,
                action=SystemLog.UPDATE,
                page='external_transfer_update',
                object_id=str(external_transfer.id),
                object_repr=f'{external_transfer.employee.full_name} ({external_transfer.employee.ministry_number})',
                description=f'تم تحديث بيانات النقل الخارجي: {external_transfer.employee.full_name}'
            )
            
            messages.success(request, 'تم تحديث بيانات النقل الخارجي بنجاح.')
            return redirect('employees:external_transfer_detail', pk=pk)
    else:
        form = ExternalTransferForm(instance=external_transfer)
    
    context = {
        'form': form,
        'external_transfer': external_transfer,
    }
    
    return render(request, 'employees/external_transfer_form.html', context)


@login_required
def external_transfer_delete(request, pk):
    """View to delete (restore) external transfer"""
    external_transfer = get_object_or_404(ExternalTransfer, pk=pk)
    
    if request.method == 'POST':
        employee_name = external_transfer.employee.full_name
        destination = external_transfer.destination_directorate
        
        # Log the action
        SystemLog.objects.create(
            user=request.user,
            ip_address=get_client_ip(request),
            module=SystemLog.EMPLOYEES,
            action=SystemLog.DELETE,
            page='external_transfer_delete',
            object_id=str(external_transfer.id),
            object_repr=f'{employee_name} ({external_transfer.employee.ministry_number})',
            description=f'تم إلغاء النقل الخارجي للموظف: {employee_name} من {destination}'
        )
        
        # Delete the external transfer record (this will restore the employee to active status)
        external_transfer.delete()
        
        messages.success(request, f'تم إلغاء النقل الخارجي للموظف {employee_name} وإعادته للخدمة بنجاح.')
        return redirect('employees:external_transfers_list')
    
    context = {
        'external_transfer': external_transfer,
    }
    
    return render(request, 'employees/external_transfer_confirm_delete.html', context)


@login_required
def search_employees_for_transfer(request):
    """AJAX view to search for employees available for external transfer"""
    search_term = request.GET.get('term', '').strip()
    
    if len(search_term) < 2:
        return JsonResponse({'results': []})
    
    # Get employees who are not retired or externally transferred
    retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
    transferred_employee_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
    
    # Combine both exclusions
    excluded_ids = list(retired_employee_ids) + list(transferred_employee_ids)
    
    employees = Employee.objects.exclude(id__in=excluded_ids)
    
    # Filter by search term (ministry number or name)
    employees = employees.filter(
        Q(ministry_number__icontains=search_term) |
        Q(full_name__icontains=search_term)
    ).order_by('full_name')[:20]  # Limit to 20 results
    
    results = []
    for employee in employees:
        results.append({
            'id': employee.id,
            'text': f"{employee.full_name} ({employee.ministry_number})",
            'ministry_number': employee.ministry_number,
            'full_name': employee.full_name,
            'national_id': employee.national_id,
            'school': employee.school,
            'specialization': employee.specialization
        })
    
    return JsonResponse({'results': results})