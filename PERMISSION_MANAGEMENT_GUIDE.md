# 📖 دليل استخدام نظام إدارة الصلاحيات المتكامل
## Quick User Guide for Integrated Permission Management System

---

## 🚀 البدء السريع

### 1. الوصول للنظام
```
🌐 الرابط: http://localhost:8000/accounts/
👤 تسجيل الدخول: كمدير أو مدير نظام
```

### 2. الواجهة الرئيسية
عند الدخول لصفحة إدارة المستخدمين ستجد:

- **📊 بطاقة الإحصائيات**: تعرض إجمالي المستخدمين والنشطين والمديرين
- **🔍 مربع البحث**: للبحث في أسماء المستخدمين والبريد الإلكتروني
- **📋 جدول المستخدمين**: يعرض معلومات مفصلة لكل مستخدم
- **⚡ أزرار الإجراءات السريعة**: لكل مستخدم

---

## 🔑 إدارة الصلاحيات الفردية

### خطوات إدارة صلاحيات مستخدم واحد:

#### 1️⃣ فتح نافذة الصلاحيات
- اضغط على زر **🔑 إدارة الصلاحيات** بجانب المستخدم المطلوب
- ستظهر نافذة منبثقة كبيرة مع واجهة إدارة الصلاحيات

#### 2️⃣ استخدام القوالب السريعة
اختر من القوالب الجاهزة:

- **👑 مدير كامل**: صلاحيات كاملة لجميع الوحدات (عرض، إضافة، تعديل، حذف)
- **🛡️ مدير**: صلاحيات إدارية (عرض، إضافة، تعديل) لجميع الوحدات
- **👔 مشرف**: صلاحيات محدودة (عرض، إضافة، تعديل) باستثناء الوحدات الحساسة
- **👤 مستخدم**: صلاحيات أساسية (عرض، إضافة) للوحدات العامة
- **👁️ قراءة فقط**: صلاحية العرض فقط للوحدات المحددة
- **🚫 بلا صلاحيات**: إزالة جميع الصلاحيات

#### 3️⃣ التخصيص اليدوي
لكل وحدة يمكنك:

- **🔄 تفعيل/تعطيل الوحدة**: باستخدام المفتاح في رأس الوحدة
- **✅ اختيار الصلاحيات الأساسية**:
  - 👁️ **عرض**: إمكانية مشاهدة البيانات
  - ➕ **إضافة**: إمكانية إضافة بيانات جديدة
  - ✏️ **تعديل**: إمكانية تعديل البيانات الموجودة
  - 🗑️ **حذف**: إمكانية حذف البيانات
- **📄 اختيار الصفحات المتاحة**: حدد الصفحات التي يمكن للمستخدم الوصول إليها

#### 4️⃣ إدارة الصفحات
لكل وحدة:
- **✅ تحديد الكل**: لاختيار جميع صفحات الوحدة
- **❌ إلغاء الكل**: لإلغاء اختيار جميع الصفحات
- **☑️ اختيار فردي**: لاختيار صفحات محددة

#### 5️⃣ حفظ التغييرات
- اضغط **💾 حفظ الصلاحيات** في أسفل النافذة
- ستظهر رسالة تأكيد النجاح
- ستتحديث الصفحة تلقائياً لإظهار التغييرات

---

## 👥 الإدارة الجماعية للصلاحيات

### خطوات تطبيق صلاحيات على عدة مستخدمين:

#### 1️⃣ اختيار المستخدمين
- ☑️ حدد خانات الاختيار بجانب المستخدمين المطلوبين
- أو استخدم **☑️ تحديد الكل** لاختيار جميع المستخدمين

#### 2️⃣ فتح الإدارة الجماعية
- اضغط زر **👥 إدارة جماعية** (يظهر عند اختيار مستخدمين)
- ستظهر نافذة الإدارة الجماعية

#### 3️⃣ اختيار نوع العملية
- **🔄 استبدال الصلاحيات**: حذف الصلاحيات الحالية واستبدالها
- **➕ إضافة صلاحيات**: إضافة للصلاحيات الموجودة
- **➖ إزالة صلاحيات**: إزالة صلاحيات محددة

#### 4️⃣ تحديد الصلاحيات
- اختر الوحدات والصلاحيات المطلوب تطبيقها
- حدد الصفحات المتاحة لكل وحدة

#### 5️⃣ التطبيق
- اضغط **✅ تطبيق على المحددين**
- ستتم معالجة جميع المستخدمين المحددين

---

## 🔄 تفعيل/تعطيل المستخدمين

### تغيير حالة مستخدم:
- اضغط زر **🔄 التفعيل/التعطيل** بجانب المستخدم
- أكد العملية في النافذة المنبثقة
- ستتحديث الحالة فوراً في الجدول

**⚠️ ملاحظة**: لا يمكن تعطيل المدير الرئيسي أو مدير النظام

---

## 🔍 البحث والفلترة

### البحث في المستخدمين:
- استخدم **🔍 مربع البحث** في أعلى الصفحة
- ابحث بـ:
  - اسم المستخدم
  - الاسم الكامل
  - البريد الإلكتروني
- النتائج تظهر فوراً أثناء الكتابة

### مراقبة الإحصائيات:
- **📊 بطاقة الإحصائيات** تتحديث تلقائياً
- تعرض العدد الحالي للمستخدمين المعروضين

---

## 🎨 فهم الواجهة

### رموز حالة المستخدم:
- **🟢 دائرة خضراء**: مستخدم نشط
- **🔴 دائرة حمراء**: مستخدم معطل

### شارات نوع المستخدم:
- **🔴 شارة حمراء**: مدير النظام
- **🔵 شارة زرقاء**: مدير
- **🟡 شارة صفراء**: مشرف
- **⚫ شارة رمادية**: مستخدم عادي

### رموز الصلاحيات:
- **👁️ أيقونة عين زرقاء**: صلاحية العرض
- **➕ أيقونة زائد خضراء**: صلاحية الإضافة
- **✏️ أيقونة قلم صفراء**: صلاحية التعديل
- **🗑️ أيقونة سلة حمراء**: صلاحية الحذف

### عرض الصلاحيات في الجدول:
- **🔴 شارة "كامل"**: مدير كامل أو مدير نظام
- **🔢 عدد الوحدات**: عدد الوحدات التي لديه صلاحيات عليها
- **🏷️ شارات الوحدات**: أسماء الوحدات (أول 3 وحدات)
- **➕ شارة العدد**: إذا كان لديه أكثر من 3 وحدات

---

## ⚡ نصائح للاستخدام الفعال

### 1. استخدام القوالب السريعة
- **وفر الوقت** باستخدام القوالب الجاهزة
- **ابدأ بقالب** ثم عدل حسب الحاجة
- **قالب "مستخدم"** مناسب لمعظم الموظفين

### 2. إدارة الصفحات بذكاء
- **لا تعطي صلاحيات زائدة** عن الحاجة
- **ابدأ بالحد الأدنى** ثم أضف حسب الحاجة
- **راجع الصلاحيات دورياً**

### 3. الإدارة الجماعية
- **استخدمها للموظفين الجدد** من نفس القسم
- **طبق قوالب موحدة** للأدوار المتشابهة
- **وفر الوقت** في إدارة عدة مستخدمين

### 4. الأمان
- **لا تعطي صلاحيات إدارية** إلا للمديرين
- **راجع صلاحيات الوحدات الحساسة** (accounts, backup, system_logs)
- **عطل المستخدمين غير النشطين**

---

## 🆘 حل المشاكل الشائعة

### المشكلة: لا تظهر نافذة الصلاحيات
**الحل:**
- تأكد من تسجيل الدخول كمدير
- تحديث الصفحة (F5)
- تحقق من اتصال الإنترنت

### المشكلة: لا يتم حفظ الصلاحيات
**الحل:**
- تأكد من اختيار صلاحية واحدة على الأقل
- تحقق من اختيار صفحة واحدة على الأقل للوحدة
- جرب إعادة تحميل الصفحة

### المشكلة: لا يمكن تعطيل مستخدم
**الحل:**
- تحقق من أن المستخدم ليس مدير نظام
- تأكد من صلاحياتك كمدير
- المدير الرئيسي محمي من التعطيل

### المشكلة: القوالب السريعة لا تعمل
**الحل:**
- انتظر تحميل النافذة بالكامل
- جرب إعادة فتح النافذة
- تحديث الصفحة وإعادة المحاولة

---

## 📊 فهم الإحصائيات

### بطاقة الإحصائيات تعرض:
- **👥 إجمالي المستخدمين**: العدد الكلي في النظام
- **✅ المستخدمين النشطين**: المستخدمين القادرين على تسجيل الدخول
- **🛡️ المديرين**: المستخدمين الذين لديهم صلاحيات إدارية
- **🔑 الصلاحيات المخصصة**: إجمالي الصلاحيات المحفوظة

### معلومات المستخدم في الجدول:
- **📧 البريد الإلكتروني**: للتواصل والإشعارات
- **🏷️ نوع المستخدم**: الدور الوظيفي
- **🔄 الحالة**: نشط أم معطل
- **🔑 الصلاحيات**: عدد الوحدات وأسماؤها
- **📅 آخر دخول**: تاريخ ووقت آخر تسجيل دخول
- **🔢 عدد مرات الدخول**: إحصائية الاستخدام

---

## 🎯 أفضل الممارسات

### 1. تخطيط الصلاحيات
- **حدد الأدوار** قبل إنشاء المستخدمين
- **وضع سياسة واضحة** للصلاحيات
- **راجع الصلاحيات شهرياً**

### 2. الأمان
- **مبدأ الحد الأدنى**: أعطي أقل صلاحيات ممكنة
- **فصل الواجبات**: لا تعطي صلاحيات متضاربة
- **مراجعة دورية**: تحقق من الصلاحيات بانتظام

### 3. التوثيق
- **سجل التغييرات** المهمة
- **وثق سبب إعطاء صلاحيات خاصة**
- **احتفظ بسجل المراجعات**

### 4. التدريب
- **درب المديرين** على استخدام النظام
- **وضح السياسات** للموظفين
- **قدم الدعم** عند الحاجة

---

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. **راجع هذا الدليل** أولاً
2. **جرب الحلول المقترحة** للمشاكل الشائعة
3. **تواصل مع مدير النظام** للمساعدة التقنية
4. **راجع سجل النظام** للأخطاء التقنية

### معلومات النظام:
- **الإصدار**: نظام إدارة الصلاحيات المتكامل v1.0
- **التاريخ**: يوليو 2025
- **الدعم**: متوفر للمديرين والمشرفين

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة الصلاحيات المتكامل بكفاءة عالية!**

---

*📝 تم إعداد هذا الدليل بواسطة فريق تطوير نظام إدارة الموارد البشرية*  
*📅 آخر تحديث: 6 يوليو 2025*