from django.contrib import admin
from .models import LeaveType, LeaveBalance, Leave, Departure

@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'max_days_per_year')

@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'year', 'initial_balance', 'used_balance', 'remaining_balance')
    list_filter = ('leave_type', 'year')
    search_fields = ('employee__full_name', 'employee__ministry_number')

@admin.register(Leave)
class LeaveAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'start_date', 'end_date', 'days_count', 'status')
    list_filter = ('leave_type', 'status', 'start_date')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'reason')
    date_hierarchy = 'start_date'

@admin.register(Departure)
class DepartureAdmin(admin.ModelAdmin):
    list_display = ('employee', 'departure_type', 'date', 'time_from', 'time_to', 'status')
    list_filter = ('departure_type', 'status', 'date')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'reason')
    date_hierarchy = 'date'
