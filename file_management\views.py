from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import JsonResponse
from .models import FileMovement, File
from .forms import FileMovementForm, FileCheckoutForm, FileReturnForm, FileForm
from employees.models import Employee

@login_required
def file_movement_list(request):
    """View for listing all file movements"""
    search_query = request.GET.get('search', '')

    if search_query:
        file_movements = FileMovement.objects.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query)
        )
    else:
        file_movements = FileMovement.objects.all()

    return render(request, 'file_management/file_movement_list.html', {
        'file_movements': file_movements,
        'search_query': search_query
    })

@login_required
def file_movement_detail(request, pk):
    """View for showing file movement details"""
    file_movement = get_object_or_404(FileMovement, pk=pk)
    return render(request, 'file_management/file_movement_detail.html', {
        'file_movement': file_movement
    })

@login_required
def file_movement_create(request):
    """View for creating a new file movement"""
    if request.method == 'POST':
        print('\n\nPOST data:', request.POST)
        form = FileMovementForm(request.POST)
        print('Form is bound:', form.is_bound)
        if form.is_valid():
            print('Form is valid')
            try:
                # Save the file movement using the custom save method in the form
                file_movement = form.save()
                print('File movement saved successfully with ID:', file_movement.id)

                messages.success(request, 'تم إضافة حركة الملف بنجاح.')
                return redirect('file_management:file_movement_list')

            except Exception as e:
                print('Error saving file movement:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ حركة الملف: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
    else:
        # Initialize form with default values
        from datetime import date
        form = FileMovementForm(initial={'checkout_date': date.today()})

    return render(request, 'file_management/file_movement_form.html', {
        'form': form
    })

@login_required
def file_movement_update(request, pk):
    """View for updating an existing file movement"""
    file_movement = get_object_or_404(FileMovement, pk=pk)

    if request.method == 'POST':
        form = FileMovementForm(request.POST)
        if form.is_valid():
            try:
                # Get data from cleaned_data
                employee = form.cleaned_data['employee']
                checkout_date = form.cleaned_data['checkout_date']
                return_date = form.cleaned_data.get('return_date')
                action_taken = form.cleaned_data.get('action_taken', '')
                notes = form.cleaned_data.get('notes', '')

                # Update file movement
                file_movement.employee = employee
                file_movement.checkout_date = checkout_date
                file_movement.return_date = return_date
                file_movement.action_taken = action_taken
                file_movement.notes = notes

                # Set status based on return date
                if return_date:
                    file_movement.status = 'returned'
                else:
                    file_movement.status = 'out'

                # Save to database
                file_movement.save()

                messages.success(request, 'تم تحديث حركة الملف بنجاح.')
                return redirect('file_management:file_movement_list')
            except Exception as e:
                print('Error updating file movement:', str(e))
                messages.error(request, f'حدث خطأ أثناء تحديث حركة الملف: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
    else:
        # Initialize form with data from file_movement
        initial_data = {
            'ministry_number': file_movement.employee.ministry_number,
            'employee_name': file_movement.employee.full_name,
            'employee_id': file_movement.employee.id,
            'checkout_date': file_movement.checkout_date,
            'return_date': file_movement.return_date,
            'action_taken': file_movement.action_taken,
            'notes': file_movement.notes
        }
        form = FileMovementForm(initial=initial_data)

    return render(request, 'file_management/file_movement_form.html', {
        'form': form,
        'file_movement': file_movement
    })

@login_required
def file_movement_delete(request, pk):
    """View for deleting a file movement"""
    file_movement = get_object_or_404(FileMovement, pk=pk)

    if request.method == 'POST':
        file_movement.delete()
        messages.success(request, 'تم حذف حركة الملف بنجاح.')
        return redirect('file_management:file_movement_list')

    return render(request, 'file_management/file_movement_confirm_delete.html', {
        'file_movement': file_movement
    })

@login_required
def file_checkout(request):
    """View for checking out a file"""
    if request.method == 'POST':
        print('\n\nPOST data:', request.POST)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee_id'] = str(employee.id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'file_management/file_checkout_form.html', {'form': FileCheckoutForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'file_management/file_checkout_form.html', {'form': FileCheckoutForm(post_data)})

        # Get the default file
        try:
            from django.db import connection
            cursor = connection.cursor()
            cursor.execute("SELECT id FROM file_management_file LIMIT 1;")
            result = cursor.fetchone()

            if result:
                default_file_id = result[0]
                post_data['file'] = str(default_file_id)
                print(f'Using default file ID: {default_file_id}')
            else:
                # Create a default file if none exists
                from datetime import datetime
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if employee_id:
                    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    cursor.execute('''
                    INSERT INTO file_management_file
                    (file_number, title, status, description, created_at, updated_at, employee_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (f'FILE-{employee_id}', 'ملف تجريبي', 'active', 'ملف تم إنشاؤه تلقائيًا', now, now, employee_id))
                    connection.commit()

                    # Get the ID of the newly created file
                    cursor.execute("SELECT last_insert_rowid();")
                    default_file_id = cursor.fetchone()[0]
                    post_data['file'] = str(default_file_id)
                    print(f'Created and using new file ID: {default_file_id}')
        except Exception as e:
            print(f'Error getting or creating default file: {str(e)}')

        # Create form with modified POST data
        form = FileCheckoutForm(post_data)
        print('Modified POST data:', post_data)
        print('Form is bound:', form.is_bound)

        if form.is_valid():
            print('Form is valid')
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'file_management/file_checkout_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'file_management/file_checkout_form.html', {'form': form})

                # Create a new file movement
                from file_management.models import FileMovement

                # Create the file movement object
                file_movement = FileMovement(
                    employee=employee,
                    checkout_date=form.cleaned_data['checkout_date'],
                    notes=form.cleaned_data.get('notes', ''),
                    status='out'
                )

                # Save to database
                file_movement.save()
                file_movement_id = file_movement.id
                print('File checkout saved successfully with ID:', file_movement_id)

                messages.success(request, 'تم تسجيل خروج الملف بنجاح.')
                return redirect('file_management:file_movement_list')

            except Exception as e:
                print('Error saving file checkout:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ خروج الملف: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
            for field, errors in form.errors.items():
                print(f'Field {field} errors: {errors}')
    else:
        # Set default checkout date to today
        from datetime import date
        form = FileCheckoutForm(initial={'checkout_date': date.today()})

    return render(request, 'file_management/file_checkout_form.html', {
        'form': form
    })

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

@login_required
def file_return_list(request):
    """View for listing completed file movements"""
    search_query = request.GET.get('search', '')

    if search_query:
        file_movements = FileMovement.objects.filter(
            status='returned'
        ).filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query)
        )
    else:
        file_movements = FileMovement.objects.filter(status='returned')

    return render(request, 'file_management/file_return_list.html', {
        'file_movements': file_movements,
        'search_query': search_query
    })

@login_required
def file_return(request, pk):
    """View for returning a file"""
    file_movement = get_object_or_404(FileMovement, pk=pk)

    if file_movement.status == 'returned':
        messages.error(request, 'هذا الملف تمت إعادته بالفعل.')
        return redirect('file_management:file_movement_list')

    if request.method == 'POST':
        form = FileReturnForm(request.POST, file_movement=file_movement)
        if form.is_valid():
            # Update file movement
            file_movement.return_date = form.cleaned_data['return_date']
            file_movement.action_taken = form.cleaned_data.get('action_taken', '')
            file_movement.status = 'returned'
            file_movement.save()

            messages.success(request, 'تم تسجيل عودة الملف بنجاح.')
            return redirect('file_management:file_movement_list')
        else:
            # Display form errors
            for error in form.non_field_errors():
                messages.error(request, error)
    else:
        # Initialize form with data from file_movement
        initial_data = {
            'return_date': file_movement.return_date or None,
            'action_taken': file_movement.action_taken or ''
        }
        form = FileReturnForm(initial=initial_data, file_movement=file_movement)

    return render(request, 'file_management/file_return_form.html', {
        'form': form,
        'file_movement': file_movement
    })

@login_required
def file_list(request):
    """View for listing all files"""
    search_query = request.GET.get('search', '')

    if search_query:
        files = File.objects.filter(
            Q(file_number__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query)
        )
    else:
        files = File.objects.all()

    return render(request, 'file_management/file_list.html', {
        'files': files,
        'search_query': search_query
    })

@login_required
def file_detail(request, pk):
    """View for showing file details"""
    file = get_object_or_404(File, pk=pk)
    return render(request, 'file_management/file_detail.html', {
        'file': file
    })

@login_required
def file_create(request):
    """View for creating a new file"""
    if request.method == 'POST':
        form = FileForm(request.POST)
        if form.is_valid():
            try:
                file = form.save()
                messages.success(request, 'تم إضافة الملف بنجاح.')
                return redirect('file_management:file_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الملف: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
    else:
        form = FileForm()

    return render(request, 'file_management/file_form.html', {
        'form': form
    })

@login_required
def file_update(request, pk):
    """View for updating an existing file"""
    file = get_object_or_404(File, pk=pk)

    if request.method == 'POST':
        form = FileForm(request.POST, instance=file)
        if form.is_valid():
            try:
                form.save()
                messages.success(request, 'تم تحديث الملف بنجاح.')
                return redirect('file_management:file_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث الملف: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
    else:
        form = FileForm(instance=file)

    return render(request, 'file_management/file_form.html', {
        'form': form,
        'file': file
    })

@login_required
def file_delete(request, pk):
    """View for deleting a file"""
    file = get_object_or_404(File, pk=pk)

    if request.method == 'POST':
        file.delete()
        messages.success(request, 'تم حذف الملف بنجاح.')
        return redirect('file_management:file_list')

    return render(request, 'file_management/file_confirm_delete.html', {
        'file': file
    })