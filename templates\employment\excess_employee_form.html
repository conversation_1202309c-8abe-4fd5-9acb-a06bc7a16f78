{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .employee-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
    }

    .employee-info h5 {
        color: #007bff;
        margin-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
    }

    .employee-info .row {
        margin-bottom: 10px;
    }

    .employee-info .label {
        font-weight: bold;
        color: #495057;
    }

    .employee-info .value {
        color: #212529;
    }

    .form-label {
        font-weight: bold;
    }

    .required-field::after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title mb-4">{{ title }}</h1>

            <div class="card">
                <div class="card-body">
                    <form method="post" id="excessEmployeeForm" action="{{ request.path }}">
                        {% csrf_token %}
                        <!-- Hidden fields -->
                        {{ form.employee }}

                        <!-- Employee Search Section -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="ministry_number_input" class="form-label required-field">الرقم الوزاري</label>
                                <div class="input-group">
                                    {{ form.ministry_number }}
                                    <button type="button" class="btn btn-primary" id="searchEmployeeBtn">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="ministry_number_error"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="employee_name_display" class="form-label">اسم الموظف</label>
                                {{ form.employee_name }}
                            </div>
                        </div>

                        <!-- Employee Info Section (hidden until employee is found) -->
                        <div class="employee-info d-none" id="employeeInfoSection">
                            <h5>معلومات الموظف</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="label">الرقم الوطني:</div>
                                    <div class="value" id="nationalIdDisplay">-</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="label">القسم الحالي:</div>
                                    <div class="value" id="departmentDisplay">-</div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Fields -->

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="{{ form.reason.id_for_label }}" class="form-label required-field">سبب اعتبار الموظف زائد</label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reason.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        {% if form.instance.pk %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.status.id_for_label }}" class="form-label">الحالة</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.status.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.resolution_notes.id_for_label }}" class="form-label">ملاحظات المعالجة</label>
                                {{ form.resolution_notes }}
                                {% if form.resolution_notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.resolution_notes.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <!-- Hidden fields for status and resolution_notes when adding new record -->
                        <input type="hidden" name="status" value="pending">
                        <input type="hidden" name="resolution_notes" value="">
                        {% endif %}

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Display Django messages -->
                        {% if messages %}
                        <div class="messages">
                            {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                {{ message }}
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'employment:excess_employee_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'ar',
            dir: 'rtl',
            width: '100%'
        });

        // Search for employee by ministry number
        $('#searchEmployeeBtn').on('click', function() {
            const ministryNumber = $('#ministry_number_input').val();
            if (!ministryNumber) {
                $('#ministry_number_error').text('الرجاء إدخال الرقم الوزاري').show();
                return;
            }

            // Clear previous errors
            $('#ministry_number_error').hide();

            // Show loading indicator
            $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري البحث...');
            $(this).prop('disabled', true);

            // Make AJAX request to get employee data
            $.ajax({
                url: "{% url 'employment:get_employee_by_ministry_number_json' %}",
                data: { ministry_number: ministryNumber },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Populate employee data
                        const employee = response.employee;
                        $('#employee_name_display').val(employee.full_name);
                        $('#nationalIdDisplay').text(employee.national_id || 'غير محدد');
                        $('#departmentDisplay').text(employee.display_department || 'غير محدد');
                        $('#id_department_display').val(employee.display_department || 'غير محدد');

                        // Set hidden field for employee
                        $('input[name="employee"]').val(employee.id);

                        // Show employee info section
                        $('#employeeInfoSection').removeClass('d-none');
                    } else {
                        // Show error message
                        $('#ministry_number_error').text(response.error).show();
                        $('#employee_name_display').val('');
                        $('#employeeInfoSection').addClass('d-none');
                        $('input[name="employee"]').val('');
                    }
                },
                error: function() {
                    $('#ministry_number_error').text('حدث خطأ أثناء البحث عن الموظف').show();
                },
                complete: function() {
                    // Reset button state
                    $('#searchEmployeeBtn').html('<i class="fas fa-search"></i> بحث');
                    $('#searchEmployeeBtn').prop('disabled', false);
                }
            });
        });

        // If editing an existing record, show employee info section
        {% if form.instance.pk %}
        $('#employeeInfoSection').removeClass('d-none');
        {% endif %}
    });
</script>
{% endblock %}
