{% extends 'base.html' %}
{% load static %}

{% block title %}البيانات التعريفية للموظفين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* تنسيقات أساسية */
    .id-number-container {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .id-number-display {
        margin-right: 10px;
        font-weight: 500;
        min-width: 80px;
    }
    .id-number-edit {
        width: 150px;
        margin-right: 10px;
    }
    .btn-group .btn {
        margin-right: 2px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">البيانات التعريفية للموظفين</h1>
        <div>
            <a href="{% url 'employment:export_identifications_pdf' %}" class="btn btn-danger me-2" target="_blank">
                <i class="fas fa-file-pdf"></i> تصدير PDF
            </a>
            <a href="{% url 'employment:export_identifications_excel' %}" class="btn btn-success me-2" target="_blank">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
            <a href="{% url 'employment:import_id_numbers' %}" class="btn btn-success me-2">
                <i class="fas fa-file-import"></i> استيراد أرقام الهوية
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">عدد الموظفين الكلي <span class="badge bg-primary ms-2 fs-5" style="font-size: 1.2rem; padding: 0.5rem 0.8rem;"> {{ employee_data|length }}</span></h6>
            <form class="d-flex" method="get">
                <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">بحث</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                            <th><i class="fas fa-id-badge me-1"></i> الرقم الوطني</th>
                            <th><i class="fas fa-address-card me-1"></i> رقم الهوية</th>
                            <th><i class="fas fa-birthday-cake me-1"></i> تاريخ الميلاد</th>
                            <th><i class="fas fa-map-marker-alt me-1"></i> العنوان</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in employee_data %}
                        <tr {% if not item.has_identification %}class="table-warning"{% endif %}>
                            <td>
                                <i class="fas fa-id-card text-primary me-1"></i>
                                {{ item.ministry_number }}
                            </td>
                            <td>
                                <a href="{% url 'employees:employee_detail' item.employee.pk %}" class="text-decoration-none fw-bold text-primary">
                                    {{ item.employee.full_name }}
                                </a>
                            </td>
                            <td>
                                <i class="fas fa-id-badge text-primary me-1"></i>
                                {{ item.national_id|default:"-" }}
                            </td>
                            <td>
                                {% if item.has_identification %}
                                <div class="id-number-container d-flex align-items-center justify-content-center">
                                    <span class="id-number-display me-2">{{ item.id_number }}</span>
                                    <div class="btn-group">
                                        <a href="{% url 'employment:edit_id_number' item.identification.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل رقم الهوية">
                                            <i class="fas fa-pencil-alt"></i>
                                        </a>
                                        <a href="{% url 'employment:delete_id_number' item.identification.pk %}" class="btn btn-sm btn-outline-danger" title="حذف رقم الهوية">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </div>
                                {% else %}
                                <div class="id-number-container d-flex align-items-center justify-content-center">
                                    <span class="id-number-display me-2">{{ item.id_number }}</span>
                                    <a href="{% url 'employment:add_id_number_page' %}?ministry_number={{ item.ministry_number }}" class="btn btn-sm btn-outline-success" title="إضافة رقم الهوية">
                                        <i class="fas fa-plus"></i> إضافة رقم الهوية
                                    </a>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <i class="far fa-calendar-alt text-primary me-1"></i>
                                {{ item.birth_date|default:"-" }}
                            </td>
                            <td>
                                <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                {{ item.address|default:"-" }}
                            </td>
                            <td>
                                <div class="btn-group justify-content-center">
                                    {% if item.has_identification %}
                                    <a href="{% url 'employment:employee_identification_detail' item.identification.pk %}" class="btn btn-outline-primary btn-sm" style="width: 120px;">
                                        <i class="fas fa-eye"></i> معاينة
                                    </a>
                                    {% else %}
                                    <span class="text-muted">لا توجد بيانات تعريفية</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد بيانات للموظفين</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[1, "asc"]],
            "pageLength": 25
        });

        // لا نحتاج إلى كود JavaScript للتعديل والحذف لأننا نستخدم صفحات منفصلة الآن
    });
</script>
{% endblock %}
