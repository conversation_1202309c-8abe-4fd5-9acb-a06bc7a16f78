{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء نسخة احتياطية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>إنشاء نسخة احتياطية</h2>
        <a href="{% url 'backup:backup_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات النسخة الاحتياطية</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}

                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">الاسم</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.backup_location.id_for_label }}" class="form-label">مكان حفظ النسخة الاحتياطية</label>
                    <div class="input-group">
                        {{ form.backup_location }}
                        <button type="button" id="browse-folder-btn" class="btn btn-outline-secondary">
                            <i class="fas fa-folder-open"></i> استعراض
                        </button>
                        <input type="file" id="folder-selector" webkitdirectory directory multiple style="display: none;">
                    </div>
                    {% if form.backup_location.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.backup_location.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.backup_location.help_text }}</div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> سيتم إنشاء نسخة احتياطية من قاعدة البيانات الحالية. قد تستغرق هذه العملية بعض الوقت.
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء النسخة الاحتياطية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const browseFolderBtn = document.getElementById('browse-folder-btn');
        const folderSelector = document.getElementById('folder-selector');
        const backupLocationInput = document.getElementById('{{ form.backup_location.id_for_label }}');

        // Add event listener to browse button
        browseFolderBtn.addEventListener('click', function() {
            // Show loading indicator
            browseFolderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختيار...';
            browseFolderBtn.disabled = true;

            // Call the server-side folder dialog
            fetch('{% url "backup:select_folder_dialog" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.folder_path) {
                        backupLocationInput.value = data.folder_path;
                    } else if (data.error) {
                        console.error('Error selecting folder:', data.error);
                        // Check if the error is about the main thread
                        if (data.error.includes('main thread')) {
                            alert('حدث خطأ في مربع حوار اختيار المجلد. يرجى كتابة المسار يدويًا.');
                        } else {
                            alert('حدث خطأ أثناء اختيار المجلد: ' + data.error);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error calling folder dialog:', error);
                    alert('حدث خطأ أثناء الاتصال بالخادم: ' + error.message);
                })
                .finally(() => {
                    // Restore button state
                    browseFolderBtn.innerHTML = '<i class="fas fa-folder-open"></i> استعراض';
                    browseFolderBtn.disabled = false;
                });
        });
    });
</script>
{% endblock %}