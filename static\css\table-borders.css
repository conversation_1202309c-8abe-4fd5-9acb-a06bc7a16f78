/*
 * table-borders.css
 * Archivo CSS global para añadir bordes a las tablas en todas las páginas
 */

/* Estilos para las tablas con bordes claros */
.table {
    border: 1px solid #000 !important;
}

.table th {
    border: 1px solid #000 !important;
    text-align: center;
    font-weight: bold;
    color: #fff !important;
    background-color: #2d2d2d !important;
}

.table td {
    border: 1px solid #000 !important;
    text-align: center;
    font-weight: bold;
    color: #000 !important;
}

.table thead th {
    border-bottom: 2px solid #000 !important;
}

/* Estilos para tablas con clase table-bordered */
.table-bordered {
    border: 1px solid #000 !important;
}

.table-bordered th {
    border: 1px solid #000 !important;
    text-align: center;
    font-weight: bold;
    color: #fff !important;
    background-color: #2d2d2d !important;
}

.table-bordered td {
    border: 1px solid #000 !important;
    text-align: center;
    font-weight: bold;
    color: #000 !important;
}

.table-bordered thead th {
    border-bottom: 2px solid #000 !important;
}

/* Asegurar que los números sean más grandes y visibles */
.table td:has(> .number),
.table-bordered td:has(> .number) {
    font-size: 16px;
}

/* Centrar contenido de botones en grupos */
.action-buttons,
.btn-group {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
}

/* Asegurar que el texto en todas las celdas de datos sea negro */
table td,
.detail-table td,
.data-table td,
.card-table td,
.info-table td {
    color: #000 !important;
}

/* Asegurar que el texto en todos los encabezados de tabla sea blanco */
table th,
.detail-table th,
.data-table th,
.card-table th,
.info-table th,
thead th,
th.header,
th.sorting,
th.sorting_asc,
th.sorting_desc {
    color: #fff !important;
    background-color: #2d2d2d !important;
}

/* Asegurar que los iconos en los encabezados de tabla sean blancos */
table th i,
.table th i,
.table-bordered th i,
thead th i,
th.header i,
th.sorting i {
    color: #fff !important;
}

/* Asegurar que los enlaces dentro de las tablas mantengan su color al hacer hover */
table td a:hover,
.table td a:hover,
.table-bordered td a:hover {
    color: #0056b3 !important;
}
