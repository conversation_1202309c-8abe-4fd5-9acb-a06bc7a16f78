from django.contrib import admin
from .models import SystemLog, SystemError

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'module', 'action', 'page', 'ip_address')
    list_filter = ('module', 'action', 'timestamp')
    search_fields = ('user__username', 'page', 'description', 'object_repr')
    date_hierarchy = 'timestamp'
    readonly_fields = ('timestamp', 'user', 'ip_address', 'module', 'action', 'page', 'description', 'object_id', 'object_repr')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(SystemError)
class SystemErrorAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'error_type', 'page_name', 'user', 'severity', 'status', 'occurrence_count')
    list_filter = ('error_type', 'severity', 'status', 'module', 'timestamp')
    search_fields = ('error_message', 'page_name', 'user__username', 'error_description')
    date_hierarchy = 'timestamp'
    readonly_fields = ('timestamp', 'last_occurrence', 'occurrence_count')
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('timestamp', 'error_type', 'error_message', 'error_description')
        }),
        ('موقع الخطأ', {
            'fields': ('page_url', 'page_name', 'file_path', 'line_number', 'function_name')
        }),
        ('معلومات المستخدم', {
            'fields': ('user', 'ip_address', 'user_agent')
        }),
        ('تفاصيل الخطأ', {
            'fields': ('stack_trace', 'request_method', 'request_data'),
            'classes': ('collapse',)
        }),
        ('التصنيف', {
            'fields': ('module', 'severity', 'status')
        }),
        ('الحل', {
            'fields': ('resolution_notes', 'resolved_by', 'resolved_at')
        }),
        ('إحصائيات', {
            'fields': ('occurrence_count', 'last_occurrence'),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        return False
