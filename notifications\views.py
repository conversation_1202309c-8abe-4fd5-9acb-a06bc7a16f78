from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Notification, NotificationReadStatus
from .forms import NotificationForm, BulkNotificationForm

User = get_user_model()

@login_required
def notification_list(request):
    """View to display all notifications for the current user"""
    notifications = Notification.get_user_notifications(request.user, limit=50)
    return render(request, 'notifications/notification_list.html', {
        'notifications': notifications
    })

@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """Mark a specific notification as read"""
    try:
        notification = get_object_or_404(Notification, id=notification_id)

        if notification.is_global:
            # For global notifications, create or update read status
            read_status, created = NotificationReadStatus.objects.get_or_create(
                notification=notification,
                user=request.user,
                defaults={'is_read': True, 'read_at': timezone.now()}
            )
            if not created and not read_status.is_read:
                read_status.mark_as_read()
        else:
            # For personal notifications, mark the notification itself as read
            if notification.user == request.user:
                notification.mark_as_read()
            else:
                return JsonResponse({'success': False, 'error': 'غير مصرح لك بهذا الإجراء'})

        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def mark_all_notifications_read(request):
    """Mark all notifications as read for the current user"""
    try:
        # Mark personal notifications as read
        personal_notifications = Notification.objects.filter(
            user=request.user,
            is_read=False
        )
        for notification in personal_notifications:
            notification.mark_as_read()

        # Mark global notifications as read for this user
        global_notifications = Notification.objects.filter(is_global=True)
        for notification in global_notifications:
            read_status, created = NotificationReadStatus.objects.get_or_create(
                notification=notification,
                user=request.user,
                defaults={'is_read': True, 'read_at': timezone.now()}
            )
            if not created and not read_status.is_read:
                read_status.mark_as_read()

        return JsonResponse({
            'success': True,
            'message': 'تم تحديد جميع الإشعارات كمقروءة'
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_notifications_ajax(request):
    """Get notifications via AJAX"""
    notifications = Notification.get_user_notifications(request.user, limit=10)
    unread_count = Notification.get_unread_count(request.user)

    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message,
            'type': notification.notification_type,
            'icon': notification.icon,
            'is_read': notification.is_read,
            'time_since': notification.get_time_since_created(),
            'background_color': notification.get_background_color(),
        })

    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': unread_count
    })

def is_admin(user):
    """Check if user is admin"""
    return user.is_superuser or user.is_staff

@user_passes_test(is_admin)
@login_required
def create_notification(request):
    """Create a new notification (admin only)"""
    if request.method == 'POST':
        form = BulkNotificationForm(request.POST)
        if form.is_valid():
            title = form.cleaned_data['title']
            message = form.cleaned_data['message']
            notification_type = form.cleaned_data['notification_type']
            icon = form.cleaned_data['icon']
            send_to_all = form.cleaned_data['send_to_all']
            send_to_admins = form.cleaned_data['send_to_admins']
            specific_users = form.cleaned_data['specific_users']

            # Create notifications based on selection
            created_count = 0

            if send_to_all:
                # Send to all users
                for user in User.objects.all():
                    Notification.objects.create(
                        user=user,
                        title=title,
                        message=message,
                        notification_type=notification_type,
                        icon=icon
                    )
                    created_count += 1
            elif send_to_admins:
                # Send to admins only
                for user in User.objects.filter(is_staff=True):
                    Notification.objects.create(
                        user=user,
                        title=title,
                        message=message,
                        notification_type=notification_type,
                        icon=icon
                    )
                    created_count += 1
            elif specific_users:
                # Send to specific users
                for user in specific_users:
                    Notification.objects.create(
                        user=user,
                        title=title,
                        message=message,
                        notification_type=notification_type,
                        icon=icon
                    )
                    created_count += 1

            messages.success(request, f'تم إرسال {created_count} إشعار بنجاح')
            return redirect('notifications:create_notification')
    else:
        form = BulkNotificationForm()

    return render(request, 'notifications/create_notification.html', {
        'form': form,
        'title': 'إنشاء إشعار جديد'
    })

@user_passes_test(is_admin)
@login_required
@require_POST
def delete_notification(request, notification_id):
    """Delete a specific notification (admin only)"""
    try:
        notification = get_object_or_404(Notification, id=notification_id)
        notification.delete()
        return JsonResponse({
            'success': True,
            'message': 'تم حذف الإشعار بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@user_passes_test(is_admin)
@login_required
@require_POST
def delete_all_notifications(request):
    """Delete all notifications (admin only)"""
    try:
        deleted_count = Notification.objects.count()
        Notification.objects.all().delete()
        return JsonResponse({
            'success': True,
            'message': f'تم حذف {deleted_count} إشعار بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
