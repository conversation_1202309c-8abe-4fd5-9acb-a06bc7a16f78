from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from .utils import create_notification, notify_admins, notify_all_users

# Import models from different apps
try:
    from employees.models import Employee
except ImportError:
    Employee = None

try:
    from leaves.models import Leave
except ImportError:
    Leave = None

try:
    from performance.models import PerformanceEvaluation
except ImportError:
    PerformanceEvaluation = None

try:
    from ranks.models import EmployeeRank
except ImportError:
    EmployeeRank = None

try:
    from file_management.models import FileMovement
except ImportError:
    FileMovement = None

try:
    from disciplinary.models import Disciplinary
except ImportError:
    Disciplinary = None

User = get_user_model()

# Employee signals
if Employee:
    @receiver(post_save, sender=Employee)
    def employee_created_notification(sender, instance, created, **kwargs):
        if created:
            title = "موظف جديد"
            message = f"تم إضافة الموظف {instance.full_name} إلى النظام"
            notify_admins(title, message, 'success', 'fa-user-plus')

    @receiver(post_delete, sender=Employee)
    def employee_deleted_notification(sender, instance, **kwargs):
        title = "حذف موظف"
        message = f"تم حذف الموظف {instance.full_name} من النظام"
        notify_admins(title, message, 'warning', 'fa-user-minus')

# Leave signals
if Leave:
    @receiver(post_save, sender=Leave)
    def leave_notification(sender, instance, created, **kwargs):
        if created:
            title = "طلب إجازة جديد"
            message = f"تم تقديم طلب إجازة من {instance.employee.full_name} من {instance.start_date} إلى {instance.end_date}"
            notify_admins(title, message, 'info', 'fa-calendar-plus')
        else:
            # Check if status changed
            if hasattr(instance, '_state') and instance._state.adding is False:
                title = "تحديث طلب إجازة"
                message = f"تم تحديث طلب إجازة {instance.employee.full_name}"
                notify_admins(title, message, 'info', 'fa-calendar-check')

    @receiver(post_delete, sender=Leave)
    def leave_deleted_notification(sender, instance, **kwargs):
        try:
            title = "حذف طلب إجازة"
            message = f"تم حذف طلب إجازة {instance.employee.full_name}"
            notify_admins(title, message, 'warning', 'fa-calendar-minus')
        except (AttributeError, Exception):
            title = "حذف طلب إجازة"
            message = "تم حذف طلب إجازة"
            notify_admins(title, message, 'warning', 'fa-calendar-minus')

# Performance Evaluation signals
if PerformanceEvaluation:
    @receiver(post_save, sender=PerformanceEvaluation)
    def performance_notification(sender, instance, created, **kwargs):
        if created:
            title = "تقييم أداء جديد"
            message = f"تم إضافة تقييم أداء للموظف {instance.employee.full_name} للعام {instance.year}"
            notify_admins(title, message, 'success', 'fa-chart-line')

    @receiver(post_delete, sender=PerformanceEvaluation)
    def performance_deleted_notification(sender, instance, **kwargs):
        try:
            title = "حذف تقييم أداء"
            message = f"تم حذف تقييم أداء الموظف {instance.employee.full_name} للعام {instance.year}"
            notify_admins(title, message, 'warning', 'fa-chart-line')
        except (AttributeError, Exception):
            title = "حذف تقييم أداء"
            message = "تم حذف تقييم أداء موظف"
            notify_admins(title, message, 'warning', 'fa-chart-line')

# Employee Rank signals
if EmployeeRank:
    @receiver(post_save, sender=EmployeeRank)
    def rank_notification(sender, instance, created, **kwargs):
        if created:
            title = "رتبة جديدة"
            message = f"تم إضافة رتبة {instance.rank_type.name} للموظف {instance.employee.full_name}"
            notify_admins(title, message, 'success', 'fa-medal')

    @receiver(post_delete, sender=EmployeeRank)
    def rank_deleted_notification(sender, instance, **kwargs):
        try:
            title = "حذف رتبة"
            message = f"تم حذف رتبة {instance.rank_type.name} من الموظف {instance.employee.full_name}"
            notify_admins(title, message, 'warning', 'fa-medal')
        except (AttributeError, Exception):
            # في حالة حذف الموظف كاملاً، قد لا تكون البيانات متاحة
            title = "حذف رتبة"
            message = "تم حذف رتبة موظف"
            notify_admins(title, message, 'warning', 'fa-medal')

# File Movement signals
if FileMovement:
    @receiver(post_save, sender=FileMovement)
    def file_movement_notification(sender, instance, created, **kwargs):
        if created:
            title = "حركة ملف جديدة"
            message = f"تم تسجيل حركة ملف للموظف {instance.employee.full_name}"
            notify_admins(title, message, 'info', 'fa-file-export')

# Disciplinary signals
if Disciplinary:
    @receiver(post_save, sender=Disciplinary)
    def disciplinary_notification(sender, instance, created, **kwargs):
        if created:
            title = "إجراء تأديبي جديد"
            message = f"تم إضافة إجراء تأديبي للموظف {instance.employee.full_name}"
            notify_admins(title, message, 'warning', 'fa-exclamation-triangle')

    @receiver(post_delete, sender=Disciplinary)
    def disciplinary_deleted_notification(sender, instance, **kwargs):
        try:
            title = "حذف إجراء تأديبي"
            message = f"تم حذف إجراء تأديبي للموظف {instance.employee.full_name}"
            notify_admins(title, message, 'info', 'fa-exclamation-triangle')
        except (AttributeError, Exception):
            title = "حذف إجراء تأديبي"
            message = "تم حذف إجراء تأديبي"
            notify_admins(title, message, 'info', 'fa-exclamation-triangle')

# User signals
@receiver(post_save, sender=User)
def user_notification(sender, instance, created, **kwargs):
    if created:
        title = "مستخدم جديد"
        message = f"تم إنشاء حساب مستخدم جديد: {instance.username}"
        notify_admins(title, message, 'success', 'fa-user-plus')

@receiver(post_delete, sender=User)
def user_deleted_notification(sender, instance, **kwargs):
    title = "حذف مستخدم"
    message = f"تم حذف حساب المستخدم: {instance.username}"
    notify_admins(title, message, 'warning', 'fa-user-minus')
