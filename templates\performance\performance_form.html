{% extends 'base.html' %}
{% load static %}

{% block title %}{% if evaluation %}تعديل تقرير سنوي{% else %}إضافة تقرير سنوي جديد{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if evaluation %}تعديل تقرير سنوي{% else %}إضافة تقرير سنوي جديد{% endif %}</h2>
    <a href="{% url 'performance:performance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير السنوية
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات التقرير السنوي</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.employee.id_for_label }}" class="form-label">الرقم الوزاري</label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">اختر الموظف بناءً على الرقم الوزاري</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.year.id_for_label }}" class="form-label">السنة</label>
                    {{ form.year }}
                    {% if form.year.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.year.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    <label for="{{ form.score.id_for_label }}" class="form-label">العلامة</label>
                    {{ form.score }}
                    {% if form.score.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.score.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Hidden fields -->
            <div style="display: none;">
                {{ form.max_score }}
                {{ form.evaluator }}
                {{ form.comments }}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Calculate percentage when score or max_score changes
        const scoreInput = document.getElementById('{{ form.score.id_for_label }}');
        const maxScoreInput = document.getElementById('{{ form.max_score.id_for_label }}');

        function calculatePercentage() {
            if (scoreInput.value && maxScoreInput.value) {
                const score = parseFloat(scoreInput.value);
                const maxScore = parseFloat(maxScoreInput.value);

                if (maxScore > 0) {
                    const percentage = (score / maxScore) * 100;
                    console.log(`Percentage: ${percentage.toFixed(2)}%`);
                }
            }
        }

        scoreInput.addEventListener('change', calculatePercentage);
        maxScoreInput.addEventListener('change', calculatePercentage);
    });
</script>
{% endblock %}
