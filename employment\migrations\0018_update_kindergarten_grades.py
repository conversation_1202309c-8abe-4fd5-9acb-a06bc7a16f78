from django.db import migrations

def update_kindergarten_grades(apps, schema_editor):
    """Update existing kg1 and kg2 values to kg"""
    Department = apps.get_model('employment', 'Department')
    
    # Update highest_grade
    Department.objects.filter(highest_grade='kg1').update(highest_grade='kg')
    Department.objects.filter(highest_grade='kg2').update(highest_grade='kg')
    
    # Update lowest_grade
    Department.objects.filter(lowest_grade='kg1').update(lowest_grade='kg')
    Department.objects.filter(lowest_grade='kg2').update(lowest_grade='kg')

def reverse_kindergarten_grades(apps, schema_editor):
    """Reverse the changes - set kg back to kg1"""
    Department = apps.get_model('employment', 'Department')
    
    # Reverse to kg1 (we can't know which was kg1 vs kg2, so default to kg1)
    Department.objects.filter(highest_grade='kg').update(highest_grade='kg1')
    Department.objects.filter(lowest_grade='kg').update(lowest_grade='kg1')

class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0017_alter_department_highest_grade_and_more'),
    ]

    operations = [
        migrations.RunPython(
            update_kindergarten_grades,
            reverse_kindergarten_grades,
        ),
    ]
