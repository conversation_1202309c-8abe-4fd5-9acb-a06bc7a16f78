@echo off
chcp 65001 >nul
title نظام شؤون الموظفين - الإعداد السريع
color 0B

echo ========================================
echo نظام شؤون الموظفين - الإعداد السريع
echo HR Management System - Quick Setup
echo ========================================
echo.

echo [INFO] هذا الملف سيقوم بإعداد النظام بالكامل
echo [INFO] This file will setup the system completely
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python غير مثبت!
    echo [ERROR] Python is not installed!
    echo [INFO] يرجى تشغيل install_requirements.bat أولاً
    echo [INFO] Please run install_requirements.bat first
    pause
    exit /b 1
)

echo [SUCCESS] تم العثور على Python
echo [SUCCESS] Python found
python --version
echo.

REM Check if Django is installed
python -c "import django" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Django غير مثبت!
    echo [ERROR] Django is not installed!
    echo [INFO] يرجى تشغيل install_requirements.bat أولاً
    echo [INFO] Please run install_requirements.bat first
    pause
    exit /b 1
)

echo [SUCCESS] تم العثور على Django
echo [SUCCESS] Django found
python -c "import django; print('Django version:', django.get_version())"
echo.

REM Check if manage.py exists
if not exist "manage.py" (
    echo [ERROR] ملف manage.py غير موجود!
    echo [ERROR] manage.py file not found!
    echo [INFO] تأكد من أنك في مجلد المشروع الصحيح
    echo [INFO] Make sure you are in the correct project folder
    pause
    exit /b 1
)

echo [SUCCESS] تم العثور على ملف manage.py
echo [SUCCESS] manage.py file found
echo.

echo ========================================
echo [INFO] بدء الإعداد السريع...
echo [INFO] Starting quick setup...
echo ========================================
echo.

REM Step 1: Database Migration
echo [STEP 1] إنشاء قاعدة البيانات...
echo [STEP 1] Creating database...
python manage.py migrate

if %errorlevel% neq 0 (
    echo [ERROR] فشل في إنشاء قاعدة البيانات!
    echo [ERROR] Failed to create database!
    pause
    exit /b 1
)

echo [SUCCESS] تم إنشاء قاعدة البيانات بنجاح
echo [SUCCESS] Database created successfully
echo.

REM Step 2: Collect Static Files
echo [STEP 2] جمع الملفات الثابتة...
echo [STEP 2] Collecting static files...
python manage.py collectstatic --noinput

if %errorlevel% neq 0 (
    echo [WARNING] حدث خطأ في جمع الملفات الثابتة
    echo [WARNING] Error occurred while collecting static files
    echo [INFO] يمكن تجاهل هذا الخطأ للآن
    echo [INFO] This error can be ignored for now
)

echo [SUCCESS] تم جمع الملفات الثابتة
echo [SUCCESS] Static files collected
echo.

REM Step 3: Create Superuser (Optional)
echo [STEP 3] إنشاء مستخدم مدير...
echo [STEP 3] Creating admin user...
echo.
echo [INFO] هل تريد إنشاء مستخدم مدير الآن؟ (y/n)
echo [INFO] Do you want to create an admin user now? (y/n)
set /p create_admin=

if /i "%create_admin%"=="y" (
    echo [INFO] سيتم طلب بيانات المستخدم المدير...
    echo [INFO] Admin user details will be requested...
    python manage.py createsuperuser
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] تم إنشاء المستخدم المدير بنجاح
        echo [SUCCESS] Admin user created successfully
    ) else (
        echo [WARNING] لم يتم إنشاء المستخدم المدير
        echo [WARNING] Admin user was not created
        echo [INFO] يمكنك إنشاؤه لاحقاً باستخدام: python manage.py createsuperuser
        echo [INFO] You can create it later using: python manage.py createsuperuser
    )
) else (
    echo [INFO] تم تخطي إنشاء المستخدم المدير
    echo [INFO] Skipped admin user creation
    echo [INFO] يمكنك إنشاؤه لاحقاً باستخدام: python manage.py createsuperuser
    echo [INFO] You can create it later using: python manage.py createsuperuser
)

echo.

REM Step 4: Load Initial Data (if exists)
if exist "fixtures" (
    echo [STEP 4] تحميل البيانات الأولية...
    echo [STEP 4] Loading initial data...
    
    for %%f in (fixtures\*.json) do (
        echo [INFO] تحميل %%f...
        echo [INFO] Loading %%f...
        python manage.py loaddata "%%f"
    )
    
    echo [SUCCESS] تم تحميل البيانات الأولية
    echo [SUCCESS] Initial data loaded
    echo.
)

REM Create desktop shortcut (optional)
echo [INFO] هل تريد إنشاء اختصار على سطح المكتب؟ (y/n)
echo [INFO] Do you want to create a desktop shortcut? (y/n)
set /p create_shortcut=

if /i "%create_shortcut%"=="y" (
    echo [INFO] إنشاء اختصار سطح المكتب...
    echo [INFO] Creating desktop shortcut...
    
    set "shortcut_path=%USERPROFILE%\Desktop\نظام شؤون الموظفين.lnk"
    set "target_path=%CD%\start_server.bat"
    set "icon_path=%CD%\static\images\logo.ico"
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut_path%'); $Shortcut.TargetPath = '%target_path%'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'نظام شؤون الموظفين'; $Shortcut.Save()"
    
    if exist "%shortcut_path%" (
        echo [SUCCESS] تم إنشاء اختصار سطح المكتب
        echo [SUCCESS] Desktop shortcut created
    ) else (
        echo [WARNING] فشل في إنشاء اختصار سطح المكتب
        echo [WARNING] Failed to create desktop shortcut
    )
    echo.
)

echo ========================================
echo [SUCCESS] تم الانتهاء من الإعداد السريع!
echo [SUCCESS] Quick setup completed!
echo ========================================
echo.

echo [INFO] معلومات مهمة:
echo [INFO] Important information:
echo.
echo • رابط النظام: http://127.0.0.1:8000
echo • System URL: http://127.0.0.1:8000
echo.
echo • رابط لوحة الإدارة: http://127.0.0.1:8000/admin/
echo • Admin panel URL: http://127.0.0.1:8000/admin/
echo.
echo • لتشغيل الخادم: انقر نقراً مزدوجاً على start_server.bat
echo • To run server: Double-click start_server.bat
echo.
echo • لإيقاف الخادم: اضغط Ctrl+C في نافذة الخادم
echo • To stop server: Press Ctrl+C in server window
echo.

echo [INFO] هل تريد تشغيل الخادم الآن؟ (y/n)
echo [INFO] Do you want to start the server now? (y/n)
set /p start_server=

if /i "%start_server%"=="y" (
    echo [INFO] جاري تشغيل الخادم...
    echo [INFO] Starting server...
    echo.
    echo [INFO] سيتم فتح المتصفح تلقائياً بعد 5 ثوانٍ
    echo [INFO] Browser will open automatically after 5 seconds
    echo.
    
    REM Start server in background and open browser
    start /min python manage.py runserver
    timeout /t 5 /nobreak >nul
    start http://127.0.0.1:8000
    
    echo [SUCCESS] تم تشغيل الخادم وفتح المتصفح
    echo [SUCCESS] Server started and browser opened
    echo.
    echo [INFO] يمكنك إغلاق هذه النافذة الآن
    echo [INFO] You can close this window now
) else (
    echo [INFO] يمكنك تشغيل الخادم لاحقاً باستخدام start_server.bat
    echo [INFO] You can start the server later using start_server.bat
)

echo.
echo [INFO] اضغط أي مفتاح للخروج...
echo [INFO] Press any key to exit...
pause >nul
