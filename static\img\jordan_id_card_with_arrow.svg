<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="500" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background for the ID card - light blue gradient to simulate the actual card -->
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e6f2ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b3d9ff;stop-opacity:1" />
    </linearGradient>
    <pattern id="cardPattern" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M25,0 L50,25 L25,50 L0,25 Z" fill="none" stroke="#d9e6f2" stroke-width="0.5"/>
    </pattern>
  </defs>
  
  <!-- ID card background with rounded corners -->
  <rect x="10" y="10" width="480" height="280" rx="15" ry="15" fill="url(#cardGradient)" stroke="#6699cc" stroke-width="2"/>
  <rect x="10" y="10" width="480" height="280" rx="15" ry="15" fill="url(#cardPattern)" fill-opacity="0.3"/>
  
  <!-- Star symbol (similar to the one in the image) -->
  <g transform="translate(250, 70)">
    <path d="M0,-20 L5,-5 L20,-5 L10,5 L15,20 L0,10 L-15,20 L-10,5 L-20,-5 L-5,-5 Z" fill="#003366" fill-opacity="0.7"/>
  </g>
  
  <!-- Blurred text areas to simulate the actual card text -->
  <rect x="30" y="40" width="150" height="15" rx="2" ry="2" fill="#003366" fill-opacity="0.3"/>
  <rect x="30" y="70" width="120" height="15" rx="2" ry="2" fill="#003366" fill-opacity="0.3"/>
  <rect x="30" y="100" width="140" height="15" rx="2" ry="2" fill="#003366" fill-opacity="0.3"/>
  <rect x="30" y="130" width="130" height="15" rx="2" ry="2" fill="#003366" fill-opacity="0.3"/>
  
  <!-- ID Number area with highlight -->
  <rect x="30" y="160" width="200" height="30" rx="2" ry="2" fill="#003366" fill-opacity="0.1"/>
  <rect x="30" y="160" width="200" height="30" rx="2" ry="2" stroke="#003366" stroke-width="1" fill="none"/>
  
  <!-- ID Number text and box (highlighted) -->
  <rect x="30" y="160" width="100" height="30" rx="2" ry="2" fill="#333333" fill-opacity="0.1"/>
  <text x="40" y="180" font-family="Arial" font-size="14" fill="#003366">ID no.: </text>
  <rect x="90" y="165" width="130" height="20" rx="2" ry="2" fill="#660000" fill-opacity="0.1" stroke="#ff0000" stroke-width="1"/>
  
  <!-- Red arrow pointing to ID number -->
  <path d="M300,170 L150,170" fill="none" stroke="#ff0000" stroke-width="3" stroke-linecap="round" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff0000"/>
    </marker>
  </defs>
  
  <!-- Arabic text labels -->
  <text x="350" y="50" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">مكان وتاريخ الولادة:</text>
  <text x="350" y="80" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">الصلاحية/Expiry:</text>
  <text x="350" y="110" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">مكان الإصدار:</text>
  <text x="350" y="140" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">مكان الإقامة:</text>
  <text x="350" y="170" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">فصيلة الدم:</text>
  <text x="350" y="200" font-family="Arial" font-size="14" text-anchor="end" fill="#003366" direction="rtl" unicode-bidi="bidi-override">التوقيع:</text>
  
  <!-- Explanatory text at the bottom -->
  <text x="250" y="250" font-family="Arial" font-size="14" text-anchor="middle" fill="#003366" direction="rtl" unicode-bidi="bidi-override">رقم الهوية يظهر في المنطقة المحددة باللون الأحمر</text>
</svg>
