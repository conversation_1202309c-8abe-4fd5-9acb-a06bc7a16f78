{% extends 'base.html' %}
{% load static %}

{% block title %}حذف مدرسة صفرية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .card-header {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        color: white;
    }
    
    .school-info {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .info-item {
        margin-bottom: 0.75rem;
    }
    
    .info-label {
        font-weight: 600;
        color: #5a5c69;
        display: inline-block;
        min-width: 120px;
    }
    
    .info-value {
        color: #3a3b45;
    }
    
    .specialization-badge {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
    
    .warning-icon {
        font-size: 3rem;
        color: #e74a3b;
        margin-bottom: 1rem;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        border: none;
    }
    
    .btn-danger:hover {
        background: linear-gradient(135deg, #d62c1a 0%, #a93226 100%);
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-trash text-danger me-2"></i>
            حذف مدرسة صفرية
        </h2>
        <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 text-center">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-exclamation-triangle me-2"></i>تأكيد عملية الحذف
            </h6>
        </div>
        
        <div class="card-body text-center">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h5 class="text-danger mb-3">هل أنت متأكد من حذف هذه المدرسة الصفرية؟</h5>
            <p class="text-muted mb-4">هذا الإجراء لا يمكن التراجع عنه</p>
            
            <div class="school-info">
                <div class="info-item">
                    <span class="info-label">اسم المدرسة:</span>
                    <span class="info-value"><strong>{{ zero_school.school_name }}</strong></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">التخصص:</span>
                    {% if zero_school.specialization == 'teacher' %}
                        <span class="badge bg-primary specialization-badge">
                            <i class="fas fa-chalkboard-teacher me-1"></i>{{ zero_school.get_specialization_display }}
                        </span>
                    {% elif zero_school.specialization == 'user' %}
                        <span class="badge bg-info specialization-badge">
                            <i class="fas fa-user me-1"></i>{{ zero_school.get_specialization_display }}
                        </span>
                    {% elif zero_school.specialization == 'guard' %}
                        <span class="badge bg-warning specialization-badge">
                            <i class="fas fa-shield-alt me-1"></i>{{ zero_school.get_specialization_display }}
                        </span>
                    {% endif %}
                </div>
                
                <div class="info-item">
                    <span class="info-label">عدد الشواغر:</span>
                    <span class="badge bg-danger fs-6">{{ zero_school.vacancies_count }}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">المبرر:</span>
                    <div class="info-value mt-2">
                        <small>{{ zero_school.justification }}</small>
                    </div>
                </div>
                
                {% if zero_school.actions %}
                <div class="info-item">
                    <span class="info-label">الإجراءات:</span>
                    <div class="info-value mt-2">
                        <small>{{ zero_school.actions }}</small>
                    </div>
                </div>
                {% endif %}
                
                <div class="info-item">
                    <span class="info-label">تاريخ الإضافة:</span>
                    <span class="info-value">{{ zero_school.created_at|date:"Y-m-d H:i" }}</span>
                </div>
            </div>
            
            <form method="post" class="d-inline">
                {% csrf_token %}
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه المدرسة الصفرية؟')">
                        <i class="fas fa-trash"></i> تأكيد الحذف
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="text-center mt-3">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            سيتم حذف جميع البيانات المرتبطة بهذه المدرسة الصفرية نهائياً
        </small>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation on form submit
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذه المدرسة الصفرية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}