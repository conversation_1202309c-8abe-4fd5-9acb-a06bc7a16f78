{% extends 'base.html' %}
{% load static %}

{% block title %}العقوبات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق بسيط للفلاتر */
    .filters-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
    }

    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تحسين مظهر الأزرار */
    .btn-filter {
        padding: 8px 12px;
        height: 100%;
    }

    /* تنسيق الإحصائيات */
    .stats-container {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    .stat-badge {
        font-size: 1.2rem;
        padding: 0.5rem 0.8rem;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">العقوبات</h1>
        <a href="{% url 'disciplinary:penalty_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة عقوبة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">جميع العقوبات</h6>
        </div>
        <div class="card-body">
            <!-- إحصائيات وفلاتر البحث -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="stat-item">
                    <span class="badge bg-primary stat-badge">{{ employee_count }}</span>
                    <span class="fw-bold">موظف لديه عقوبات</span>
                </div>

                <div class="d-flex">
                    <!-- شريط البحث -->
                    <div class="me-2">
                        <div class="input-group">
                            <input type="text" class="form-control filter-control" id="searchInput" placeholder="بحث..." value="{{ search_query|default:'' }}">
                            <button class="btn btn-dark" type="button" onclick="applyFilters()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- فلتر السنة -->
                    <div class="me-2">
                        <select class="form-control filter-control" id="yearFilter" onchange="applyFilters()">
                            <option value="">جميع السنوات</option>
                            {% for year in years %}
                            <option value="{{ year }}" {% if year_filter == year|stringformat:"i" %}selected{% endif %}>
                                {{ year }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- فلتر نوع العقوبة -->
                    <div class="me-2">
                        <select class="form-control filter-control" id="penaltyTypeFilter" onchange="applyFilters()">
                            <option value="">جميع أنواع العقوبات</option>
                            {% for type in penalty_types %}
                            <option value="{{ type.id }}" {% if penalty_type_filter == type.id|stringformat:"i" %}selected{% endif %}>
                                {{ type.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- زر إعادة ضبط الفلاتر -->
                    <div>
                        <a href="{% url 'disciplinary:penalty_list' %}" class="btn btn-secondary btn-filter" title="إعادة تعيين الفلاتر">
                            <i class="fas fa-redo me-1"></i> إعادة الضبط
                        </a>
                    </div>
                </div>
            </div>

            <!-- جدول العقوبات -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-user me-1"></i> الموظف</th>
                            <th><i class="fas fa-exclamation-triangle me-1"></i> نوع العقوبة</th>
                            <th><i class="fas fa-calendar-alt me-1"></i> التاريخ</th>
                            <th><i class="fas fa-hashtag me-1"></i> رقم القرار</th>
                            <th><i class="fas fa-calendar-check me-1"></i> تاريخ القرار</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for penalty in penalties %}
                        <tr>
                            <td>
                                <a href="{% url 'employees:employee_detail' penalty.employee.pk %}">
                                    {{ penalty.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ penalty.penalty_type.name }}</td>
                            <td>{{ penalty.date|date:"Y-m-d" }}</td>
                            <td>{{ penalty.decision_number|default:"-" }}</td>
                            <td>{% if penalty.decision_date %}{{ penalty.decision_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
                            <td>
                                <a href="{% url 'disciplinary:penalty_detail' penalty.pk %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{% url 'disciplinary:penalty_update' penalty.pk %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'disciplinary:penalty_delete' penalty.pk %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد عقوبات</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Apply filters function
    window.applyFilters = function() {
        const year = $('#yearFilter').val();
        const penaltyType = $('#penaltyTypeFilter').val();
        const search = $('#searchInput').val();

        let url = '?';
        if (year) url += `year=${year}&`;
        if (penaltyType) url += `penalty_type=${penaltyType}&`;
        if (search) url += `search=${search}&`;

        // Remove trailing &
        if (url.endsWith('&')) {
            url = url.slice(0, -1);
        }

        // If only ? is left, remove it
        if (url === '?') {
            url = '';
        }

        window.location.href = url;
    };

    // Add event listener for search input
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            applyFilters();
            e.preventDefault();
        }
    });
</script>
{% endblock %}