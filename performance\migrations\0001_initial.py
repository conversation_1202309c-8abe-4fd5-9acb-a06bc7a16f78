# Generated by Django 5.2 on 2025-04-07 10:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField(verbose_name='Year')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Score')),
                ('max_score', models.DecimalField(decimal_places=2, default=100.0, max_digits=5, verbose_name='Maximum Score')),
                ('evaluator', models.CharField(blank=True, max_length=255, null=True, verbose_name='Evaluator')),
                ('comments', models.TextField(blank=True, null=True, verbose_name='Comments')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_evaluations', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Performance Evaluation',
                'verbose_name_plural': 'Performance Evaluations',
                'ordering': ['-year'],
                'unique_together': {('employee', 'year')},
            },
        ),
    ]
