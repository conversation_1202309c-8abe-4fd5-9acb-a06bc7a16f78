{% extends 'base.html' %}
{% load static %}

{% block title %}النقل الفني{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">النقل الفني</h1>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات النقل الفني</h6>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <form method="post" id="technical-transfer-form">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.ministry_number.id_for_label }}">{{ form.ministry_number.label }}</label>
                                    {{ form.ministry_number }}
                                    <button type="button" id="search-employee-btn" class="btn btn-primary mt-2">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.employee_name.id_for_label }}">{{ form.employee_name.label }}</label>
                                    {{ form.employee_name }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.current_department.id_for_label }}">{{ form.current_department.label }}</label>
                                    {{ form.current_department }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.new_department.id_for_label }}">{{ form.new_department.label }}</label>
                                    {{ form.new_department }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <a href="{% url 'home:technical_transfer_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Search for employee when search button is clicked
        $('#search-employee-btn').click(function() {
            searchEmployee();
        });

        // Also search when Enter key is pressed in ministry number field
        $('#ministry_number_input').keypress(function(e) {
            if (e.which == 13) {
                e.preventDefault();
                searchEmployee();
            }
        });

        function searchEmployee() {
            const ministryNumber = $('#ministry_number_input').val();

            if (!ministryNumber) {
                showAlert('error', 'الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            showLoading();

            // Make AJAX request to search for employee
            $.ajax({
                url: '{% url "home:technical_transfer_search" %}',
                data: {
                    'ministry_number': ministryNumber
                },
                dataType: 'json',
                success: function(data) {
                    hideLoading();

                    if (data.success) {
                        // Fill form fields with employee data
                        $('#id_employee_name').val(data.employee.full_name);
                        $('#id_current_department').val(data.employee.current_department);
                        $('#id_last_position').val(data.employee.last_position);

                        // Focus on new department field
                        $('#id_new_department').focus();
                    } else {
                        showAlert('error', data.error);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showAlert('error', 'حدث خطأ أثناء البحث عن الموظف. الرجاء المحاولة مرة أخرى.');
                    console.error(error);
                }
            });
        }

        function showAlert(type, message) {
            // Remove any existing alerts
            $('.alert').remove();

            // Create new alert
            const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            const alertHtml = `<div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>`;

            // Insert alert before form
            $('#technical-transfer-form').before(alertHtml);
        }

        function showLoading() {
            // Disable search button and show spinner
            $('#search-employee-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري البحث...');
        }

        function hideLoading() {
            // Enable search button and restore text
            $('#search-employee-btn').prop('disabled', false).html('<i class="fas fa-search"></i> بحث');
        }
    });
</script>
{% endblock %}
