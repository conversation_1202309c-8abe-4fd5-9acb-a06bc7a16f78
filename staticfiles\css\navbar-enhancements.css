/* Navbar Enhancements CSS */

/* Notifications */
.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.dropdown-header {
    font-weight: 600;
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    white-space: normal;
}

.dropdown-item:active {
    background-color: #f8f9fa;
    color: #212529;
}

.dropdown-divider {
    margin: 0;
}

.nav-link .badge {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
}

/* User Profile */
.user-info-container {
    display: flex;
    flex-direction: column;
    text-align: right;
    margin-right: 5px;
    line-height: 1.2;
}

.username {
    font-weight: 600;
    font-size: 0.9rem;
}

.account-type {
    font-size: 0.75rem;
    color: #6c757d;
    display: block;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .user-info-container {
        display: none;
    }

    .nav-link.dropdown-toggle.d-flex .fa-user-circle {
        margin-right: 0;
        margin-left: 0;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .username {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* Notification animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.nav-link .badge {
    animation: pulse 2s infinite;
}

/* Hover effects */
.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item:hover .bg-primary,
.dropdown-item:hover .bg-success,
.dropdown-item:hover .bg-warning {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Notification icons */
.dropdown-item .rounded-circle {
    transition: all 0.2s ease;
}

/* User profile dropdown */
.dropdown-toggle::after {
    margin-right: 0.5rem;
}

.dropdown-toggle .fa-user-circle {
    font-size: 1.2rem;
    margin-left: 0.5rem;
}

/* Navbar adjustments */
.navbar {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.navbar-brand {
    font-weight: 700;
}

/* Dropdown menu animations */
.dropdown-menu {
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Notification badge position adjustment for RTL */
.position-absolute.top-0.start-100 {
    right: auto;
    left: 0;
}

/* RTL specific adjustments */
.me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

/* Fix dropdown toggle arrow position for RTL */
.dropdown-toggle::after {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Fix user profile alignment */
.nav-link.dropdown-toggle.d-flex {
    text-align: right;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;
}

.nav-link.dropdown-toggle.d-flex:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.dropdown-toggle.d-flex .fa-user-circle {
    font-size: 1.5rem;
    color: #fff;
}

/* Fix notification badge position */
.position-absolute.top-0.start-100.translate-middle {
    left: 0 !important;
    right: auto !important;
    transform: translate(-50%, -50%) !important;
}
