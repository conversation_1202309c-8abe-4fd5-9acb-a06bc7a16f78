{% extends 'base.html' %}
{% load static %}

{% block title %}حذ<PERSON> حقل BTEC{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .delete-card .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 20px;
    }
    
    .field-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .detail-item:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
    }
    
    .detail-value {
        color: #6c757d;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-trash text-danger me-2"></i>
                حذف حقل BTEC
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:btec_field_list' %}">حقول BTEC</a></li>
                    <li class="breadcrumb-item active">حذف حقل BTEC</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-card">
                <div class="card-header text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body text-center p-4">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    
                    <h4 class="text-danger mb-3">هل أنت متأكد من حذف هذا الحقل؟</h4>
                    <p class="text-muted mb-4">
                        سيتم حذف حقل BTEC نهائياً ولا يمكن التراجع عن هذا الإجراء.
                    </p>

                    <!-- Field Details -->
                    <div class="field-details">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-tag me-2"></i>
                            تفاصيل الحقل
                        </h6>
                        
                        <div class="detail-item">
                            <span class="detail-label">اسم الحقل:</span>
                            <span class="detail-value">
                                <strong>{{ field.name }}</strong>
                            </span>
                        </div>
                        
                        {% if field.description %}
                        <div class="detail-item">
                            <span class="detail-label">الوصف:</span>
                            <span class="detail-value">{{ field.description }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="detail-item">
                            <span class="detail-label">تاريخ الإنشاء:</span>
                            <span class="detail-value">{{ field.created_at|date:"Y/m/d H:i" }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">آخر تحديث:</span>
                            <span class="detail-value">{{ field.updated_at|date:"Y/m/d H:i" }}</span>
                        </div>
                    </div>

                    <!-- Warning Note -->
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تأكد من عدم وجود معلمين مرتبطين بهذا الحقل قبل الحذف.
                    </div>

                    <!-- Buttons -->
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:btec_field_list' %}" class="btn btn-cancel text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-delete text-white">
                                <i class="fas fa-trash me-2"></i>حذف نهائياً
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا الحقل نهائياً؟\n\nسيؤثر هذا على جميع المعلمين المرتبطين بهذا الحقل.')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
