from django.urls import path
from . import views

app_name = 'system_logs'

urlpatterns = [
    path('', views.system_log_list, name='system_log_list'),
    path('ajax/', views.get_system_logs_ajax, name='get_system_logs_ajax'),
    path('clear/', views.clear_system_logs, name='clear_system_logs'),
    path('update-descriptions/', views.update_log_descriptions, name='update_log_descriptions'),
    
    # System Errors URLs
    path('errors/', views.system_error_list, name='system_error_list'),
    path('errors/<int:error_id>/', views.system_error_detail, name='system_error_detail'),
    path('errors/<int:error_id>/update-status/', views.update_error_status, name='update_error_status'),
]
