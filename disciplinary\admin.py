from django.contrib import admin
from .models import PenaltyType, Penalty

@admin.register(PenaltyType)
class PenaltyTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')

@admin.register(Penalty)
class PenaltyAdmin(admin.ModelAdmin):
    list_display = ('employee', 'penalty_type', 'date', 'decision_number', 'decision_date')
    list_filter = ('penalty_type', 'date')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'description', 'decision_number')
    date_hierarchy = 'date'
