/*
 * نظام تنسيق موحد لجميع صفحات النظام
 * يتضمن ألوان وتنسيقات متناسقة مع القائمة الجانبية
 */

/* تنسيق الجداول */
.table {
    min-width: 1200px;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: none;
}

.table th {
    text-align: center;
    vertical-align: middle;
    font-size: 0.9rem;
    white-space: nowrap;
    background: #2d2d2d; /* لون القائمة الجانبية - الرمادي الداكن */
    color: white;
    font-weight: 600;
    padding: 15px 10px;
    border: none;
    position: relative;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
    padding: 12px 10px;
    border-bottom: 1px solid #e3e6f0;
    border-right: 1px solid #f8f9fc;
    color: #5a5c69;
    font-weight: 600; /* جعل جميع الخط غامق */
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 110, 95, 0.05);
    transition: background-color 0.3s ease;
}

/* تمييز الصفوف بألوان متناوبة */
.table-hover tbody tr:nth-child(even) {
    background-color: #f8f9fc;
}

/* تطبيق التنسيقات على جميع أنواع الجداول */
.table-bordered,
.table-striped,
.table-responsive table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: none;
}

.table-bordered th,
.table-striped th,
.table-responsive table th {
    text-align: center;
    vertical-align: middle;
    font-size: 0.9rem;
    white-space: nowrap;
    background: #2d2d2d; /* لون القائمة الجانبية - الرمادي الداكن */
    color: white;
    font-weight: 600;
    padding: 15px 10px;
    border: none;
    position: relative;
}

.table-bordered td,
.table-striped td,
.table-responsive table td {
    vertical-align: middle;
    font-size: 0.9rem;
    padding: 12px 10px;
    border-bottom: 1px solid #e3e6f0;
    border-right: 1px solid #f8f9fc;
    color: #5a5c69;
    font-weight: 600; /* جعل جميع الخط غامق */
}

/* إزالة الحدود الافتراضية للجداول المحددة */
.table-bordered,
.table-bordered th,
.table-bordered td {
    border: none;
}

.table-bordered td {
    border-bottom: 1px solid #e3e6f0;
    border-right: 1px solid #f8f9fc;
}

/* تحسين مظهر الجداول في الشاشات الصغيرة */
@media (max-width: 992px) {
    .table,
    .table-bordered,
    .table-striped,
    .table-responsive table {
        min-width: 100%;
    }
}

/* تنسيق البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    background: #006e5f; /* لون القائمة الجانبية - الأخضر الزمردي */
    color: white;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    position: relative;
}

.card-header h6 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.card-header .badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1rem;
    padding: 0.5rem 0.8rem;
    border-radius: 30px;
    margin-right: 10px;
}

.card-body {
    padding: 1.5rem;
    background-color: #fff;
}

/* تنسيق شريط البحث */
.card-header form {
    display: flex;
    align-items: center;
}

.card-header .form-control {
    border: none;
    border-radius: 20px 0 0 20px;
    padding: 0.6rem 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-header .form-control:focus {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.card-header .btn-outline-primary {
    border-radius: 0 20px 20px 0;
    border: none;
    background-color: #2d2d2d; /* لون متناسق مع القائمة الجانبية - الرمادي الداكن */
    color: white;
    padding: 0.6rem 1.2rem;
    transition: all 0.3s ease;
}

.card-header .btn-outline-primary:hover {
    background-color: #222222; /* لون متناسق مع القائمة الجانبية - أغمق قليلاً */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

/* تنسيق النوافذ المنبثقة */
.modal-header {
    background: #006e5f; /* لون القائمة الجانبية - الأخضر الزمردي */
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
    color: white;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body input,
.modal-body select,
.modal-body textarea {
    background-color: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    padding: 0.6rem 1rem;
    transition: all 0.3s ease;
    pointer-events: auto;
    opacity: 1;
}

.modal-body input:focus,
.modal-body select:focus,
.modal-body textarea:focus {
    border-color: #006e5f;
    box-shadow: 0 0 0 0.2rem rgba(0, 110, 95, 0.25);
}

.modal-footer {
    border-top: 1px solid #f8f9fc;
    padding: 1rem 1.5rem;
}

.modal-footer .btn-primary {
    background-color: #006e5f;
    border-color: #006e5f;
    font-weight: 600;
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
}

.modal-footer .btn-primary:hover {
    background-color: #005a4d;
    border-color: #004a40;
}

/* تنسيق الأزرار */
.btn-group {
    white-space: nowrap;
    display: flex;
    justify-content: center;
}

.btn-group .btn {
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تنسيق أزرار الإجراءات (عرض، تعديل، حذف) */
.btn-group .btn-info,
.btn-group .btn-warning,
.btn-group .btn-danger,
.btn-group .btn-success,
.btn-group .btn-primary {
    border: none;
}

/* زر العرض - أبيض */
.btn-group .btn-info {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #dddddd;
}
.btn-group .btn-info:hover {
    background-color: #f8f9fc;
    color: #000000;
}

/* زر التعديل - أسود */
.btn-group .btn-warning {
    background-color: #000000;
    color: #ffffff;
}
.btn-group .btn-warning:hover {
    background-color: #333333;
    color: #ffffff;
}

/* زر الحذف - أحمر */
.btn-group .btn-danger {
    background-color: #e74a3b;
    color: #ffffff;
    border: 1px solid #e74a3b;
}
.btn-group .btn-danger:hover {
    background-color: #e02d1b;
    color: #ffffff;
}

/* زر الإضافة */
.btn-group .btn-success {
    background-color: #1cc88a;
}
.btn-group .btn-success:hover {
    background-color: #17a673;
}

/* زر آخر */
.btn-group .btn-primary {
    background-color: #4e73df;
}
.btn-group .btn-primary:hover {
    background-color: #2e59d9;
}

/* تنسيق الأيقونات داخل الأزرار */
.btn-group .btn i {
    margin: 0;
    font-size: 0.875rem;
}

/* تنسيق أزرار الإجراءات في الجداول */
table .btn-info,
table .btn-warning,
table .btn-danger {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* تأكيد ألوان أزرار الإجراءات في الجداول */
table .btn-info {
    background-color: #ffffff !important;
    border-color: #dddddd !important;
    color: #000000 !important;
}
table .btn-info:hover {
    background-color: #f8f9fc !important;
    border-color: #dddddd !important;
    color: #000000 !important;
}

table .btn-warning {
    background-color: #000000 !important;
    border-color: #000000 !important;
    color: #ffffff !important;
}
table .btn-warning:hover {
    background-color: #333333 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

table .btn-danger {
    background-color: #e74a3b !important;
    border-color: #e74a3b !important;
    color: #ffffff !important;
}
table .btn-danger:hover {
    background-color: #e02d1b !important;
    border-color: #e02d1b !important;
    color: #ffffff !important;
}

/* تنسيق زر شهادة الخبرة */
table .btn-primary {
    background-color: #ffffff !important;
    border-color: #dddddd !important;
    color: #000000 !important;
}
table .btn-primary:hover {
    background-color: #f8f9fc !important;
    border-color: #dddddd !important;
    color: #000000 !important;
}

/* تنسيق زر شهادة الخبرة في صفحة شهادات الخبرة */
a[href*="experience-certificate-pdf"],
a[href*="experience-certificate-pdf"].btn,
a[href*="experience-certificate-pdf"].btn-primary,
a[href*="experience-certificate-pdf"].btn-sm {
    background-color: #ffffff !important;
    border-color: #dddddd !important;
    color: #000000 !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    border-radius: 4px !important;
    text-decoration: none !important;
}

a[href*="experience-certificate-pdf"]:hover,
a[href*="experience-certificate-pdf"].btn:hover,
a[href*="experience-certificate-pdf"].btn-primary:hover,
a[href*="experience-certificate-pdf"].btn-sm:hover {
    background-color: #f8f9fc !important;
    border-color: #dddddd !important;
    color: #000000 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* تنسيق أيقونة زر شهادة الخبرة */
a[href*="experience-certificate-pdf"] i,
a[href*="experience-certificate-pdf"].btn i,
a[href*="experience-certificate-pdf"].btn-primary i,
a[href*="experience-certificate-pdf"].btn-sm i {
    margin-left: 0.25rem !important;
    font-size: 0.875rem !important;
}

/* تنسيق الأزرار خارج مجموعة الأزرار */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 4px;
}

.btn-sm i {
    margin-left: 0.25rem;
}

/* تنسيق أزرار الإجراءات في الصفحات */
.card-header .btn,
.card-body .btn,
.modal-footer .btn,
.modal-body .btn {
    border-radius: 4px;
    transition: all 0.2s ease;
    margin: 0 2px;
}

.card-header .btn:hover,
.card-body .btn:hover,
.modal-footer .btn:hover,
.modal-body .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تنسيق أزرار الإجراءات حسب النوع */
.btn-info {
    background-color: #ffffff;
    border-color: #dddddd;
    color: #000000;
}
.btn-info:hover {
    background-color: #f8f9fc;
    border-color: #dddddd;
    color: #000000;
}

.btn-warning {
    background-color: #000000;
    border-color: #000000;
    color: #ffffff;
}
.btn-warning:hover {
    background-color: #333333;
    border-color: #333333;
    color: #ffffff;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
    color: #ffffff;
}
.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #e02d1b;
    color: #ffffff;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
    color: white;
}
.btn-success:hover {
    background-color: #17a673;
    border-color: #17a673;
    color: white;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}
.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2e59d9;
    color: white;
}

/* تنسيق الشارات */
.badge {
    padding: 0.5rem 0.8rem;
    border-radius: 30px;
}

/* تنسيق الأيقونات في عناوين الجداول */
.table th i,
.table-bordered th i,
.table-striped th i,
.table-responsive table th i {
    margin-left: 0.5rem;
    opacity: 0.8;
}

/* تنسيق الأيقونات في خلايا الجداول */
.table td i,
.table-bordered td i,
.table-striped td i,
.table-responsive table td i {
    margin-left: 0.3rem;
}

/* تنسيق الشارات في الجداول */
.table .badge,
.table-bordered .badge,
.table-striped .badge,
.table-responsive table .badge {
    font-size: 0.85rem;
    padding: 0.35rem 0.65rem;
}

/* تنسيق الروابط في الجداول */
.table a,
.table-bordered a,
.table-striped a,
.table-responsive table a {
    text-decoration: none;
    font-weight: 600;
    color: #006e5f;
    transition: color 0.2s ease;
}

.table a:hover,
.table-bordered a:hover,
.table-striped a:hover,
.table-responsive table a:hover {
    color: #004a40;
}

/* تنسيق الرسائل الفارغة */
.empty-message {
    text-align: center;
    padding: 3rem 0;
}

.empty-message i {
    font-size: 3rem;
    color: #d1d3e2;
    margin-bottom: 1rem;
}

.empty-message h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-message p {
    color: #858796;
    margin-bottom: 1.5rem;
}

/* تنسيق الصفوف الفارغة في الجداول */
.table tr td[colspan],
.table-bordered tr td[colspan],
.table-striped tr td[colspan],
.table-responsive table tr td[colspan] {
    text-align: center;
    padding: 2rem 1rem;
}

.table tr td[colspan] i,
.table-bordered tr td[colspan] i,
.table-striped tr td[colspan] i,
.table-responsive table tr td[colspan] i {
    font-size: 3rem;
    color: #d1d3e2;
    margin-bottom: 1rem;
    display: block;
}

.table tr td[colspan] h5,
.table-bordered tr td[colspan] h5,
.table-striped tr td[colspan] h5,
.table-responsive table tr td[colspan] h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.table tr td[colspan] p,
.table-bordered tr td[colspan] p,
.table-striped tr td[colspan] p,
.table-responsive table tr td[colspan] p {
    color: #858796;
    margin-bottom: 1.5rem;
}

/* تنسيق أزرار الإجراءات في الصفوف الفارغة */
.table tr td[colspan] .btn,
.table-bordered tr td[colspan] .btn,
.table-striped tr td[colspan] .btn,
.table-responsive table tr td[colspan] .btn {
    margin-top: 1rem;
}
