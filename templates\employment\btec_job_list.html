{% extends 'base.html' %}
{% load static %}

{% block title %}وظائف BTEC - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .job-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 15px;
        overflow: hidden;
    }

    .job-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .job-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
        text-align: center;
    }

    .job-card .card-body {
        padding: 25px;
        background: #f8f9fa;
    }

    .job-title {
        font-size: 1.4rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .teacher-count {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin: 15px 0;
    }

    .job-description {
        color: #6c757d;
        font-style: italic;
        margin-bottom: 20px;
        min-height: 50px;
    }

    .btn-group-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .btn-action {
        padding: 8px 15px;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: scale(1.05);
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 30px;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stats-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-briefcase text-primary me-2"></i>
                وظائف BTEC
            </h1>
            <p class="text-muted mb-0">إدارة وظائف BTEC</p>
        </div>
        <div>
            <a href="{% url 'employment:btec_job_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> إضافة وظيفة جديدة
            </a>
            <a href="{% url 'employment:btec_list' %}" class="btn btn-info">
                <i class="fas fa-graduation-cap"></i> معلمي BTEC
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ jobs.count }}</div>
                <div class="stats-label">إجمالي الوظائف</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stats-card">
                <div class="stats-number">{{ total_teachers }}</div>
                <div class="stats-label">إجمالي المعلمين</div>
            </div>
        </div>
    </div>

    <!-- Jobs Grid -->
    {% if jobs %}
        <div class="row">
            {% for job in jobs %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card job-card">
                    <div class="card-header">
                        <div class="job-title">{{ job.name }}</div>
                        <div class="teacher-count">{{ job.teacher_count }}</div>
                        <small>معلم</small>
                    </div>
                    <div class="card-body">
                        <div class="job-description">
                            {{ job.description|default:"لا يوجد وصف" }}
                        </div>
                        <div class="btn-group-actions">
                            <a href="{% url 'employment:btec_job_update' job.pk %}" 
                               class="btn btn-warning btn-action">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            {% if job.teacher_count == 0 %}
                            <a href="{% url 'employment:btec_job_delete' job.pk %}" 
                               class="btn btn-danger btn-action">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                            {% else %}
                            <button class="btn btn-secondary btn-action" disabled 
                                    title="لا يمكن حذف الوظيفة لوجود معلمين مرتبطين بها">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-briefcase"></i>
                    <h4>لا توجد وظائف BTEC</h4>
                    <p>لم يتم إضافة أي وظائف BTEC بعد</p>
                    <a href="{% url 'employment:btec_job_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة وظيفة BTEC الأولى
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add hover effects
    $('.job-card').hover(
        function() {
            $(this).find('.card-header').css('background', 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)');
        },
        function() {
            $(this).find('.card-header').css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
        }
    );

    // Tooltip for disabled delete buttons
    $('[title]').tooltip();
});
</script>
{% endblock %}
