{% extends 'base.html' %}
{% load static %}

{% block title %}حذف حركة النقل - {{ transfer.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        border: 2px solid #e74a3b;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(231, 74, 59, 0.15);
    }
    .delete-card .card-header {
        background: linear-gradient(45deg, #e74a3b, #c82333);
        color: white;
        border-bottom: none;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .transfer-info {
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
    }
    .info-item {
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 0;
    }
    .info-item:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 600;
        color: #5a5c69;
    }
    .info-value {
        color: #3a3b45;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-trash text-danger"></i>
            حذف حركة النقل
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> عرض التفاصيل
            </a>
            <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Message -->
            <div class="warning-box">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">تحذير مهم!</h5>
                        <p class="mb-0">
                            أنت على وشك حذف سجل حركة النقل هذا نهائياً. هذا الإجراء لا يمكن التراجع عنه.
                            يرجى التأكد من صحة قرارك قبل المتابعة.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation Card -->
            <div class="card delete-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-trash"></i>
                        تأكيد حذف حركة النقل
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Transfer Information -->
                    <div class="transfer-info mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-info-circle text-primary"></i>
                            معلومات حركة النقل المراد حذفها:
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">الموظف:</div>
                                    <div class="info-value">
                                        <strong>{{ transfer.employee.full_name }}</strong>
                                        <br>
                                        <span class="badge bg-primary">{{ transfer.employee.ministry_number }}</span>
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">التخصص:</div>
                                    <div class="info-value">{{ transfer.employee.specialization }}</div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">القسم السابق:</div>
                                    <div class="info-value">
                                        <span class="badge bg-warning text-dark">{{ transfer.previous_department }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">القسم الجديد:</div>
                                    <div class="info-value">
                                        <span class="badge bg-success">{{ transfer.new_department }}</span>
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">تاريخ النقل:</div>
                                    <div class="info-value">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        {{ transfer.transfer_date }}
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">تاريخ المباشرة:</div>
                                    <div class="info-value">
                                        <i class="fas fa-calendar-check text-success"></i>
                                        {{ transfer.start_date }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if transfer.notes %}
                        <div class="info-item mt-3">
                            <div class="info-label">الملاحظات:</div>
                            <div class="info-value">
                                <div class="alert alert-light">
                                    {{ transfer.notes|linebreaks }}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="info-item">
                            <div class="info-label">تاريخ الإنشاء:</div>
                            <div class="info-value">
                                <small class="text-muted">{{ transfer.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Consequences Warning -->
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-circle"></i>
                            نتائج الحذف:
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم حذف جميع المعلومات المتعلقة بهذه الحركة نهائياً</li>
                            <li>لن يكون بالإمكان استرداد هذه البيانات</li>
                            <li>قد يؤثر ذلك على التقارير والإحصائيات</li>
                            <li>سيتم تحديث سجلات النظام تلقائياً</li>
                        </ul>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="post" id="deleteForm">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash"></i> نعم، احذف نهائياً
                                </button>
                                <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" 
                                   class="btn btn-secondary ms-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                            <div>
                                <a href="{% url 'employees:internal_transfers_list' %}" 
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-list"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0">
                        <i class="fas fa-lightbulb"></i>
                        معلومة مفيدة
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">
                        <strong>بديل عن الحذف:</strong> 
                        إذا كنت تريد تصحيح معلومات النقل، يمكنك استخدام خيار "تعديل" بدلاً من الحذف.
                        هذا سيحافظ على تسلسل البيانات وسلامة السجلات.
                    </p>
                    <div class="mt-3">
                        <a href="{% url 'employees:internal_transfer_update' transfer.pk %}" 
                           class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> تعديل بدلاً من الحذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete() {
        // Show confirmation dialog
        if (confirm('هل أنت متأكد تماماً من حذف سجل حركة النقل هذا؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
            // Show second confirmation
            if (confirm('تأكيد نهائي: سيتم حذف السجل نهائياً.\n\nاضغط OK للمتابعة أو Cancel للإلغاء.')) {
                document.getElementById('deleteForm').submit();
            }
        }
    }

    // Prevent accidental form submission
    document.getElementById('deleteForm').addEventListener('submit', function(e) {
        e.preventDefault();
        confirmDelete();
    });

    // Add warning on page unload if user was about to delete
    let deleteIntention = false;
    document.querySelector('.btn-danger').addEventListener('click', function() {
        deleteIntention = true;
    });

    window.addEventListener('beforeunload', function(e) {
        if (deleteIntention) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
</script>
{% endblock %}