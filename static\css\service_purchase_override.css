/* Service Purchase Table Text Color Override */

/* Force all table text to be dark */
.service-purchase-table tbody td,
.service-purchase-table tbody td span:not(.badge),
.service-purchase-table tbody td strong,
.service-purchase-table tbody td .text-dark {
    color: #212529 !important;
    font-weight: 500 !important;
}

/* Specific overrides for different elements */
.service-purchase-table tbody td .fw-bold {
    font-weight: 600 !important;
    color: #212529 !important;
}

.service-purchase-table tbody td .fw-medium {
    font-weight: 500 !important;
    color: #212529 !important;
}

/* Keep badges white text */
.service-purchase-table tbody td .badge {
    color: white !important;
}

/* Override Bootstrap default colors */
.table tbody tr td {
    color: #212529 !important;
}

.table tbody tr:hover td {
    color: #212529 !important;
}

/* Additional specificity */
.service-purchase-card .table tbody td {
    color: #212529 !important;
}

.service-purchase-card .table tbody td * {
    color: #212529 !important;
}

.service-purchase-card .table tbody td .badge {
    color: white !important;
}

.service-purchase-card .table tbody td .badge * {
    color: white !important;
}