<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .letter-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .letter-header h2 {
            margin: 5px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .letter-header h3 {
            margin: 5px 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        .letter-header h4 {
            margin: 5px 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .letter-header h5 {
            margin: 5px 0;
            font-size: 12px;
            font-weight: bold;
        }
        
        .letter-header p {
            margin: 10px 0;
            font-size: 12px;
        }
        
        .letter-body {
            margin: 30px 0;
            line-height: 2;
        }
        
        .letter-body h4 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: underline;
        }
        
        .letter-body p {
            margin: 15px 0;
            font-size: 14px;
        }
        
        .letter-footer {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .signature-line {
            text-align: center;
            margin-top: 50px;
        }
        
        .signature-line p {
            margin: 5px 0;
            font-weight: bold;
            border-top: 1px solid #333;
            padding-top: 10px;
            width: 200px;
            margin: 0 auto;
        }
        
        .no-print {
            margin: 20px 0;
            text-align: center;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="btn" onclick="window.print();">
            <i class="fas fa-print"></i> طباعة
        </button>
        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لإدارة النقل
        </a>
    </div>

    <div class="letter-header">
        <h2>المملكة الأردنية الهاشمية</h2>
        <h3>وزارة التربية والتعليم</h3>
        <h4>مديرية التربية والتعليم</h4>
        <h5>قسم شؤون الموظفين</h5>
        <p>التاريخ: {% now "Y-m-d" %}</p>
    </div>

    <div class="letter-body">
        <h4>
            كتاب 
            {% if transfer.transfer_type == 'internal_transfer' %}
                النقل الداخلي
            {% else %}
                التكليف المؤقت
            {% endif %}
        </h4>

        <p>السيد/ة: {{ transfer.employee_name }} المحترم/ة</p>
        <p>الرقم الوزاري: {{ transfer.ministry_number }}</p>
        <p>التخصص: {{ transfer.specialization|default:"-" }}</p>
        <p>الخدمة الفعلية: {{ transfer.actual_service|default:"-" }}</p>

        <p>تحية طيبة وبعد،</p>

        <p>
            {% if transfer.transfer_type == 'internal_transfer' %}
                بناءً على طلب النقل الداخلي المقدم من قبلكم، وبعد دراسة الطلب والموافقة عليه، 
                نود إعلامكم بأنه تقرر نقلكم من {{ transfer.current_department }}
                إلى {{ transfer.new_department.name }} اعتباراً من تاريخ _____________________
            {% else %}
                بناءً على متطلبات العمل والحاجة الماسة، نود إعلامكم بأنه تقرر تكليفكم مؤقتاً 
                من {{ transfer.current_department }} إلى {{ transfer.new_department.name }} 
                اعتباراً من تاريخ _____________________ ولمدة _____________________
            {% endif %}
        </p>

        <p>
            {% if transfer.endorsement == 'admin_financial_manager' %}
                وذلك بناءً على تنسيب مدير الشؤون الإدارية والمالية.
            {% else %}
                وذلك بناءً على تنسيب لجنة الموارد البشرية.
            {% endif %}
        </p>

        <p>
            نرجو منكم مراجعة قسم شؤون الموظفين لاستكمال إجراءات 
            {% if transfer.transfer_type == 'internal_transfer' %}النقل{% else %}التكليف{% endif %}.
        </p>

        {% if transfer.notes %}
        <p>
            <strong>ملاحظات:</strong> {{ transfer.notes }}
        </p>
        {% endif %}

        <p>وتفضلوا بقبول فائق الاحترام والتقدير،</p>
    </div>

    <div class="letter-footer">
        <div class="signature-line">
            <p>مدير التربية والتعليم</p>
        </div>
    </div>

    <script>
        // Auto-print when the page loads
        window.onload = function() {
            // Wait a moment for the page to fully render
            setTimeout(function() {
                // Uncomment the next line if you want auto-print
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
