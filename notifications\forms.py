from django import forms
from django.contrib.auth import get_user_model
from .models import Notification

User = get_user_model()

class NotificationForm(forms.ModelForm):
    """Form for creating notifications"""
    
    # Add custom field for recipient type
    RECIPIENT_CHOICES = [
        ('all', 'جميع المستخدمين'),
        ('admins', 'المديرين فقط'),
        ('specific', 'مستخدم محدد'),
    ]
    
    recipient_type = forms.ChoiceField(
        choices=RECIPIENT_CHOICES,
        label='المستقبل',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    specific_user = forms.ModelChoiceField(
        queryset=User.objects.all(),
        required=False,
        label='المستخدم المحدد',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    class Meta:
        model = Notification
        fields = ['title', 'message', 'notification_type', 'icon']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان الإشعار'
            }),
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'محتوى الإشعار'
            }),
            'notification_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'icon': forms.Select(attrs={
                'class': 'form-control'
            }),
        }
        labels = {
            'title': 'العنوان',
            'message': 'الرسالة',
            'notification_type': 'نوع الإشعار',
            'icon': 'الأيقونة',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make specific_user field conditional
        self.fields['specific_user'].widget.attrs.update({
            'style': 'display: none;'
        })

class BulkNotificationForm(forms.Form):
    """Form for sending bulk notifications"""
    
    title = forms.CharField(
        max_length=200,
        label='العنوان',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'عنوان الإشعار'
        })
    )
    
    message = forms.CharField(
        label='الرسالة',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'محتوى الإشعار'
        })
    )
    
    notification_type = forms.ChoiceField(
        choices=Notification.NOTIFICATION_TYPES,
        label='نوع الإشعار',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    icon = forms.ChoiceField(
        choices=Notification.ICON_CHOICES,
        label='الأيقونة',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    send_to_all = forms.BooleanField(
        required=False,
        label='إرسال لجميع المستخدمين',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    send_to_admins = forms.BooleanField(
        required=False,
        label='إرسال للمديرين فقط',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    specific_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.all(),
        required=False,
        label='مستخدمين محددين',
        widget=forms.SelectMultiple(attrs={
            'class': 'form-control',
            'size': '5'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        send_to_all = cleaned_data.get('send_to_all')
        send_to_admins = cleaned_data.get('send_to_admins')
        specific_users = cleaned_data.get('specific_users')
        
        if not send_to_all and not send_to_admins and not specific_users:
            raise forms.ValidationError('يجب اختيار على الأقل نوع واحد من المستقبلين')
        
        return cleaned_data
