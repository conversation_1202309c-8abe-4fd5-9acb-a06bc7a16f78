<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توليد صور توضيحية</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #343a40;
        }
        .canvas-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }
        canvas {
            border: 1px solid #dee2e6;
            margin-bottom: 15px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .image-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .image-item {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }
        .image-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        .image-preview {
            max-width: 100%;
            height: auto;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>توليد صور توضيحية لصفحة التعليمات</h1>
        
        <div class="controls">
            <button id="generateAll" class="btn">توليد جميع الصور</button>
            <button id="downloadAll" class="btn">تنزيل جميع الصور</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
            <div>
                <button id="download" class="btn">تنزيل الصورة الحالية</button>
                <select id="imageType" class="btn">
                    <option value="login">شاشة تسجيل الدخول</option>
                    <option value="dashboard">لوحة التحكم الرئيسية</option>
                    <option value="employee_list">قائمة الموظفين</option>
                    <option value="add_employee">نموذج إضافة موظف جديد</option>
                    <option value="leave_list">قائمة الإجازات</option>
                    <option value="add_leave">نموذج إضافة إجازة جديدة</option>
                    <option value="reports">صفحة التقارير</option>
                    <option value="profile">صفحة الملف الشخصي</option>
                    <option value="change_password">نموذج تغيير كلمة المرور</option>
                </select>
            </div>
        </div>
        
        <h2>الصور المولدة</h2>
        <div id="imageList" class="image-list"></div>
    </div>
    
    <script>
        // تعريف الصور المطلوبة
        const images = [
            { id: "login", title: "شاشة تسجيل الدخول", icon: "🔑" },
            { id: "dashboard", title: "لوحة التحكم الرئيسية", icon: "📊" },
            { id: "employee_list", title: "قائمة الموظفين", icon: "👥" },
            { id: "add_employee", title: "نموذج إضافة موظف جديد", icon: "➕" },
            { id: "leave_list", title: "قائمة الإجازات", icon: "📅" },
            { id: "add_leave", title: "نموذج إضافة إجازة جديدة", icon: "📝" },
            { id: "reports", title: "صفحة التقارير", icon: "📈" },
            { id: "profile", title: "صفحة الملف الشخصي", icon: "👤" },
            { id: "change_password", title: "نموذج تغيير كلمة المرور", icon: "🔒" }
        ];
        
        // الحصول على عناصر الصفحة
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const downloadBtn = document.getElementById('download');
        const generateAllBtn = document.getElementById('generateAll');
        const downloadAllBtn = document.getElementById('downloadAll');
        const imageTypeSelect = document.getElementById('imageType');
        const imageListContainer = document.getElementById('imageList');
        
        // تخزين الصور المولدة
        const generatedImages = {};
        
        // دالة لرسم الصورة التوضيحية
        function drawPlaceholder(imageInfo) {
            // تنظيف الكانفاس
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // رسم الخلفية
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // رسم إطار
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 4;
            ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
            
            // رسم شريط عنوان
            ctx.fillStyle = '#007bff';
            ctx.fillRect(10, 10, canvas.width - 20, 60);
            
            // كتابة عنوان الصفحة
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 24px Tajawal, Arial';
            ctx.textAlign = 'center';
            ctx.fillText('نظام شؤون الموظفين', canvas.width / 2, 50);
            
            // رسم الأيقونة
            ctx.font = '72px Arial';
            ctx.fillStyle = '#6c757d';
            ctx.fillText(imageInfo.icon, canvas.width / 2, canvas.height / 2 - 50);
            
            // كتابة عنوان الصورة
            ctx.fillStyle = '#343a40';
            ctx.font = 'bold 28px Tajawal, Arial';
            ctx.fillText(imageInfo.title, canvas.width / 2, canvas.height / 2 + 50);
            
            // كتابة تعليمات
            ctx.fillStyle = '#6c757d';
            ctx.font = '18px Tajawal, Arial';
            ctx.fillText('يرجى استبدال هذه الصورة بلقطة شاشة حقيقية من النظام', canvas.width / 2, canvas.height / 2 + 100);
            
            // إضافة تاريخ التوليد
            const date = new Date();
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px Tajawal, Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`تم التوليد: ${date.toLocaleDateString('ar-JO')}`, 20, canvas.height - 20);
            
            // حفظ الصورة المولدة
            generatedImages[imageInfo.id] = canvas.toDataURL('image/png');
            
            // تحديث قائمة الصور
            updateImageList();
        }
        
        // دالة لتحديث قائمة الصور المولدة
        function updateImageList() {
            imageListContainer.innerHTML = '';
            
            for (const image of images) {
                if (generatedImages[image.id]) {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    
                    const title = document.createElement('h3');
                    title.textContent = image.title;
                    
                    const preview = document.createElement('img');
                    preview.src = generatedImages[image.id];
                    preview.className = 'image-preview';
                    preview.alt = image.title;
                    
                    const downloadLink = document.createElement('a');
                    downloadLink.href = generatedImages[image.id];
                    downloadLink.download = `${image.id}.png`;
                    downloadLink.className = 'btn';
                    downloadLink.textContent = 'تنزيل';
                    
                    imageItem.appendChild(title);
                    imageItem.appendChild(preview);
                    imageItem.appendChild(downloadLink);
                    
                    imageListContainer.appendChild(imageItem);
                }
            }
        }
        
        // دالة لتنزيل الصورة الحالية
        function downloadCurrentImage() {
            const imageType = imageTypeSelect.value;
            const imageInfo = images.find(img => img.id === imageType);
            
            if (!imageInfo) return;
            
            // رسم الصورة إذا لم تكن موجودة
            if (!generatedImages[imageInfo.id]) {
                drawPlaceholder(imageInfo);
            }
            
            // إنشاء رابط تنزيل
            const link = document.createElement('a');
            link.href = generatedImages[imageInfo.id];
            link.download = `${imageInfo.id}.png`;
            link.click();
        }
        
        // دالة لتوليد جميع الصور
        function generateAllImages() {
            for (const image of images) {
                drawPlaceholder(image);
            }
        }
        
        // دالة لتنزيل جميع الصور
        function downloadAllImages() {
            // توليد الصور أولاً إذا لم تكن موجودة
            generateAllImages();
            
            // تنزيل كل صورة
            for (const image of images) {
                const link = document.createElement('a');
                link.href = generatedImages[image.id];
                link.download = `${image.id}.png`;
                link.click();
                
                // إضافة تأخير بسيط بين التنزيلات
                setTimeout(() => {}, 100);
            }
        }
        
        // إضافة مستمعي الأحداث
        downloadBtn.addEventListener('click', downloadCurrentImage);
        generateAllBtn.addEventListener('click', generateAllImages);
        downloadAllBtn.addEventListener('click', downloadAllImages);
        
        imageTypeSelect.addEventListener('change', () => {
            const imageType = imageTypeSelect.value;
            const imageInfo = images.find(img => img.id === imageType);
            
            if (imageInfo) {
                drawPlaceholder(imageInfo);
            }
        });
        
        // رسم الصورة الأولى عند تحميل الصفحة
        window.onload = () => {
            const firstImage = images[0];
            drawPlaceholder(firstImage);
        };
    </script>
</body>
</html>
