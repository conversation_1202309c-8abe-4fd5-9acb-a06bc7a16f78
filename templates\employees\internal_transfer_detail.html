{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل حركة النقل - {{ transfer.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    .detail-card .card-header {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        border-bottom: none;
    }
    .info-row {
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 0;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 600;
        color: #5a5c69;
    }
    .info-value {
        color: #3a3b45;
    }
    .badge-custom {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    .timeline-item {
        padding: 1rem;
        border-left: 3px solid #007bff;
        margin-left: 1rem;
        position: relative;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 1.5rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-arrows-alt text-primary"></i>
            تفاصيل حركة النقل
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employees:internal_transfer_update' transfer.pk %}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{% url 'employees:internal_transfer_delete' transfer.pk %}" class="btn btn-danger btn-sm"
               onclick="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                <i class="fas fa-trash"></i> حذف
            </a>
            <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information -->
        <div class="col-lg-6 mb-4">
            <div class="card detail-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">الرقم الوزاري:</div>
                            <div class="col-sm-7 info-value">
                                <span class="badge badge-custom bg-primary">{{ transfer.employee.ministry_number }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">الاسم الكامل:</div>
                            <div class="col-sm-7 info-value">{{ transfer.employee.full_name }}</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">التخصص:</div>
                            <div class="col-sm-7 info-value">{{ transfer.employee.specialization }}</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">القسم الحالي:</div>
                            <div class="col-sm-7 info-value">
                                <span class="badge badge-custom bg-success">{{ transfer.employee.school }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">تاريخ التوظيف:</div>
                            <div class="col-sm-7 info-value">{{ transfer.employee.hire_date }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Details -->
        <div class="col-lg-6 mb-4">
            <div class="card detail-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exchange-alt"></i>
                        تفاصيل النقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">القسم السابق:</div>
                            <div class="col-sm-7 info-value">
                                <span class="badge badge-custom bg-warning text-dark">{{ transfer.previous_department }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">القسم الجديد:</div>
                            <div class="col-sm-7 info-value">
                                <span class="badge badge-custom bg-success">{{ transfer.new_department }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">تاريخ النقل:</div>
                            <div class="col-sm-7 info-value">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                {{ transfer.transfer_date }}
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">تاريخ المباشرة:</div>
                            <div class="col-sm-7 info-value">
                                <i class="fas fa-calendar-check text-success"></i>
                                {{ transfer.start_date }}
                            </div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="row">
                            <div class="col-sm-5 info-label">تاريخ التسجيل:</div>
                            <div class="col-sm-7 info-value">
                                <small class="text-muted">{{ transfer.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Timeline -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card detail-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-timeline"></i>
                        مخطط زمني للنقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline-item bg-light">
                        <h6 class="mb-1">القسم السابق</h6>
                        <p class="mb-1"><strong>{{ transfer.previous_department }}</strong></p>
                        <small class="text-muted">حتى تاريخ {{ transfer.transfer_date }}</small>
                    </div>
                    <div class="timeline-item bg-light">
                        <h6 class="mb-1">القسم الجديد</h6>
                        <p class="mb-1"><strong>{{ transfer.new_department }}</strong></p>
                        <small class="text-muted">بدءاً من {{ transfer.start_date }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    {% if transfer.notes %}
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card detail-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-sticky-note"></i>
                        الملاحظات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p class="mb-0">{{ transfer.notes|linebreaks }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Employee's Other Transfers -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card detail-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-history"></i>
                        حركات النقل الأخرى للموظف
                    </h6>
                </div>
                <div class="card-body">
                    {% with other_transfers=transfer.employee.internal_transfers.all %}
                    {% if other_transfers.count > 1 %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>من</th>
                                    <th>إلى</th>
                                    <th>تاريخ النقل</th>
                                    <th>تاريخ المباشرة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for other_transfer in other_transfers %}
                                {% if other_transfer.pk != transfer.pk %}
                                <tr {% if other_transfer.pk == transfer.pk %}class="table-primary"{% endif %}>
                                    <td>{{ other_transfer.previous_department }}</td>
                                    <td>{{ other_transfer.new_department }}</td>
                                    <td>{{ other_transfer.transfer_date }}</td>
                                    <td>{{ other_transfer.start_date }}</td>
                                    <td>
                                        <a href="{% url 'employees:internal_transfer_detail' other_transfer.pk %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        هذه هي حركة النقل الوحيدة المسجلة لهذا الموظف.
                    </div>
                    {% endif %}
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any additional JavaScript here if needed
</script>
{% endblock %}