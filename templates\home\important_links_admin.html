{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-link me-2"></i>
            {{ title }}
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'home:important_links' %}" class="btn btn-info" target="_blank">
                <i class="fas fa-eye me-2"></i>عرض صفحة الروابط
            </a>
            <a href="{% url 'home:important_link_add' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i> إضافة رابط جديد
            </a>
        </div>
    </div>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الروابط المهمة</h6>
        </div>
        <div class="card-body">
            {% if links %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><i class="fas fa-tag me-2"></i>اسم الرابط</th>
                            <th><i class="fas fa-link me-2"></i>الرابط</th>
                            <th><i class="fas fa-info-circle me-2"></i>الوصف</th>
                            <th><i class="fas fa-sort-numeric-up me-2"></i>الترتيب</th>
                            <th><i class="fas fa-toggle-on me-2"></i>الحالة</th>
                            <th><i class="fas fa-calendar me-2"></i>تاريخ الإنشاء</th>
                            <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for link in links %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if link.has_icon %}
                                    <img src="{{ link.get_icon_url }}"
                                         alt="icon"
                                         class="me-2 favicon-img"
                                         style="width: 20px; height: 20px; border-radius: 3px; object-fit: cover;"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                    <i class="fas fa-link me-2 text-muted" style="display: none;"></i>
                                    {% else %}
                                    <i class="fas fa-link me-2 text-primary"></i>
                                    {% endif %}
                                    <strong>{{ link.name }}</strong>
                                </div>
                            </td>
                            <td>
                                <a href="{{ link.url }}" target="_blank" class="text-primary">
                                    {{ link.url|truncatechars:50 }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </td>
                            <td>{{ link.description|truncatechars:100|default:"لا يوجد وصف" }}</td>
                            <td>
                                <span class="badge badge-secondary">{{ link.order }}</span>
                            </td>
                            <td>
                                {% if link.is_active %}
                                <span class="badge badge-success">نشط</span>
                                {% else %}
                                <span class="badge badge-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>{{ link.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'home:important_link_update' link.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'home:important_link_delete' link.pk %}" 
                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-link fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد روابط مهمة</h5>
                <p class="text-muted">ابدأ بإضافة أول رابط مهم</p>
                <a href="{% url 'home:important_link_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> إضافة رابط جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 3, "asc" ]], // Sort by order column
        "pageLength": 25
    });
});
</script>
{% endblock %}
