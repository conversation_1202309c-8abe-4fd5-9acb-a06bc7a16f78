@echo off
echo This script will create a new virtual environment and install all required packages.
echo.

REM Check common Python installation paths
if exist "C:\Python311\python.exe" (
    echo Using Python from C:\Python311\python.exe
    C:\Python311\python.exe -m venv venv
    goto :install
)

if exist "C:\Python310\python.exe" (
    echo Using Python from C:\Python310\python.exe
    C:\Python310\python.exe -m venv venv
    goto :install
)

if exist "C:\Python39\python.exe" (
    echo Using Python from C:\Python39\python.exe
    C:\Python39\python.exe -m venv venv
    goto :install
)

if exist "C:\Program Files\Python311\python.exe" (
    echo Using Python from C:\Program Files\Python311\python.exe
    "C:\Program Files\Python311\python.exe" -m venv venv
    goto :install
)

if exist "C:\Program Files\Python310\python.exe" (
    echo Using Python from C:\Program Files\Python310\python.exe
    "C:\Program Files\Python310\python.exe" -m venv venv
    goto :install
)

if exist "C:\Program Files\Python39\python.exe" (
    echo Using Python from C:\Program Files\Python39\python.exe
    "C:\Program Files\Python39\python.exe" -m venv venv
    goto :install
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" -m venv venv
    goto :install
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" -m venv venv
    goto :install
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" -m venv venv
    goto :install
)

echo Python not found in common locations.
echo Please install Python first, then run this script again.
echo.
echo Press any key to exit...
pause > nul
goto :end

:install
echo.
echo Virtual environment created. Installing packages...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements.txt

echo.
echo Installation completed. You can now run the server with run_with_venv.bat
echo.
echo Press any key to exit...
pause > nul

:end
