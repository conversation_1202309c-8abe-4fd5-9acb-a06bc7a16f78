# إجازات الأمومة - Maternity Leave System

## نظرة عامة
تم إضافة نظام إدارة إجازات الأمومة إلى نظام الموارد البشرية. يتيح هذا النظام إدارة وتتبع إجازات الأمومة للموظفات.

## الميزات الجديدة

### 1. نموذج إجازة الأمومة (MaternityLeave Model)
- **الموظفة**: ربط بالموظفة (الإناث فقط)
- **تاريخ البداية**: تاريخ بداية الإجازة
- **تاريخ الانتهاء**: يتم حسابه تلقائياً (90 يوماً من تاريخ البداية + 1 يوم)
- **الحالة**: نشطة أو منتهية
- **الملاحظات**: ملاحظات إضافية
- **تواريخ النظام**: تاريخ الإنشاء والتحديث

### 2. الصفحات والواجهات

#### أ. صفحة قائمة إجازات الأمومة
- **المسار**: `/employees/maternity-leaves/`
- **الوصول**: من القائمة الجانبية تحت "إجازات الأمومة"
- **المحتوى**:
  - جدول يعرض جميع إجازات الأمومة
  - البحث في الإجازات
  - تصدير البيانات إلى Excel (الإجازات النشطة فقط)
  - إضافة إجازة أمومة جديدة

#### ب. صفحة إضافة إجازة أمومة
- **المسار**: `/employees/maternity-leaves/add/`
- **الميزات**:
  - البحث عن الموظفات (الإناث فقط)
  - عرض تفاصيل الموظفة المحددة
  - إدخال تاريخ بداية الإجازة
  - حساب تاريخ الانتهاء تلقائياً
  - إضافة ملاحظات

#### ج. صفحة تفاصيل إجازة الأمومة
- **المسار**: `/employees/maternity-leaves/<id>/`
- **المحتوى**:
  - عرض جميع تفاصيل الإجازة
  - معلومات الموظفة
  - حالة الإجازة
  - الإجراءات المتاحة

#### د. صفحة تعديل إجازة الأمومة
- **المسار**: `/employees/maternity-leaves/<id>/edit/`
- **الميزات**:
  - تعديل تاريخ البداية
  - تغيير حالة الإجازة
  - تعديل الملاحظات

#### هـ. صفحة حذف إجازة الأمومة
- **المسار**: `/employees/maternity-leaves/<id>/delete/`
- **الميزات**:
  - تأكيد الحذف
  - عرض تفاصيل الإجازة قبل الحذف

### 3. الميزات التقنية

#### أ. الحساب التلقائي
- مدة الإجازة: 90 يوماً
- يبدأ الحساب من اليوم التالي لتاريخ البداية
- تحديث الحالة تلقائياً عند انتهاء الإجازة

#### ب. التحقق من الصحة
- التأكد من أن الموظفة أنثى
- منع إضافة إجازة أمومة لموظفة لديها إجازة نشطة
- التحقق من صحة التواريخ

#### ج. البحث والتصفية
- البحث بالاسم أو الرقم الوزاري
- تصفية الإجازات النشطة/المنتهية
- ترتيب البيانات

#### د. التصدير
- تصدير الإجازات النشطة فقط إلى Excel
- تنسيق احترافي للملف المصدر
- معلومات شاملة في الملف

### 4. الملفات المضافة/المعدلة

#### الملفات الجديدة:
- `employees/maternity_views.py` - عرض صفحات إجازات الأمومة
- `templates/employees/maternity_leaves_list.html` - قائمة الإجازات
- `templates/employees/add_maternity_leave.html` - إضافة إجازة
- `templates/employees/maternity_leave_detail.html` - تفاصيل الإجازة
- `templates/employees/maternity_leave_update.html` - تعديل الإجازة
- `templates/employees/maternity_leave_delete.html` - حذف الإجازة

#### الملفات المعدلة:
- `employees/models.py` - إضافة نموذج MaternityLeave
- `employees/forms.py` - إضافة نماذج إجازات الأمومة
- `employees/urls.py` - إضافة مسارات إجازات الأمومة
- `employees/admin.py` - إضافة إدارة إجازات الأمومة
- `templates/base.html` - إضافة رابط إجازات الأمومة للقائمة

#### ملفات قاعدة البيانات:
- `employees/migrations/0006_maternityleave.py` - إنشاء جدول إجازات الأمومة

### 5. كيفية الاستخدام

#### إضافة إجازة أمومة جديدة:
1. انتقل إلى "إجازات الأمومة" من القائمة الجانبية
2. اضغط على "إضافة إجازة أمومة"
3. ابحث عن الموظفة المطلوبة
4. حدد تاريخ بداية الإجازة
5. أضف أي ملاحظات إضافية
6. احفظ الإجازة

#### عرض وإدارة الإجازات:
1. انتقل إلى قائمة إجازات الأمومة
2. استخدم البحث للعثور على إجازة محددة
3. اضغط على "عرض" لرؤية التفاصيل
4. استخدم "تعديل" لتغيير بيانات الإجازة
5. استخدم "حذف" لإزالة الإجازة

#### تصدير البيانات:
1. من صفحة قائمة الإجازات
2. اضغط على "تصدير إلى Excel"
3. سيتم تحميل ملف يحتوي على الإجازات النشطة فقط

### 6. الأمان والصلاحيات
- جميع الصفحات محمية بتسجيل الدخول
- يمكن للمشرفين فقط الوصول للميزة
- التحقق من صحة البيانات في الخادم والعميل

### 7. الدعم التقني
- النظام متوافق مع Django 4.x
- يدعم قواعد البيانات المختلفة
- واجهة مستخدم متجاوبة
- دعم اللغة العربية

## ملاحظات مهمة
- يتم حساب مدة الإجازة من اليوم التالي لتاريخ البداية
- النظام يحدث حالة الإجازة تلقائياً عند انتهائها
- يمكن للموظفة الواحدة أن تحصل على إجازة أمومة واحدة نشطة فقط
- التصدير يشمل الإجازات النشطة فقط لحماية الخصوصية