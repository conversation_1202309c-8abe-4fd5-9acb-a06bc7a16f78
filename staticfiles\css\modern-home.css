/* Modern Home CSS - Enhanced Design for HR System */

:root {
    /* Modern color palette */
    --primary-color: #3f51b5;      /* Indigo */
    --primary-dark: #303f9f;
    --primary-light: #c5cae9;
    --secondary-color: #ff4081;    /* Pink */
    --secondary-dark: #f50057;
    --secondary-light: #ff80ab;
    --accent-color: #00bcd4;       /* Cyan */
    --accent-dark: #0097a7;
    --accent-light: #b2ebf2;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    --gradient-success: linear-gradient(135deg, var(--success-color), #388e3c);
    --gradient-info: linear-gradient(135deg, var(--info-color), #1976d2);
    
    /* RGB values for opacity variations */
    --primary-color-rgb: 63, 81, 181;
    --secondary-color-rgb: 255, 64, 129;
    --accent-color-rgb: 0, 188, 212;
}

/* Enhanced Hero Section */
.hero-section {
    background: var(--gradient-primary);
    position: relative;
    padding: 6rem 0;
    margin-bottom: 4rem;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(var(--primary-color-rgb), 0.2);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05'/%3E%3C/svg%3E");
    opacity: 0.8;
}

.hero-section .display-4 {
    font-weight: 800;
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hero-section .lead {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 2rem;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.hero-section .btn-light {
    background: white;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.hero-section .btn-light:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Modern Feature Cards */
.feature-card {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.5s ease;
    height: 100%;
    position: relative;
    z-index: 1;
    background: white;
}

.feature-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 35px rgba(var(--primary-color-rgb), 0.1);
}

.feature-card .card-body {
    padding: 2rem;
    text-align: center;
}

.feature-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.2);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-card .card-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.feature-card .card-text {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.feature-card .btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.feature-card .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
}

/* System Features Section */
.features-section {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    padding: 4rem 0;
    border-radius: 20px;
    margin-top: 3rem;
}

.features-card {
    border-radius: 20px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.features-card .card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(10px);
}

.feature-icon-small {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    background: var(--gradient-success);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
}

/* Animations */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Navbar Enhancement */
.navbar {
    background: var(--gradient-primary);
    box-shadow: 0 5px 20px rgba(var(--primary-color-rgb), 0.2);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
}

/* Footer Enhancement */
.footer {
    background: var(--gradient-primary);
    padding: 3rem 0 1.5rem;
    margin-top: 5rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%23ffffff' fill-opacity='0.05'/%3E%3C/svg%3E");
    opacity: 0.5;
}
