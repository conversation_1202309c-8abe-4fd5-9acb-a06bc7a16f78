{% extends 'base.html' %}
{% load static %}

{% block title %}حذف معلم BTEC{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .delete-card .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 20px;
    }
    
    .employee-details {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .detail-item:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
    }
    
    .detail-value {
        color: #6c757d;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-trash text-danger me-2"></i>
                حذف معلم BTEC
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:btec_list' %}">معلمي BTEC</a></li>
                    <li class="breadcrumb-item active">حذف معلم BTEC</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-card">
                <div class="card-header text-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body text-center p-4">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    
                    <h4 class="text-danger mb-3">هل أنت متأكد من حذف هذا المعلم؟</h4>
                    <p class="text-muted mb-4">
                        سيتم حذف معلم BTEC نهائياً ولا يمكن التراجع عن هذا الإجراء.
                    </p>

                    <!-- Employee Details -->
                    <div class="employee-details">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i>
                            تفاصيل المعلم
                        </h6>
                        
                        <div class="detail-item">
                            <span class="detail-label">الرقم الوزاري:</span>
                            <span class="detail-value">
                                <span class="badge bg-primary">{{ btec_teacher.ministry_number }}</span>
                            </span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">الاسم الكامل:</span>
                            <span class="detail-value">
                                <strong>{{ btec_teacher.employee.full_name }}</strong>
                            </span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">التخصص:</span>
                            <span class="detail-value">{{ btec_teacher.specialization }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">المدرسة:</span>
                            <span class="detail-value">{{ btec_teacher.department_name }}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">الحقل:</span>
                            <span class="detail-value">
                                <span class="badge bg-info">{{ btec_teacher.field.name }}</span>
                            </span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">تاريخ الإضافة:</span>
                            <span class="detail-value">{{ btec_teacher.created_at|date:"Y/m/d H:i" }}</span>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:btec_list' %}" class="btn btn-cancel text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-delete text-white">
                                <i class="fas fa-trash me-2"></i>حذف نهائياً
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا المعلم نهائياً؟')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
