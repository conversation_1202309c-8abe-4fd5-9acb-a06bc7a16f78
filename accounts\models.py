from django.db import models
from django.contrib.auth.models import AbstractUser, Group, Permission
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    """Extended User model for the HR system"""
    is_admin = models.BooleanField(_('Admin Status'), default=False)
    is_full_admin = models.BooleanField(_('Full Admin Status'), default=False, help_text=_('Gives the user full admin privileges without being a superuser'))
    phone_number = models.CharField(_('Phone Number'), max_length=20, blank=True, null=True)
    address = models.TextField(_('Address'), blank=True, null=True)
    login_count = models.PositiveIntegerField(_('Login Count'), default=0, help_text=_('Number of times the user has logged in'))

    class Meta(AbstractUser.Meta):
        verbose_name = _('User')
        verbose_name_plural = _('Users')

    def __str__(self):
        return self.username

class UserPermission(models.Model):
    """Model for storing custom user permissions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_permissions')
    module_name = models.CharField(_('Module Name'), max_length=50)
    can_view = models.BooleanField(_('Can View'), default=False)
    can_add = models.BooleanField(_('Can Add'), default=False)
    can_edit = models.BooleanField(_('Can Edit'), default=False)
    can_delete = models.BooleanField(_('Can Delete'), default=False)
    visible_pages = models.TextField(_('Visible Pages'), blank=True, null=True, help_text=_('Comma-separated list of pages visible to the user'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('User Permission')
        verbose_name_plural = _('User Permissions')
        unique_together = ['user', 'module_name']

    def __str__(self):
        return f"{self.user.username} - {self.module_name}"
