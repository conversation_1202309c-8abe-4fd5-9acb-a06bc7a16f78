<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملخص النقل الداخلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .table th, .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .table tr:hover {
            background-color: #f5f5f5;
        }

        .button-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                padding: 0;
            }

            .table {
                page-break-inside: auto;
            }

            .table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }

            @page {
                size: landscape;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>المملكة الأردنية الهاشمية</h2>
        <h3>وزارة التربية والتعليم</h3>
        <h4>مديرية التربية والتعليم / لواء قصبة المفرق</h4>
        <h5>قسم شؤون الموظفين</h5>
        <h3 class="mt-4">ملخص النقل الداخلي للعام {{ current_year }}</h3>
        <p>التاريخ: {{ today_date }}</p>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الرقم الوزاري</th>
                    <th>الاسم</th>
                    <th>مركز العمل الحالي</th>
                    <th>مركز العمل الجديد</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for transfer in transfers %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ transfer.ministry_number }}</td>
                    <td>{{ transfer.employee_name }}</td>
                    <td>{{ transfer.current_department }}</td>
                    <td>{{ transfer.new_department|default:"-" }}</td>
                    <td>{{ transfer.notes|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center">لا توجد بيانات نقل داخلي</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="button-container no-print">
        <button class="btn btn-primary" onclick="window.print();">
            <i class="fas fa-print"></i> طباعة
        </button>
        <a href="{% url 'home:print_transfer_letters' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لصفحة طباعة كتب النقل
        </a>
    </div>

    <script>
        // Auto-print when the page loads
        window.onload = function() {
            // Wait a moment for the page to fully render
            setTimeout(function() {
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
