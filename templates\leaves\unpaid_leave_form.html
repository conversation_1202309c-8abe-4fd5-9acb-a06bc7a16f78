{% extends 'base.html' %}
{% load static %}

{% block title %}{% if leave %}تعديل إجازة بدون راتب{% else %}إضافة إجازة بدون راتب{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% if leave %}تعديل إجازة بدون راتب{% else %}إضافة إجازة بدون راتب{% endif %}</h1>
        <a href="{% url 'leaves:unpaid_leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% if leave %}تعديل إجازة بدون راتب{% else %}إضافة إجازة بدون راتب جديدة{% endif %}</h6>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}

                {{ form.employee_id }}
                {{ form.employee }}

                <div class="mb-3">
                    <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                    <div class="input-group">
                        {{ form.ministry_number }}
                        <button type="button" class="btn btn-primary" id="search_employee_btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                    {% if form.ministry_number.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.ministry_number.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                    {{ form.employee_name }}
                    {% if form.employee_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee_name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.start_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية</label>
                            {{ form.end_date }}
                            {% if form.end_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.end_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>



                <div class="mb-3">
                    <label for="{{ form.reason.id_for_label }}" class="form-label">السبب</label>
                    {{ form.reason }}
                    {% if form.reason.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.reason.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>



                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/leaves/unpaid/get-employee/?ministry_number=${ministryNumber}`)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // Update form fields
                    employeeNameDisplay.value = data.full_name;
                    employeeIdInput.value = data.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.full_name, data.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    }
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        searchButton.addEventListener('click', searchEmployee);

        // Allow searching by pressing Enter in the ministry number field
        ministryNumberInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // Prevent form submission
                searchEmployee();
            }
        });

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput.value && !employeeNameDisplay.value) {
            searchEmployee();
        }
    });
</script>
{% endblock %}
