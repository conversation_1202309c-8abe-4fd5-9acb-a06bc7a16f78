{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }
    
    .form-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 5px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .preview-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid #dee2e6;
        margin-top: 20px;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .character-count {
        font-size: 0.875rem;
        color: #6c757d;
        text-align: left;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Form Header -->
    <div class="form-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                {% if 'إضافة' in title %}
                    <i class="fas fa-plus fa-3x"></i>
                {% else %}
                    <i class="fas fa-edit fa-3x"></i>
                {% endif %}
            </div>
            <div class="col-md-8">
                <h2 class="mb-0">{{ title }}</h2>
            </div>
            <div class="col-md-2">
                <a href="{% url 'announcements:announcements_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Form Section -->
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-form"></i> بيانات الإعلان
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="announcementForm">
                        {% csrf_token %}
                        
                        <!-- Title -->
                        <div class="form-group">
                            <label for="{{ form.title.id_for_label }}" class="form-label required-field">
                                <i class="fas fa-heading text-primary"></i> {{ form.title.label }}
                            </label>
                            {{ form.title }}
                            <div class="character-count" id="title-count">0 / 200</div>
                            {% if form.title.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Content -->
                        <div class="form-group">
                            <label for="{{ form.content.id_for_label }}" class="form-label required-field">
                                <i class="fas fa-paragraph text-info"></i> {{ form.content.label }}
                            </label>
                            {{ form.content }}
                            <div class="character-count" id="content-count">0 / 1000</div>
                            {% if form.content.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.content.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Type and Priority -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.announcement_type.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag text-warning"></i> {{ form.announcement_type.label }}
                                    </label>
                                    {{ form.announcement_type }}
                                    {% if form.announcement_type.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.announcement_type.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.priority.id_for_label }}" class="form-label">
                                        <i class="fas fa-exclamation-circle text-danger"></i> {{ form.priority.label }}
                                    </label>
                                    {{ form.priority }}
                                    {% if form.priority.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.priority.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Dates -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label required-field">
                                        <i class="fas fa-calendar-plus text-success"></i> {{ form.start_date.label }}
                                    </label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.start_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus text-danger"></i> {{ form.end_date.label }}
                                    </label>
                                    {{ form.end_date }}
                                    <small class="text-muted">{{ form.end_date.help_text }}</small>
                                    {% if form.end_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.end_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Link -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="{{ form.link_url.id_for_label }}" class="form-label">
                                        <i class="fas fa-link text-info"></i> {{ form.link_url.label }}
                                    </label>
                                    {{ form.link_url }}
                                    {% if form.link_url.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.link_url.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.link_text.id_for_label }}" class="form-label">
                                        <i class="fas fa-text-width text-secondary"></i> {{ form.link_text.label }}
                                    </label>
                                    {{ form.link_text }}
                                    <small class="text-muted">{{ form.link_text.help_text }}</small>
                                    {% if form.link_text.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.link_text.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Checkboxes -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <i class="fas fa-toggle-on text-success"></i> {{ form.is_active.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.show_on_homepage }}
                                    <label class="form-check-label" for="{{ form.show_on_homepage.id_for_label }}">
                                        <i class="fas fa-home text-primary"></i> {{ form.show_on_homepage.label }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger mt-3">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <!-- Submit Buttons -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-action">
                                <i class="fas fa-save"></i> 
                                {% if announcement %}تحديث الإعلان{% else %}إضافة الإعلان{% endif %}
                            </button>
                            <a href="{% url 'announcements:announcements_list' %}" class="btn btn-secondary btn-action">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Preview Section -->
        <div class="col-lg-4">
            <div class="preview-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-eye"></i> معاينة الإعلان
                </h5>
                <div id="preview-content">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong id="preview-title">عنوان الإعلان</strong>
                        <div id="preview-text" class="mt-2">محتوى الإعلان سيظهر هنا...</div>
                        <div id="preview-link" class="mt-2" style="display: none;">
                            <a href="#" class="btn btn-sm btn-outline-primary">
                                <span id="preview-link-text">اقرأ المزيد</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6 class="text-secondary">إرشادات:</h6>
                    <ul class="small text-muted">
                        <li>العنوان يجب أن يكون واضحاً ومختصراً</li>
                        <li>المحتوى يجب أن يكون مفهوماً للجميع</li>
                        <li>اختر النوع والأولوية المناسبة</li>
                        <li>حدد تاريخ البداية والنهاية بدقة</li>
                        <li>الرابط اختياري ولكن مفيد</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Character counting
    function updateCharacterCount(inputId, countId, maxLength) {
        const input = document.getElementById(inputId);
        const counter = document.getElementById(countId);
        
        function updateCount() {
            const currentLength = input.value.length;
            counter.textContent = `${currentLength} / ${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.style.color = '#dc3545';
            } else if (currentLength > maxLength * 0.7) {
                counter.style.color = '#fd7e14';
            } else {
                counter.style.color = '#6c757d';
            }
        }
        
        input.addEventListener('input', updateCount);
        updateCount(); // Initial count
    }
    
    updateCharacterCount('{{ form.title.id_for_label }}', 'title-count', 200);
    updateCharacterCount('{{ form.content.id_for_label }}', 'content-count', 1000);
    
    // Live preview
    function updatePreview() {
        const title = $('#{{ form.title.id_for_label }}').val() || 'عنوان الإعلان';
        const content = $('#{{ form.content.id_for_label }}').val() || 'محتوى الإعلان سيظهر هنا...';
        const linkUrl = $('#{{ form.link_url.id_for_label }}').val();
        const linkText = $('#{{ form.link_text.id_for_label }}').val() || 'اقرأ المزيد';
        const type = $('#{{ form.announcement_type.id_for_label }}').val() || 'info';
        
        $('#preview-title').text(title);
        $('#preview-text').text(content);
        $('#preview-link-text').text(linkText);
        
        if (linkUrl) {
            $('#preview-link').show();
            $('#preview-link a').attr('href', linkUrl);
        } else {
            $('#preview-link').hide();
        }
        
        // Update alert class based on type
        const alertDiv = $('#preview-content .alert');
        alertDiv.removeClass('alert-info alert-warning alert-success alert-danger')
                .addClass(`alert-${type === 'info' ? 'info' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'danger'}`);
        
        // Update icon based on type
        const iconMap = {
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-triangle', 
            'success': 'fa-check-circle',
            'danger': 'fa-exclamation-circle'
        };
        alertDiv.find('i').removeClass().addClass(`fas ${iconMap[type] || 'fa-info-circle'}`);
    }
    
    // Bind preview update to form changes
    $('#{{ form.title.id_for_label }}, #{{ form.content.id_for_label }}, #{{ form.link_url.id_for_label }}, #{{ form.link_text.id_for_label }}, #{{ form.announcement_type.id_for_label }}').on('input change', updatePreview);
    
    // Initial preview update
    updatePreview();
    
    // Form validation
    $('#announcementForm').on('submit', function(e) {
        const title = $('#{{ form.title.id_for_label }}').val().trim();
        const content = $('#{{ form.content.id_for_label }}').val().trim();
        const startDate = $('#{{ form.start_date.id_for_label }}').val();
        
        if (!title) {
            e.preventDefault();
            alert('الرجاء إدخال عنوان الإعلان');
            $('#{{ form.title.id_for_label }}').focus();
            return false;
        }
        
        if (!content) {
            e.preventDefault();
            alert('الرجاء إدخال محتوى الإعلان');
            $('#{{ form.content.id_for_label }}').focus();
            return false;
        }
        
        if (!startDate) {
            e.preventDefault();
            alert('الرجاء تحديد تاريخ بداية الإعلان');
            $('#{{ form.start_date.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}