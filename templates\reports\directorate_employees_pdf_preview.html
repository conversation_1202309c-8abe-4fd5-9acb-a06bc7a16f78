{% extends 'base.html' %}
{% load static %}

{% block title %}معاينة تقرير موظفي المديرية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<!-- Add CSS for print styling -->
<style>
    @media print {
        /* Hide everything except the table container */
        body * {
            visibility: hidden;
        }
        #tableContainer, #tableContainer * {
            visibility: visible;
        }
        #tableContainer {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 20px;
        }
        .no-print {
            display: none !important;
        }

        /* Ensure table takes full width */
        #previewTable {
            width: 100% !important;
            border-collapse: collapse;
        }

        /* Improve table styling for print */
        #previewTable {
            border: 2px solid #000 !important;
            border-collapse: collapse;
        }

        #previewTable th, #previewTable td {
            border: 1px solid #000 !important;
            padding: 8px;
            font-size: 16px;
            text-align: center;
            font-weight: bold;
        }

        #previewTable thead th {
            border-bottom: 2px solid #000 !important;
        }

        /* Set background colors that print well */
        .bg-success, .bg-success.bg-opacity-25 {
            background-color: #d4edda !important;
            color: #000 !important;
        }

        .bg-danger, .bg-danger.bg-opacity-25 {
            background-color: #f8d7da !important;
            color: #000 !important;
        }

        .bg-info, .bg-info.bg-opacity-25 {
            background-color: #d1ecf1 !important;
            color: #000 !important;
        }

        /* Make numbers larger and more visible */
        #previewTable td:nth-child(n+4) {
            font-size: 18px !important;
        }

        /* Ensure page breaks don't happen inside rows */
        tr {
            page-break-inside: avoid;
        }
    }

    /* Make sure the table is fully visible */
    #previewTable {
        width: 100% !important;
    }

    /* Ensure Arabic text is properly displayed */
    #previewTable th, #previewTable td {
        font-family: 'Tajawal', sans-serif;
        text-align: center;
        font-weight: bold;
    }

    /* Add background colors for the table cells */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Add clear borders to table cells */
    #previewTable {
        border: 2px solid #dee2e6 !important;
    }

    #previewTable th,
    #previewTable td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
        font-size: 16px;
    }

    #previewTable thead th {
        border-bottom: 2px solid #000 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ report_title }} - معاينة قبل التصدير</h1>
        <div>
            <a href="{% url 'reports:directorate_employees_report' %}{% if selected_department %}?department={{ selected_department.id }}{% endif %}" class="btn btn-secondary me-2 no-print">
                <i class="fas fa-arrow-left"></i> العودة للتقرير
            </a>
            <button onclick="printDocument()" class="btn btn-danger no-print">
                <i class="fas fa-file-pdf"></i> تحميل PDF
            </button>
            <button onclick="printTable()" class="btn btn-info ms-2 no-print">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الموظفين</h6>
            <span class="badge bg-primary fs-6 p-2">عدد الموظفين: {{ employee_data|length }}</span>
        </div>
        <div class="card-body">
            {% if employee_data %}
            <div id="tableContainer" class="table-responsive">
                <h3 class="text-center mb-3">{{ report_title }}</h3>
                <p class="text-center mb-4">تاريخ التقرير: {% now "d/m/Y" %}</p>
                <table class="table table-bordered" id="previewTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th rowspan="2">الرقم الوزاري</th>
                            <th rowspan="2">الاسم</th>
                            <th rowspan="2">القسم</th>
                            {% for leave_type in leave_types %}
                            <th colspan="3" class="text-center">{{ leave_type.display_name }}</th>
                            {% endfor %}
                        </tr>
                        <tr>
                            {% for leave_type in leave_types %}
                            <th class="bg-success text-white">الرصيد الأولي</th>
                            <th class="bg-danger text-white">الإجازات المستخدمة</th>
                            <th class="bg-info text-white">الرصيد المتبقي</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in employee_data %}
                        <tr class="text-center">
                            <td>{{ item.employee.ministry_number }}</td>
                            <td>{{ item.employee.full_name }}</td>
                            <td>{{ item.department }}</td>

                            <!-- الإجازات السنوية -->
                            <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ item.annual_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ item.annual_balance.used }}</td>
                            <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ item.annual_balance.remaining }}</td>

                            <!-- الإجازات المرضية -->
                            <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ item.sick_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ item.sick_balance.used }}</td>
                            <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ item.sick_balance.remaining }}</td>

                            <!-- الإجازات العرضية -->
                            <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ item.casual_balance.initial }}</td>
                            <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ item.casual_balance.used }}</td>
                            <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ item.casual_balance.remaining }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <p class="text-left mt-3">عدد الموظفين: {{ employee_data|length }}</p>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد بيانات للموظفين تطابق معايير البحث.
            </div>
            {% endif %}
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
        </div>
        <div class="card-body">
            <p>هذه معاينة للتقرير قبل تحميله كملف PDF. يمكنك الآن:</p>
            <ul>
                <li>مراجعة البيانات للتأكد من صحتها</li>
                <li>النقر على زر "تحميل PDF" لتحميل التقرير كملف PDF</li>
                <li>العودة إلى صفحة التقرير الرئيسية لإجراء تعديلات على معايير التصفية</li>
            </ul>
            <p><strong>ملاحظة:</strong> عند تصدير التقرير إلى PDF، يتم ترتيب الجدول من اليمين إلى اليسار بدءًا من الرقم الوزاري.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include required libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
    // Initialize DataTable when document is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable with minimal features for the preview
        var table = $('#previewTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[0, "asc"]],
            "pageLength": -1,  // Show all rows
            "dom": 'rt',       // Only show the table, no search or pagination
            "buttons": []
        });
    });

    // Function to handle PDF download
    function printDocument() {
        // Show loading message
        alert('جاري إنشاء ملف PDF، يرجى الانتظار...');

        // Get the table container
        var element = document.getElementById('tableContainer');

        // Use html2canvas to capture the table as an image
        html2canvas(element, {
            scale: 2,  // Higher scale for better quality
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false,
            allowTaint: true,
            foreignObjectRendering: false
        }).then(function(canvas) {
            try {
                // Get the jsPDF class from the loaded library
                var { jsPDF } = window.jspdf;

                // Create a new PDF document in landscape orientation
                var pdf = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });

                // Get canvas as image
                var imgData = canvas.toDataURL('image/jpeg', 1.0);

                // Calculate dimensions to fit the page
                var pdfWidth = pdf.internal.pageSize.getWidth();
                var pdfHeight = pdf.internal.pageSize.getHeight();

                var imgWidth = pdfWidth - 20;  // 10mm margin on each side
                var imgHeight = (canvas.height * imgWidth) / canvas.width;

                // If image is too tall, scale it to fit the page height
                if (imgHeight > pdfHeight - 20) {
                    imgHeight = pdfHeight - 20;  // 10mm margin on top and bottom
                    imgWidth = (canvas.width * imgHeight) / canvas.height;
                }

                // Calculate position to center the image
                var x = (pdfWidth - imgWidth) / 2;
                var y = (pdfHeight - imgHeight) / 2;

                // Add the image to the PDF
                pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

                // Save the PDF
                pdf.save('{{ report_title }}.pdf');

                // Show success message
                alert('تم إنشاء ملف PDF بنجاح!');
            } catch (error) {
                // Show error message
                console.error('Error creating PDF:', error);
                alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
            }
        }).catch(function(error) {
            // Show error message
            console.error('Error capturing image:', error);
            alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
        });
    }

    // Function to handle printing
    function printTable() {
        // Show loading message
        alert('جاري تجهيز الصفحة للطباعة، يرجى الانتظار...');

        // Get the table container
        var element = document.getElementById('tableContainer');

        // Use html2canvas to capture the table as an image
        html2canvas(element, {
            scale: 2,  // Higher scale for better quality
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false,
            allowTaint: true,
            foreignObjectRendering: false
        }).then(function(canvas) {
            try {
                // Create a new window
                var printWindow = window.open('', '_blank');

                // Write HTML content to the new window
                printWindow.document.write('<html><head><title>{{ report_title }}</title>');
                printWindow.document.write('<style>body { margin: 0; padding: 20px; direction: rtl; } img { max-width: 100%; height: auto; }</style>');
                printWindow.document.write('</head><body>');

                // Add the canvas as an image
                printWindow.document.write('<img src="' + canvas.toDataURL('image/jpeg', 1.0) + '" />');

                printWindow.document.write('</body></html>');

                // Close the document
                printWindow.document.close();

                // Wait for the image to load before printing
                printWindow.onload = function() {
                    printWindow.print();
                    // Close the window after printing (optional)
                    // printWindow.close();
                };
            } catch (error) {
                // Show error message
                console.error('Error preparing for print:', error);
                alert('حدث خطأ أثناء تجهيز الصفحة للطباعة: ' + error.message);
            }
        }).catch(function(error) {
            // Show error message
            console.error('Error capturing image:', error);
            alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
        });
    }
</script>
{% endblock %}
