{% extends 'base.html' %}
{% load static %}

{% block title %}المدارس الصفرية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-left: 4px solid #4e73df;
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        color: #333;
        border: 1px solid #e3e6f0;
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .stats-label {
        font-size: 0.9rem;
        color: #5a5c69;
        font-weight: 600;
    }
    
    .specialization-badge {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }
    
    .table th {
        background-color: #f8f9fc;
        border-top: none;
        font-weight: 600;
        color: #5a5c69;
    }
    
    .btn-group-actions {
        white-space: nowrap;
    }
    
    .btn-group-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* تحسين شكل الفلتر */
    .filter-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #e3e6f0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .filter-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }
    
    .filter-form .form-control, .filter-form .form-select {
        border-radius: 8px;
        border: 1px solid #d1d3e2;
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .filter-form .form-control:focus, .filter-form .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    
    .filter-btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
    }
    
    /* تحسين رؤوس الأعمدة */
    .table th {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        padding: 15px 10px;
    }
    
    .table th i {
        margin-left: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-school text-warning me-2"></i>المدارس الصفرية</h2>
    <div>
        <a href="{% url 'employment:export_zero_schools_excel' %}{% if search_query or specialization_filter %}?{% if search_query %}search={{ search_query }}{% endif %}{% if specialization_filter %}{% if search_query %}&{% endif %}specialization={{ specialization_filter }}{% endif %}{% endif %}" class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> تصدير إلى إكسل
        </a>
        <a href="{% url 'employment:technical_position_list' %}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> العودة للموقف الفني
        </a>
        <a href="{% url 'employment:zero_school_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة مدرسة صفرية
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ total_schools }}</div>
                <div class="stats-label">إجمالي المدارس الصفرية</div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ total_vacancies }}</div>
                <div class="stats-label">إجمالي الشواغر</div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الفلتر المحسن -->
<div class="filter-section">
    <div class="filter-title">
        <i class="fas fa-filter text-primary me-2"></i>البحث والتصفية
    </div>
    <form class="filter-form" method="get">
        <div class="row g-3 align-items-end">
            <div class="col-md-5">
                <label for="search" class="form-label">
                    <i class="fas fa-search me-1"></i>البحث في اسم المدرسة أو المبرر
                </label>
                <input class="form-control" type="search" id="search" placeholder="ابحث في اسم المدرسة أو المبرر..." name="search" value="{{ search_query }}">
            </div>
            <div class="col-md-3">
                <label for="specialization" class="form-label">
                    <i class="fas fa-tags me-1"></i>التخصص
                </label>
                <select name="specialization" id="specialization" class="form-select">
                    <option value="">جميع التخصصات</option>
                    {% for choice in specialization_choices %}
                        <option value="{{ choice.0 }}" {% if specialization_filter == choice.0 %}selected{% endif %}>
                            {{ choice.1 }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary filter-btn w-100" type="submit">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
            <div class="col-md-2">
                <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-outline-secondary filter-btn w-100">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </div>
    </form>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list me-2"></i>قائمة المدارس الصفرية
        </h6>
    </div>
    
    <div class="card-body">
        {% if zero_schools %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th width="5%">
                                <i class="fas fa-hashtag"></i>#
                            </th>
                            <th width="25%">
                                <i class="fas fa-school"></i>اسم المدرسة
                            </th>
                            <th width="15%">
                                <i class="fas fa-user-tag"></i>التخصص
                            </th>
                            <th width="10%">
                                <i class="fas fa-users"></i>عدد الشواغر
                            </th>
                            <th width="25%">
                                <i class="fas fa-clipboard-list"></i>المبرر
                            </th>
                            <th width="15%">
                                <i class="fas fa-tasks"></i>الإجراءات
                            </th>
                            <th width="5%">
                                <i class="fas fa-cogs"></i>العمليات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for school in zero_schools %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <strong>{{ school.school_name }}</strong>
                            </td>
                            <td>
                                {% if school.specialization == 'مستخدم' %}
                                    <span class="badge bg-info specialization-badge">
                                        <i class="fas fa-user me-1"></i>{{ school.specialization }}
                                    </span>
                                {% elif school.specialization == 'حارس' %}
                                    <span class="badge bg-warning specialization-badge">
                                        <i class="fas fa-shield-alt me-1"></i>{{ school.specialization }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-primary specialization-badge">
                                        <i class="fas fa-chalkboard-teacher me-1"></i>{{ school.specialization }}
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-danger fs-6">{{ school.vacancies_count }}</span>
                            </td>
                            <td>
                                <small>{{ school.justification|truncatechars:100 }}</small>
                            </td>
                            <td>
                                {% if school.actions %}
                                    <small>{{ school.actions|truncatechars:80 }}</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group-actions">
                                    <a href="{% url 'employment:zero_school_edit' school.pk %}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employment:zero_school_delete' school.pk %}" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مدارس صفرية</h5>
                <p class="text-muted">لم يتم العثور على أي مدارس صفرية مطابقة لمعايير البحث.</p>
                <a href="{% url 'employment:zero_school_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle"></i> إضافة مدرسة صفرية جديدة
                </a>
            </div>
        {% endif %}
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit form when specialization filter changes
    $('select[name="specialization"]').change(function() {
        $(this).closest('form').submit();
    });
    
    // Confirm delete
    $('.btn-outline-danger').click(function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        if (confirm('هل أنت متأكد من حذف هذه المدرسة الصفرية؟')) {
            window.location.href = url;
        }
    });
});
</script>
{% endblock %}