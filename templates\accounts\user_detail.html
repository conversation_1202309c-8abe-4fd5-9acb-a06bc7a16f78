{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المستخدم{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل المستخدم</h1>
        <a href="{% url 'accounts:user_update' user_obj.pk %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المستخدم</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم المستخدم:</strong> {{ user_obj.username }}</p>
                            <p><strong>الاسم الكامل:</strong> {{ user_obj.get_full_name }}</p>
                            <p><strong>البريد الإلكتروني:</strong> {{ user_obj.email|default:"غير محدد" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الانضمام:</strong> {{ user_obj.date_joined|date:"Y-m-d" }}</p>
                            <p><strong>آخر تسجيل دخول:</strong> {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل الدخول بعد" }}</p>
                            <p><strong>عدد مرات تسجيل الدخول:</strong> <span class="badge bg-info">{{ user_obj.login_count }}</span></p>
                            <p><strong>الحالة:</strong>
                                {% if user_obj.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </p>
                            <p><strong>نوع المستخدم:</strong>
                                {% if user_obj.is_superuser or user_obj.is_full_admin %}
                                <span class="badge bg-danger">مدير النظام</span>
                                {% elif user_obj.is_admin %}
                                <span class="badge bg-primary">مدير</span>
                                {% elif user_obj.is_staff %}
                                <span class="badge bg-warning">مشرف</span>
                                {% else %}
                                <span class="badge bg-secondary">مستخدم عادي</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الصلاحيات</h6>
                </div>
                <div class="card-body">
                    {% if user_permissions %}
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>القسم</th>
                                <th>مستوى الصلاحية</th>
                                <th>الصفحات المرئية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for perm in user_permissions %}
                            <tr>
                                <td>
                                    {% if perm.module_name == 'employment' %}الكادر
                                    {% elif perm.module_name == 'leaves' %}الإجراءات
                                    {% elif perm.module_name == 'directorate_leaves' %}إجازات الموظفين (المديرية)
                                    {% elif perm.module_name == 'files' %}الملفات
                                    {% elif perm.module_name == 'ranks' %}الرتب
                                    {% elif perm.module_name == 'performance' %}التقارير السنوية
                                    {% elif perm.module_name == 'reports' %}تقارير النظام
                                    {% elif perm.module_name == 'accounts' %}المستخدمين
                                    {% elif perm.module_name == 'backup' %}النسخ الاحتياطية
                                    {% elif perm.module_name == 'system_logs' %}سجل حركات النظام
                                    {% elif perm.module_name == 'disciplinary' %}العقوبات
                                    {% elif perm.module_name == 'file_management' %}إدارة الملفات
                                    {% elif perm.module_name == 'employees' %}بيانات الموظفين
                                    {% else %}{{ perm.module_name }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.can_delete %}
                                    <span class="badge bg-primary">مدير (عرض - اضافة - تعديل - حذف)</span>
                                    {% elif perm.can_edit %}
                                    <span class="badge bg-info">مشرف (عرض - اضافة - تعديل)</span>
                                    {% elif perm.can_add %}
                                    <span class="badge bg-success">مستخدم عادي (عرض - اضافة)</span>
                                    {% elif perm.can_view %}
                                    <span class="badge bg-secondary">عرض فقط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.visible_pages %}
                                    <a href="{% url 'accounts:user_visible_pages' user_obj.pk perm.id %}" class="btn btn-sm btn-info">
                                        عرض الصفحات ({{ perm.visible_pages.split|length }})
                                    </a>
                                    {% else %}
                                    <span class="badge bg-secondary">لا توجد صفحات محددة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% endif %}
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">خيارات متقدمة</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="{% url 'accounts:admin_change_password' user_obj.pk %}" class="btn btn-warning">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </a>
                        {% if user_obj.username != 'admin' and not user_obj.is_superuser %}
                        <a href="{% url 'accounts:user_delete' user_obj.pk %}" class="btn btn-danger">
                            <i class="fas fa-trash"></i> حذف المستخدم
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
