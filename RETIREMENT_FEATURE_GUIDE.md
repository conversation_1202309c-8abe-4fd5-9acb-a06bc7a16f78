# دليل استخدام ميزة المتقاعدين

## نظرة عامة
تم إضافة ميزة جديدة لإدارة المتقاعدين في نظام شؤون الموظفين. هذه الميزة تسمح بـ:
- تقاعد الموظفين وإزالتهم من قائمة الموظفين النشطين
- عرض قائمة المتقاعدين منفصلة
- البحث في المتقاعدين
- إدارة بيانات التقاعد
- إمكانية إلغاء التقاعد (إعادة الموظف للخدمة)

## الوصول إلى الميزة
1. اذهب إلى صفحة "بيانات الموظفين"
2. انقر على زر "المتقاعدين" (الأزرار العلوية)
3. ستنتقل إلى صفحة المتقاعدين

## تقاعد موظف جديد

### الخطوات:
1. في صفحة المتقاعدين، انقر على "تقاعد موظف"
2. استخدم حقل البحث للعثور على الموظف:
   - يمكنك البحث بالرقم الوزاري
   - أو البحث بالاسم
   - ابدأ الكتابة وستظهر النتائج تلقائياً
3. انقر على الموظف المطلوب من نتائج البحث
4. أدخل تاريخ التقاعد
5. أدخل سبب التقاعد (افتراضي: "تقاعد عادي")
6. أدخل أي ملاحظات إضافية (اختياري)
7. انقر على "تقاعد الموظف"

### مميزات البحث:
- **البحث السريع**: ابدأ الكتابة وستظهر النتائج فوراً
- **البحث الذكي**: يبحث في الرقم الوزاري والاسم معاً
- **عرض معلومات إضافية**: يعرض القسم مع كل نتيجة
- **اختيار سهل**: انقر على النتيجة لاختيارها

## إدارة المتقاعدين

### عرض قائمة المتقاعدين:
- تعرض جميع الموظفين المتقاعدين
- يمكن البحث في القائمة بالرقم الوزاري، الاسم، أو سبب التقاعد
- تعرض تاريخ التقاعد وسبب التقاعد

### عرض تفاصيل متقاعد:
- انقر على أيقونة العين للعرض
- تعرض جميع معلومات الموظف
- تعرض تفاصيل التقاعد
- تعرض معلومات الاتصال

### تعديل بيانات متقاعد:
- انقر على أيقونة التعديل
- يمكن تعديل:
  - تاريخ التقاعد
  - سبب التقاعد
  - الملاحظات

### إلغاء التقاعد (إعادة للخدمة):
- انقر على أيقونة الإلغاء (السهم الأحمر)
- سيظهر تحذير مفصل
- عند التأكيد:
  - سيحذف سجل التقاعد
  - سيعود الموظف لقائمة الموظفين النشطين
  - **تحذير**: لا يمكن التراجع عن هذا الإجراء

## التأثير على النظام

### قائمة الموظفين:
- **قبل**: تعرض جميع الموظفين
- **بعد**: تعرض الموظفين النشطين فقط (تستثني المتقاعدين)

### التقارير:
- تقارير الموظفين تشمل النشطين فقط
- يمكن إنشاء تقارير منفصلة للمتقاعدين

### البحث:
- البحث في الموظفين يشمل النشطين فقط
- البحث في المتقاعدين منفصل

## الحماية والأمان

### التحقق من الصحة:
- لا يمكن تقاعد موظف متقاعد بالفعل
- يجب إدخال تاريخ التقاعد
- يجب إدخال سبب التقاعد

### السجلات (Logs):
- جميع عمليات التقاعد تسجل في النظام
- جميع عمليات التعديل تسجل
- جميع عمليات الإلغاء تسجل

### الصلاحيات:
- تتطلب تسجيل الدخول
- تحتاج صلاحيات الموظفين

## الملفات المضافة/المحدثة

### نماذج البيانات:
- `employees/models.py`: إضافة نموذج `RetiredEmployee`
- `employees/forms.py`: إضافة نماذج التقاعد

### العروض (Views):
- `employees/retired_views.py`: عروض المتقاعدين
- `employees/views.py`: تحديث لاستثناء المتقاعدين

### القوالب:
- `templates/employees/retired_employees_list.html`: صفحة المتقاعدين
- `templates/employees/retired_employee_detail.html`: تفاصيل المتقاعد
- `templates/employees/retired_employee_form.html`: نموذج التعديل
- `templates/employees/retired_employee_confirm_delete.html`: تأكيد الإلغاء

### الروابط:
- `employees/urls.py`: إضافة روابط المتقاعدين

### الإدارة:
- `employees/admin.py`: إضافة إدارة المتقاعدين

## استكشاف الأخطاء

### مشكلة: لا تظهر نتائج البحث
- تأكد من أن JavaScript مفعل
- تأكد من اتصال الإنترنت
- تحقق من وجود أخطاء في وحدة تحكم المتصفح

### مشكلة: لا يمكن اختيار الموظف
- تأكد من النقر على النتيجة المطلوبة
- تأكد من ظهور اسم الموظف في حقل "الموظف المحدد"

### مشكلة: رسالة خطأ عند التقاعد
- تأكد من إدخال جميع البيانات المطلوبة
- تأكد من أن الموظف غير متقاعد بالفعل
- تحقق من صحة التاريخ

## دعم إضافي
للمساعدة الإضافية أو الإبلاغ عن مشاكل، يرجى التواصل مع مطور النظام.