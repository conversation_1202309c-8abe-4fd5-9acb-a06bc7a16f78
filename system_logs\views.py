from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
import csv
import json
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
import pytz
import platform
import locale
from .templatetags.system_log_tags import localize_datetime

from .models import SystemLog, SystemError

def is_admin_or_staff(user):
    """Check if user is admin or staff"""
    return user.is_superuser or user.is_staff

@login_required
@user_passes_test(is_admin_or_staff)
def system_log_list(request):
    """View for listing system logs"""
    # Get filter parameters
    module = request.GET.get('module', '')
    action = request.GET.get('action', '')
    user_id = request.GET.get('user', '')
    date_range = request.GET.get('date_range', '')
    search_query = request.GET.get('search', '')

    # Check if any filter is applied
    has_filters = bool(module or action or user_id or date_range or search_query)

    # Base queryset - استبعاد سجلات العرض
    logs = SystemLog.objects.exclude(action=SystemLog.VIEW)
    
    # Apply filters only if filters are provided
    if has_filters:
        # Apply filters
        if module:
            logs = logs.filter(module=module)

        if action:
            logs = logs.filter(action=action)

        if user_id:
            logs = logs.filter(user_id=user_id)

        # Apply date range filter
        if date_range is not None and date_range != '':
            try:
                days = int(date_range)
                if days > 0:
                    start_date = timezone.now() - timedelta(days=days)
                    logs = logs.filter(timestamp__gte=start_date)
                # إذا كان days = 0، فهذا يعني عرض كل السجلات (لا نطبق فلتر زمني)
            except (ValueError, TypeError):
                pass

        # Apply search query
        if search_query:
            logs = logs.filter(
                Q(page__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(object_repr__icontains=search_query) |
                Q(user__username__icontains=search_query)
            )
    else:
        # If no filters are applied, return empty queryset
        logs = logs.none()

    # Get unique values for filters with Arabic names
    modules_data = []
    for module_choice in SystemLog.MODULE_CHOICES:
        if SystemLog.objects.filter(module=module_choice[0]).exists():
            modules_data.append({
                'value': module_choice[0],
                'display': module_choice[1]
            })

    actions_data = []
    for action_choice in SystemLog.LOG_TYPE_CHOICES:
        # استبعاد نوع الإجراء "عرض" من قائمة التصفية
        if action_choice[0] != SystemLog.VIEW and SystemLog.objects.filter(action=action_choice[0]).exists():
            actions_data.append({
                'value': action_choice[0],
                'display': action_choice[1]
            })

    # Get unique users with Arabic display names
    users_data = []
    user_ids = set()
    for log in SystemLog.objects.select_related('user').exclude(user=None).order_by('user__username'):
        if log.user and log.user.id not in user_ids:
            users_data.append({
                'id': log.user.id,
                'username': log.user.username
            })
            user_ids.add(log.user.id)

    # Get sidebar pages for selected module only
    sidebar_pages = {}
    if module:  # Only get pages for the selected module
        module_pages = SystemLog.objects.filter(module=module).values_list('page', flat=True).distinct()
        sidebar_pages[module] = sorted(list(set(module_pages)))

    # Export to CSV if requested
    if 'export' in request.GET:
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="system_logs.csv"'

        # Get Jordan timezone
        jordan_timezone = timezone.get_current_timezone()

        writer = csv.writer(response)
        writer.writerow(['التاريخ والوقت', 'المستخدم', 'عنوان IP', 'القسم', 'الإجراء', 'الصفحة', 'الوصف', 'معرف العنصر', 'تمثيل العنصر'])

        for log in logs:
            # Convert timestamp to Jordan timezone
            if log.timestamp:
                if timezone.is_naive(log.timestamp):
                    timestamp = timezone.make_aware(log.timestamp, timezone.utc)
                else:
                    timestamp = log.timestamp

                local_timestamp = timestamp.astimezone(jordan_timezone)
                formatted_timestamp = local_timestamp.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_timestamp = ''

            writer.writerow([
                formatted_timestamp,
                log.user.username if log.user else 'مجهول',
                log.ip_address or '',
                log.get_module_display(),
                log.get_action_display(),
                log.page,
                log.description or '',
                log.object_id or '',
                log.object_repr or ''
            ])

        return response

    # Order by timestamp (newest first)
    logs = logs.order_by('-timestamp')

    # Add pagination
    from django.core.paginator import Paginator
    page_number = request.GET.get('page', 1)
    per_page_param = request.GET.get('per_page', '50')

    # Handle "all" option
    if per_page_param == 'all':
        per_page = logs.count()  # Show all records
        page_obj = None
        paginated_logs = logs
    else:
        per_page = int(per_page_param)
        paginator = Paginator(logs, per_page)
        page_obj = paginator.get_page(page_number)
        paginated_logs = page_obj

    return render(request, 'system_logs/system_log_list.html', {
        'logs': paginated_logs,
        'modules_data': modules_data,
        'actions_data': actions_data,
        'users_data': users_data,
        'sidebar_pages': sidebar_pages,
        'selected_module': module,
        'selected_action': action,
        'selected_user': user_id,
        'selected_date_range': date_range,
        'search_query': search_query,
        'per_page': per_page_param,
        'page_obj': page_obj,
        'total_logs': logs.count()
    })

@login_required
@user_passes_test(is_admin_or_staff)
def get_system_logs_ajax(request):
    """AJAX view for getting system logs with pagination"""
    # Get filter parameters
    module = request.GET.get('module', '')
    action = request.GET.get('action', '')
    user_id = request.GET.get('user', '')
    date_range = request.GET.get('date_range', '')
    search_query = request.GET.get('search', '')

    # Check if any filter is applied
    has_filters = bool(module or action or user_id or date_range or search_query)

    # Get pagination parameters
    limit = int(request.GET.get('limit', 100))

    # Base queryset - استبعاد سجلات العرض
    logs = SystemLog.objects.exclude(action=SystemLog.VIEW)
    
    # Apply filters only if filters are provided
    if has_filters:
        # Apply filters
        if module:
            logs = logs.filter(module=module)

        if action:
            logs = logs.filter(action=action)

        if user_id:
            logs = logs.filter(user_id=user_id)

        # Apply date range filter
        if date_range is not None and date_range != '':
            try:
                days = int(date_range)
                if days > 0:
                    start_date = timezone.now() - timedelta(days=days)
                    logs = logs.filter(timestamp__gte=start_date)
                # إذا كان days = 0، فهذا يعني عرض كل السجلات (لا نطبق فلتر زمني)
            except (ValueError, TypeError):
                pass

        # Apply search query
        if search_query:
            logs = logs.filter(
                Q(page__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(object_repr__icontains=search_query) |
                Q(user__username__icontains=search_query)
            )
    else:
        # If no filters are applied, return empty queryset
        logs = logs.none()

    # Get total count
    total_count = logs.count()

    # Apply limit (if limit is 0, show all logs)
    if limit > 0:
        logs = logs[:limit]

    # Render logs to HTML
    logs_html = render_to_string('system_logs/logs_table_rows.html', {'logs': logs})

    # Return JSON response
    return JsonResponse({
        'html': logs_html,
        'total_count': total_count
    })

@login_required
@user_passes_test(is_admin_or_staff)
def clear_system_logs(request):
    """View for clearing system logs"""
    if request.method == 'POST':
        # Check for confirmation
        confirmation = request.POST.get('confirmation', '')

        if confirmation != 'DELETE_ALL':
            messages.error(request, 'يرجى كتابة "DELETE_ALL" للتأكيد على حذف جميع السجلات.')
            return redirect('system_logs:system_log_list')

        # Log the clear action first
        log_user_action(
            request=request,
            user=request.user,
            module=SystemLog.SYSTEM,
            action=SystemLog.DELETE,
            page='Clear System Activity Logs',
            description='تم مسح جميع سجلات حركات النظام'
        )

        # Then delete all logs except the one we just created
        latest_log = SystemLog.objects.latest('timestamp')
        deleted_count = SystemLog.objects.exclude(pk=latest_log.pk).count()
        SystemLog.objects.exclude(pk=latest_log.pk).delete()

        messages.success(request, f'تم مسح {deleted_count} من سجلات حركات النظام بنجاح.')

    return redirect('system_logs:system_log_list')

@login_required
@user_passes_test(is_admin_or_staff)
def update_log_descriptions(request):
    """View for updating log descriptions to Arabic"""
    if request.method == 'POST':
        try:
            updated_count = 0

            # Get all logs
            logs = SystemLog.objects.all()
            total_logs = logs.count()

            for log in logs:
                # Get module name in Arabic
                module_name = dict(SystemLog.MODULE_CHOICES).get(log.module, log.module)

                # Get action name in Arabic
                action_name = dict(SystemLog.LOG_TYPE_CHOICES).get(log.action, log.action)

                # Get user name
                user_name = log.user.username if log.user else "مستخدم مجهول"

                # Generate detailed Arabic description based on page and action
                page_name = log.page or ""

                # Generate more specific descriptions based on page patterns
                if log.action == SystemLog.CREATE:
                    if 'employee' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بإضافة موظف جديد'
                    elif 'department' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بإضافة قسم جديد'
                    elif 'leave' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بإضافة طلب إجازة جديد'
                    else:
                        description = f'قام المستخدم <strong>{user_name}</strong> بإضافة عنصر جديد في قسم <strong>{module_name}</strong>'

                elif log.action == SystemLog.UPDATE:
                    if 'employee' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بتحديث بيانات موظف'
                    elif 'department' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بتحديث بيانات قسم'
                    elif 'leave' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بتحديث طلب إجازة'
                    else:
                        description = f'قام المستخدم <strong>{user_name}</strong> بتعديل بيانات في قسم <strong>{module_name}</strong>'

                elif log.action == SystemLog.DELETE:
                    if 'employee' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بحذف موظف'
                    elif 'department' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بحذف قسم'
                    elif 'leave' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بحذف طلب إجازة'
                    else:
                        description = f'قام المستخدم <strong>{user_name}</strong> بحذف عنصر من قسم <strong>{module_name}</strong>'

                elif log.action == SystemLog.VIEW:
                    if 'list' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بعرض قائمة {module_name}'
                    elif 'detail' in page_name.lower():
                        description = f'قام المستخدم <strong>{user_name}</strong> بعرض تفاصيل عنصر في {module_name}'
                    else:
                        description = f'قام المستخدم <strong>{user_name}</strong> بعرض صفحة في قسم <strong>{module_name}</strong>'

                elif log.action == SystemLog.LOGIN:
                    description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الدخول إلى النظام'
                elif log.action == SystemLog.LOGOUT:
                    description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الخروج من النظام'
                elif log.action == SystemLog.EXPORT:
                    description = f'قام المستخدم <strong>{user_name}</strong> بتصدير بيانات من قسم <strong>{module_name}</strong>'
                elif log.action == SystemLog.IMPORT:
                    description = f'قام المستخدم <strong>{user_name}</strong> باستيراد بيانات إلى قسم <strong>{module_name}</strong>'
                elif log.action == SystemLog.BACKUP:
                    description = f'قام المستخدم <strong>{user_name}</strong> بإنشاء نسخة احتياطية من النظام'
                elif log.action == SystemLog.RESTORE:
                    description = f'قام المستخدم <strong>{user_name}</strong> باستعادة نسخة احتياطية للنظام'
                else:
                    description = f'قام المستخدم <strong>{user_name}</strong> بتنفيذ إجراء <strong>{action_name}</strong> في قسم <strong>{module_name}</strong>'

                # Add object info if available
                if log.object_repr:
                    description += f' <span class="object-name">({log.object_repr})</span>'

                # Add page info if available and not already included
                if log.page and not any(keyword in page_name.lower() for keyword in ['employee', 'department', 'leave', 'list', 'detail']):
                    description += f' - صفحة: <strong>{log.page}</strong>'

                # Update log description
                log.description = description
                log.save()
                updated_count += 1

            # Log this action
            log_user_action(
                request=request,
                user=request.user,
                module=SystemLog.SYSTEM,
                action=SystemLog.UPDATE,
                page='تحديث أوصاف السجلات',
                description=f'قام المستخدم <strong>{request.user.username}</strong> بتحديث أوصاف {updated_count} سجل إلى اللغة العربية'
            )

            # Return JSON response for AJAX
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': f'تم تحديث {updated_count} من سجلات حركات النظام بنجاح.',
                    'updated_count': updated_count,
                    'total_logs': total_logs
                })
            else:
                messages.success(request, f'تم تحديث {updated_count} من سجلات حركات النظام بنجاح.')
                return redirect('system_logs:system_log_list')

        except Exception as e:
            # Return error response
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': f'حدث خطأ أثناء تحديث الأوصاف: {str(e)}'
                }, status=500)
            else:
                messages.error(request, f'حدث خطأ أثناء تحديث الأوصاف: {str(e)}')
                return redirect('system_logs:system_log_list')

    return redirect('system_logs:system_log_list')

def log_user_action(request=None, user=None, module=None, action=None, page=None, description=None, object_id=None, object_repr=None):
    """Helper function to log user actions"""
    try:
        # Get IP address from request
        ip_address = None
        operating_system = None

        if request:
            # Get IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')

            # Get operating system from user agent with version
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # Windows detection with version
            if 'Windows' in user_agent:
                if 'Windows NT 10.0' in user_agent:
                    operating_system = 'Windows 10/11'
                elif 'Windows NT 6.3' in user_agent:
                    operating_system = 'Windows 8.1'
                elif 'Windows NT 6.2' in user_agent:
                    operating_system = 'Windows 8'
                elif 'Windows NT 6.1' in user_agent:
                    operating_system = 'Windows 7'
                elif 'Windows NT 6.0' in user_agent:
                    operating_system = 'Windows Vista'
                elif 'Windows NT 5.1' in user_agent:
                    operating_system = 'Windows XP'
                else:
                    operating_system = 'Windows'

            # macOS detection with version
            elif 'Mac OS X' in user_agent:
                mac_version = ''
                if 'Mac OS X 10_15' in user_agent:
                    mac_version = 'Catalina'
                elif 'Mac OS X 10_14' in user_agent:
                    mac_version = 'Mojave'
                elif 'Mac OS X 10_13' in user_agent:
                    mac_version = 'High Sierra'
                elif 'Mac OS X 10_12' in user_agent:
                    mac_version = 'Sierra'
                elif 'Mac OS X 10_11' in user_agent:
                    mac_version = 'El Capitan'
                elif 'Mac OS X 10.16' in user_agent or 'Mac OS X 11' in user_agent:
                    mac_version = 'Big Sur'
                elif 'Mac OS X 12' in user_agent:
                    mac_version = 'Monterey'
                elif 'Mac OS X 13' in user_agent:
                    mac_version = 'Ventura'
                elif 'Mac OS X 14' in user_agent:
                    mac_version = 'Sonoma'

                if mac_version:
                    operating_system = f'macOS {mac_version}'
                else:
                    operating_system = 'macOS'

            # Linux detection
            elif 'Linux' in user_agent:
                if 'Ubuntu' in user_agent:
                    operating_system = 'Ubuntu Linux'
                elif 'Fedora' in user_agent:
                    operating_system = 'Fedora Linux'
                elif 'Debian' in user_agent:
                    operating_system = 'Debian Linux'
                elif 'CentOS' in user_agent:
                    operating_system = 'CentOS Linux'
                elif 'RHEL' in user_agent:
                    operating_system = 'Red Hat Linux'
                else:
                    operating_system = 'Linux'

            # Mobile OS detection
            elif 'Android' in user_agent:
                # Try to extract Android version
                import re
                android_version = re.search(r'Android\s([0-9\.]+)', user_agent)
                if android_version:
                    operating_system = f'Android {android_version.group(1)}'
                else:
                    operating_system = 'Android'

            elif 'iPhone' in user_agent or 'iPad' in user_agent or 'iPod' in user_agent:
                # Try to extract iOS version
                import re
                ios_version = re.search(r'OS\s([0-9_]+)', user_agent)
                if ios_version:
                    version = ios_version.group(1).replace('_', '.')
                    operating_system = f'iOS {version}'
                else:
                    operating_system = 'iOS'

            else:
                operating_system = 'Unknown'

        # Generate Arabic description if not provided
        if not description:
            # Get module name in Arabic
            module_name = dict(SystemLog.MODULE_CHOICES).get(module, module)

            # Get action name in Arabic
            action_name = dict(SystemLog.LOG_TYPE_CHOICES).get(action, action)

            # Get user name if available
            user_name = user.username if user else "مستخدم مجهول"

            # Generate detailed Arabic description based on page and action
            page_name = page or ""

            # Generate more specific descriptions based on page patterns
            if action == SystemLog.CREATE:
                if 'employee' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بإضافة موظف جديد'
                elif 'department' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بإضافة قسم جديد'
                elif 'leave' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بإضافة طلب إجازة جديد'
                else:
                    description = f'قام المستخدم <strong>{user_name}</strong> بإضافة عنصر جديد في قسم <strong>{module_name}</strong>'

            elif action == SystemLog.UPDATE:
                if 'employee' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بتحديث بيانات موظف'
                elif 'department' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بتحديث بيانات قسم'
                elif 'leave' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بتحديث طلب إجازة'
                else:
                    description = f'قام المستخدم <strong>{user_name}</strong> بتعديل بيانات في قسم <strong>{module_name}</strong>'

            elif action == SystemLog.DELETE:
                if 'employee' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بحذف موظف'
                elif 'department' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بحذف قسم'
                elif 'leave' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بحذف طلب إجازة'
                else:
                    description = f'قام المستخدم <strong>{user_name}</strong> بحذف عنصر من قسم <strong>{module_name}</strong>'

            elif action == SystemLog.VIEW:
                if 'list' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بعرض قائمة {module_name}'
                elif 'detail' in page_name.lower():
                    description = f'قام المستخدم <strong>{user_name}</strong> بعرض تفاصيل عنصر في {module_name}'
                else:
                    description = f'قام المستخدم <strong>{user_name}</strong> بعرض صفحة في قسم <strong>{module_name}</strong>'

            elif action == SystemLog.LOGIN:
                description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الدخول إلى النظام'
            elif action == SystemLog.LOGOUT:
                description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الخروج من النظام'
            elif action == SystemLog.EXPORT:
                description = f'قام المستخدم <strong>{user_name}</strong> بتصدير بيانات من قسم <strong>{module_name}</strong>'
            elif action == SystemLog.IMPORT:
                description = f'قام المستخدم <strong>{user_name}</strong> باستيراد بيانات إلى قسم <strong>{module_name}</strong>'
            elif action == SystemLog.BACKUP:
                description = f'قام المستخدم <strong>{user_name}</strong> بإنشاء نسخة احتياطية من النظام'
            elif action == SystemLog.RESTORE:
                description = f'قام المستخدم <strong>{user_name}</strong> باستعادة نسخة احتياطية للنظام'
            else:
                description = f'قام المستخدم <strong>{user_name}</strong> بتنفيذ إجراء <strong>{action_name}</strong> في قسم <strong>{module_name}</strong>'

            # Add object info if available
            if object_repr:
                description += f' <span class="object-name">({object_repr})</span>'

            # Add page info if available and not already included
            if page and not any(keyword in page_name.lower() for keyword in ['employee', 'department', 'leave', 'list', 'detail']):
                description += f' - صفحة: <strong>{page}</strong>'

        # Create log entry
        SystemLog.objects.create(
            user=user,
            ip_address=ip_address,
            operating_system=operating_system,
            module=module,
            action=action,
            page=page or '',
            description=description,
            object_id=object_id,
            object_repr=object_repr
        )

        return True
    except Exception as e:
        print(f"Error logging user action: {str(e)}")
        return False


# ==================== System Errors Views ====================

@login_required
@user_passes_test(is_admin_or_staff)
def system_error_list(request):
    """View for listing system errors with filters"""
    
    # Get filter parameters
    error_type = request.GET.get('error_type', '')
    severity = request.GET.get('severity', '')
    status = request.GET.get('status', '')
    module = request.GET.get('module', '')
    date_range = request.GET.get('date_range', '')
    search_query = request.GET.get('search', '')
    user_id = request.GET.get('user', '')
    
    # Initialize empty queryset
    errors = SystemError.objects.none()
    show_results = False
    
    # Check if any filter is applied
    if any([error_type, severity, status, module, date_range, search_query, user_id]):
        show_results = True
        errors = SystemError.objects.all()
        
        # Apply filters
        if error_type:
            errors = errors.filter(error_type=error_type)
        
        if severity:
            errors = errors.filter(severity=severity)
            
        if status:
            errors = errors.filter(status=status)
            
        if module:
            errors = errors.filter(module=module)
            
        if user_id:
            errors = errors.filter(user_id=user_id)
            
        # Apply date range filter
        if date_range:
            try:
                days = int(date_range)
                if days > 0:
                    start_date = timezone.now() - timedelta(days=days)
                    errors = errors.filter(timestamp__gte=start_date)
            except ValueError:
                pass
        
        # Apply search filter
        if search_query:
            errors = errors.filter(
                Q(error_message__icontains=search_query) |
                Q(error_description__icontains=search_query) |
                Q(page_name__icontains=search_query) |
                Q(function_name__icontains=search_query)
            )
    
    # Get filter options
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    filter_options = {
        'error_types': SystemError.ERROR_TYPE_CHOICES,
        'severities': SystemError.SEVERITY_CHOICES,
        'statuses': SystemError.STATUS_CHOICES,
        'modules': SystemError.objects.values_list('module', flat=True).distinct().exclude(module__isnull=True),
        'users': User.objects.filter(system_errors__isnull=False).distinct(),
        'date_ranges': [
            ('1', 'آخر يوم'),
            ('7', 'آخر أسبوع'),
            ('30', 'آخر شهر'),
            ('90', 'آخر 3 أشهر'),
            ('365', 'آخر سنة'),
        ]
    }
    
    # Get statistics
    stats = {}
    if show_results:
        stats = {
            'total_errors': errors.count(),
            'new_errors': errors.filter(status=SystemError.NEW).count(),
            'critical_errors': errors.filter(severity=SystemError.CRITICAL).count(),
            'resolved_errors': errors.filter(status=SystemError.RESOLVED).count(),
        }
    
    context = {
        'errors': errors,
        'show_results': show_results,
        'filter_options': filter_options,
        'stats': stats,
        'current_filters': {
            'error_type': error_type,
            'severity': severity,
            'status': status,
            'module': module,
            'date_range': date_range,
            'search_query': search_query,
            'user_id': user_id,
        }
    }
    
    return render(request, 'system_logs/system_error_list.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def system_error_detail(request, error_id):
    """View for displaying detailed error information"""
    from django.shortcuts import get_object_or_404
    
    error = get_object_or_404(SystemError, id=error_id)
    
    context = {
        'error': error,
    }
    
    return render(request, 'system_logs/system_error_detail.html', context)


@login_required
@user_passes_test(is_admin_or_staff)
def update_error_status(request, error_id):
    """AJAX view for updating error status"""
    if request.method == 'POST':
        from django.shortcuts import get_object_or_404
        import json
        
        error = get_object_or_404(SystemError, id=error_id)
        data = json.loads(request.body)
        
        new_status = data.get('status')
        resolution_notes = data.get('resolution_notes', '')
        
        if new_status in [choice[0] for choice in SystemError.STATUS_CHOICES]:
            error.status = new_status
            if resolution_notes:
                error.resolution_notes = resolution_notes
            
            if new_status == SystemError.RESOLVED:
                error.resolved_by = request.user
                error.resolved_at = timezone.now()
            
            error.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم تحديث حالة الخطأ بنجاح',
                'new_status': error.get_status_display(),
                'status_color': error.get_status_color()
            })
    
    return JsonResponse({'success': False, 'message': 'خطأ في تحديث الحالة'})


def log_system_error(request, error_type, error_message, **kwargs):
    """
    Utility function to log system errors
    
    Args:
        request: Django request object
        error_type: Type of error (from SystemError.ERROR_TYPE_CHOICES)
        error_message: Error message
        **kwargs: Additional error details
    """
    try:
        # Extract error details
        page_url = request.get_full_path() if request else ''
        page_name = kwargs.get('page_name', page_url.split('/')[-2] if page_url else 'Unknown')
        
        # Get user information
        user = request.user if request and hasattr(request, 'user') and request.user.is_authenticated else None
        ip_address = get_client_ip(request) if request else None
        user_agent = request.META.get('HTTP_USER_AGENT', '') if request else ''
        
        # Determine severity based on error type
        severity_mapping = {
            SystemError.SYNTAX_ERROR: SystemError.HIGH,
            SystemError.TYPE_ERROR: SystemError.MEDIUM,
            SystemError.VALUE_ERROR: SystemError.MEDIUM,
            SystemError.ATTRIBUTE_ERROR: SystemError.MEDIUM,
            SystemError.KEY_ERROR: SystemError.MEDIUM,
            SystemError.INDEX_ERROR: SystemError.MEDIUM,
            SystemError.NAME_ERROR: SystemError.HIGH,
            SystemError.IMPORT_ERROR: SystemError.HIGH,
            SystemError.PERMISSION_ERROR: SystemError.HIGH,
            SystemError.DATABASE_ERROR: SystemError.CRITICAL,
            SystemError.VALIDATION_ERROR: SystemError.LOW,
            SystemError.HTTP_ERROR: SystemError.MEDIUM,
            SystemError.TEMPLATE_ERROR: SystemError.MEDIUM,
            SystemError.OTHER_ERROR: SystemError.MEDIUM,
        }
        
        severity = kwargs.get('severity', severity_mapping.get(error_type, SystemError.MEDIUM))
        
        # Create error description
        error_descriptions = {
            SystemError.SYNTAX_ERROR: 'خطأ في بناء الجملة البرمجية - يحدث عندما يكون هناك خطأ في كتابة الكود',
            SystemError.TYPE_ERROR: 'خطأ في نوع البيانات - يحدث عند محاولة استخدام نوع بيانات خاطئ',
            SystemError.VALUE_ERROR: 'خطأ في قيمة البيانات - يحدث عند تمرير قيمة غير صحيحة لدالة',
            SystemError.ATTRIBUTE_ERROR: 'خطأ في الخاصية - يحدث عند محاولة الوصول لخاصية غير موجودة',
            SystemError.KEY_ERROR: 'خطأ في المفتاح - يحدث عند محاولة الوصول لمفتاح غير موجود في القاموس',
            SystemError.INDEX_ERROR: 'خطأ في الفهرس - يحدث عند محاولة الوصول لفهرس خارج نطاق القائمة',
            SystemError.NAME_ERROR: 'خطأ في الاسم - يحدث عند استخدام متغير غير معرف',
            SystemError.IMPORT_ERROR: 'خطأ في الاستيراد - يحدث عند فشل استيراد مكتبة أو وحدة',
            SystemError.PERMISSION_ERROR: 'خطأ في الصلاحيات - يحدث عند عدم وجود صلاحية للوصول للمورد',
            SystemError.DATABASE_ERROR: 'خطأ في قاعدة البيانات - يحدث عند فشل عملية قاعدة البيانات',
            SystemError.VALIDATION_ERROR: 'خطأ في التحقق - يحدث عند فشل التحقق من صحة البيانات',
            SystemError.HTTP_ERROR: 'خطأ في HTTP - يحدث عند فشل طلب HTTP',
            SystemError.TEMPLATE_ERROR: 'خطأ في القالب - يحدث عند وجود خطأ في قالب HTML',
            SystemError.OTHER_ERROR: 'خطأ آخر - خطأ غير مصنف',
        }
        
        error_description = kwargs.get('error_description', 
                                     error_descriptions.get(error_type, 'خطأ في النظام'))
        
        # Check if similar error exists (same error_type, page_url, and error_message)
        existing_error = SystemError.objects.filter(
            error_type=error_type,
            page_url=page_url,
            error_message=error_message
        ).first()
        
        if existing_error:
            # Increment occurrence count
            existing_error.increment_occurrence()
            return existing_error
        else:
            # Create new error record
            error_record = SystemError.objects.create(
                error_type=error_type,
                error_message=error_message,
                error_description=error_description,
                page_url=page_url,
                page_name=page_name,
                file_path=kwargs.get('file_path'),
                line_number=kwargs.get('line_number'),
                function_name=kwargs.get('function_name'),
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                stack_trace=kwargs.get('stack_trace'),
                request_method=request.method if request else None,
                request_data=str(request.POST) if request and request.method == 'POST' else None,
                module=kwargs.get('module'),
                severity=severity,
            )
            
            return error_record
            
    except Exception as e:
        print(f"Error logging system error: {str(e)}")
        return None


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
