# Generated by Django 5.2 on 2025-05-19 15:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0005_approvedform'),
    ]

    operations = [
        migrations.CreateModel(
            name='TechnicalTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ministry_number', models.CharField(max_length=20, verbose_name='الرقم الوزاري')),
                ('employee_name', models.CharField(max_length=100, verbose_name='اسم الموظف')),
                ('current_department', models.CharField(max_length=100, verbose_name='القسم الحالي')),
                ('new_department', models.CharField(max_length=100, verbose_name='مركز العمل الجديد')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نقل فني',
                'verbose_name_plural': 'النقل الفني',
                'ordering': ['-created_at'],
            },
        ),
    ]
