{% extends 'base.html' %}

{% block title %}{% if is_create %}إضافة موظف جديد{% else %}تعديل بيانات الموظف{% endif %}{% endblock %}

{% block styles %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .form-group {
        margin-bottom: 1rem;
    }
    .form-group label {
        font-weight: bold;
    }
    .card {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
    .btn-primary {
        font-weight: bold;
    }
    
    /* Select2 Custom Styles */
    .select2-container--bootstrap-5 .select2-selection {
        min-height: calc(1.5em + 0.75rem + 2px);
    }
    
    .select2-container--bootstrap-5 .select2-selection--single {
        padding: 0.375rem 0.75rem;
    }
    
    .select2-container {
        width: 100% !important;
    }
    
    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-user-edit ml-2"></i>
                    <strong>تعليمات إدخال بيانات الموظف</strong>
                </div>
                <ul>
                    <li><strong>الرقم الوزاري:</strong> يجب إدخال الرقم الوزاري للموظف بشكل صحيح.</li>
                    <li><strong>الاسم الرباعي:</strong> يجب إدخال الاسم الرباعي للموظف.</li>
                    <li><strong>الجنس:</strong> اختر جنس الموظف (ذكر أو أنثى).</li>
                    <li><strong>تاريخ التعيين:</strong> أدخل تاريخ تعيين الموظف بالتنسيق الصحيح.</li>
                    <li><strong>القسم:</strong> اختر القسم الذي يعمل فيه الموظف.</li>
                    <li><strong>المسمى الوظيفي:</strong> اختر المسمى الوظيفي للموظف.</li>
                </ul>
            </div>
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        {% if is_create %}
                            إضافة موظف جديد
                        {% else %}
                            تعديل بيانات {{ employee.full_name }}
                        {% endif %}
                    </h6>
                    <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة إلى قائمة الموظفين
                    </a>
                </div>
                <div class="card-body">
                    <form method="post" action="{% if is_create %}{% url 'employees:employee_create' %}{% else %}{% url 'employees:employee_update' employee.pk %}{% endif %}" novalidate>
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_ministry_number">الرقم الوزاري</label>
                                    <input type="text" name="ministry_number" id="id_ministry_number" class="form-control" value="{% if employee %}{{ employee.ministry_number }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_national_id">الرقم الوطني</label>
                                    <input type="text" name="national_id" id="id_national_id" class="form-control" value="{% if employee %}{{ employee.national_id|default:'' }}{% endif %}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="id_full_name">الاسم الرباعي</label>
                            <input type="text" name="full_name" id="id_full_name" class="form-control" value="{% if employee %}{{ employee.full_name }}{% endif %}" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_gender">الجنس</label>
                                    <select name="gender" id="id_gender" class="form-control" required>
                                        <option value="">---------</option>
                                        <option value="male" {% if employee and employee.gender == 'male' %}selected{% endif %}>ذكر</option>
                                        <option value="female" {% if employee and employee.gender == 'female' %}selected{% endif %}>انثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_birth_date">تاريخ الميلاد</label>
                                    <input type="date" name="birth_date" id="id_birth_date" class="form-control" value="{% if employee %}{{ employee.birth_date|date:'Y-m-d' }}{% endif %}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_qualification">المؤهل العلمي (بكالوريس / دبلوم)</label>
                                    <input type="text" name="qualification" id="id_qualification" class="form-control" value="{% if employee %}{{ employee.qualification|default:'' }}{% endif %}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_post_graduate_diploma">المؤهل العلمي (دبلوم بعد البكالوريس)</label>
                                    <input type="text" name="post_graduate_diploma" id="id_post_graduate_diploma" class="form-control" value="{% if employee %}{{ employee.post_graduate_diploma|default:'' }}{% endif %}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_masters_degree">المؤهل العلمي (ماجستير)</label>
                                    <input type="text" name="masters_degree" id="id_masters_degree" class="form-control" value="{% if employee %}{{ employee.masters_degree|default:'' }}{% endif %}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_phd_degree">المؤهل العلمي (دكتوراه)</label>
                                    <input type="text" name="phd_degree" id="id_phd_degree" class="form-control" value="{% if employee %}{{ employee.phd_degree|default:'' }}{% endif %}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="id_specialization">التخصص</label>
                            <input type="text" name="specialization" id="id_specialization" class="form-control" value="{% if employee %}{{ employee.specialization|default:'' }}{% endif %}">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_hire_date">تاريخ التعيين</label>
                                    <input type="date" name="hire_date" id="id_hire_date" class="form-control" value="{% if employee %}{{ employee.hire_date|date:'Y-m-d' }}{% endif %}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_appointment_type">صفة التعيين</label>
                                    <select name="appointment_type" id="id_appointment_type" class="form-control select2">
                                        <option value="">اختر صفة التعيين...</option>
                                        {% for apt in appointment_types %}
                                            <option value="{{ apt.id }}" {% if employee %}{% with current_employment=employee.employments.filter.first %}{% if current_employment and current_employment.appointment_type and current_employment.appointment_type.id == apt.id %}selected{% endif %}{% endwith %}{% endif %}>{{ apt.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.department_choice.id_for_label }}">القسم</label>
                                    {{ form.department_choice }}
                                    {% if form.department_choice.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.department_choice.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_position">المسمى الوظيفي</label>
                                    <select name="position" id="id_position" class="form-control select2">
                                        <option value="">اختر المسمى الوظيفي...</option>
                                        {% for pos in positions %}
                                            <option value="{{ pos.id }}" {% if employee %}{% with current_employment=employee.employments.filter.first %}{% if current_employment and current_employment.position.id == pos.id %}selected{% endif %}{% endwith %}{% endif %}>{{ pos.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_phone_number">رقم الهاتف</label>
                                    <input type="text" name="phone_number" id="id_phone_number" class="form-control" value="{% if employee %}{{ employee.phone_number|default:'' }}{% endif %}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_address">العنوان</label>
                                    <textarea name="address" id="id_address" class="form-control" rows="3">{% if employee %}{{ employee.address|default:'' }}{% endif %}</textarea>
                                </div>
                            </div>
                        </div>



                        <div class="mt-4 text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> 
                                {% if is_create %}
                                    إضافة الموظف
                                {% else %}
                                    حفظ التغييرات
                                {% endif %}
                            </button>
                            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary btn-lg ms-2">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 for department selection
    $('#id_department_choice').select2({
        theme: 'bootstrap-5',
        placeholder: 'ابحث عن القسم...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            },
            loadingMore: function() {
                return "جاري تحميل المزيد...";
            }
        }
    });
    
    // Initialize Select2 for position selection
    $('#id_position').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر المسمى الوظيفي...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
    
    // Initialize Select2 for appointment type selection
    $('#id_appointment_type').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر صفة التعيين...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Enable PhD field only when Masters field has value
    function checkPhdAvailability() {
        var mastersValue = $('#id_masters_degree').val();
        var phdField = $('#id_phd_degree');
        
        if (mastersValue && mastersValue.trim() !== '') {
            phdField.prop('disabled', false);
            phdField.removeClass('disabled');
        } else {
            phdField.prop('disabled', true);
            phdField.addClass('disabled');
            phdField.val(''); // Clear PhD field if masters is empty
        }
    }
    
    // Check on page load
    checkPhdAvailability();
    
    // Check when masters field changes
    $('#id_masters_degree').on('input change', function() {
        checkPhdAvailability();
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        var mastersValue = $('#id_masters_degree').val();
        var phdValue = $('#id_phd_degree').val();
        
        if (phdValue && phdValue.trim() !== '' && (!mastersValue || mastersValue.trim() === '')) {
            e.preventDefault();
            alert('يجب إدخال المؤهل العلمي (ماجستير) قبل إدخال المؤهل العلمي (دكتوراه)');
            return false;
        }
        
        // Validate Select2 fields
        var departmentValue = $('#id_department_choice').val();
        if (!departmentValue) {
            e.preventDefault();
            alert('يرجى اختيار القسم');
            $('#id_department_choice').focus();
            return false;
        }
    });
    
    // Custom validation styles for Select2
    $('.select2-container').on('focus', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>

<style>
.form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

/* Additional styles for Select2 validation */
.select2-container.is-invalid .select2-selection {
    border-color: #dc3545;
}

.select2-container.is-invalid .select2-selection:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
</style>
{% endblock %}
