/**
 * Simple Dark Mode Toggle
 * This script provides a simple dark mode toggle functionality
 * that only adds/removes the dark-mode class without any additional styling
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the dark mode toggle button
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    if (!darkModeToggle) {
        console.error('Dark mode toggle button not found');
        return;
    }
    
    // Get the icon inside the button
    const icon = darkModeToggle.querySelector('i');
    
    if (!icon) {
        console.error('Icon element not found inside dark mode toggle button');
        return;
    }
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    
    // Apply dark mode if saved preference is 'dark'
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        updateIcon(true);
    }
    
    // Add click event listener to the toggle button
    darkModeToggle.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Toggle dark mode class on body
        const isDarkMode = document.body.classList.toggle('dark-mode');
        
        // Update the icon
        updateIcon(isDarkMode);
        
        // Save preference to localStorage
        localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
    });
    
    // Function to update the icon based on dark mode state
    function updateIcon(isDarkMode) {
        // Clear existing classes
        icon.className = '';
        
        // Add appropriate icon class
        if (isDarkMode) {
            icon.classList.add('fas', 'fa-sun');
        } else {
            icon.classList.add('fas', 'fa-moon');
        }
    }
});
