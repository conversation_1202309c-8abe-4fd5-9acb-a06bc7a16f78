{% extends 'base.html' %}
{% load static %}

{% block title %}العلاوات - الرتب والعلاوات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard-style cards */
    .dashboard-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none !important;
        border-radius: 15px !important;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
    }

    .dashboard-card .text-xs {
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
    }

    .dashboard-card .h3 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    }

    .dashboard-card .rounded-circle {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dashboard-card .fa-2x {
        font-size: 1.8rem;
    }

    .dashboard-card .badge {
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
    }

    .dashboard-card .text-muted.small {
        font-size: 0.8rem;
        color: #6c757d !important;
    }

    /* Border colors for different card types */
    .border-primary {
        border-left: 4px solid #007bff !important;
    }

    .border-success {
        border-left: 4px solid #28a745 !important;
    }

    .border-info {
        border-left: 4px solid #17a2b8 !important;
    }

    .border-warning {
        border-left: 4px solid #ffc107 !important;
    }

    .border-secondary {
        border-left: 4px solid #6c757d !important;
    }

    .border-dark {
        border-left: 4px solid #343a40 !important;
    }

    /* Background opacity classes */
    .bg-opacity-10 {
        background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
    }

    .bg-primary.bg-opacity-10 {
        background-color: rgba(0, 123, 255, 0.1) !important;
    }

    .bg-success.bg-opacity-10 {
        background-color: rgba(40, 167, 69, 0.1) !important;
    }

    .bg-info.bg-opacity-10 {
        background-color: rgba(23, 162, 184, 0.1) !important;
    }

    .bg-warning.bg-opacity-10 {
        background-color: rgba(255, 193, 7, 0.1) !important;
    }

    .bg-secondary.bg-opacity-10 {
        background-color: rgba(108, 117, 125, 0.1) !important;
    }

    .bg-dark.bg-opacity-10 {
        background-color: rgba(52, 58, 64, 0.1) !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .dashboard-card .h3 {
            font-size: 2rem;
        }
        .dashboard-card .text-xs {
            font-size: 0.7rem;
        }
        .dashboard-card .rounded-circle {
            width: 50px;
            height: 50px;
        }
        .dashboard-card .fa-2x {
            font-size: 1.5rem;
        }
    }

    /* Animation for numbers */
    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .dashboard-card .h3 {
        animation: countUp 0.6s ease-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>العلاوات</h2>
    <div>
        <a href="{% url 'ranks:employee_allowance_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة علاوات موظف
        </a>
        <a href="?export=excel{% if search_term %}&search={{ search_term }}{% endif %}{% if education_allowance %}&education_allowance={{ education_allowance }}{% endif %}{% if adjustment_allowance %}&adjustment_allowance={{ adjustment_allowance }}{% endif %}{% if transportation_allowance %}&transportation_allowance={{ transportation_allowance }}{% endif %}{% if supervisory_allowance %}&supervisory_allowance={{ supervisory_allowance }}{% endif %}{% if technical_allowance %}&technical_allowance={{ technical_allowance }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للرتب
        </a>
    </div>
</div>

<!-- Alert for success messages -->
<div id="alert-container"></div>

<!-- Dashboard Statistics Cards -->
<div class="row mb-4">
    <!-- Total Allowances Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي السجلات</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ total_allowances }}</div>
                        <div class="text-muted small mt-2">موظف مسجل</div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-chart-bar fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Education Allowance Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">علاوة التعليم</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ education_count }}</div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="text-muted small">من {{ total_allowances }}</span>
                            <span class="badge bg-success rounded-pill">
                                {% widthratio education_count total_allowances 100 %}%
                            </span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-graduation-cap fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Adjustment Allowance Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-info shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">التجيير</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ adjustment_count }}</div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="text-muted small">من {{ total_allowances }}</span>
                            <span class="badge bg-info rounded-pill">
                                {% widthratio adjustment_count total_allowances 100 %}%
                            </span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-cogs fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transportation Allowance Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-warning shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">التنقلات</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ transportation_count }}</div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="text-muted small">من {{ total_allowances }}</span>
                            <span class="badge bg-warning rounded-pill text-dark">
                                {% widthratio transportation_count total_allowances 100 %}%
                            </span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-car fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supervisory Allowance Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-secondary shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-secondary text-uppercase mb-1">العلاوة الإشرافية</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ supervisory_count }}</div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="text-muted small">من {{ total_allowances }}</span>
                            <span class="badge bg-secondary rounded-pill">
                                {% widthratio supervisory_count total_allowances 100 %}%
                            </span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-secondary bg-opacity-10 p-3">
                            <i class="fas fa-user-tie fa-2x text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Technical Allowance Card -->
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-start border-4 border-dark shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-dark text-uppercase mb-1">علاوة فنية</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ technical_count }}</div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="text-muted small">من {{ total_allowances }}</span>
                            <span class="badge bg-dark rounded-pill">
                                {% widthratio technical_count total_allowances 100 %}%
                            </span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-dark bg-opacity-10 p-3">
                            <i class="fas fa-tools fa-2x text-dark"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <div class="row g-3">
            <!-- Education Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-graduation-cap text-success"></i> علاوة التعليم
                </label>
                <select id="educationFilter" class="form-select form-select-sm filter-select" data-column="3">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Adjustment Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-cogs text-info"></i> التجيير
                </label>
                <select id="adjustmentFilter" class="form-select form-select-sm filter-select" data-column="4">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Transportation Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-car text-warning"></i> التنقلات
                </label>
                <select id="transportationFilter" class="form-select form-select-sm filter-select" data-column="5">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Supervisory Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-user-tie text-secondary"></i> العلاوة الإشرافية
                </label>
                <select id="supervisoryFilter" class="form-select form-select-sm filter-select" data-column="6">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Technical Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-tools text-dark"></i> علاوة فنية
                </label>
                <select id="technicalFilter" class="form-select form-select-sm filter-select" data-column="7">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Reset Button -->
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" id="resetFilters" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-undo"></i> إعادة ضبط
                </button>
            </div>
        </div>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    عرض جميع العلاوات
                </small>
            </div>
        </div>
    </div>
</div>



<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع العلاوات</h6>
        <div class="d-flex align-items-center">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" id="searchInput" class="form-control form-control-sm"
                       placeholder="بحث في العلاوات...">
            </div>
            <span class="badge bg-info ms-2">{{ total_allowances }} سجل</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="allowancesTable">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <i class="fas fa-hashtag text-primary"></i>
                            الرقم
                        </th>
                        <th width="12%">
                            <i class="fas fa-id-card text-primary"></i>
                            الرقم الوزاري
                        </th>
                        <th width="20%">
                            <i class="fas fa-user text-primary"></i>
                            الاسم الكامل
                        </th>
                        <th width="10%">
                            <i class="fas fa-graduation-cap text-success"></i>
                            علاوة التعليم
                        </th>
                        <th width="10%">
                            <i class="fas fa-cogs text-info"></i>
                            التجيير
                        </th>
                        <th width="10%">
                            <i class="fas fa-car text-warning"></i>
                            التنقلات
                        </th>
                        <th width="12%">
                            <i class="fas fa-user-tie text-secondary"></i>
                            العلاوة الإشرافية
                        </th>
                        <th width="10%">
                            <i class="fas fa-tools text-dark"></i>
                            علاوة فنية
                        </th>
                        <th width="11%">
                            <i class="fas fa-cogs text-dark"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for allowance in allowances %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ allowance.ministry_number|default:'-' }}</td>
                        <td>{{ allowance.employee.full_name }}</td>
                        <td class="text-center">
                            {% if allowance.education_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.adjustment_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.transportation_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.supervisory_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.technical_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'ranks:employee_allowance_update' allowance.pk %}"
                                   class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'ranks:employee_allowance_delete' allowance.pk %}"
                                   class="btn btn-danger btn-sm" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف علاوات هذا الموظف؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="alert alert-info mb-0">
                                لا توجد علاوات. <a href="{% url 'ranks:employee_allowance_create' %}">إضافة علاوات جديدة</a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Animate dashboard cards on page load
    animateDashboardCards();
    
    // Get total count for calculations
    var totalCount = $('#allowancesTable tbody tr').length;
    var visibleCount = totalCount;

    // Search functionality
    $('#searchInput').on('keyup', function() {
        applyAllFilters();
    });

    // Filter functionality
    $('.filter-select').on('change', function() {
        applyAllFilters();
    });

    // Reset filters
    $('#resetFilters').on('click', function() {
        $('.filter-select').val('');
        $('#searchInput').val('');
        applyAllFilters();
    });

    // Apply all filters function
    function applyAllFilters() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var educationFilter = $('#educationFilter').val();
        var adjustmentFilter = $('#adjustmentFilter').val();
        var transportationFilter = $('#transportationFilter').val();
        var supervisoryFilter = $('#supervisoryFilter').val();
        var technicalFilter = $('#technicalFilter').val();

        visibleCount = 0;

        $('#allowancesTable tbody tr').each(function() {
            var $row = $(this);
            var show = true;

            // Skip empty rows
            if ($row.find('td').length <= 1) {
                return;
            }

            // Search filter (ministry number and name)
            if (searchValue) {
                var ministryNumber = $row.find('td:eq(1)').text().toLowerCase();
                var employeeName = $row.find('td:eq(2)').text().toLowerCase();

                if (ministryNumber.indexOf(searchValue) === -1 &&
                    employeeName.indexOf(searchValue) === -1) {
                    show = false;
                }
            }

            // Education allowance filter
            if (educationFilter && show) {
                var educationText = $row.find('td:eq(3) .badge').text().trim();
                if (educationText !== educationFilter) {
                    show = false;
                }
            }

            // Adjustment allowance filter
            if (adjustmentFilter && show) {
                var adjustmentText = $row.find('td:eq(4) .badge').text().trim();
                if (adjustmentText !== adjustmentFilter) {
                    show = false;
                }
            }

            // Transportation allowance filter
            if (transportationFilter && show) {
                var transportationText = $row.find('td:eq(5) .badge').text().trim();
                if (transportationText !== transportationFilter) {
                    show = false;
                }
            }

            // Supervisory allowance filter
            if (supervisoryFilter && show) {
                var supervisoryText = $row.find('td:eq(6) .badge').text().trim();
                if (supervisoryText !== supervisoryFilter) {
                    show = false;
                }
            }

            // Technical allowance filter
            if (technicalFilter && show) {
                var technicalText = $row.find('td:eq(7) .badge').text().trim();
                if (technicalText !== technicalFilter) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('.badge.bg-info').text(visibleCount + ' سجل');

        // Update filter info
        updateFilterInfo(searchValue, educationFilter, adjustmentFilter, transportationFilter, supervisoryFilter, technicalFilter, visibleCount);
    }

    // Update filter info function
    function updateFilterInfo(search, education, adjustment, transportation, supervisory, technical, count) {
        var filters = [];

        if (search) filters.push('البحث: "' + search + '"');
        if (education) filters.push('علاوة التعليم: ' + education);
        if (adjustment) filters.push('التجيير: ' + adjustment);
        if (transportation) filters.push('التنقلات: ' + transportation);
        if (supervisory) filters.push('العلاوة الإشرافية: ' + supervisory);
        if (technical) filters.push('علاوة فنية: ' + technical);

        var infoText = '';
        if (filters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + filters.join(' | ') + ' - النتائج: ' + count;
        } else {
            infoText = '<i class="fas fa-info-circle"></i> عرض جميع العلاوات - المجموع: ' + count;
        }

        $('#filterInfo').html(infoText);
    }

    // Initial filter application
    applyAllFilters();

    // Dashboard cards animation function
    function animateDashboardCards() {
        $('.dashboard-card').each(function(index) {
            var $card = $(this);
            var $number = $card.find('.h3');
            var finalNumber = parseInt($number.text()) || 0;
            
            // Reset number to 0 for animation
            $number.text('0');
            
            // Animate card appearance
            setTimeout(function() {
                $card.addClass('animate__animated animate__fadeInUp');
                
                // Animate number counting
                $({ countNum: 0 }).animate({
                    countNum: finalNumber
                }, {
                    duration: 1500,
                    easing: 'swing',
                    step: function() {
                        $number.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $number.text(finalNumber);
                    }
                });
            }, index * 200); // Stagger animation
        });
    }

    // Add hover effects to dashboard cards
    $('.dashboard-card').hover(
        function() {
            $(this).find('.rounded-circle').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).find('.rounded-circle').removeClass('animate__animated animate__pulse');
        }
    );

    // Add click effect to dashboard cards
    $('.dashboard-card').on('click', function() {
        var $this = $(this);
        $this.addClass('animate__animated animate__heartBeat');
        setTimeout(function() {
            $this.removeClass('animate__animated animate__heartBeat');
        }, 1000);
    });
});
</script>

<!-- Add Animate.css for enhanced animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
{% endblock %}
