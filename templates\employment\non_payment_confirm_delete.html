{% extends 'base.html' %}
{% load static %}

{% block title %}حذف سجل عدم الصرف - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 2px solid #dc3545;
    }

    .delete-card .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 20px;
    }

    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .info-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #dc3545;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        display: block;
    }

    .info-value {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }

    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-trash text-danger me-2"></i>
                حذف سجل عدم الصرف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:non_payment_list' %}">عدم الصرف</a></li>
                    <li class="breadcrumb-item active">حذف سجل</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="delete-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف سجل عدم الصرف
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle warning-icon"></i>
                        <h4 class="text-danger">تحذير!</h4>
                        <p class="text-muted">هل أنت متأكد من رغبتك في حذف سجل عدم الصرف التالي؟</p>
                        <p class="text-danger"><strong>هذا الإجراء لا يمكن التراجع عنه!</strong></p>
                    </div>

                    <!-- Record Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-user me-2"></i>اسم الموظف:
                                </label>
                                <span class="info-value">{{ non_payment.employee.full_name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-id-card me-2"></i>الرقم الوزاري:
                                </label>
                                <span class="info-value">{{ non_payment.ministry_number }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-building me-2"></i>القسم:
                                </label>
                                <span class="info-value">{{ non_payment.department_name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-calendar me-2"></i>التاريخ:
                                </label>
                                <span class="info-value">{{ non_payment.date|date:"Y-m-d" }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-calendar-times me-2"></i>عدد الأيام:
                                </label>
                                <span class="info-value badge bg-warning text-dark">{{ non_payment.days_count }} يوم</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-exchange-alt me-2"></i>حالة الترحيل:
                                </label>
                                {% if non_payment.is_transferred %}
                                    <span class="info-value badge bg-success">تم الترحيل</span>
                                {% else %}
                                    <span class="info-value badge bg-warning text-dark">معلق</span>
                                {% endif %}
                            </div>
                        </div>
                        {% if non_payment.notes %}
                        <div class="col-12">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="fas fa-sticky-note me-2"></i>الملاحظات:
                                </label>
                                <span class="info-value">{{ non_payment.notes }}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:non_payment_list' %}" class="btn btn-cancel text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-delete text-white">
                                <i class="fas fa-trash me-2"></i>تأكيد الحذف
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog for delete button
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من رغبتك في حذف هذا السجل؟ لا يمكن التراجع عن هذا الإجراء!')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
