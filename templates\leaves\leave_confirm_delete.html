{% extends 'base.html' %}
{% load static %}

{% block title %}حذف إجازة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف إجازة</h2>
    <a href="{% url 'leaves:leave_detail' leave.pk %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">تأكيد الحذف</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تحذير:</strong> هل أنت متأكد من حذف هذه الإجازة؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                معلومات الإجازة
            </div>
            <div class="card-body">
                <p><strong>الموظف:</strong> {{ leave.employee.full_name }}</p>
                <p><strong>نوع الإجازة:</strong> {{ leave.leave_type.get_name_display }}</p>
                <p><strong>من تاريخ:</strong> {{ leave.start_date }}</p>
                <p><strong>إلى تاريخ:</strong> {{ leave.end_date }}</p>
                <p><strong>عدد الأيام:</strong> {{ leave.days_count }}</p>
                <p><strong>الحالة:</strong> 
                    {% if leave.status == 'pending' %}
                        <span class="badge bg-warning">قيد الانتظار</span>
                    {% elif leave.status == 'approved' %}
                        <span class="badge bg-success">موافق عليها</span>
                    {% elif leave.status == 'rejected' %}
                        <span class="badge bg-danger">مرفوضة</span>
                    {% endif %}
                </p>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'leaves:leave_detail' leave.pk %}" class="btn btn-secondary me-md-2">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
