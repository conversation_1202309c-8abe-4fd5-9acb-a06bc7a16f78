from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from django.http import JsonResponse
from leaves.models import LeaveType, LeaveBalance, Leave
from employees.models import Employee
from employment.models import Department, Employment
import datetime

@login_required
def leave_list(request):
    # Redirect to the main leaves module
    return redirect('leaves:leave_list')

@login_required
def leave_balance_list(request):
    # Redirect to the main leaves module
    return redirect('leaves:leave_balance_list')

@login_required
def leave_reports(request):
    # Redirect to the main leaves module
    return redirect('leaves:leave_reports')

@login_required
def whatsapp_send(request):
    """View for sending WhatsApp messages with leave balances"""
    # Get current year and allow selection of other years
    current_year = timezone.now().year
    selected_year = request.GET.get('year', current_year)
    try:
        selected_year = int(selected_year)
    except (ValueError, TypeError):
        selected_year = current_year

    # Get all years with leave balances
    years = LeaveBalance.objects.values_list('year', flat=True).distinct().order_by('-year')

    # Get departments that are in the directorate
    directorate_departments = Department.objects.filter(workplace='directorate').order_by('name')

    # Get selected department (if any)
    selected_department_id = request.GET.get('department', '')
    selected_department = None
    if selected_department_id:
        try:
            selected_department = Department.objects.get(id=selected_department_id)
        except (Department.DoesNotExist, ValueError):
            selected_department = None

    # Get employees with leave balances for the selected year
    employees_query = Employee.objects.filter(
        leave_balances__year=selected_year
    ).distinct()

    # Filter by department if selected
    if selected_department:
        # Get employees in the selected department
        employees_in_dept = Employment.objects.filter(
            department=selected_department,
            is_current=True
        ).values_list('employee_id', flat=True)
        employees_query = employees_query.filter(id__in=employees_in_dept)

    # Search functionality first (before ordering)
    search_query = request.GET.get('search', '')
    if search_query:
        employees_query = employees_query.filter(
            Q(full_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query)
        )

    # Order by ministry number (numeric sorting)
    from employees.utils import order_employees_by_ministry_number
    employees_with_balances = order_employees_by_ministry_number(employees_query)

    return render(request, 'directorate_leaves/whatsapp_send.html', {
        'employees': employees_with_balances,
        'selected_year': selected_year,
        'years': years,
        'search_query': search_query,
        'departments': directorate_departments,
        'selected_department': selected_department,
    })

@login_required
def get_leave_balance_whatsapp(request, employee_id, year=None):
    """AJAX endpoint to get leave balance and last leave date for WhatsApp message"""
    try:
        # Get employee
        employee = Employee.objects.get(pk=employee_id)

        # Get year (default to current year if not provided)
        if year is None:
            year = timezone.now().year
        else:
            year = int(year)

        # Get all leave balances for this employee for the specified year
        balances = LeaveBalance.objects.filter(
            employee_id=employee_id,
            year=year
        )

        # Get the latest leave for this employee
        latest_leave = Leave.objects.filter(
            employee_id=employee_id,
            status='approved'
        ).order_by('-end_date').first()

        # Format phone number (remove any non-digit characters and ensure it starts with 962)
        phone = employee.phone_number
        if phone:
            # Remove any non-digit characters
            phone = ''.join(filter(str.isdigit, phone))
            # Ensure it starts with 962 (Jordan country code)
            if phone.startswith('0'):
                phone = '962' + phone[1:]
            elif not phone.startswith('962'):
                phone = '962' + phone
        else:
            return JsonResponse({'success': False, 'error': 'لا يوجد رقم هاتف لهذا الموظف'})

        # Construct message
        message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {year}:\n"

        for balance in balances:
            message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

        if latest_leave:
            message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
        else:
            message += "\nلا يوجد إجازات سابقة\n"

        message += "\nمع تحيات قسم شؤون الموظفين"

        # Create WhatsApp URL
        whatsapp_url = f"https://wa.me/{phone}?text={message}"

        return JsonResponse({
            'success': True,
            'whatsapp_url': whatsapp_url,
            'phone': phone,
            'message': message
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_department_employees_whatsapp(request):
    """AJAX endpoint to get all employees in a department for WhatsApp messages"""
    try:
        # Get parameters
        department_id = request.GET.get('department_id')
        year = request.GET.get('year')

        if not department_id:
            return JsonResponse({'success': False, 'error': 'لم يتم تحديد القسم'})

        # Get department
        department = Department.objects.get(pk=department_id)

        # Get year (default to current year if not provided)
        if not year:
            year = timezone.now().year
        else:
            year = int(year)

        # Get employees in the department
        employees_in_dept = Employment.objects.filter(
            department=department,
            is_current=True
        ).values_list('employee_id', flat=True)

        # Get employees with leave balances
        employees = Employee.objects.filter(
            id__in=employees_in_dept,
            leave_balances__year=year
        ).distinct()

        # Prepare employee data
        employee_data = []
        for employee in employees:
            # Get phone number
            phone = employee.phone_number
            if phone:
                # Remove any non-digit characters
                phone = ''.join(filter(str.isdigit, phone))
                # Ensure it starts with 962 (Jordan country code)
                if phone.startswith('0'):
                    phone = '962' + phone[1:]
                elif not phone.startswith('962'):
                    phone = '962' + phone

                # Get leave balances
                balances = LeaveBalance.objects.filter(
                    employee_id=employee.id,
                    year=year
                )

                # Get latest leave
                latest_leave = Leave.objects.filter(
                    employee_id=employee.id,
                    status='approved'
                ).order_by('-end_date').first()

                # Construct message
                message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {year}:\n"

                for balance in balances:
                    message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

                if latest_leave:
                    message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
                else:
                    message += "\nلا يوجد إجازات سابقة\n"

                message += "\nمع تحيات قسم شؤون الموظفين"

                # Create WhatsApp URL
                whatsapp_url = f"https://wa.me/{phone}?text={message}"

                employee_data.append({
                    'id': employee.id,
                    'name': employee.full_name,
                    'ministry_number': employee.ministry_number,
                    'phone': phone,
                    'whatsapp_url': whatsapp_url,
                    'message': message
                })

        return JsonResponse({
            'success': True,
            'department_name': department.name,
            'year': year,
            'employees': employee_data
        })
    except Department.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على القسم'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_all_directorate_departments_whatsapp(request):
    """AJAX endpoint to get all employees in all directorate departments for WhatsApp messages"""
    try:
        # Get year parameter
        year = request.GET.get('year')

        # Get year (default to current year if not provided)
        if not year:
            year = timezone.now().year
        else:
            year = int(year)

        # Get all departments in the directorate
        directorate_departments = Department.objects.filter(workplace='directorate').order_by('name')

        # Prepare department data
        departments_data = []
        total_employees = 0

        for department in directorate_departments:
            # Get employees in the department
            employees_in_dept = Employment.objects.filter(
                department=department,
                is_current=True
            ).values_list('employee_id', flat=True)

            # Get employees with leave balances
            employees = Employee.objects.filter(
                id__in=employees_in_dept,
                leave_balances__year=year
            ).distinct()

            # Prepare employee data
            employee_data = []
            for employee in employees:
                # Get phone number
                phone = employee.phone_number
                if phone:
                    # Remove any non-digit characters
                    phone = ''.join(filter(str.isdigit, phone))
                    # Ensure it starts with 962 (Jordan country code)
                    if phone.startswith('0'):
                        phone = '962' + phone[1:]
                    elif not phone.startswith('962'):
                        phone = '962' + phone

                    # Get leave balances
                    balances = LeaveBalance.objects.filter(
                        employee_id=employee.id,
                        year=year
                    )

                    # Get latest leave
                    latest_leave = Leave.objects.filter(
                        employee_id=employee.id,
                        status='approved'
                    ).order_by('-end_date').first()

                    # Construct message
                    message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {year}:\n"

                    for balance in balances:
                        message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

                    if latest_leave:
                        message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
                    else:
                        message += "\nلا يوجد إجازات سابقة\n"

                    message += "\nمع تحيات قسم شؤون الموظفين"

                    # Create WhatsApp URL
                    whatsapp_url = f"https://wa.me/{phone}?text={message}"

                    employee_data.append({
                        'id': employee.id,
                        'name': employee.full_name,
                        'ministry_number': employee.ministry_number,
                        'phone': phone,
                        'whatsapp_url': whatsapp_url,
                        'message': message
                    })

            # Add department data if it has employees
            if employee_data:
                departments_data.append({
                    'id': department.id,
                    'name': department.name,
                    'employees': employee_data,
                    'employee_count': len(employee_data)
                })
                total_employees += len(employee_data)

        return JsonResponse({
            'success': True,
            'year': year,
            'departments': departments_data,
            'total_departments': len(departments_data),
            'total_employees': total_employees
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
