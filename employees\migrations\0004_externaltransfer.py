# Generated by Django 5.2 on 2025-06-17 06:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0003_retiredemployee'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateField(help_text='Date when the employee was transferred', verbose_name='Transfer Date')),
                ('destination_directorate', models.CharField(help_text='The directorate the employee was transferred to', max_length=200, verbose_name='Destination Directorate')),
                ('transfer_reason', models.CharField(default='نقل خارجي', help_text='Reason for the transfer', max_length=200, verbose_name='Transfer Reason')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the transfer', null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='external_transfer', to='employees.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'External Transfer',
                'verbose_name_plural': 'External Transfers',
                'ordering': ['-transfer_date'],
            },
        ),
    ]
