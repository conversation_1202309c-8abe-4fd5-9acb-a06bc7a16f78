{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل حركة الملف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل حركة الملف</h1>
        <div>
            {% if file_movement.status == 'out' %}
            <a href="{% url 'file_management:file_return' file_movement.pk %}" class="btn btn-success">
                <i class="fas fa-file-import"></i> تسجيل عودة الملف
            </a>
            {% endif %}
            <a href="{% url 'file_management:file_movement_update' file_movement.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات حركة الملف</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">الموظف</th>
                            <td>
                                <a href="{% url 'employees:employee_detail' file_movement.employee.pk %}">
                                    {{ file_movement.employee.full_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th class="bg-light">الرقم الوزاري</th>
                            <td>{{ file_movement.employee.ministry_number }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ خروج الملف</th>
                            <td>{{ file_movement.checkout_date|date:"Y-m-d" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ عودة الملف</th>
                            <td>{% if file_movement.return_date %}{{ file_movement.return_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">الحالة</th>
                            <td>
                                {% if file_movement.status == 'out' %}
                                <span class="badge bg-warning text-dark">{{ file_movement.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-success">{{ file_movement.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ الإنشاء</th>
                            <td>{{ file_movement.created_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">آخر تحديث</th>
                            <td>{{ file_movement.updated_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            {% if file_movement.action_taken %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">الإجراء المتخذ</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ file_movement.action_taken|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if file_movement.notes %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">ملاحظات</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ file_movement.notes|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
