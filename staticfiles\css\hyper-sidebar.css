/* Hyper Sidebar CSS - Dark Elegant Theme */

:root {
    /* Main theme colors - Dark Elegant theme */
    --hyper-primary: #6366f1;
    --hyper-primary-dark: #4f46e5;
    --hyper-primary-light: #a5b4fc;
    --hyper-secondary: #6c757d;
    --hyper-success: #10b981;
    --hyper-info: #0ea5e9;
    --hyper-warning: #f59e0b;
    --hyper-danger: #ef4444;
    --hyper-dark: #000000;
    --hyper-darker: #000000;
    --hyper-light: #f8f9fa;
    --hyper-bg: #fafbfe;
    --hyper-border: #333333;
    --hyper-text: #ffffff;
    --hyper-text-dark: #ffffff;
    --hyper-text-muted: #cccccc;
    --hyper-text-hover: #ffffff;
    --hyper-sidebar-bg: #000000;
    --hyper-sidebar-hover: #333333;
    --hyper-sidebar-active: #6366f1;

    /* Sidebar dimensions */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --sidebar-icon-size: 18px;
    --sidebar-icon-width: 24px;
    --sidebar-transition: all 0.3s ease;

    /* Dark mode colors */
    --dark-bg: #0f172a;
    --dark-card-bg: #1e293b;
    --dark-border: #334155;
    --dark-text: #e2e8f0;
    --dark-text-muted: #94a3b8;
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background-color: #000000 !important;
    color: #ffffff !important;
    z-index: 1030;
    transition: var(--sidebar-transition);
    box-shadow: 0 0 35px 0 rgba(0, 0, 0, 0.25);
    padding-top: 70px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Sidebar header */
.sidebar-header {
    padding: 0.75rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid #333333;
}

.sidebar-brand {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 0.5rem 0;
}

.sidebar-brand:hover {
    color: #ffffff;
    text-decoration: none;
}

.sidebar-brand i {
    font-size: 1.5rem;
    margin-left: 0.75rem;
    color: #6366f1;
}

/* Sidebar menu */
.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-item {
    position: relative;
    margin: 0.25rem 0;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.625rem 1.5rem;
    color: #ffffff !important;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--sidebar-transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    border-radius: 0.25rem;
    margin: 0 0.5rem;
}

.sidebar-link:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #333333;
}

.sidebar-link.active {
    color: #ffffff;
    background-color: #6366f1;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

.sidebar-link i {
    font-size: var(--sidebar-icon-size);
    width: var(--sidebar-icon-width);
    margin-left: 0.75rem;
    text-align: center;
    transition: var(--sidebar-transition);
    color: #6366f1;
}

.sidebar-link.active i {
    color: #ffffff;
}

.sidebar-link span {
    transition: var(--sidebar-transition);
    white-space: nowrap;
}

.sidebar-link .fa-chevron-down {
    margin-right: auto;
    margin-left: 0;
    font-size: 0.75rem;
    transition: transform 0.3s ease;
    color: var(--hyper-text-muted);
}

.sidebar-link[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

/* Sidebar submenu */
.sidebar-submenu {
    padding-right: 2.5rem;
}

.collapse:not(.show) {
    display: none;
}

.collapse.show {
    display: block;
}

.sidebar-submenu .sidebar-item {
    margin: 0;
}

.sidebar-submenu .sidebar-link {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    opacity: 0.8;
}

.sidebar-submenu .sidebar-link:hover {
    opacity: 1;
}

.sidebar-submenu .sidebar-link.active {
    opacity: 1;
}

/* Collapsed sidebar styles */
body.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

body.sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}

body.sidebar-collapsed .sidebar-brand span,
body.sidebar-collapsed .sidebar-link span,
body.sidebar-collapsed .sidebar-link .fa-chevron-down {
    display: none;
}

body.sidebar-collapsed .sidebar-link {
    justify-content: center;
    padding: 0.75rem;
}

body.sidebar-collapsed .sidebar-link i {
    margin: 0;
    font-size: 1.25rem;
}

body.sidebar-collapsed .sidebar-submenu {
    display: none;
}

body.sidebar-collapsed .sidebar-header {
    padding: 0.75rem 0;
}

/* Sidebar toggle button */
.sidebar-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--hyper-primary);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1040;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: var(--sidebar-transition);
}

.sidebar-toggle-btn:hover {
    background-color: var(--hyper-primary-dark);
    transform: scale(1.1);
}

body.sidebar-collapsed .sidebar-toggle-btn {
    right: calc(var(--sidebar-collapsed-width) - 20px);
}

/* Main content adjustment */
.main-content {
    margin-right: var(--sidebar-width);
    transition: var(--sidebar-transition);
    width: calc(100% - var(--sidebar-width));
}

/* Dark mode transition */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Add transitions to elements that change in dark mode */
.card, .modal-content, .dropdown-menu, .navbar, .sidebar, .footer,
.form-control, .form-select, .btn, .alert, .badge, .table, a {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark mode styles */
body.dark-mode {
    background-color: #121212 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .card,
body.dark-mode .modal-content,
body.dark-mode .dropdown-menu {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
}

body.dark-mode .card-header,
body.dark-mode .card-footer,
body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    background-color: #252525 !important;
    border-color: #333333 !important;
}

body.dark-mode .navbar {
    background-color: #000000 !important;
}

body.dark-mode .sidebar {
    background-color: #000000 !important;
}

body.dark-mode .footer {
    background-color: #000000 !important;
    border-color: #333333 !important;
}

body.dark-mode .text-muted {
    color: #aaaaaa !important;
}

body.dark-mode .table {
    color: #e0e0e0 !important;
}

body.dark-mode .table-bordered th,
body.dark-mode .table-bordered td {
    border-color: #333333 !important;
}

body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #252525 !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background-color: #2c2c2c !important;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25) !important;
}

body.dark-mode .btn-light {
    background-color: #333333 !important;
    border-color: #444444 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .btn-light:hover {
    background-color: #444444 !important;
    border-color: #555555 !important;
    color: #ffffff !important;
}

body.dark-mode .dropdown-item {
    color: #e0e0e0 !important;
}

body.dark-mode .dropdown-item:hover {
    background-color: #333333 !important;
    color: #ffffff !important;
}

body.dark-mode .dropdown-divider {
    border-color: #333333 !important;
}

body.dark-mode .page-link {
    background-color: #252525 !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .page-link:hover {
    background-color: #333333 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

body.dark-mode .page-item.active .page-link {
    background-color: #6366f1 !important;
    border-color: #6366f1 !important;
    color: #ffffff !important;
}

/* Additional dark mode styles */
body.dark-mode .alert {
    background-color: #252525 !important;
    color: #e0e0e0 !important;
    border-color: #333333 !important;
}

body.dark-mode .alert-success {
    background-color: rgba(16, 185, 129, 0.2) !important;
    color: #10b981 !important;
}

body.dark-mode .alert-danger {
    background-color: rgba(239, 68, 68, 0.2) !important;
    color: #ef4444 !important;
}

body.dark-mode .alert-warning {
    background-color: rgba(245, 158, 11, 0.2) !important;
    color: #f59e0b !important;
}

body.dark-mode .alert-info {
    background-color: rgba(14, 165, 233, 0.2) !important;
    color: #0ea5e9 !important;
}

body.dark-mode a {
    color: #6366f1 !important;
}

body.dark-mode a:hover {
    color: #818cf8 !important;
}

body.dark-mode .badge {
    background-color: #333333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .badge-primary {
    background-color: #6366f1 !important;
    color: #ffffff !important;
}

body.dark-mode .badge-success {
    background-color: #10b981 !important;
    color: #ffffff !important;
}

body.dark-mode .badge-danger {
    background-color: #ef4444 !important;
    color: #ffffff !important;
}

body.dark-mode .badge-warning {
    background-color: #f59e0b !important;
    color: #ffffff !important;
}

body.dark-mode .badge-info {
    background-color: #0ea5e9 !important;
    color: #ffffff !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(100%);
        width: var(--sidebar-width);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }

    body.sidebar-collapsed .main-content {
        margin-right: 0;
        width: 100%;
    }

    body.sidebar-collapsed .sidebar {
        transform: translateX(100%);
    }

    body.sidebar-collapsed .sidebar.show {
        transform: translateX(0);
        width: var(--sidebar-width);
    }

    body.sidebar-collapsed .sidebar-brand span,
    body.sidebar-collapsed .sidebar-link span,
    body.sidebar-collapsed .sidebar-link .fa-chevron-down {
        display: inline-block;
    }

    body.sidebar-collapsed .sidebar-link {
        justify-content: flex-start;
        padding: 0.625rem 1.5rem;
    }

    body.sidebar-collapsed .sidebar-link i {
        margin-left: 0.75rem;
        font-size: var(--sidebar-icon-size);
    }

    .sidebar-toggle-btn {
        display: none;
    }
}
