{% extends 'base.html' %}
{% load static %}

{% block title %}حذف علاوات الموظف - الرتب والعلاوات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف علاوات الموظف</h2>
    <a href="{% url 'ranks:employee_allowance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">
            <i class="fas fa-exclamation-triangle"></i> تأكيد حذف علاوات الموظف
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> تحذير</h5>
            <p class="mb-0">هل أنت متأكد من حذف علاوات هذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        </div>

        <!-- Employee Information -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-user text-primary"></i> معلومات الموظف
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>الرقم الوزاري:</strong>
                        <span>{{ allowance.ministry_number|default:'-' }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>اسم الموظف:</strong>
                        <span>{{ allowance.employee.full_name }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Allowances Status -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-money-bill-wave text-primary"></i> العلاوات الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-graduation-cap text-success"></i> علاوة التعليم:</span>
                            {% if allowance.education_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-cogs text-info"></i> التجيير:</span>
                            {% if allowance.adjustment_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-car text-warning"></i> التنقلات:</span>
                            {% if allowance.transportation_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-user-tie text-secondary"></i> العلاوة الإشرافية:</span>
                            {% if allowance.supervisory_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-tools text-dark"></i> علاوة فنية:</span>
                            {% if allowance.technical_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="text-center">
                <button type="submit" class="btn btn-danger btn-lg me-3">
                    <i class="fas fa-trash"></i> نعم، احذف العلاوات
                </button>
                <a href="{% url 'ranks:employee_allowance_list' %}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
