from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from notifications.models import Notification

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample notifications for testing'

    def handle(self, *args, **options):
        # Get the first user (usually admin)
        try:
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting user: {e}'))
            return

        # Create sample notifications
        notifications_data = [
            {
                'title': 'مرحباً بك في النظام',
                'message': 'تم تسجيل دخولك بنجاح إلى نظام إدارة الموارد البشرية',
                'notification_type': 'success',
                'icon': 'fa-check-circle',
                'user': user,
            },
            {
                'title': 'موظف جديد',
                'message': 'تم إضافة موظف جديد إلى النظام بنجاح',
                'notification_type': 'info',
                'icon': 'fa-user-plus',
                'user': user,
            },
            {
                'title': 'طلب إجازة جديد',
                'message': 'يوجد طلب إجازة جديد يحتاج إلى مراجعة',
                'notification_type': 'warning',
                'icon': 'fa-calendar',
                'user': user,
            },
            {
                'title': 'تحديث النظام',
                'message': 'تم تحديث النظام بنجاح إلى الإصدار الجديد',
                'notification_type': 'success',
                'icon': 'fa-cog',
                'user': user,
            },
            {
                'title': 'إشعار عام',
                'message': 'هذا إشعار عام لجميع المستخدمين في النظام',
                'notification_type': 'info',
                'icon': 'fa-bell',
                'is_global': True,
            },
        ]

        created_count = 0
        for notification_data in notifications_data:
            try:
                notification = Notification.objects.create(**notification_data)
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created notification: {notification.title}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating notification: {e}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} sample notifications')
        )
