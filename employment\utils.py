"""
Utility functions for employment app
"""
from django.db.models import Q
from django.core.cache import cache
from .models import ServicePurchase
from employees.models import Employee, RetiredEmployee, ExternalTransfer


def get_available_employees():
    """
    Get employees that are available for service purchase
    (not retired, not transferred, not already in service purchase)
    """
    cache_key = 'available_employees_for_service_purchase'
    available_employees = cache.get(cache_key)
    
    if available_employees is None:
        # Get excluded employee IDs
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        service_purchase_ids = ServicePurchase.objects.filter(is_active=True).values_list('employee_id', flat=True)
        
        # Combine all exclusions
        excluded_ids = list(retired_ids) + list(transferred_ids) + list(service_purchase_ids)
        
        # Get available employees
        available_employees = Employee.objects.exclude(id__in=excluded_ids).order_by('full_name')
        
        # Cache for 5 minutes
        cache.set(cache_key, available_employees, 300)
    
    return available_employees


def get_service_purchase_employees():
    """
    Get employees that are currently in service purchase
    """
    cache_key = 'service_purchase_employees'
    service_purchase_employees = cache.get(cache_key)
    
    if service_purchase_employees is None:
        service_purchase_employees = ServicePurchase.objects.filter(is_active=True).select_related('employee')
        
        # Cache for 5 minutes
        cache.set(cache_key, service_purchase_employees, 300)
    
    return service_purchase_employees


def clear_employee_cache():
    """
    Clear employee-related cache when data changes
    """
    cache.delete('available_employees_for_service_purchase')
    cache.delete('service_purchase_employees')


def search_service_purchases(queryset, search_query):
    """
    Apply search filters to service purchase queryset
    """
    if search_query:
        return queryset.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__national_id__icontains=search_query) |
            Q(notes__icontains=search_query)
        )
    return queryset


def filter_service_purchases(queryset, filters):
    """
    Apply filters to service purchase queryset
    """
    if filters.get('specialization'):
        queryset = queryset.filter(employee__specialization=filters['specialization'])
    
    if filters.get('school'):
        queryset = queryset.filter(target_school=filters['school'])
    
    return queryset


def calculate_service_statistics(service_purchases):
    """
    Calculate statistics for service purchases
    """
    if not service_purchases:
        return {
            'total_count': 0,
        }
    
    total_count = len(service_purchases)
    
    return {
        'total_count': total_count,
    }


def validate_service_purchase_data(data):
    """
    Validate service purchase data
    """
    errors = []
    
    # Validate employee
    if not data.get('employee'):
        errors.append('الرجاء اختيار الموظف')
    
    # Validate target school
    if not data.get('target_school'):
        errors.append('الرجاء اختيار المدرسة')
    
    return errors


# Service period formatting function removed as these fields no longer exist