#!/usr/bin/env python3
"""
📊 نظام تصدير PDF مبسط للموظفين
Simple PDF Export System - HR System
"""

from django.http import HttpResponse
from django.template.loader import render_to_string
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.contrib import messages
from django.utils import timezone
import pytz
from .models import Employee, RetiredEmployee, ExternalTransfer
from .import_export_simple import get_employee_comprehensive_data

def get_jordan_time():
    """الحصول على الوقت الحالي بتوقيت الأردن"""
    try:
        jordan_tz = pytz.timezone('Asia/Amman')
        jordan_time = timezone.now().astimezone(jordan_tz)
        return jordan_time.strftime('%Y-%m-%d %H:%M')
    except:
        # في حالة عدم توفر pytz، استخدم الوقت المحلي
        return timezone.localtime().strftime('%Y-%m-%d %H:%M')

@login_required
def export_employees_pdf_simple(request):
    """تصدير بيانات الموظفين إلى PDF بطريقة مبسطة (HTML to PDF)"""
    
    # الحصول على الموظفين
    include_all = request.GET.get('include_all', 'false') == 'true'
    active_only = request.GET.get('active_only', 'false') == 'true'
    
    if include_all:
        employees = Employee.objects.all()
        title = 'تقرير شامل لجميع الموظفين'
    elif active_only:
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        excluded_ids = list(retired_ids) + list(transferred_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)
        title = 'تقرير شامل للموظفين النشطين'
    else:
        employees = Employee.objects.all()
        title = 'تقرير الموظفين'
    
    # ترتيب الموظفين
    try:
        from .utils import order_employees_by_ministry_number
        employees = order_employees_by_ministry_number(employees)
    except ImportError:
        employees = employees.order_by('ministry_number')
    
    # إعداد البيانات للعرض
    employees_data = []
    for employee in employees:
        # إنشاء كائن بسيط للعرض في القالب
        employee_obj = {
            'ministry_number': employee.ministry_number or '-',
            'full_name': employee.full_name or '-',
            'gender': 'ذكر' if employee.gender == 'male' else 'أنثى' if employee.gender == 'female' else '-',
            'qualification': employee.qualification or '-',
            'post_graduate_diploma': employee.post_graduate_diploma or '-',
            'masters_degree': employee.masters_degree or '-',
            'phd_degree': employee.phd_degree or '-',
            'specialization': employee.specialization or '-',
            'school': employee.school or '-',
            'current_position': employee.get_latest_position() if hasattr(employee, 'get_latest_position') else '-',
            'employment_status': '-',
            'employee_status': 'نشط',
            'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '-',
            'phone_number': employee.phone_number or '-',
        }
        
        # تحديد حالة الموظف
        try:
            if hasattr(employee, 'retirement'):
                employee_obj['employee_status'] = 'متقاعد'
            elif hasattr(employee, 'external_transfer'):
                employee_obj['employee_status'] = 'منقول خارجياً'
        except:
            pass
        
        # الحصول على نوع التوظيف
        try:
            from employment.models import Employment
            current_employment = Employment.objects.filter(employee=employee, is_current=True).first()
            if current_employment and current_employment.status:
                employee_obj['employment_status'] = current_employment.status.get_name_display()
        except:
            pass
        
        employees_data.append(employee_obj)
    
    # إنشاء HTML للطباعة
    context = {
        'title': title,
        'employees_data': employees_data,
        'total_count': len(employees_data),
        'report_date': get_jordan_time(),
    }
    
    html_content = render_to_string('employees/pdf_report_template.html', context)
    
    # إرجاع HTML كاستجابة للعرض
    response = HttpResponse(html_content, content_type='text/html; charset=utf-8')
    response['Content-Disposition'] = 'inline; filename="employees_report.html"'
    
    return response

@login_required
def export_employees_pdf_download(request):
    """تحميل بيانات الموظفين كملف HTML للطباعة كـ PDF"""
    
    # الحصول على الموظفين
    include_all = request.GET.get('include_all', 'false') == 'true'
    active_only = request.GET.get('active_only', 'false') == 'true'
    
    if include_all:
        employees = Employee.objects.all()
        title = 'تقرير شامل لجميع الموظفين'
        filename = 'تقرير_جميع_الموظفين.html'
    elif active_only:
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        excluded_ids = list(retired_ids) + list(transferred_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)
        title = 'تقرير شامل للموظفين النشطين'
        filename = 'تقرير_الموظفين_النشطين.html'
    else:
        employees = Employee.objects.all()
        title = 'تقرير الموظفين'
        filename = 'تقرير_الموظفين.html'
    
    # ترتيب الموظفين
    try:
        from .utils import order_employees_by_ministry_number
        employees = order_employees_by_ministry_number(employees)
    except ImportError:
        employees = employees.order_by('ministry_number')
    
    # إعداد البيانات للعرض
    employees_data = []
    for employee in employees:
        # إنشاء كائن بسيط للعرض في القالب
        employee_obj = {
            'ministry_number': employee.ministry_number or '-',
            'full_name': employee.full_name or '-',
            'gender': 'ذكر' if employee.gender == 'male' else 'أنثى' if employee.gender == 'female' else '-',
            'qualification': employee.qualification or '-',
            'post_graduate_diploma': employee.post_graduate_diploma or '-',
            'masters_degree': employee.masters_degree or '-',
            'phd_degree': employee.phd_degree or '-',
            'specialization': employee.specialization or '-',
            'school': employee.school or '-',
            'current_position': employee.get_latest_position() if hasattr(employee, 'get_latest_position') else '-',
            'employment_status': '-',
            'employee_status': 'نشط',
            'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '-',
            'phone_number': employee.phone_number or '-',
        }
        
        # تحديد حالة الموظف
        try:
            if hasattr(employee, 'retirement'):
                employee_obj['employee_status'] = 'متقاعد'
            elif hasattr(employee, 'external_transfer'):
                employee_obj['employee_status'] = 'منقول خارجياً'
        except:
            pass
        
        # الحصول على نوع التوظيف
        try:
            from employment.models import Employment
            current_employment = Employment.objects.filter(employee=employee, is_current=True).first()
            if current_employment and current_employment.status:
                employee_obj['employment_status'] = current_employment.status.get_name_display()
        except:
            pass
        
        employees_data.append(employee_obj)
    
    # إنشاء HTML للطباعة
    context = {
        'title': title,
        'employees_data': employees_data,
        'total_count': len(employees_data),
        'report_date': get_jordan_time(),
        'for_download': True,  # علامة للتمييز بين العرض والتحميل
    }
    
    html_content = render_to_string('employees/pdf_download_template.html', context)
    
    # إرجاع HTML كملف للتحميل
    response = HttpResponse(html_content, content_type='text/html; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    return response