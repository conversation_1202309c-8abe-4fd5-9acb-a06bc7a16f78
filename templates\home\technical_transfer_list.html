{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة النقل الفني</h6>
                    <div>
                        <a href="{% url 'home:technical_transfer' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> إضافة نقل فني جديد
                        </a>
                        <a href="{% url 'home:print_transfer_letters' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة لطباعة كتب النقل الداخلي
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="technicalTransfersTable">
                            <thead>
                                <tr class="text-center">
                                    <th><i class="fas fa-hashtag me-1"></i> #</th>
                                    <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                                    <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                                    <th><i class="fas fa-building me-1"></i> القسم الحالي</th>
                                    <th><i class="fas fa-map-marker-alt me-1"></i> مركز العمل الجديد</th>
                                    <th><i class="fas fa-sticky-note me-1"></i> ملاحظات</th>
                                    <th><i class="fas fa-calendar-alt me-1"></i> تاريخ الإنشاء</th>
                                    <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in technical_transfers %}
                                <tr class="text-center">
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ transfer.ministry_number }}</td>
                                    <td>{{ transfer.employee_name }}</td>
                                    <td>{{ transfer.current_department }}</td>
                                    <td>{{ transfer.new_department }}</td>
                                    <td>{{ transfer.notes|default:"-" }}</td>
                                    <td>{{ transfer.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'home:technical_transfer_print' pk=transfer.id %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                                <i class="fas fa-print"></i> طباعة
                                            </a>
                                            <a href="{% url 'home:technical_transfer_delete' pk=transfer.id %}" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات نقل فني</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        try {
            $('#technicalTransfersTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json"
                },
                "order": [[6, "desc"]], // Sort by creation date (desc)
                "paging": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                "info": true
            });
            console.log("DataTable initialized");
        } catch (error) {
            console.error("Error initializing DataTable:", error);
        }
    });
</script>
{% endblock %}
