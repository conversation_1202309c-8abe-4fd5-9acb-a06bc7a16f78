# HR Management System - PowerShell Installation Script
# نظام شؤون الموظفين - سكريبت تثبيت PowerShell

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "نظام شؤون الموظفين - تثبيت المتطلبات"

# Colors for output
$InfoColor = "Cyan"
$SuccessColor = "Green"
$WarningColor = "Yellow"
$ErrorColor = "Red"

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $InfoColor
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $SuccessColor
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $WarningColor
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $ErrorColor
}

# Header
Write-Host "========================================" -ForegroundColor White
Write-Host "نظام شؤون الموظفين - تثبيت المتطلبات" -ForegroundColor White
Write-Host "HR Management System - Install Requirements" -ForegroundColor White
Write-Host "========================================" -ForegroundColor White
Write-Host ""

Write-Info "بدء عملية تثبيت المكتبات المطلوبة..."
Write-Info "Starting installation of required packages..."
Write-Host ""

# Check execution policy
$executionPolicy = Get-ExecutionPolicy
if ($executionPolicy -eq "Restricted") {
    Write-Warning "سياسة التنفيذ مقيدة. جاري تغييرها..."
    Write-Warning "Execution policy is restricted. Changing it..."
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Success "تم تغيير سياسة التنفيذ بنجاح"
        Write-Success "Execution policy changed successfully"
    }
    catch {
        Write-Error-Custom "فشل في تغيير سياسة التنفيذ"
        Write-Error-Custom "Failed to change execution policy"
        Write-Info "يرجى تشغيل PowerShell كمدير وتنفيذ:"
        Write-Info "Please run PowerShell as administrator and execute:"
        Write-Host "Set-ExecutionPolicy RemoteSigned" -ForegroundColor Yellow
        Read-Host "اضغط Enter للخروج | Press Enter to exit"
        exit 1
    }
}

# Check if Python is installed
Write-Info "البحث عن Python..."
Write-Info "Looking for Python..."

$pythonCmd = $null
$pythonPaths = @(
    "python",
    "python3",
    "C:\Python313\python.exe",
    "C:\Python312\python.exe",
    "C:\Python311\python.exe",
    "C:\Python310\python.exe",
    "C:\Program Files\Python313\python.exe",
    "C:\Program Files\Python312\python.exe",
    "C:\Program Files\Python311\python.exe",
    "C:\Program Files\Python310\python.exe",
    "$env:USERPROFILE\AppData\Local\Programs\Python\Python313\python.exe",
    "$env:USERPROFILE\AppData\Local\Programs\Python\Python312\python.exe",
    "$env:USERPROFILE\AppData\Local\Programs\Python\Python311\python.exe",
    "$env:USERPROFILE\AppData\Local\Programs\Python\Python310\python.exe"
)

# Check virtual environments first
$venvPaths = @(
    "venv\Scripts\python.exe",
    "env\Scripts\python.exe",
    "hr_system_env\Scripts\python.exe"
)

foreach ($path in $venvPaths) {
    if (Test-Path $path) {
        $pythonCmd = $path
        Write-Success "تم العثور على البيئة الافتراضية: $path"
        Write-Success "Found virtual environment: $path"
        break
    }
}

# If no virtual environment found, check system Python
if (-not $pythonCmd) {
    foreach ($path in $pythonPaths) {
        try {
            $version = & $path --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                $pythonCmd = $path
                Write-Success "تم العثور على Python: $path"
                Write-Success "Found Python: $path"
                Write-Host $version -ForegroundColor Green
                break
            }
        }
        catch {
            continue
        }
    }
}

if (-not $pythonCmd) {
    Write-Error-Custom "لم يتم العثور على Python!"
    Write-Error-Custom "Python not found!"
    Write-Info "يرجى تثبيت Python من: https://www.python.org/downloads/"
    Write-Info "Please install Python from: https://www.python.org/downloads/"
    Write-Info "تأكد من إضافة Python إلى PATH أثناء التثبيت"
    Write-Info "Make sure to add Python to PATH during installation"
    Read-Host "اضغط Enter للخروج | Press Enter to exit"
    exit 1
}

Write-Host ""

# Check pip
Write-Info "التحقق من pip..."
Write-Info "Checking pip..."

try {
    $pipVersion = & $pythonCmd -m pip --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "تم العثور على pip"
        Write-Success "pip found"
        Write-Host $pipVersion -ForegroundColor Green
    }
    else {
        throw "pip not found"
    }
}
catch {
    Write-Warning "pip غير متوفر، جاري تثبيته..."
    Write-Warning "pip not available, installing..."
    & $pythonCmd -m ensurepip --upgrade
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Custom "فشل في تثبيت pip!"
        Write-Error-Custom "Failed to install pip!"
        Read-Host "اضغط Enter للخروج | Press Enter to exit"
        exit 1
    }
}

Write-Host ""

# Ask about virtual environment
if (-not ($pythonCmd -like "*venv*" -or $pythonCmd -like "*env*")) {
    $createVenv = Read-Host "هل تريد إنشاء بيئة افتراضية؟ (y/n) | Do you want to create a virtual environment? (y/n)"
    
    if ($createVenv -eq "y" -or $createVenv -eq "Y") {
        Write-Info "جاري إنشاء البيئة الافتراضية..."
        Write-Info "Creating virtual environment..."
        
        & $pythonCmd -m venv hr_system_env
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "تم إنشاء البيئة الافتراضية"
            Write-Success "Virtual environment created"
            $pythonCmd = "hr_system_env\Scripts\python.exe"
            Write-Success "تم تفعيل البيئة الافتراضية"
            Write-Success "Virtual environment activated"
        }
        else {
            Write-Error-Custom "فشل في إنشاء البيئة الافتراضية"
            Write-Error-Custom "Failed to create virtual environment"
            Read-Host "اضغط Enter للخروج | Press Enter to exit"
            exit 1
        }
        Write-Host ""
    }
}

# Upgrade pip
Write-Info "جاري تحديث pip..."
Write-Info "Upgrading pip..."
& $pythonCmd -m pip install --upgrade pip
Write-Host ""

# Install basic tools
Write-Info "جاري تثبيت الأدوات الأساسية..."
Write-Info "Installing basic tools..."
& $pythonCmd -m pip install wheel setuptools
Write-Host ""

# Check if requirements.txt exists
if (-not (Test-Path "requirements.txt")) {
    Write-Error-Custom "ملف requirements.txt غير موجود!"
    Write-Error-Custom "requirements.txt file not found!"
    Write-Info "يرجى التأكد من وجود الملف في نفس مجلد هذا الملف"
    Write-Info "Please make sure the file exists in the same folder as this file"
    Read-Host "اضغط Enter للخروج | Press Enter to exit"
    exit 1
}

Write-Success "تم العثور على ملف requirements.txt"
Write-Success "requirements.txt file found"
Write-Host ""

# Install requirements
Write-Host "========================================" -ForegroundColor White
Write-Info "جاري تثبيت المكتبات من requirements.txt..."
Write-Info "Installing packages from requirements.txt..."
Write-Info "هذا قد يستغرق عدة دقائق..."
Write-Info "This may take several minutes..."
Write-Host "========================================" -ForegroundColor White
Write-Host ""

& $pythonCmd -m pip install -r requirements.txt

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Warning "حدث خطأ أثناء تثبيت بعض المكتبات!"
    Write-Warning "Error occurred while installing some packages!"
    Write-Info "جاري المحاولة مرة أخرى مع تجاهل الأخطاء..."
    Write-Info "Trying again with error tolerance..."
    & $pythonCmd -m pip install -r requirements.txt --ignore-installed --force-reinstall
    
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "بعض المكتبات قد لا تكون مثبتة بشكل صحيح"
        Write-Warning "Some packages may not be installed correctly"
        Write-Info "يمكنك تثبيتها يدوياً لاحقاً"
        Write-Info "You can install them manually later"
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor White
Write-Success "تم الانتهاء من تثبيت المكتبات!"
Write-Success "Package installation completed!"
Write-Host "========================================" -ForegroundColor White
Write-Host ""

# Show installed packages
Write-Info "عرض المكتبات المثبتة..."
Write-Info "Showing installed packages..."
& $pythonCmd -m pip list
Write-Host ""

# Check Django installation
Write-Info "التحقق من تثبيت Django..."
Write-Info "Checking Django installation..."
try {
    $djangoVersion = & $pythonCmd -c "import django; print('Django version:', django.get_version())" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "تم تثبيت Django بنجاح!"
        Write-Success "Django installed successfully!"
        Write-Host $djangoVersion -ForegroundColor Green
    }
    else {
        Write-Error-Custom "Django غير مثبت بشكل صحيح!"
        Write-Error-Custom "Django not installed correctly!"
    }
}
catch {
    Write-Error-Custom "Django غير مثبت بشكل صحيح!"
    Write-Error-Custom "Django not installed correctly!"
}
Write-Host ""

# Additional setup instructions
Write-Host "========================================" -ForegroundColor White
Write-Host "تعليمات الإعداد التالية - Next Setup Instructions" -ForegroundColor White
Write-Host "========================================" -ForegroundColor White
Write-Host ""
Write-Info "الخطوات التالية لتشغيل النظام:"
Write-Info "Next steps to run the system:"
Write-Host ""
Write-Host "1. إنشاء قاعدة البيانات:" -ForegroundColor Yellow
Write-Host "   Create database:" -ForegroundColor Yellow
Write-Host "   $pythonCmd manage.py migrate" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. إنشاء مستخدم مدير:" -ForegroundColor Yellow
Write-Host "   Create admin user:" -ForegroundColor Yellow
Write-Host "   $pythonCmd manage.py createsuperuser" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. جمع الملفات الثابتة:" -ForegroundColor Yellow
Write-Host "   Collect static files:" -ForegroundColor Yellow
Write-Host "   $pythonCmd manage.py collectstatic" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. تشغيل الخادم:" -ForegroundColor Yellow
Write-Host "   Run server:" -ForegroundColor Yellow
Write-Host "   $pythonCmd manage.py runserver" -ForegroundColor Cyan
Write-Host ""
Write-Host "5. فتح المتصفح والذهاب إلى:" -ForegroundColor Yellow
Write-Host "   Open browser and go to:" -ForegroundColor Yellow
Write-Host "   http://127.0.0.1:8000" -ForegroundColor Cyan
Write-Host ""

# Create quick start script
Write-Info "إنشاء ملف تشغيل سريع..."
Write-Info "Creating quick start script..."

$startServerContent = @"
@echo off
title نظام شؤون الموظفين - خادم التطوير
echo جاري تشغيل خادم التطوير...
echo Starting development server...
$pythonCmd manage.py runserver
pause
"@

$startServerContent | Out-File -FilePath "start_server.bat" -Encoding UTF8

Write-Success "تم إنشاء ملف start_server.bat لتشغيل الخادم"
Write-Success "Created start_server.bat to run the server"
Write-Host ""

if ($createVenv -eq "y" -or $createVenv -eq "Y") {
    Write-Info "لتفعيل البيئة الافتراضية في المرات القادمة:"
    Write-Info "To activate virtual environment in the future:"
    Write-Host "hr_system_env\Scripts\Activate.ps1" -ForegroundColor Cyan
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor White
Write-Success "تم الانتهاء بنجاح!"
Write-Success "Installation completed successfully!"
Write-Host "========================================" -ForegroundColor White
Write-Host ""
Write-Info "يمكنك الآن تشغيل النظام باستخدام start_server.bat"
Write-Info "You can now run the system using start_server.bat"
Write-Host ""
Read-Host "اضغط Enter للخروج | Press Enter to exit"
