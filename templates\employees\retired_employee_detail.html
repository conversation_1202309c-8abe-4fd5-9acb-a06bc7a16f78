{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المتقاعد - {{ retired_employee.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .retired-header {
        background: linear-gradient(135deg, #dc3545 0%, #e9ecef 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .info-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .info-row {
        border-bottom: 1px solid #f1f3f4;
        padding: 15px 0;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .info-value {
        color: #212529;
        font-size: 1.1rem;
    }
    
    .retirement-badge {
        background: linear-gradient(45deg, #dc3545, #e74c3c);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: 600;
        display: inline-block;
    }
    
    .action-buttons .btn {
        border-radius: 25px;
        padding: 10px 20px;
        font-weight: 600;
        margin: 5px;
        transition: all 0.3s ease;
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Retired Employee Header -->
    <div class="retired-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-user-clock fa-4x"></i>
            </div>
            <div class="col-md-8">
                <h1 class="mb-2">{{ retired_employee.employee.full_name }}</h1>
                <h4 class="mb-0">الرقم الوزاري: {{ retired_employee.employee.ministry_number }}</h4>
            </div>
            <div class="col-md-2">
                <div class="retirement-badge">
                    متقاعد
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="action-buttons text-center">
                <a href="{% url 'employees:retired_employees_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
                <a href="{% url 'employees:retired_employee_update' retired_employee.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل البيانات
                </a>
                <a href="{% url 'employees:retired_employee_delete' retired_employee.pk %}" 
                   class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من إلغاء تقاعد هذا الموظف؟')">
                    <i class="fas fa-undo"></i> إلغاء التقاعد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information -->
        <div class="col-md-6 mb-4">
            <div class="card info-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i> معلومات الموظف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label">الاسم الكامل</div>
                        <div class="info-value">{{ retired_employee.employee.full_name }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">الرقم الوزاري</div>
                        <div class="info-value">
                            <span class="badge bg-secondary fs-6">{{ retired_employee.employee.ministry_number }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">الرقم الوطني</div>
                        <div class="info-value">{{ retired_employee.employee.national_id }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">الجنس</div>
                        <div class="info-value">
                            {% if retired_employee.employee.gender == 'male' %}
                                <i class="fas fa-mars text-primary"></i> ذكر
                            {% else %}
                                <i class="fas fa-venus text-danger"></i> أنثى
                            {% endif %}
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">تاريخ الميلاد</div>
                        <div class="info-value">{{ retired_employee.employee.birth_date }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">تاريخ التعيين</div>
                        <div class="info-value">{{ retired_employee.employee.hire_date }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">القسم</div>
                        <div class="info-value">{{ retired_employee.employee.school }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">التخصص</div>
                        <div class="info-value">{{ retired_employee.employee.specialization }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">المؤهل</div>
                        <div class="info-value">{{ retired_employee.employee.qualification }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Retirement Information -->
        <div class="col-md-6 mb-4">
            <div class="card info-card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-clock"></i> معلومات التقاعد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label">تاريخ التقاعد</div>
                        <div class="info-value">
                            <span class="retirement-badge">{{ retired_employee.retirement_date }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">سبب التقاعد</div>
                        <div class="info-value">{{ retired_employee.retirement_reason }}</div>
                    </div>
                    {% if retired_employee.notes %}
                    <div class="info-row">
                        <div class="info-label">ملاحظات</div>
                        <div class="info-value">{{ retired_employee.notes }}</div>
                    </div>
                    {% endif %}
                    <div class="info-row">
                        <div class="info-label">تاريخ الإضافة للنظام</div>
                        <div class="info-value">{{ retired_employee.created_at|date:"Y-m-d H:i" }}</div>
                    </div>
                    {% if retired_employee.updated_at != retired_employee.created_at %}
                    <div class="info-row">
                        <div class="info-label">آخر تحديث</div>
                        <div class="info-value">{{ retired_employee.updated_at|date:"Y-m-d H:i" }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card info-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book"></i> معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">رقم الهاتف</div>
                                <div class="info-value">
                                    <i class="fas fa-phone text-success"></i> {{ retired_employee.employee.phone_number }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">العنوان</div>
                                <div class="info-value">
                                    <i class="fas fa-map-marker-alt text-danger"></i> {{ retired_employee.employee.address }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}