{% extends 'home/base.html' %}
{% load static %}

{% block title %}روابط مهمة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-link me-2"></i> روابط مهمة</h3>
                </div>
                <div class="card-body">
                    <p class="lead">فيما يلي مجموعة من الروابط المهمة التي قد تحتاجها في عملك:</p>

                    <div class="row mt-4">
                        {% for link in links %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 hover-shadow attractive-card">
                                <div class="card-body text-center d-flex flex-column justify-content-between">
                                    <div class="logo-container mb-3">
                                    {% if link.has_icon %}
                                        <div class="logo-wrapper">
                                            <img src="{{ link.get_icon_url }}" alt="{{ link.name }}" class="site-logo" style="width: 64px; height: 64px; object-fit: contain;">
                                        </div>
                                    {% elif link.name == 'وزارة التربية والتعليم' %}
                                        <div class="logo-wrapper">
                                            <img src="/static/direct_images/moe-emblem.png" alt="{{ link.name }}" class="official-logo-img">
                                        </div>
                                    {% elif link.name == 'منصة الموظفين' %}
                                        <div class="logo-wrapper">
                                            <img src="/static/direct_images/employees-platform.jpg" alt="{{ link.name }}" class="official-logo-img">
                                        </div>
                                    {% elif link.name == 'منصة تدريب المعلمين' %}
                                        <div class="logo-wrapper">
                                            <img src="/static/direct_images/teachers-training.png" alt="{{ link.name }}" class="official-logo-img">
                                        </div>
                                    {% elif link.name == 'OpenEmis' %}
                                        <div class="logo-wrapper">
                                            <img src="/static/direct_images/openemis.png" alt="{{ link.name }}" class="official-logo-img">
                                        </div>
                                    {% elif link.name == 'BtecEmis JO' or link.name == 'التعليم الإضافي' or link.name == 'الوصف الوظيفي' or link.name == 'نماذج تقييم الأداء السنوي' %}
                                        <div class="logo-wrapper">
                                            <img src="/static/direct_images/moe-emblem.png" alt="{{ link.name }}" class="official-logo-img">
                                        </div>
                                    {% else %}
                                        <i class="fas fa-external-link-alt fa-4x text-primary"></i>
                                    {% endif %}
                                    </div>
                                    <h4 class="card-title">{{ link.name }}</h4>
                                    <p class="card-text text-muted small">
                                        {% if link.description %}
                                            {{ link.description }}
                                        {% elif link.name == 'وزارة التربية والتعليم' %}
                                            الموقع الرسمي لوزارة التربية والتعليم الأردنية
                                        {% elif link.name == 'منصة الموظفين' %}
                                            منصة إدارة شؤون الموظفين في وزارة التربية والتعليم
                                        {% elif link.name == 'منصة تدريب المعلمين' %}
                                            منصة التدريب والتطوير المهني للمعلمين
                                        {% elif link.name == 'OpenEmis' %}
                                            نظام ادارة المعلومات التربوية الاردن
                                        {% elif link.name == 'BtecEmis JO' %}
                                            نظام إدارة معلومات التعليم المهني والتقني
                                        {% elif link.name == 'التعليم الإضافي' %}
                                            منصة إدارة التعليم الإضافي للمعلمين
                                        {% elif link.name == 'الوصف الوظيفي' %}
                                            بطاقات الوصف الوظيفي للوظائف التعليمية والإدارية
                                        {% elif link.name == 'نماذج تقييم الأداء السنوي' %}
                                            نماذج التقييم السنوي المعتمدة
                                        {% else %}
                                            رابط مهم
                                        {% endif %}
                                    </p>
                                    <div class="mt-3">
                                        <a href="{{ link.url }}" target="_blank" class="btn btn-primary link-button">
                                            <i class="fas fa-external-link-alt me-1"></i> زيارة الموقع
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد روابط متاحة حالياً.
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        ملاحظة: جميع الروابط الخارجية تفتح في نافذة جديدة وتخضع لسياسات الخصوصية الخاصة بتلك المواقع.
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .hover-shadow {
        transition: all 0.3s ease;
    }
    .hover-shadow:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .logo-container {
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        padding: 10px;
    }
    .site-logo {
        max-height: 80px;
        max-width: 100%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 10px;
        background-color: white;
        transition: transform 0.3s ease;
        filter: drop-shadow(0 0 3px rgba(0,0,0,0.1));
    }

    .fallback-logo {
        max-height: 60px;
        background-color: #f8f9fa;
        padding: 10px;
        border: 1px dashed #dee2e6;
    }

    .custom-logo {
        width: 120px;
        height: 80px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
    }

    .hover-shadow:hover .custom-logo {
        transform: scale(1.05);
    }

    .logo-text {
        margin-top: 5px;
        font-size: 12px;
        font-weight: bold;
    }

    .emp-logo {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
    }

    .teachers-logo {
        background: linear-gradient(135deg, #1cc88a 0%, #169a6b 100%);
        color: white;
    }

    .openemis-logo {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
        color: white;
    }

    .jordan-emblem {
        background: linear-gradient(135deg, #B22234 0%, #8B0000 100%);
        color: white;
        position: relative;
        padding-top: 25px;
    }

    .crown {
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        color: #FFD700;
        font-size: 20px;
    }

    .emblem-circle {
        width: 40px;
        height: 40px;
        background-color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
    }

    .emblem-circle i {
        color: #B22234;
        font-size: 24px;
    }

    .logo-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
        transition: transform 0.3s ease;
        width: 100%;
        height: 100%;
    }

    .official-logo-img {
        max-width: 100%;
        max-height: 80px;
        object-fit: contain;
        border-radius: 8px;
        padding: 5px;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .hover-shadow:hover .logo-wrapper {
        transform: scale(1.05);
    }
    .hover-shadow:hover .site-logo {
        transform: scale(1.05);
    }
    .icon-stack {
        position: relative;
        display: inline-block;
        transition: transform 0.3s ease;
    }
    .hover-shadow:hover .icon-stack {
        transform: scale(1.1);
    }
    .icon-overlay {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .site-logo-container {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 50%;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Attractive Card Styles */
    .attractive-card {
        position: relative;
        border: none;
        transition: all 0.4s ease;
        overflow: hidden;
        z-index: 1;
        background-color: white;
    }

    .attractive-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #009688, #3f51b5, #ff5722, #009688);
        background-size: 400% 400%;
        z-index: -1;
        animation: borderGradient 6s ease infinite;
        border-radius: var(--border-radius-lg);
    }

    .attractive-card::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: calc(var(--border-radius-lg) - 2px);
        z-index: -1;
    }

    .attractive-card:hover::before {
        animation: borderGradient 3s ease infinite;
    }

    @keyframes borderGradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .attractive-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .attractive-card .card-body {
        z-index: 2;
        background-color: transparent;
    }

    /* Attractive Link Button Styles */
    .link-button {
        position: relative;
        border: none;
        transition: all 0.4s ease;
        overflow: hidden;
        z-index: 1;
        padding: 0.6rem 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .link-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
        z-index: -1;
    }

    .link-button:hover::before {
        transform: translateX(100%);
    }

    .link-button::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #ff5722, #009688, #3f51b5, #ff5722);
        background-size: 400% 400%;
        z-index: -2;
        animation: borderGradient 3s ease infinite;
        border-radius: 6px;
    }

    .link-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .link-button i {
        transition: all 0.3s ease;
    }

    .link-button:hover i {
        transform: translateX(-3px);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle image loading errors
        const siteLogos = document.querySelectorAll('.site-logo');
        siteLogos.forEach(function(logo) {
            logo.onerror = function() {
                // Get the site name from the alt attribute
                const siteName = this.alt;

                // Set favicon URL based on site name
                let faviconUrl = '';
                if (siteName === 'وزارة التربية والتعليم') {
                    faviconUrl = 'https://moe.gov.jo/favicon.ico';
                } else if (siteName === 'منصة الموظفين') {
                    faviconUrl = 'https://emp.moe.gov.jo/favicon.ico';
                } else if (siteName === 'منصة تدريب المعلمين') {
                    faviconUrl = 'https://teachers.gov.jo/favicon.ico';
                } else if (siteName === 'OpenEmis') {
                    faviconUrl = 'https://emis.moe.gov.jo/favicon.ico';
                } else if (siteName === 'BtecEmis JO') {
                    faviconUrl = 'https://apps.moe.gov.jo/favicon.ico';
                }

                // Replace with favicon and add special styling
                if (faviconUrl) {
                    this.src = faviconUrl;
                    this.classList.add('favicon-fallback');
                }
            };
        });
    });
</script>
{% endblock %}