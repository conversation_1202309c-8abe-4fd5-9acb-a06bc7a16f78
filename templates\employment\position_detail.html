{% extends 'base.html' %}
{% load static %}

{% block title %}{{ position.name }} - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>المسمى الوظيفي: {{ position.name }}</h2>
    <div>
        <a href="{% url 'employment:position_update' position.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employment:position_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للمسميات الوظيفية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات المسمى الوظيفي</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الاسم</th>
                        <td>{{ position.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الوصف</th>
                        <td>{{ position.description|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الإنشاء</th>
                        <td>{{ position.created_at }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">آخر تحديث</th>
                        <td>{{ position.updated_at }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الموظفون بهذا المسمى الوظيفي</h6>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>تاريخ التعيين</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employment in employees %}
                            <tr>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employment.employee.pk %}">
                                        {{ employment.employee.full_name }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{% url 'employment:department_detail' employment.department.pk %}">
                                        {{ employment.department.name }}
                                    </a>
                                </td>
                                <td>{{ employment.start_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا يوجد موظفون بهذا المسمى الوظيفي حالياً.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
