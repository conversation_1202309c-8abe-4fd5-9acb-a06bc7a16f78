/* CSS for Instructions and Help Text */

.instructions-box {
    background-color: #f8f9fa;
    border-right: 4px solid #009688;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.instructions-title {
    font-weight: 700;
    color: #009688;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.instructions-title i {
    margin-left: 8px;
    font-size: 1.2em;
}

.instructions-content {
    color: #333;
    line-height: 1.6;
}

.instructions-content ul {
    padding-right: 20px;
    margin-bottom: 0;
}

.instructions-content li {
    margin-bottom: 5px;
}

.instructions-content strong {
    color: #009688;
}

.instructions-content code {
    background-color: #e9ecef;
    padding: 2px 5px;
    border-radius: 4px;
    font-family: monospace;
    color: #d63384;
}

.instructions-box.primary {
    border-right-color: #009688;
}

.instructions-box.primary .instructions-title {
    color: #009688;
}

.instructions-box.info {
    border-right-color: #03a9f4;
}

.instructions-box.info .instructions-title {
    color: #03a9f4;
}

.instructions-box.warning {
    border-right-color: #ff9800;
}

.instructions-box.warning .instructions-title {
    color: #ff9800;
}

.instructions-box.danger {
    border-right-color: #f44336;
}

.instructions-box.danger .instructions-title {
    color: #f44336;
}

/* Sticky instructions for forms */
.sticky-instructions {
    position: sticky;
    top: 80px;
    z-index: 100;
}

/* Print styles */
@media print {
    .instructions-box {
        border: 1px solid #ddd;
        border-right: 4px solid #009688;
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sticky-instructions {
        position: static;
    }
}
