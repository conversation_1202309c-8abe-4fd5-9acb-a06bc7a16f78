@echo off
echo This script will help you disable Python app execution aliases.
echo.
echo Steps to disable Python app execution aliases:
echo 1. This will open the Windows Settings app to the App execution aliases page.
echo 2. Find "python.exe" and "python3.exe" in the list.
echo 3. Turn off the toggles for both.
echo 4. Close the Settings app and run run_with_venv.bat to start the server.
echo.
echo Press any key to open the Settings app...
pause > nul

REM Open Windows Settings to App execution aliases
start ms-settings:appsfeatures-app

echo.
echo Settings app should be opening...
echo.
echo 1. Click on "App execution aliases" on the right side.
echo 2. Find "python.exe" and "python3.exe" in the list.
echo 3. Turn off the toggles for both.
echo 4. Close the Settings app and run run_with_venv.bat to start the server.
echo.
echo Press any key to exit...
pause > nul
