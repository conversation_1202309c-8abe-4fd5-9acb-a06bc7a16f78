{% extends 'base.html' %}
{% load static %}

{% block title %}الموظفين الزوائد{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Card Styles */
    .dashboard-card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        letter-spacing: 0.05em;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
    }

    .rounded-circle {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .filters-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
    }

    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: 38px;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-filter {
        padding: 8px 12px;
        height: 38px;
    }

    .status-badge {
        font-size: 0.85rem;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .status-pending {
        background-color: #ffc107;
        color: #000;
    }

    .status-resolved {
        background-color: #28a745;
        color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="page-title mb-0">الموظفين الزوائد</h1>
                <div class="d-flex">
                    <a href="{% url 'employment:excess_employee_create' %}" class="btn btn-success me-2">
                        <i class="fas fa-plus"></i> إضافة موظف زائد
                    </a>
                    <a href="{% url 'employment:excess_employee_list' %}?{% if request.GET.search_term %}search_term={{ request.GET.search_term }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}export_excel=1" class="btn btn-primary">
                        <i class="fas fa-file-export"></i> تصدير البيانات
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي عدد الموظفين الزوائد</div>
                                    <div class="h3 mb-0 fw-bold text-gray-800">{{ pending_count }}</div>
                                    <div class="text-muted small mt-2">الموظفين الزوائد قيد المعالجة</div>
                                </div>
                                <div class="col-auto">
                                    <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                        <i class="fas fa-users fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs fw-bold text-success text-uppercase mb-1">تم تزويب الزوائد</div>
                                    <div class="h3 mb-0 fw-bold text-gray-800">{{ resolved_count }}</div>
                                    <div class="text-muted small mt-2">الموظفين الزوائد الذين تمت معالجتهم</div>
                                </div>
                                <div class="col-auto">
                                    <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                        <i class="fas fa-check-circle fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-start border-4 border-info shadow h-100 py-2 rounded-3 dashboard-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs fw-bold text-info text-uppercase mb-1">الأقسام</div>
                                    <div class="h3 mb-0 fw-bold text-gray-800">{{ departments_count }}</div>
                                    <div class="text-muted small mt-2">عدد الأقسام التي يوجد فيها زوائد</div>
                                </div>
                                <div class="col-auto">
                                    <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                        <i class="fas fa-building fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <form method="GET" action="{% url 'employment:excess_employee_list' %}">
                <div class="filters-container">
                    <div class="row">
                        <!-- Search Form -->
                        <div class="col-md-4 mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control" id="search_term" name="search_term" placeholder="بحث بالرقم الوزاري أو الاسم..." value="{{ search_form.search_term.value|default:'' }}">
                                <button class="btn btn-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="col-md-8 mb-2">
                            <div class="d-flex">
                                <!-- Department Filter -->
                                <select class="form-select me-1" id="department" name="department" style="width: 200px;">
                                    <option value="">جميع الأقسام</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if search_form.department.value|stringformat:'s' == dept.id|stringformat:'s' %}selected{% endif %}>{{ dept.name }}</option>
                                    {% endfor %}
                                </select>

                                <!-- Status Filter -->
                                <select class="form-select me-1" id="status" name="status" style="width: 150px;">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {% if search_form.status.value == 'pending' %}selected{% endif %}>اجمالي عدد الموظفين الزوائد</option>
                                    <option value="resolved" {% if search_form.status.value == 'resolved' %}selected{% endif %}>تم تزويب الزوائد</option>
                                </select>

                                <button type="submit" class="btn btn-secondary me-1" title="تطبيق الفلاتر">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                                <a href="{% url 'employment:excess_employee_list' %}" class="btn btn-secondary" title="إعادة تعيين الفلاتر">
                                    <i class="fas fa-redo"></i> إعادة الضبط
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>



            <!-- Data Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="dataTable">
                            <thead>
                                <tr class="text-center">
                                    <th><i class="fas fa-hashtag me-1"></i> #</th>
                                    <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                                    <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                                    <th><i class="fas fa-building me-1"></i> القسم الحالي</th>
                                    <th><i class="fas fa-briefcase me-1"></i> المسمى الوظيفي</th>
                                    <th><i class="fas fa-info-circle me-1"></i> سبب الزيادة</th>
                                    <th><i class="fas fa-flag me-1"></i> الحالة</th>
                                    <th><i class="fas fa-calendar-alt me-1"></i> تاريخ الإضافة</th>
                                    <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for excess_employee in excess_employees %}
                                <tr class="text-center">
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ excess_employee.employee.ministry_number }}</td>
                                    <td>{{ excess_employee.employee.full_name }}</td>
                                    <td>{{ excess_employee.current_department.name }}</td>
                                    <td>{{ excess_employee.position.name }}</td>
                                    <td>{{ excess_employee.reason|truncatechars:50 }}</td>
                                    <td>
                                        {% if excess_employee.status == 'pending' %}
                                        <span class="badge status-badge status-pending">موظف زائد</span>
                                        {% else %}
                                        <span class="badge status-badge status-resolved">تم تزويب الزائد</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ excess_employee.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        <a href="{% url 'employment:excess_employee_detail' excess_employee.id %}" class="btn btn-sm btn-info" title="عرض">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="{% url 'employment:excess_employee_update' excess_employee.id %}" class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        {% if excess_employee.status == 'pending' %}
                                        <a href="{% url 'employment:excess_employee_resolve' excess_employee.id %}" class="btn btn-sm btn-success" title="معالجة">
                                            <i class="fas fa-check"></i> معالجة
                                        </a>
                                        {% endif %}
                                        <a href="{% url 'employment:excess_employee_delete' excess_employee.id %}" class="btn btn-sm btn-danger" title="حذف">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center">لا يوجد موظفين زوائد</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json"
            },
            "order": [[7, "desc"]], // Sort by date in descending order
            "searching": false,     // Disable DataTables search as we use our own
            "paging": false,        // Disable pagination as we handle filtering server-side
            "info": false           // Disable showing "Showing X of Y entries"
        });
    });
</script>
{% endblock %}
