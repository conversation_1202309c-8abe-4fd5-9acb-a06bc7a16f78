{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.title.id_for_label }}" class="form-label required-field">عنوان النموذج</label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.title.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.file.id_for_label }}" class="form-label required-field">الملف</label>
                                {{ form.file }}
                                {% if form.file.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.file.errors }}
                                </div>
                                {% endif %}
                                {% if approved_form and approved_form.file %}
                                <div class="form-text">
                                    الملف الحالي: {{ approved_form.filename }} ({{ approved_form.file.size|filesizeformat }})
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="{{ form.description.id_for_label }}" class="form-label">وصف النموذج</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.order.id_for_label }}" class="form-label">الترتيب</label>
                                {{ form.order }}
                                {% if form.order.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.order.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    يتم ترتيب النماذج تصاعدياً حسب هذا الرقم
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        نشط
                                    </label>
                                    {% if form.is_active.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.is_active.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">
                                        النماذج غير النشطة لا تظهر للمستخدمين
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <a href="{% url 'home:approved_forms_admin' %}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
