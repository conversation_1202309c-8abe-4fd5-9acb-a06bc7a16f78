from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import HttpResponse
import pandas as pd
from io import BytesIO

# Try to import xlsxwriter
try:
    import xlsxwriter
    XLSXWRITER_AVAILABLE = True
except ImportError:
    XLSXWRITER_AVAILABLE = False
from datetime import datetime
from .models import PerformanceEvaluation
from .forms import PerformanceEvaluationForm, PerformanceImportForm
from employees.models import Employee

@login_required
def performance_list(request):
    search_query = request.GET.get('search', '')
    selected_year = request.GET.get('year', '')
    export_format = request.GET.get('export', '')

    # Get all available years
    years = PerformanceEvaluation.objects.values_list('year', flat=True).distinct().order_by('-year')

    # Base queryset
    evaluations = PerformanceEvaluation.objects.all()

    # Apply search filter if provided
    if search_query:
        evaluations = evaluations.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(evaluator__icontains=search_query)
        )

    # Apply year filter if provided
    if selected_year and selected_year != 'all':
        evaluations = evaluations.filter(year=selected_year)

    # Calculate statistics
    from django.utils import timezone
    current_year = timezone.now().year

    # Total evaluations count (all time)
    total_evaluations_count = PerformanceEvaluation.objects.count()

    # Current year evaluations count
    current_year_evaluations_count = PerformanceEvaluation.objects.filter(year=current_year).count()

    # Handle export requests
    if export_format:
        if export_format == 'excel':
            return export_to_excel(evaluations)
        elif export_format == 'pdf_preview':
            return render(request, 'performance/performance_pdf_preview.html', {
                'evaluations': evaluations,
                'search_query': search_query,
                'selected_year': selected_year,
                'employee_count': evaluations.count(),
                'report_title': 'تقرير التقييمات السنوية'
            })

    return render(request, 'performance/performance_list.html', {
        'evaluations': evaluations,
        'search_query': search_query,
        'years': years,
        'selected_year': selected_year,
        'employee_count': evaluations.count(),
        'total_evaluations_count': total_evaluations_count,
        'current_year_evaluations_count': current_year_evaluations_count,
        'current_year': current_year
    })

@login_required
def performance_create(request):
    if request.method == 'POST':
        form = PerformanceEvaluationForm(request.POST)
        if form.is_valid():
            evaluation = form.save()
            messages.success(request, 'تم إضافة تقييم الأداء بنجاح.')
            return redirect('performance:performance_detail', pk=evaluation.pk)
    else:
        form = PerformanceEvaluationForm()
    return render(request, 'performance/performance_form.html', {'form': form})

@login_required
def performance_detail(request, pk):
    evaluation = get_object_or_404(PerformanceEvaluation, pk=pk)
    return render(request, 'performance/performance_detail.html', {'evaluation': evaluation})

@login_required
def performance_update(request, pk):
    evaluation = get_object_or_404(PerformanceEvaluation, pk=pk)
    if request.method == 'POST':
        form = PerformanceEvaluationForm(request.POST, instance=evaluation)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث تقييم الأداء بنجاح.')
            return redirect('performance:performance_detail', pk=evaluation.pk)
    else:
        form = PerformanceEvaluationForm(instance=evaluation)
    return render(request, 'performance/performance_form.html', {'form': form, 'evaluation': evaluation})

@login_required
def performance_delete(request, pk):
    evaluation = get_object_or_404(PerformanceEvaluation, pk=pk)
    if request.method == 'POST':
        employee_name = evaluation.employee.full_name
        evaluation.delete()
        messages.success(request, f'تم حذف تقييم الأداء للموظف {employee_name} بنجاح.')
        return redirect('performance:performance_list')
    return render(request, 'performance/performance_confirm_delete.html', {'evaluation': evaluation})

@login_required
def performance_import(request):
    if request.method == 'POST' and request.FILES.get('excel_file'):
        form = PerformanceImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['excel_file']
            try:
                # Read the Excel file
                df = pd.read_excel(excel_file)

                # Process each row
                for _, row in df.iterrows():
                    try:
                        # Get employee by ministry number
                        ministry_number = str(row.get('الرقم الوزاري', ''))
                        try:
                            employee = Employee.objects.get(ministry_number=ministry_number)
                        except Employee.DoesNotExist:
                            continue

                        # Get or create performance evaluation
                        year = int(row.get('السنة', 0))
                        if year == 0:
                            continue

                        evaluation, created = PerformanceEvaluation.objects.get_or_create(
                            employee=employee,
                            year=year,
                            defaults={
                                'score': float(row.get('الدرجة', 0)),
                                'max_score': float(row.get('الدرجة القصوى', 100)),
                                'evaluator': str(row.get('المقيم', '')),
                                'comments': str(row.get('الملاحظات', ''))
                            }
                        )

                        if not created:
                            # Update existing evaluation
                            evaluation.score = float(row.get('الدرجة', evaluation.score))
                            evaluation.max_score = float(row.get('الدرجة القصوى', evaluation.max_score))
                            evaluation.evaluator = str(row.get('المقيم', evaluation.evaluator))
                            evaluation.comments = str(row.get('الملاحظات', evaluation.comments))
                            evaluation.save()
                    except Exception as e:
                        # Log the error and continue with the next row
                        print(f"Error importing row: {e}")
                        continue

                messages.success(request, 'تم استيراد تقييمات الأداء بنجاح.')
                return redirect('performance:performance_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء استيراد البيانات: {str(e)}')
        else:
            messages.error(request, 'الرجاء تصحيح الأخطاء أدناه.')
    else:
        form = PerformanceImportForm()
    return render(request, 'performance/performance_import.html', {'form': form})

def export_to_excel(evaluations):
    """Export performance evaluations to Excel"""
    if not XLSXWRITER_AVAILABLE:
        # Return a simple CSV response if xlsxwriter is not available
        response = HttpResponse(content_type='text/csv; charset=utf-8')
        response['Content-Disposition'] = 'attachment; filename="performance_evaluations.csv"'

        import csv
        writer = csv.writer(response)

        # Write headers
        writer.writerow(['الرقم الوزاري', 'اسم الموظف', 'السنة', 'العلامة', 'الدرجة القصوى', 'النسبة المئوية', 'المقيم', 'الملاحظات'])

        # Write data
        for evaluation in evaluations:
            writer.writerow([
                evaluation.employee.ministry_number,
                evaluation.employee.full_name,
                evaluation.year,
                float(evaluation.score),
                float(evaluation.max_score),
                f"{evaluation.percentage:.2f}%",
                evaluation.evaluator or '',
                evaluation.comments or ''
            ])

        return response

    # Create a BytesIO buffer
    output = BytesIO()

    # Create a workbook and add a worksheet
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('التقارير السنوية')

    # Add formats
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#f2f2f2',
        'border': 1,
        'font_size': 12
    })

    cell_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'font_size': 11
    })

    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    # Add title and metadata
    worksheet.merge_range('A1:G1', 'تقرير التقييمات السنوية', title_format)
    worksheet.merge_range('A2:G2', f'عدد الموظفين: {evaluations.count()}', title_format)
    worksheet.merge_range('A3:G3', f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d")}', title_format)

    # Set column widths
    worksheet.set_column('A:A', 15)  # Ministry Number
    worksheet.set_column('B:B', 30)  # Employee Name
    worksheet.set_column('C:C', 10)  # Year
    worksheet.set_column('D:D', 10)  # Score
    worksheet.set_column('E:E', 15)  # Max Score
    worksheet.set_column('F:F', 15)  # Percentage
    worksheet.set_column('G:G', 20)  # Evaluator
    worksheet.set_column('H:H', 30)  # Comments

    # Write headers
    headers = ['الرقم الوزاري', 'اسم الموظف', 'السنة', 'العلامة', 'الدرجة القصوى', 'النسبة المئوية', 'المقيم', 'الملاحظات']
    for col_num, header in enumerate(headers):
        worksheet.write(4, col_num, header, header_format)

    # Write data
    for row_num, evaluation in enumerate(evaluations, 5):
        worksheet.write(row_num, 0, evaluation.employee.ministry_number, cell_format)
        worksheet.write(row_num, 1, evaluation.employee.full_name, cell_format)
        worksheet.write(row_num, 2, evaluation.year, cell_format)
        worksheet.write(row_num, 3, float(evaluation.score), cell_format)
        worksheet.write(row_num, 4, float(evaluation.max_score), cell_format)
        worksheet.write(row_num, 5, f"{evaluation.percentage:.2f}%", cell_format)
        worksheet.write(row_num, 6, evaluation.evaluator or '', cell_format)
        worksheet.write(row_num, 7, evaluation.comments or '', cell_format)

    # Close the workbook
    workbook.close()

    # Create response
    output.seek(0)
    response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="performance_evaluations.xlsx"'

    return response