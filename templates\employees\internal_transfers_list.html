{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل حركات النقل - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .card-header {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
        border-top: none;
    }
    .table td {
        vertical-align: middle;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .search-box {
        max-width: 300px;
    }
    .transfer-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    /* Search input styling */
    #searchInput {
        border-radius: 0.375rem 0 0 0.375rem;
        transition: all 0.3s ease;
    }
    
    #searchInput:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    #searchInput.border-primary {
        border-color: #007bff;
    }
    
    /* Per page selector styling */
    #perPageSelect {
        border: 2px solid #e3e6f0;
        border-radius: 6px;
        font-weight: 600;
        color: #007bff;
        padding: 8px 12px;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
        transition: all 0.3s ease;
    }
    
    #perPageSelect:focus {
        border-color: #0056b3;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }
    
    /* Badge styling */
    #transferCounter {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }
    
    /* Highlight search results */
    mark.bg-warning {
        background-color: #fff3cd !important;
        color: #856404;
        font-weight: 600;
        border-radius: 3px;
        padding: 0 2px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-arrows-alt text-primary"></i>
            تفاصيل حركات النقل
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employees:internal_transfers_statistics' %}" class="btn btn-info btn-sm">
                <i class="fas fa-chart-bar"></i> الإحصائيات
            </a>
            <a href="{% url 'employees:export_internal_transfers_excel' %}" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> العودة لإدارة الكادر
            </a>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <!-- Title -->
                <h6 class="m-0 font-weight-bold">البحث والتصفية</h6>
                
                <!-- Search Controls -->
                <div class="d-flex align-items-center gap-3">
                    <!-- Per Page Selector -->
                    <div class="d-flex align-items-center gap-2">
                        <label for="perPageSelect" class="text-primary font-weight-bold">عرض:</label>
                        <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;">
                            <option value="10" {% if per_page == '10' %}selected{% endif %}>10</option>
                            <option value="25" {% if per_page == '25' or not per_page %}selected{% endif %}>25</option>
                            <option value="50" {% if per_page == '50' %}selected{% endif %}>50</option>
                            <option value="100" {% if per_page == '100' %}selected{% endif %}>100</option>
                            <option value="all" {% if per_page == 'all' %}selected{% endif %}>الكل</option>
                        </select>
                    </div>

                    <!-- Search input -->
                    <div class="input-group" style="width: 280px;">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control form-control-sm"
                               placeholder="بحث بالرقم الوزاري، الاسم، أو القسم..." 
                               value="{{ search_query|default:'' }}">
                    </div>

                    <!-- Clear Search Button -->
                    {% if search_query %}
                    <button type="button" id="clearSearch" class="btn btn-outline-secondary btn-sm" title="مسح البحث">
                        <i class="fas fa-times"></i>
                    </button>
                    {% endif %}

                    <!-- Transfer counter -->
                    <span class="badge bg-info" id="transferCounter">
                        {{ total_transfers }} حركة نقل
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <span>
                    إجمالي حركات النقل: <strong>{{ total_transfers }}</strong>
                    {% if search_query %}
                    | نتائج البحث عن: "<strong>{{ search_query }}</strong>"
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <!-- Internal Transfers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-white">
                <i class="fas fa-table"></i>
                جدول حركات النقل الداخلي
            </h6>
        </div>
        <div class="card-body">
            {% if transfers %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="transfersTable">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 5%;">#</th>
                            <th class="text-center" style="width: 12%;">الرقم الوزاري</th>
                            <th class="text-center" style="width: 20%;">الاسم الكامل</th>
                            <th class="text-center" style="width: 15%;">التخصص</th>
                            <th class="text-center" style="width: 15%;">القسم السابق</th>
                            <th class="text-center" style="width: 15%;">القسم الجديد</th>
                            <th class="text-center" style="width: 10%;">تاريخ النقل</th>
                            <th class="text-center" style="width: 8%;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transfer in transfers %}
                        <tr>
                            <td class="text-center">{{ forloop.counter }}</td>
                            <td class="text-center">
                                <span class="badge bg-primary">{{ transfer.employee.ministry_number }}</span>
                            </td>
                            <td>{{ transfer.employee.full_name }}</td>
                            <td class="text-center">{{ transfer.employee.specialization }}</td>
                            <td class="text-center">
                                <span class="badge bg-warning text-dark">{{ transfer.previous_department }}</span>
                                <br><small class="text-muted">{{ transfer.transfer_date }}</small>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-success">{{ transfer.new_department }}</span>
                                <br><small class="text-muted">{{ transfer.start_date }}</small>
                            </td>
                            <td class="text-center">
                                <span class="transfer-badge badge bg-info">
                                    {{ transfer.transfer_date }}
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" 
                                       class="btn btn-info btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'employees:internal_transfer_update' transfer.pk %}" 
                                       class="btn btn-warning btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employees:internal_transfer_delete' transfer.pk %}" 
                                       class="btn btn-danger btn-sm" title="حذف"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-arrows-alt fa-3x text-muted mb-3"></i>
                    <h4>لا توجد حركات نقل</h4>
                    <p class="text-muted">
                        {% if search_query %}
                        لم يتم العثور على حركات نقل تطابق معايير البحث الحالية.
                        {% else %}
                        لم يتم تسجيل أي حركات نقل داخلي حتى الآن.
                        {% endif %}
                    </p>
                    {% if search_query %}
                    <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-primary">
                        <i class="fas fa-list"></i> عرض جميع حركات النقل
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj and page_obj.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="صفحات حركات النقل">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if per_page %}per_page={{ per_page }}&{% endif %}page={{ page_obj.previous_page_number }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if per_page %}per_page={{ per_page }}&{% endif %}page={{ num }}">{{ num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if per_page %}per_page={{ per_page }}&{% endif %}page={{ page_obj.next_page_number }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            
            <!-- Pagination info -->
            <div class="text-center mt-2">
                <small class="text-muted">
                    عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} حركة نقل
                </small>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Search functionality with AJAX
        let searchTimeout;
        $('#searchInput').on('keyup input', function() {
            clearTimeout(searchTimeout);
            const searchValue = $(this).val();
            
            searchTimeout = setTimeout(function() {
                // Update URL with search parameter and reload page
                const currentUrl = new URL(window.location);
                if (searchValue.trim()) {
                    currentUrl.searchParams.set('search', searchValue.trim());
                } else {
                    currentUrl.searchParams.delete('search');
                }
                
                // Reload with new search
                window.location.href = currentUrl.toString();
            }, 800); // 800ms delay to prevent too many requests
        });

        // Clear search functionality
        $('#clearSearch').on('click', function() {
            $('#searchInput').val('');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search');
            window.location.href = currentUrl.toString();
        });

        // Per page selector functionality
        $('#perPageSelect').on('change', function() {
            const perPage = $(this).val();
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('per_page', perPage);
            window.location.href = currentUrl.toString();
        });

        // Initialize DataTable with Arabic support (without search since we have custom search)
        $('#transfersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[ 6, "desc" ]], // Sort by transfer date (column 6) descending
            "pageLength": {{ per_page|default:"25" }},
            "responsive": true,
            "searching": false, // Disable built-in search since we have custom search
            "paging": false, // Disable built-in paging since we handle it server-side
            "info": false, // Disable info display
            "columnDefs": [
                { "orderable": false, "targets": [7] } // Disable sorting for Actions column
            ]
        });

        // Add search highlighting
        function highlightSearchTerm() {
            const searchTerm = '{{ search_query|escapejs }}';
            if (searchTerm) {
                const regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
                $('#transfersTable tbody tr').each(function() {
                    $(this).find('td').each(function() {
                        const $cell = $(this);
                        if (!$cell.find('a, button').length) { // Skip cells with links/buttons
                            const text = $cell.text();
                            const highlightedText = text.replace(regex, '<mark class="bg-warning">$1</mark>');
                            if (text !== highlightedText) {
                                $cell.html(highlightedText);
                            }
                        }
                    });
                });
            }
        }

        // Apply highlighting
        highlightSearchTerm();

        // Add live search feedback
        $('#searchInput').on('input', function() {
            const searchValue = $(this).val();
            if (searchValue.length > 0) {
                $(this).addClass('border-primary');
            } else {
                $(this).removeClass('border-primary');
            }
        });
    });
</script>
{% endblock %}