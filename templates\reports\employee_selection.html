{% extends 'base.html' %}
{% load static %}

{% block title %}اختيار موظف للتقرير - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .table th {
        text-align: center;
        vertical-align: middle;
        font-size: 13px;
        font-weight: 600;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 12px 8px;
        line-height: 1.2;
    }
    
    .table td {
        text-align: center;
        vertical-align: middle;
        font-size: 13px;
        border: 1px solid #dee2e6;
        padding: 10px 8px;
    }
    
    .badge {
        font-size: 11px;
        padding: 4px 8px;
    }
    
    .text-muted {
        font-size: 11px;
        font-style: italic;
    }
    
    /* Search bar styling */
    .input-group {
        transition: all 0.3s ease;
    }
    
    .input-group.shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }
    
    #searchInput {
        border-right: none;
    }
    
    #searchInput:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-left: none;
        color: #6c757d;
    }
    
    /* Highlight search results */
    .highlight {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
    }
    
    /* Stats styling */
    .text-muted {
        font-size: 12px;
    }
    
    .text-muted i {
        margin-left: 5px;
    }
    
    /* Custom badge colors */
    .bg-pink {
        background-color: #e91e63 !important;
        color: white;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .table th, .table td {
            font-size: 11px;
            padding: 6px 4px;
        }
        
        .badge {
            font-size: 10px;
            padding: 2px 6px;
        }
        
        .col-md-6 {
            margin-bottom: 10px;
        }
        
        .d-flex.align-items-center {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>اختيار موظف للتقرير</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">اختر موظفاً لإنشاء تقرير عنه</h6>
    </div>
    <div class="card-body">
        <!-- شريط البحث -->
        <div class="row mb-3">
            <div class="col-md-8">
                <form method="GET" class="d-flex">
                    <div class="input-group flex-grow-1">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" id="searchInput" 
                               value="{{ search_query }}" 
                               placeholder="ابحث عن موظف (الاسم، الرقم الوزاري، الرقم الوطني، المؤهل العلمي...)">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        {% if search_query %}
                        <a href="{% url 'reports:employee_selection' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> مسح
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-end">
                    <span class="text-muted me-2" style="font-size: 22px;">
                        <i class="fas fa-users"></i>
                        {% if search_query %}
                            نتائج البحث: <span class="fw-bold text-primary" style="font-size: 22px;">{{ employees.count }}</span>
                        {% else %}
                            إجمالي الموظفين: <span class="fw-bold" style="font-size: 22px;">{{ employees.count }}</span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
        
        {% if search_query %}
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-search"></i>
            <strong>نتائج البحث عن:</strong> "{{ search_query }}" - تم العثور على {{ employees.count }} موظف
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الرقم الوزاري</th>
                        <th>الرقم الوطني</th>
                        <th>الاسم الرباعي</th>
                        <th>الجنس</th>
                        <th>التخصص</th>
                        <th>المؤهل العلمي<br><small>(بكالوريس / دبلوم)</small></th>
                        <th>المؤهل العلمي<br><small>(دبلوم بعد البكالوريس)</small></th>
                        <th>المؤهل العلمي<br><small>(ماجستير)</small></th>
                        <th>المؤهل العلمي<br><small>(دكتوراه)</small></th>
                        <th>تاريخ التعيين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.ministry_number }}</td>
                        <td>{{ employee.national_id|default:"-" }}</td>
                        <td>{{ employee.full_name }}</td>
                        <td>
                            {% if employee.gender == 'male' %}
                                <span class="badge bg-info">ذكر</span>
                            {% elif employee.gender == 'female' %}
                                <span class="badge bg-pink">أنثى</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.specialization|default:"-" }}</td>
                        <td>
                            {% if employee.qualification %}
                                <span class="badge bg-primary">{{ employee.qualification }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.post_graduate_diploma %}
                                <span class="badge bg-success">{{ employee.post_graduate_diploma }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.masters_degree %}
                                <span class="badge bg-info">{{ employee.masters_degree }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.phd_degree %}
                                <span class="badge bg-warning text-dark">{{ employee.phd_degree }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date|default:"-" }}</td>
                        <td>
                            <a href="{% url 'reports:employee_report' employee.pk %}" class="btn btn-dark btn-sm text-white">
                                <i class="fas fa-file-alt"></i> إنشاء تقرير
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="11" class="text-center">
                            {% if search_query %}
                                <i class="fas fa-search"></i> لا توجد نتائج مطابقة للبحث "{{ search_query }}"
                            {% else %}
                                لا يوجد موظفين
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const table = document.querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        const searchForm = searchInput.closest('form');
        
        // تجاهل الصف الفارغ إذا كان موجوداً
        const dataRows = Array.from(rows).filter(row => !row.textContent.includes('لا يوجد موظفين'));
        
        // البحث الفوري (client-side) للتصفية السريعة
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.toLowerCase().trim();
            
            // البحث الفوري للنتائج المحملة
            dataRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (searchTerm === '' || text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            
            // البحث من الخادم بعد توقف الكتابة لثانية واحدة
            searchTimeout = setTimeout(() => {
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    searchForm.submit();
                }
            }, 1000);
        });
        
        // البحث عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearTimeout(searchTimeout);
                searchForm.submit();
            }
        });
        
        // إضافة تأثيرات بصرية
        searchInput.addEventListener('focus', function() {
            this.closest('.input-group').classList.add('shadow-sm');
        });
        
        searchInput.addEventListener('blur', function() {
            this.closest('.input-group').classList.remove('shadow-sm');
        });
        
        // تمييز النص المطابق في النتائج
        function highlightSearchTerm() {
            const searchTerm = searchInput.value.trim();
            if (searchTerm && searchTerm.length >= 2) {
                dataRows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    cells.forEach(cell => {
                        if (!cell.querySelector('a')) { // تجنب تمييز الأزرار
                            const text = cell.textContent;
                            const regex = new RegExp(`(${searchTerm})`, 'gi');
                            if (regex.test(text)) {
                                cell.innerHTML = text.replace(regex, '<span class="highlight">$1</span>');
                            }
                        }
                    });
                });
            }
        }
        
        // تطبيق التمييز إذا كان هناك بحث مسبق
        if (searchInput.value.trim()) {
            highlightSearchTerm();
        }
        
        // إضافة مؤشر التحميل
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'text-center mt-3';
        loadingIndicator.style.display = 'none';
        loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
        table.parentNode.insertBefore(loadingIndicator, table);
        
        searchForm.addEventListener('submit', function() {
            loadingIndicator.style.display = 'block';
            table.style.opacity = '0.5';
        });
        
        // تركيز تلقائي على شريط البحث
        searchInput.focus();
        
        // إضافة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl+F للبحث
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
                searchInput.select();
            }
            
            // Escape لمسح البحث
            if (e.key === 'Escape' && document.activeElement === searchInput) {
                if (searchInput.value) {
                    searchInput.value = '';
                    searchInput.dispatchEvent(new Event('input'));
                } else {
                    searchInput.blur();
                }
            }
        });
        
        // إضافة tooltip للبحث
        searchInput.title = 'يمكنك البحث بالاسم، الرقم الوزاري، الرقم الوطني، أو المؤهل العلمي\nاستخدم Ctrl+F للبحث السريع';
    });
</script>
{% endblock %}
