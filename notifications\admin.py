from django.contrib import admin
from .models import Notification, NotificationReadStatus

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'user', 'notification_type', 'is_read', 'is_global', 'created_at']
    list_filter = ['notification_type', 'is_read', 'is_global', 'created_at']
    search_fields = ['title', 'message', 'user__username']
    readonly_fields = ['created_at', 'read_at']
    list_editable = ['is_read', 'is_global']

    fieldsets = (
        ('معلومات الإشعار', {
            'fields': ('title', 'message', 'notification_type', 'icon')
        }),
        ('إعدادات الإشعار', {
            'fields': ('user', 'is_global', 'is_read')
        }),
        ('معلومات التوقيت', {
            'fields': ('created_at', 'read_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(NotificationReadStatus)
class NotificationReadStatusAdmin(admin.ModelAdmin):
    list_display = ['notification', 'user', 'is_read', 'read_at', 'created_at']
    list_filter = ['is_read', 'created_at', 'read_at']
    search_fields = ['notification__title', 'user__username']
    readonly_fields = ['created_at', 'read_at']
    list_editable = ['is_read']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('notification', 'user')
