{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المستخدمين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .permission-badge {
        font-size: 0.75rem;
        margin: 2px;
    }
    
    .user-permissions {
        max-width: 200px;
        overflow: hidden;
    }
    
    .permission-summary {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .permission-count {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: bold;
    }
    
    .user-status-active {
        color: #28a745;
    }
    
    .user-status-inactive {
        color: #dc3545;
    }
    
    .quick-actions {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }
    
    .btn-xs {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        line-height: 1.2;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stats-item {
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    /* Enhanced checkbox styling */
    .checkbox-cell {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 15px !important;
        width: 60px;
        background-color: #f8f9fc;
        border-right: 2px solid #e3e6f0;
    }

    .checkbox-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        min-height: 45px;
    }

    .user-checkbox {
        cursor: pointer;
        margin: 0;
        position: relative;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 22px;
        height: 22px;
        border: 2px solid #007bff;
        border-radius: 4px;
        background-color: white;
        transition: all 0.2s ease;
    }

    /* Header checkbox styling */
    .select-all-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        padding: 8px 0;
    }

    #selectAll {
        cursor: pointer;
        margin: 0;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 22px;
        height: 22px;
        border: 2px solid #007bff;
        border-radius: 4px;
        background-color: white;
        transition: all 0.2s ease;
    }

    /* Hover effects for checkboxes */
    .user-checkbox:hover,
    #selectAll:hover {
        transform: scale(1.1);
        border-color: #0056b3;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
    }

    /* Focus styles for accessibility */
    .user-checkbox:focus,
    #selectAll:focus {
        outline: none;
        border-color: #0056b3;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
    }

    /* Checked state */
    .user-checkbox:checked,
    #selectAll:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .user-checkbox:checked::after,
    #selectAll:checked::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        font-size: 16px;
        line-height: 1;
    }

    /* Row highlighting when checkbox is checked */
    tr:has(.user-checkbox:checked) {
        background-color: rgba(0, 123, 255, 0.08) !important;
        border-left: 4px solid #007bff;
    }

    /* Checkbox cell highlighting */
    .checkbox-cell:has(.user-checkbox:checked) {
        background-color: rgba(0, 123, 255, 0.15) !important;
    }

    /* Animation for checkbox selection */
    @keyframes checkboxPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    .user-checkbox:checked,
    #selectAll:checked {
        animation: checkboxPulse 0.3s ease-in-out;
    }

    /* Improved table header for checkbox column */
    th:first-child {
        background-color: #495057 !important;
        border-right: 2px solid #6c757d;
    }

    /* Better visual feedback */
    .user-checkbox:active,
    #selectAll:active {
        transform: scale(0.95);
    }

    /* Tooltip styling */
    .user-checkbox[title],
    #selectAll[title] {
        position: relative;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-users text-primary"></i>
                        إدارة المستخدمين
                    </h2>
                    <p class="text-muted mb-0">إدارة شاملة للمستخدمين والصلاحيات</p>
                </div>
                <div>
                    <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-card">
        <div class="row">
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ user_stats.total_users }}</span>
                    <span class="stats-label">إجمالي المستخدمين</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ user_stats.active_users }}</span>
                    <span class="stats-label">مستخدمين نشطين</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ user_stats.admin_users }}</span>
                    <span class="stats-label">مديرين</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ user_stats.users_with_permissions }}</span>
                    <span class="stats-label">لديهم صلاحيات مخصصة</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i>
                جميع المستخدمين
            </h6>
            <div class="d-flex gap-2">
                <form class="d-flex" method="get">
                    <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ request.GET.search }}">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                <button class="btn btn-success" onclick="bulkPermissionUpdate()">
                    <i class="fas fa-users-cog"></i>
                    إدارة جماعية
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="5%" class="text-center">
                                <div class="select-all-wrapper">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" title="تحديد جميع المستخدمين">
                                    <label for="selectAll" class="visually-hidden">تحديد الكل</label>
                                </div>
                            </th>
                            <th width="12%"><i class="fas fa-user me-2"></i>المستخدم</th>
                            <th width="15%"><i class="fas fa-id-card me-2"></i>الاسم الكامل</th>
                            <th width="10%"><i class="fas fa-shield-alt me-2"></i>النوع</th>
                            <th width="8%"><i class="fas fa-signal me-2"></i>الحالة</th>
                            <th width="20%"><i class="fas fa-key me-2"></i>الصلاحيات</th>
                            <th width="12%"><i class="fas fa-clock me-2"></i>آخر دخول</th>
                            <th width="18%"><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user_obj in users %}
                        <tr id="user-row-{{ user_obj.id }}">
                            <td class="checkbox-cell">
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" class="user-checkbox" value="{{ user_obj.id }}" title="اختيار {{ user_obj.username }}">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-primary rounded-circle">
                                            {{ user_obj.username|first|upper }}
                                        </div>
                                    </div>
                                    <div>
                                        <strong>{{ user_obj.username }}</strong>
                                        {% if user_obj.email %}
                                            <br><small class="text-muted">{{ user_obj.email }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {{ user_obj.get_full_name|default:"-" }}
                                {% if user_obj.phone_number %}
                                    <br><small class="text-muted">{{ user_obj.phone_number }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if user_obj.is_superuser or user_obj.is_full_admin %}
                                    <span class="badge bg-danger">مدير النظام</span>
                                {% elif user_obj.is_admin %}
                                    <span class="badge bg-primary">مدير</span>
                                {% elif user_obj.is_staff %}
                                    <span class="badge bg-warning">مشرف</span>
                                {% else %}
                                    <span class="badge bg-secondary">مستخدم</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user_obj.is_active %}
                                    <i class="fas fa-circle user-status-active" title="نشط"></i>
                                    <small class="text-success">نشط</small>
                                {% else %}
                                    <i class="fas fa-circle user-status-inactive" title="غير نشط"></i>
                                    <small class="text-danger">معطل</small>
                                {% endif %}
                            </td>
                            <td class="user-permissions">
                                <div class="permission-summary">
                                    {% if user_obj.is_superuser or user_obj.is_full_admin %}
                                        <span class="badge bg-danger permission-badge">كامل</span>
                                    {% else %}
                                        {% with user_obj.custom_permissions.count as perm_count %}
                                            {% if perm_count > 0 %}
                                                <span class="permission-count">{{ perm_count }} وحدة</span>
                                                {% for perm in user_obj.custom_permissions.all|slice:":3" %}
                                                    <span class="badge bg-info permission-badge">{{ perm.module_name }}</span>
                                                {% endfor %}
                                                {% if perm_count > 3 %}
                                                    <span class="badge bg-secondary permission-badge">+{{ perm_count|add:"-3" }}</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-light text-dark permission-badge">لا توجد</span>
                                            {% endif %}
                                        {% endwith %}
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if user_obj.last_login %}
                                    {{ user_obj.last_login|date:"Y-m-d" }}<br>
                                    <small class="text-muted">{{ user_obj.last_login|time:"H:i" }}</small>
                                {% else %}
                                    <span class="text-muted">لم يدخل</span>
                                {% endif %}
                                <br><small class="badge bg-info">{{ user_obj.login_count }} مرة</small>
                            </td>
                            <td>
                                <div class="quick-actions">
                                    <a href="{% url 'accounts:user_detail' user_obj.pk %}" 
                                       class="btn btn-info btn-xs" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounts:user_update' user_obj.pk %}" 
                                       class="btn btn-warning btn-xs" title="تعديل المستخدم">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounts:user_permissions_page' user_obj.id %}" 
                                       class="btn btn-primary btn-xs" 
                                       title="إدارة الصلاحيات">
                                        <i class="fas fa-key"></i>
                                    </a>
                                    {% if not user_obj.is_superuser and user_obj.username != 'admin' %}
                                        <button class="btn btn-{% if user_obj.is_active %}secondary{% else %}success{% endif %} btn-xs"
                                                onclick="toggleUserStatus({{ user_obj.id }}, {{ user_obj.is_active|yesno:'false,true' }})"
                                                title="{% if user_obj.is_active %}تعطيل{% else %}تفعيل{% endif %}">
                                            <i class="fas fa-{% if user_obj.is_active %}ban{% else %}check{% endif %}"></i>
                                        </button>
                                        <a href="{% url 'accounts:user_delete' user_obj.pk %}" 
                                           class="btn btn-danger btn-xs" title="حذف المستخدم"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <h5>لا يوجد مستخدمين</h5>
                                    <p>لم يتم العثور على أي مستخدمين في النظام</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Permission Management Modal -->
<div class="modal fade" id="permissionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key text-primary"></i>
                    إدارة صلاحيات المستخدم: <span id="modalUsername"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="permissionContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل الصلاحيات...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">
                    <i class="fas fa-save"></i>
                    حفظ الصلاحيات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Permission Management Modal -->
<div class="modal fade" id="bulkPermissionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-users-cog text-primary"></i>
                    إدارة الصلاحيات الجماعية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="bulkPermissionContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل واجهة الإدارة الجماعية...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="applyBulkPermissions()">
                    <i class="fas fa-users"></i>
                    تطبيق على المحددين
                </button>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
let currentUserId = null;
let selectedUsers = [];

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedUsers();
}

// Update selected users array
function updateSelectedUsers() {
    selectedUsers = [];
    document.querySelectorAll('.user-checkbox:checked').forEach(checkbox => {
        selectedUsers.push(parseInt(checkbox.value));
    });
    
    // Update bulk management button visibility and text
    updateBulkManagementButton();
}

// Update bulk management button based on selection
function updateBulkManagementButton() {
    const bulkButton = document.querySelector('button[onclick="bulkPermissionUpdate()"]');
    const selectedCount = selectedUsers.length;
    
    if (bulkButton) {
        if (selectedCount > 0) {
            bulkButton.innerHTML = `
                <i class="fas fa-users-cog"></i>
                إدارة جماعية (${selectedCount})
            `;
            bulkButton.classList.remove('btn-success');
            bulkButton.classList.add('btn-primary');
            bulkButton.disabled = false;
        } else {
            bulkButton.innerHTML = `
                <i class="fas fa-users-cog"></i>
                إدارة جماعية
            `;
            bulkButton.classList.remove('btn-primary');
            bulkButton.classList.add('btn-success');
            bulkButton.disabled = false;
        }
    }
}

// Add event listeners to checkboxes
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedUsers);
        
        // Add visual feedback on click
        checkbox.addEventListener('click', function() {
            this.closest('tr').classList.toggle('table-primary', this.checked);
        });
    });
    
    // Initialize bulk management button state
    updateBulkManagementButton();
    
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = true;
                toggleSelectAll();
            }
        }
    });
});

// Manage individual user permissions
function managePermissions(userId, username) {
    currentUserId = userId;
    document.getElementById('modalUsername').textContent = username;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
    modal.show();
    
    // Load permissions
    loadUserPermissions(userId);
}

// Load user permissions via AJAX
function loadUserPermissions(userId) {
    console.log('Loading permissions for user:', userId);
    
    // Show loading spinner
    document.getElementById('permissionContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل الصلاحيات...</p>
        </div>
    `;
    
    fetch(`/accounts/user/${userId}/permissions/`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            document.getElementById('permissionContent').innerHTML = data.html;
            
            // Initialize permission management after loading
            initializePermissionManagement();
        } else {
            document.getElementById('permissionContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${data.message || 'خطأ في تحميل الصلاحيات'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading permissions:', error);
        document.getElementById('permissionContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                حدث خطأ في تحميل الصلاحيات: ${error.message}
            </div>
        `;
    });
}

// Initialize permission management functionality
function initializePermissionManagement() {
    console.log('Initializing permission management...');
    
    // Add event listeners for permission presets
    document.querySelectorAll('.preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const presetName = this.dataset.preset;
            applyPermissionPreset(presetName);
        });
    });
    
    // Add event listeners for module toggles
    document.querySelectorAll('.module-toggle').forEach((toggle, index) => {
        const module = toggle.closest('.permission-module');
        const moduleName = toggle.dataset.module || module.dataset.module;
        
        console.log(`Setting up toggle ${index + 1} for module: ${moduleName}`);
        
        // Remove any existing event listeners to prevent duplicates
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);
        
        // Add new event listener to the cloned element
        newToggle.addEventListener('change', function(e) {
            console.log(`Toggle event fired for ${moduleName}: ${this.checked}`);
            handleModuleToggle(e, moduleName);
        });
        
        // Also add click event listener as backup
        newToggle.addEventListener('click', function(e) {
            console.log(`Toggle clicked for ${moduleName}: ${this.checked}`);
            // Small delay to ensure the checked state is updated
            setTimeout(() => {
                handleModuleToggle(e, moduleName);
            }, 10);
        });
        
        // Initialize visual state based on current state
        updateModuleVisualState(module, newToggle.checked);
    });
    
    // Add event listeners for select all pages buttons
    document.querySelectorAll('.select-all-pages').forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleName = this.dataset.module;
            selectAllPages(moduleName);
        });
    });
    
    // Add event listeners for clear all pages buttons
    document.querySelectorAll('.clear-all-pages').forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleName = this.dataset.module;
            clearAllPages(moduleName);
        });
    });
}

// Handle module toggle change
function handleModuleToggle(event, moduleName) {
    const toggle = event.target;
    const isEnabled = toggle.checked;
    
    console.log(`Module toggle changed: ${moduleName} = ${isEnabled}`);
    console.log(`Toggle element:`, toggle);
    console.log(`Toggle checked state:`, toggle.checked);
    
    // Prevent event bubbling
    event.stopPropagation();
    event.preventDefault();
    
    // Ensure the toggle state is correct
    toggle.checked = isEnabled;
    
    // Toggle module permissions
    toggleModulePermissions(moduleName, isEnabled);
    
    // Force visual update
    const module = toggle.closest('.permission-module');
    if (module) {
        updateModuleVisualState(module, isEnabled);
    }
}

// Apply permission preset
function applyPermissionPreset(presetName) {
    console.log('Applying preset:', presetName);
    
    const presets = {
        'full_admin': {
            permissions: ['can_view', 'can_add', 'can_edit', 'can_delete'],
            modules: 'all'
        },
        'admin': {
            permissions: ['can_view', 'can_add', 'can_edit'],
            modules: 'all'
        },
        'supervisor': {
            permissions: ['can_view', 'can_add', 'can_edit'],
            excludeModules: ['accounts', 'backup', 'system_logs']
        },
        'user': {
            permissions: ['can_view', 'can_add'],
            excludeModules: ['accounts', 'backup', 'system_logs', 'disciplinary']
        },
        'readonly': {
            permissions: ['can_view'],
            excludeModules: ['accounts', 'backup', 'system_logs']
        },
        'clear': {
            permissions: [],
            modules: 'none'
        }
    };
    
    const preset = presets[presetName];
    if (!preset) return;
    
    // Clear all permissions first
    document.querySelectorAll('.permission-module').forEach(module => {
        const moduleName = module.dataset.module;
        const moduleToggle = module.querySelector('.module-toggle');
        
        // Clear all checkboxes
        module.querySelectorAll('input[type="checkbox"]').forEach(cb => {
            cb.checked = false;
        });
        
        // Update module visual state to disabled
        updateModuleVisualState(module, false);
        
        // Skip if preset is 'clear' or module is excluded
        if (preset.modules === 'none' || 
            (preset.excludeModules && preset.excludeModules.includes(moduleName))) {
            return;
        }
        
        // Enable module toggle
        if (moduleToggle) {
            moduleToggle.checked = true;
            updateModuleVisualState(module, true);
        }
        
        // Apply permissions
        preset.permissions.forEach(permission => {
            const checkbox = module.querySelector(`.${permission.replace('can_', 'can-')}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // Select all pages if view permission is granted
        if (preset.permissions.includes('can_view')) {
            module.querySelectorAll('.page-checkbox').forEach(pageCheckbox => {
                pageCheckbox.checked = true;
            });
        }
    });
}

// Toggle module permissions
function toggleModulePermissions(moduleName, enabled) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    console.log(`Toggling module ${moduleName}: ${enabled ? 'enabled' : 'disabled'}`);
    
    // Update module visual state
    updateModuleVisualState(module, enabled);
    
    if (enabled) {
        // Enable view permission by default
        const viewCheckbox = module.querySelector('.can-view');
        if (viewCheckbox) {
            viewCheckbox.checked = true;
            console.log(`Enabled view permission for ${moduleName}`);
        }
        
        // Select all pages
        const pageCheckboxes = module.querySelectorAll('.page-checkbox');
        pageCheckboxes.forEach(cb => {
            cb.checked = true;
        });
        console.log(`Selected ${pageCheckboxes.length} pages for ${moduleName}`);
        
    } else {
        // Disable all permissions except the module toggle
        const checkboxes = module.querySelectorAll('input[type="checkbox"]:not(.module-toggle)');
        checkboxes.forEach(cb => {
            cb.checked = false;
        });
        console.log(`Disabled all permissions for ${moduleName}`);
    }
}

// Update module visual state
function updateModuleVisualState(module, enabled) {
    console.log(`Updating visual state for module: ${module.dataset.module} = ${enabled}`);
    
    if (enabled) {
        module.classList.remove('module-disabled');
        module.classList.add('module-enabled');
        
        // Enable all form elements in the module
        const formElements = module.querySelectorAll('input, button, select, textarea');
        formElements.forEach(element => {
            if (!element.classList.contains('module-toggle')) {
                element.disabled = false;
            }
        });
        
        // Remove disabled styling from card body
        const cardBody = module.querySelector('.card-body');
        if (cardBody) {
            cardBody.style.pointerEvents = 'auto';
            cardBody.style.opacity = '1';
        }
        
    } else {
        module.classList.remove('module-enabled');
        module.classList.add('module-disabled');
        
        // Disable all form elements in the module except the toggle
        const formElements = module.querySelectorAll('input, button, select, textarea');
        formElements.forEach(element => {
            if (!element.classList.contains('module-toggle')) {
                element.disabled = true;
            }
        });
        
        // Add disabled styling to card body
        const cardBody = module.querySelector('.card-body');
        if (cardBody) {
            cardBody.style.pointerEvents = 'none';
            cardBody.style.opacity = '0.5';
        }
    }
}

// Select all pages for a module
function selectAllPages(moduleName) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    module.querySelectorAll('.page-checkbox').forEach(cb => {
        cb.checked = true;
    });
}

// Clear all pages for a module
function clearAllPages(moduleName) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    module.querySelectorAll('.page-checkbox').forEach(cb => {
        cb.checked = false;
    });
}

// Save user permissions
function savePermissions() {
    if (!currentUserId) return;
    
    const formData = new FormData();
    
    // Collect all permission data
    document.querySelectorAll('.permission-module').forEach(module => {
        const moduleName = module.dataset.module;
        const canView = module.querySelector('.can-view').checked;
        const canAdd = module.querySelector('.can-add').checked;
        const canEdit = module.querySelector('.can-edit').checked;
        const canDelete = module.querySelector('.can-delete').checked;
        
        // Collect visible pages
        const visiblePages = [];
        module.querySelectorAll('.page-checkbox:checked').forEach(pageCheckbox => {
            visiblePages.push(pageCheckbox.value);
        });
        
        formData.append(`${moduleName}_can_view`, canView);
        formData.append(`${moduleName}_can_add`, canAdd);
        formData.append(`${moduleName}_can_edit`, canEdit);
        formData.append(`${moduleName}_can_delete`, canDelete);
        formData.append(`${moduleName}_pages`, visiblePages.join(','));
    });
    
    // Add CSRF token
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch(`/accounts/user/${currentUserId}/permissions/save/`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('permissionModal'));
            modal.hide();
            
            // Refresh page to show updated permissions
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message || 'خطأ في حفظ الصلاحيات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في حفظ الصلاحيات');
    });
}

// Toggle user active status
function toggleUserStatus(userId, newStatus) {
    const action = newStatus ? 'تفعيل' : 'تعطيل';
    
    if (!confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
        return;
    }
    
    fetch(`/accounts/user/${userId}/toggle-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            is_active: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Update UI
            const row = document.getElementById(`user-row-${userId}`);
            const statusCell = row.querySelector('td:nth-child(5)');
            const actionButton = row.querySelector(`button[onclick*="toggleUserStatus(${userId}"]`);
            
            if (newStatus) {
                statusCell.innerHTML = '<i class="fas fa-circle user-status-active" title="نشط"></i><small class="text-success">نشط</small>';
                actionButton.className = 'btn btn-secondary btn-xs';
                actionButton.innerHTML = '<i class="fas fa-ban"></i>';
                actionButton.title = 'تعطيل';
                actionButton.setAttribute('onclick', `toggleUserStatus(${userId}, false)`);
            } else {
                statusCell.innerHTML = '<i class="fas fa-circle user-status-inactive" title="غير نشط"></i><small class="text-danger">معطل</small>';
                actionButton.className = 'btn btn-success btn-xs';
                actionButton.innerHTML = '<i class="fas fa-check"></i>';
                actionButton.title = 'تفعيل';
                actionButton.setAttribute('onclick', `toggleUserStatus(${userId}, true)`);
            }
        } else {
            showAlert('danger', data.message || 'خطأ في تحديث حالة المستخدم');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في تحديث حالة المستخدم');
    });
}

// Bulk permission management
function bulkPermissionUpdate() {
    updateSelectedUsers();
    
    if (selectedUsers.length === 0) {
        showAlert('warning', 'يرجى اختيار مستخدم واحد على الأقل');
        return;
    }
    
    // Show bulk permission modal
    const modal = new bootstrap.Modal(document.getElementById('bulkPermissionModal'));
    modal.show();
    
    // Load bulk permission interface
    loadBulkPermissionInterface();
}

// Load bulk permission interface
function loadBulkPermissionInterface() {
    fetch('/accounts/bulk-permissions/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            user_ids: selectedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('bulkPermissionContent').innerHTML = data.html;
        } else {
            showAlert('danger', data.message || 'خطأ في تحميل واجهة الصلاحيات الجماعية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في تحميل واجهة الصلاحيات الجماعية');
    });
}

// Apply bulk permissions
function applyBulkPermissions() {
    // Implementation for bulk permission application
    showAlert('info', 'جاري تطبيق الصلاحيات على المستخدمين المحددين...');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkPermissionModal'));
    modal.hide();
}

// Show alert messages
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Module permission toggle functions
function toggleModulePermissions(moduleName, enabled) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    const checkboxes = module.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = enabled;
        checkbox.disabled = !enabled;
    });
}

function toggleAllPages(moduleName, enabled) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    const pageCheckboxes = module.querySelectorAll('.page-checkbox');
    pageCheckboxes.forEach(checkbox => {
        checkbox.checked = enabled;
    });
}
</script>
{% endblock %}
