/* Light Mode CSS */

/*
 * هذا الملف يحتوي على الأنماط الافتراضية للوضع النهاري
 * هذه الأنماط تطبق عندما لا تكون فئة dark-mode موجودة على عنصر body
 *
 * ملاحظة مهمة: هذه الأنماط تستخدم فقط كمرجع للأنماط الأصلية
 * عند العودة من الوضع المظلم إلى الوضع النهاري، سيتم إزالة جميع الأنماط المضافة
 * وسيعود النظام إلى الأنماط الأصلية المحددة في ملفات CSS الأخرى
 */

/* Base styles for light mode */
:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --text-color: #212529;
    --border-color: #dee2e6;
    --background-color: #f8f9fa;
    --card-bg: #ffffff;
    --card-header-bg: #f8f9fa;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-text: #212529;
    --btn-light-bg: #f8f9fa;
    --btn-light-border: #dee2e6;
    --btn-light-text: #212529;
    --table-border: #dee2e6;
    --table-stripe: rgba(0, 0, 0, 0.05);
    --text-muted: #6c757d;
}

/* Navbar styles */
.navbar {
    background-color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand,
.nav-link,
.navbar .dropdown-toggle {
    color: #fff;
}

.navbar .username {
    color: #fff;
}

.navbar .account-type {
    color: rgba(255, 255, 255, 0.7);
}

/* Sidebar styles */
.sidebar {
    background: linear-gradient(to bottom, #000000, #333333);
    border-right: 1px solid var(--border-color);
}

.sidebar-link {
    color: #fff;
}

.sidebar-link:hover,
.sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-link i {
    color: rgba(255, 255, 255, 0.7);
}

/* Search bar in light mode */
.navbar-search .form-control {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: #fff;
}

.navbar-search .form-control:focus {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.3);
}

.navbar-search .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.navbar-search .search-icon {
    color: rgba(255, 255, 255, 0.7);
}

/* Dark mode toggle in light mode */
.dark-mode-toggle {
    color: #fff;
}

.dark-mode-toggle:hover {
    background-color: rgba(0, 0, 0, 0.2);
}
