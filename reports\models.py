from django.db import models
from django.utils.translation import gettext_lazy as _

class Report(models.Model):
    """Model for storing generated reports"""
    ATTENDANCE = 'attendance'
    EMPLOYMENT = 'employment'
    EMPLOYEE = 'employee'
    DEPARTMENT = 'department'
    LEAVE = 'leave'
    STAFF = 'staff'
    TECHNICAL_POSITION = 'technical_position'
    POSITION = 'position'
    SPECIALIZATION = 'specialization'
    RANKS = 'ranks'
    FILES = 'files'
    PENALTIES = 'penalties'
    POSITIONS_MOVEMENT = 'positions_movement'

    REPORT_TYPE_CHOICES = [
        (ATTENDANCE, _('تقرير الحضور')),
        (EMPLOYMENT, _('تقرير التوظيف')),
        (EMPLOYEE, _('تقرير الموظف')),
        (DEPARTMENT, _('تقرير القسم')),
        (LEAVE, _('تقرير الإجازات')),
        (STAFF, _('تقرير الكادر')),
        (TECHNICAL_POSITION, _('تقرير الموقف الفني')),
        (POSITION, _('تقرير المناصب')),
        (SPECIALIZATION, _('تقرير التخصصات')),
        (RANKS, _('تقرير الرتب')),
        (FILES, _('تقرير الملفات')),
        (PENALTIES, _('تقرير العقوبات')),
        (POSITIONS_MOVEMENT, _('تقرير الحراك الوظيفي')),
    ]

    title = models.CharField(_('Title'), max_length=255)
    report_type = models.CharField(_('Report Type'), max_length=20, choices=REPORT_TYPE_CHOICES)
    parameters = models.JSONField(_('Parameters'), blank=True, null=True)
    file = models.FileField(_('File'), upload_to='reports/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(_('Created By'), max_length=255, blank=True, null=True)

    class Meta:
        verbose_name = _('Report')
        verbose_name_plural = _('Reports')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.get_report_type_display()}) - {self.created_at.strftime('%Y-%m-%d')}"
