{% extends 'base.html' %}
{% load static %}

{% block title %}الحالات المرضية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Card Styles */
    .dashboard-card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        letter-spacing: 0.05em;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
    }

    .rounded-circle {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content_css %}
<style>
    .filters-container {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
        box-shadow: 0 3px 6px rgba(0,0,0,0.08);
    }

    .filter-control {
        font-size: 1.1rem;
        padding: 10px 15px;
        height: 48px;
        border: 1px solid #ced4da;
        border-radius: 8px;
        margin-bottom: 0;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-filter {
        padding: 10px 15px;
        height: 48px;
        font-size: 1.1rem;
        border-radius: 8px;
    }

    .select2-container--bootstrap-5 .select2-selection {
        height: 48px !important;
        padding: 10px 15px !important;
        font-size: 1.1rem !important;
        border-radius: 8px !important;
    }

    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
        line-height: 26px !important;
    }

    .select2-container--bootstrap-5 .select2-dropdown {
        font-size: 1.15rem !important;
    }

    .select2-container--bootstrap-5 .select2-results__option {
        padding: 10px 15px !important;
    }

    .form-label {
        font-size: 1.1rem;
        color: #495057;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الحالات المرضية</h2>
    <div>
        <a href="{% url 'employment:medical_condition_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة حالة مرضية جديدة
        </a>
        <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-info">
            <i class="fas fa-list-alt"></i> أسماء الحالات المرضية
        </a>
        <a href="{% url 'employment:medical_condition_list' %}?export_excel=1" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي الحالات المرضية</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ total_count }}</div>
                        <div class="text-muted small mt-2">العدد الكلي للحالات المرضية المسجلة</div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-notes-medical fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">الأقسام</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ departments_count }}</div>
                        <div class="text-muted small mt-2">عدد الأقسام التي يوجد فيها حالات مرضية</div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-building fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<form method="GET" action="{% url 'employment:medical_condition_list' %}">
    <div class="filters-container">
        <div class="row align-items-end">
            <!-- Search Form -->
            <div class="col-md-3">
                <label for="search_term" class="form-label fw-bold mb-2">بحث:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search_term" name="search_term" placeholder="بحث بالرقم الوزاري أو الاسم..." value="{{ search_form.search_term.value|default:'' }}">
                    <button class="btn btn-secondary" type="button" id="searchButton">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Condition Filter -->
            <div class="col-md-3">
                <label for="condition" class="form-label fw-bold mb-2">الحالة المرضية:</label>
                <select class="form-select select2" id="condition" name="condition">
                    <option value="">جميع الحالات المرضية</option>
                    {% for condition in search_form.condition.field.queryset %}
                    <option value="{{ condition.id }}" {% if search_form.condition.value|stringformat:'s' == condition.id|stringformat:'s' %}selected{% endif %}>{{ condition.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Department Filter -->
            <div class="col-md-2">
                <label for="department" class="form-label fw-bold mb-2">القسم:</label>
                <select class="form-select select2" id="department" name="department">
                    <option value="">جميع الأقسام</option>
                    {% for dept in search_form.department.field.queryset %}
                    <option value="{{ dept.id }}" {% if search_form.department.value|stringformat:'s' == dept.id|stringformat:'s' %}selected{% endif %}>{{ dept.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Position Filter -->
            <div class="col-md-2">
                <label for="position" class="form-label fw-bold mb-2">المسمى الوظيفي:</label>
                <select class="form-select select2" id="position" name="position">
                    <option value="">جميع المسميات</option>
                    {% for pos in search_form.position.field.queryset %}
                    <option value="{{ pos.id }}" {% if search_form.position.value|stringformat:'s' == pos.id|stringformat:'s' %}selected{% endif %}>{{ pos.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Action Buttons -->
            <div class="col-md-2">
                <div class="d-flex">
                    <button type="submit" class="btn btn-secondary me-2 flex-grow-1" title="تطبيق الفلاتر">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                    <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary flex-grow-1" title="إعادة تعيين الفلاتر">
                        <i class="fas fa-redo"></i> إعادة ضبط
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Medical Conditions Table -->
<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-hashtag me-1"></i> #</th>
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                        <th><i class="fas fa-notes-medical me-1"></i> الحالة المرضية</th>
                        <th><i class="fas fa-building me-1"></i> القسم</th>
                        <th><i class="fas fa-briefcase me-1"></i> المسمى الوظيفي</th>
                        <th><i class="fas fa-calendar-alt me-1"></i> تاريخ التقرير الطبي</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for condition in medical_conditions %}
                    <tr class="text-center">
                        <td>{{ forloop.counter }}</td>
                        <td>{{ condition.employee.ministry_number }}</td>
                        <td>{{ condition.employee.full_name }}</td>
                        <td>{{ condition.condition.name }}</td>
                        <td>
                            {% with current_employment=condition.employee.employments.filter|first %}
                                {% if current_employment and current_employment.department %}
                                    {{ current_employment.department.name }}
                                {% elif condition.employee.school %}
                                    {{ condition.employee.school.name }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            {% endwith %}
                        </td>
                        <td>
                            {% if condition.employee.positions.all %}
                                {% with latest_position=condition.employee.positions.all|dictsort:"date_obtained"|last %}
                                    {{ latest_position.position.name }}
                                {% endwith %}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                        <td>{{ condition.medical_report_date|date:"Y-m-d" }}</td>
                        <td>
                            <a href="{% url 'employment:medical_condition_detail' condition.pk %}" class="btn btn-info btn-sm" title="عرض">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a href="{% url 'employment:medical_condition_update' condition.pk %}" class="btn btn-warning btn-sm" title="تعديل">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'employment:medical_condition_delete' condition.pk %}" class="btn btn-danger btn-sm" title="حذف">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا توجد حالات مرضية مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize select2 for dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'ar',
            dir: 'rtl',
            width: '100%',
            dropdownAutoWidth: true
        });

        // Initialize DataTable
        $('.table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json"
            },
            "order": [[6, "desc"]], // Sort by date in descending order
            "searching": false,     // Disable DataTables search as we use our own
            "paging": false,        // Disable pagination as we handle filtering server-side
            "info": false           // Disable showing "Showing X of Y entries"
        });

        // Handle search button click
        $('#searchButton').on('click', function() {
            $('form').submit();
        });

        // Handle Enter key in search field
        $('#search_term').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('form').submit();
            }
        });
    });
</script>
{% endblock %}
