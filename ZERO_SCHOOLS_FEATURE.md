# ميزة المدارس الصفرية - Zero Schools Feature

## 📋 نظرة عامة
تم إضافة ميزة جديدة لإدارة "المدارس الصفرية" - وهي المدارس التي لا يوجد فيها معلمين من تخصص معين أو لا يوجد فيها مستخدمين أو حراس.

## 🎯 الهدف من الميزة
- **تتبع المدارس** التي تعاني من نقص في الكوادر
- **توثيق الشواغر** وأسباب النقص
- **متابعة الإجراءات** المتخذة لحل المشكلة
- **إنتاج تقارير** شاملة عن الوضع

## 🔧 المكونات المضافة

### 1. النموذج (Model)
**الملف:** `employment/models.py`
```python
class ZeroSchool(models.Model):
    school_name = models.CharField('اسم المدرسة', max_length=255)
    specialization = models.CharField('التخصص', max_length=20, choices=SPECIALIZATION_CHOICES)
    vacancies_count = models.PositiveIntegerField('عدد الشواغر')
    justification = models.TextField('المبرر')
    actions = models.TextField('الإجراءات', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### 2. النماذج (Forms)
**الملف:** `employment/forms.py`
- `ZeroSchoolForm` - نموذج إضافة وتعديل المدارس الصفرية

### 3. العروض (Views)
**الملف:** `employment/views.py`
- `zero_schools_list()` - عرض قائمة المدارس الصفرية
- `zero_school_create()` - إضافة مدرسة صفرية جديدة
- `zero_school_edit()` - تعديل مدرسة صفرية
- `zero_school_delete()` - حذف مدرسة صفرية
- `export_zero_schools_excel()` - تصدير البيانات إلى Excel

### 4. الروابط (URLs)
**الملف:** `employment/urls.py`
```python
# Zero Schools URLs
path('zero-schools/', views.zero_schools_list, name='zero_schools_list'),
path('zero-schools/add/', views.zero_school_create, name='zero_school_create'),
path('zero-schools/<int:pk>/edit/', views.zero_school_edit, name='zero_school_edit'),
path('zero-schools/<int:pk>/delete/', views.zero_school_delete, name='zero_school_delete'),
path('zero-schools/export-excel/', views.export_zero_schools_excel, name='export_zero_schools_excel'),
```

### 5. القوالب (Templates)
- `templates/employment/zero_schools_list.html` - صفحة قائمة المدارس الصفرية
- `templates/employment/zero_school_form.html` - صفحة إضافة/تعديل مدرسة صفرية
- `templates/employment/zero_school_confirm_delete.html` - صفحة تأكيد الحذف

## 🎨 واجهة المستخدم

### 1. زر الوصول
تم إضافة زر "المدارس الصفرية" في صفحة الموقف الفني:
```html
<a href="{% url 'employment:zero_schools_list' %}" class="btn btn-warning me-2">
    <i class="fas fa-school"></i> المدارس الصفرية
</a>
```

### 2. صفحة القائمة الرئيسية
**المسار:** `/employment/zero-schools/`

**المميزات:**
- ✅ **عرض جدولي** لجميع المدارس الصفرية
- ✅ **بحث متقدم** في اسم المدرسة والمبرر
- ✅ **فلترة حسب التخصص** (معلم/مستخدم/حارس)
- ✅ **إحصائيات سريعة** (إجمالي المدارس والشواغر)
- ✅ **أزرار العمليات** (تعديل/حذف)

### 3. صفحة الإضافة/التعديل
**المسار:** `/employment/zero-schools/add/` أو `/employment/zero-schools/<id>/edit/`

**الحقول:**
- **اسم المدرسة** (مطلوب)
- **التخصص** (معلم/مستخدم/حارس)
- **عدد الشواغر** (رقم موجب)
- **المبرر** (نص مطلوب)
- **الإجراءات** (نص اختياري)

### 4. تصدير Excel
**المسار:** `/employment/zero-schools/export-excel/`

**المحتوى:**
- اسم المدرسة
- التخصص
- عدد الشواغر
- المبرر
- الإجراءات
- تاريخ الإضافة

## 📊 أنواع التخصصات

### 1. معلم (Teacher)
- **الأيقونة:** 👨‍🏫 `fas fa-chalkboard-teacher`
- **اللون:** أزرق `bg-primary`
- **الوصف:** نقص في المعلمين من تخصص معين

### 2. مستخدم (User)
- **الأيقونة:** 👤 `fas fa-user`
- **اللون:** سماوي `bg-info`
- **الوصف:** نقص في المستخدمين (النظافة، الخدمات)

### 3. حارس (Guard)
- **الأيقونة:** 🛡️ `fas fa-shield-alt`
- **اللون:** أصفر `bg-warning`
- **الوصف:** نقص في الحراس الأمنيين

## 🔍 مميزات البحث والفلترة

### البحث النصي:
- البحث في **اسم المدرسة**
- البحث في **المبرر**
- البحث في **الإجراءات**

### الفلترة:
- **حسب التخصص:** جميع التخصصات أو تخصص محدد
- **الترتيب:** حسب اسم المدرسة والتخصص

## 📈 الإحصائيات

### البطاقات الإحصائية:
1. **إجمالي المدارس الصفرية**
2. **إجمالي الشواغر**

### التقارير:
- **تصدير Excel** مع جميع البيانات
- **فلترة التقارير** حسب معايير البحث

## 🛡️ الأمان والصلاحيات

### متطلبات الوصول:
- ✅ **تسجيل الدخول مطلوب** `@login_required`
- ✅ **حماية CSRF** في جميع النماذج
- ✅ **التحقق من صحة البيانات** قبل الحفظ

### التحقق من البيانات:
- **اسم المدرسة:** مطلوب، حد أقصى 255 حرف
- **التخصص:** اختيار من قائمة محددة
- **عدد الشواغر:** رقم موجب مطلوب
- **المبرر:** نص مطلوب
- **منع التكرار:** نفس المدرسة والتخصص

## 🎯 حالات الاستخدام

### 1. إضافة مدرسة صفرية جديدة
```
المستخدم → صفحة الموقف الفني → زر "المدارس الصفرية" → زر "إضافة مدرسة صفرية" → ملء النموذج → حفظ
```

### 2. البحث عن مدارس معينة
```
المستخدم → صفحة المدارس الصفرية → كتابة في مربع البحث → اختيار التخصص → زر "بحث"
```

### 3. تصدير التقرير
```
المستخدم → صفحة المدارس الصفرية → (اختياري: فلترة البيانات) → زر "تصدير إلى إكسل"
```

### 4. تعديل بيانات مدرسة
```
المستخدم → صفحة المدارس الصفرية → زر "تعديل" → تعديل البيانات → حفظ التغييرات
```

## 📱 التصميم المتجاوب

### الألوان والأيقونات:
- **اللون الأساسي:** أصفر تحذيري `btn-warning`
- **الأيقونة الرئيسية:** 🏫 `fas fa-school`
- **تدرج الألوان:** متناسق مع باقي النظام

### التخطيط:
- **متجاوب** مع جميع أحجام الشاشات
- **جداول قابلة للتمرير** على الشاشات الصغيرة
- **أزرار واضحة** ومتاحة

## 🔄 التكامل مع النظام

### الربط مع الموقف الفني:
- **زر مباشر** من صفحة الموقف الفني
- **تكامل في التصميم** والألوان
- **سهولة التنقل** بين الصفحات

### قاعدة البيانات:
- **جدول منفصل** `employment_zeroschool`
- **فهارس محسنة** للبحث السريع
- **قيود التكامل** لمنع البيانات المكررة

## 🚀 الاستخدام العملي

### للإدارة:
1. **متابعة النقص** في الكوادر
2. **تخطيط التعيينات** الجديدة
3. **إنتاج التقارير** للجهات العليا

### للموارد البشرية:
1. **تحديد الأولويات** في التعيين
2. **توثيق الإجراءات** المتخذة
3. **متابعة التقدم** في حل المشاكل

## 📋 قائمة المهام المكتملة

- ✅ إنشاء النموذج والجداول
- ✅ تطوير النماذج والتحقق
- ✅ إنشاء العروض والمنطق
- ✅ تصميم القوالب والواجهات
- ✅ إضافة الروابط والتنقل
- ✅ تطبيق الأمان والصلاحيات
- ✅ إنشاء ميزة التصدير
- ✅ إضافة لوحة الإدارة
- ✅ إنشاء البيانات التجريبية
- ✅ اختبار النظام

## 🎉 النتيجة النهائية

تم إنشاء نظام شامل لإدارة المدارس الصفرية يتضمن:

### الصفحات الجديدة:
1. **📋 قائمة المدارس الصفرية** - `/employment/zero-schools/`
2. **➕ إضافة مدرسة صفرية** - `/employment/zero-schools/add/`
3. **✏️ تعديل مدرسة صفرية** - `/employment/zero-schools/<id>/edit/`
4. **🗑️ حذف مدرسة صفرية** - `/employment/zero-schools/<id>/delete/`
5. **📊 تصدير Excel** - `/employment/zero-schools/export-excel/`

### المميزات الرئيسية:
- ✅ **واجهة سهلة الاستخدام**
- ✅ **بحث وفلترة متقدمة**
- ✅ **إحصائيات فورية**
- ✅ **تصدير شامل للبيانات**
- ✅ **تصميم متجاوب**
- ✅ **أمان عالي**

---
**تاريخ الإنشاء:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")  
**المطور:** نظام إدارة الموارد البشرية  
**الحالة:** ✅ مكتمل وجاهز للاستخدام