from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, update_session_auth_hash
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
import json
# from django.urls import reverse  # Not used
from .models import User, UserPermission
from .forms import Custom<PERSON>serCreationForm, CustomUserChangeForm, UserPermissionForm, AdminPasswordChangeForm


def update_user_admin_status(user, permissions=None, request=None):
    """
    Update user's admin status based on their permissions.
    A user is considered a full admin if they have full permissions for critical modules.

    Args:
        user: The user to update
        permissions: Optional list of UserPermission objects to check
        request: Optional request object to update session

    Returns:
        bool: True if user is now a full admin, False otherwise
    """
    # Store original status to check if it changed
    original_is_full_admin = user.is_full_admin
    original_is_admin = user.is_admin

    # If the user is already marked as a full admin in the form, respect that setting
    if hasattr(user, '_is_full_admin_from_form'):
        if user._is_full_admin_from_form:
            print(f"User {user.username} is marked as full admin from form")
            user.is_full_admin = True
            user.is_admin = True
            user.save()

            # If this is the current user and status changed, update session
            if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
                print(f"Updating session for current user {user.username}")
                # Reload permissions in session
                reload_user_permissions(request)
                # Force session save
                request.session.save()

            return True
        else:
            # If explicitly set to False from form, respect that too
            print(f"User {user.username} is explicitly NOT marked as full admin from form")
            user.is_full_admin = False
            # Keep is_admin status as it is
            user.save()

            # If this is the current user and status changed, update session
            if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
                print(f"Updating session for current user {user.username}")
                # Reload permissions in session
                reload_user_permissions(request)
                # Force session save
                request.session.save()

            return False

    # Define critical modules that indicate full admin status
    critical_modules = ['accounts', 'backup', 'system_logs']

    # If no permissions provided, get them from the database
    if permissions is None:
        permissions = UserPermission.objects.filter(user=user)

    # If user has no permissions at all, they're definitely not a full admin
    if isinstance(permissions, list):
        has_permissions = len(permissions) > 0
    else:
        has_permissions = permissions.exists()
        
    if not has_permissions:
        print(f"User {user.username} has no permissions at all, not a full admin")
        user.is_full_admin = False
        user.save()

        # If this is the current user and status changed, update session
        if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
            print(f"Updating session for current user {user.username}")
            # Reload permissions in session
            reload_user_permissions(request)
            # Force session save
            request.session.save()

        return False

    # Create a permission form to get all possible pages
    permission_form = UserPermissionForm()
    all_pages_choices = permission_form.get_pages_choices()

    # Check if user has full permissions for all critical modules
    for module in critical_modules:
        # Get all pages for this module
        all_module_pages = [page[0] for page in all_pages_choices if page[0].startswith(f'{module}:')]

        # Find the permission for this module
        if isinstance(permissions, list):
            module_perm = next((p for p in permissions if p.module_name == module), None)
        else:
            module_perm = permissions.filter(module_name=module).first()

        # If user doesn't have permission for this module, they're not a full admin
        if not module_perm:
            print(f"User {user.username} is not a full admin: missing permissions for {module}")
            user.is_full_admin = False
            user.save()

            # If this is the current user and status changed, update session
            if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
                print(f"Updating session for current user {user.username}")
                # Reload permissions in session
                reload_user_permissions(request)
                # Force session save
                request.session.save()

            return False

        # If user doesn't have all CRUD permissions, they're not a full admin
        if not (module_perm.can_view and module_perm.can_add and module_perm.can_edit and module_perm.can_delete):
            print(f"User {user.username} is not a full admin: incomplete CRUD permissions for {module}")
            user.is_full_admin = False
            user.save()

            # If this is the current user and status changed, update session
            if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
                print(f"Updating session for current user {user.username}")
                # Reload permissions in session
                reload_user_permissions(request)
                # Force session save
                request.session.save()

            return False

        # If user doesn't have all pages for this module, they're not a full admin
        if module_perm.visible_pages:
            module_pages = module_perm.visible_pages.split(',')
            # Filter out empty strings
            module_pages = [page for page in module_pages if page.strip()]
            if set(module_pages) != set(all_module_pages):
                print(f"User {user.username} is not a full admin: incomplete page access for {module}")
                print(f"User has pages: {module_pages}")
                print(f"Required pages: {all_module_pages}")
                user.is_full_admin = False
                user.save()

                # If this is the current user and status changed, update session
                if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
                    print(f"Updating session for current user {user.username}")
                    # Reload permissions in session
                    reload_user_permissions(request)
                    # Force session save
                    request.session.save()

                return False

    # If we get here, user has full permissions for all critical modules
    print(f"User {user.username} is now a full admin based on permissions")
    user.is_full_admin = True
    user.is_admin = True
    user.save()

    # If this is the current user and status changed, update session
    if request and request.user.id == user.id and (original_is_full_admin != user.is_full_admin or original_is_admin != user.is_admin):
        print(f"Updating session for current user {user.username}")
        # Reload permissions in session
        reload_user_permissions(request)
        # Force session save
        request.session.save()

    return True

def reload_user_permissions(request, user_id=None):
    """
    Reload user permissions in the session.
    This is useful when permissions are changed and we want to update the session.

    Args:
        request: The request object
        user_id: Optional user ID to reload permissions for (defaults to request.user)
    """
    if not request.user.is_authenticated:
        return

    # If user_id is provided and it's not the current user, do nothing
    if user_id and request.user.id != user_id:
        return

    # Always clear existing permissions from session to ensure fresh data
    if 'user_visible_pages' in request.session:
        del request.session['user_visible_pages']
        print(f"reload_user_permissions: Cleared permissions from session for user {request.user.username}")

    # Force a database refresh of the user object to get the latest status
    from django.contrib.auth import get_user_model
    User = get_user_model()
    fresh_user = User.objects.get(pk=request.user.pk)

    # Update the request.user with fresh data
    request.user = fresh_user

    # Get all possible pages from the permission form
    from accounts.forms import UserPermissionForm
    permission_form = UserPermissionForm()
    all_pages = [choice[0] for choice in permission_form.get_pages_choices()]

    # If user is superuser or full admin, they can see all pages
    if fresh_user.is_superuser or fresh_user.is_full_admin:
        request.session['user_visible_pages'] = all_pages
        print(f"reload_user_permissions: Added {len(all_pages)} pages to session for admin user {fresh_user.username}")
    else:
        # Get user permissions from database - use fresh_user to ensure we have latest data
        user_permissions = fresh_user.custom_permissions.all().select_related()

        # If user is a regular admin, they should see all pages
        if fresh_user.is_admin:
            request.session['user_visible_pages'] = all_pages
            print(f"reload_user_permissions: Added {len(all_pages)} pages to session for regular admin user {fresh_user.username}")
        else:
            # For non-admin users, collect pages from their permissions
            visible_pages = []

            for permission in user_permissions:
                if permission.can_view and permission.visible_pages:
                    pages = permission.visible_pages.split(',')
                    # Filter out empty strings
                    pages = [page for page in pages if page.strip()]
                    visible_pages.extend(pages)
                    print(f"reload_user_permissions: User {fresh_user.username} has access to {len(pages)} pages from module {permission.module_name}")

            # Remove duplicates
            visible_pages = list(set(visible_pages))

            # Make sure 'employees:employee_list' is always included
            if 'employees:employee_list' not in visible_pages:
                visible_pages.append('employees:employee_list')
                print(f"reload_user_permissions: Added employees:employee_list to visible pages for user {fresh_user.username}")

            request.session['user_visible_pages'] = visible_pages
            print(f"reload_user_permissions: Added {len(visible_pages)} pages to session for user {fresh_user.username}")

    # Force session save to update permissions
    request.session.modified = True

    # Explicitly save the session to ensure changes are persisted
    request.session.save()

    # Log the reload
    print(f"reload_user_permissions: Completed reloading permissions for user {fresh_user.username}")

    return True

def ensure_full_admin_permissions(user, request=None):
    """
    Ensure that a user marked as full admin has all the necessary permissions.
    This creates or updates permissions for all critical modules.

    Args:
        user: The user to update permissions for
        request: Optional request object to update session
    """
    # Define critical modules that indicate full admin status
    critical_modules = ['accounts', 'backup', 'system_logs']

    # Create a permission form to get all possible pages
    permission_form = UserPermissionForm()
    all_pages_choices = permission_form.get_pages_choices()

    # For each critical module, ensure the user has full permissions
    for module in critical_modules:
        # Get all pages for this module
        all_module_pages = [page[0] for page in all_pages_choices if page[0].startswith(f'{module}:')]
        visible_pages_str = ','.join(all_module_pages)

        # Check if permission already exists
        permission, created = UserPermission.objects.get_or_create(
            user=user,
            module_name=module,
            defaults={
                'can_view': True,
                'can_add': True,
                'can_edit': True,
                'can_delete': True,
                'visible_pages': visible_pages_str
            }
        )

        if not created:
            # Update existing permission to ensure it has full access
            permission.can_view = True
            permission.can_add = True
            permission.can_edit = True
            permission.can_delete = True
            permission.visible_pages = visible_pages_str
            permission.save()

            print(f"Updated permission for full admin {user.username}: module={module}, pages={visible_pages_str}")
        else:
            print(f"Created permission for full admin {user.username}: module={module}, pages={visible_pages_str}")

    # Update user's admin status with the new permissions
    all_permissions = UserPermission.objects.filter(user=user)
    update_user_admin_status(user, all_permissions, request)

def is_admin(user):
    return user.is_staff or user.is_admin or user.is_superuser

def get_all_pages_by_module():
    """الحصول على جميع الصفحات مقسمة حسب الوحدات"""
    form = UserPermissionForm()
    all_pages = form.get_pages_choices()
    
    modules = {}
    for page_url, page_name in all_pages:
        module_name = page_url.split(':')[0]
        if module_name not in modules:
            modules[module_name] = []
        modules[module_name].append(page_url)
    
    return modules

def create_user_permissions_automatically(user, is_full_admin=False, permission_level='user'):
    """إنشاء صلاحيات شاملة للمستخدم تلقائياً"""
    
    print(f"🔄 إنشاء صلاحيات تلقائية للمستخدم: {user.username}")
    
    # التعامل مع employee_viewer بشكل خاص
    if user.username == 'employee_viewer' or user.username.endswith('_viewer'):
        print(f"🔍 إنشاء صلاحيات خاصة للمستخدم {user.username}")
        
        # حذف جميع الصلاحيات الموجودة
        UserPermission.objects.filter(user=user).delete()
        
        # إنشاء صلاحية واحدة فقط لعرض الموظفين
        UserPermission.objects.create(
            user=user,
            module_name='employees',
            can_view=True,
            can_add=False,
            can_edit=False,
            can_delete=False,
            visible_pages='employees:employee_list'
        )
        
        print(f"✅ تم إنشاء صلاحية عرض الموظفين فقط")
        return
    
    # الحصول على جميع الصفحات
    all_modules = get_all_pages_by_module()
    
    # تحديد الصلاحيات حسب المستوى
    if is_full_admin or user.is_full_admin:
        can_view = True
        can_add = True
        can_edit = True
        can_delete = True
        modules_to_add = list(all_modules.keys())
        print(f"   👑 مدير كامل - سيحصل على جميع الوحدات")
    elif user.is_admin:
        can_view = True
        can_add = True
        can_edit = True
        can_delete = False
        modules_to_add = list(all_modules.keys())
        print(f"   🔧 مدير - سيحصل على جميع الوحدات")
    else:
        # للمستخدمين العاديين، تحديد الصلاحيات حسب المستوى
        if permission_level == 'admin':
            can_view = True
            can_add = True
            can_edit = True
            can_delete = True
            modules_to_add = list(all_modules.keys())
        elif permission_level == 'supervisor':
            can_view = True
            can_add = True
            can_edit = True
            can_delete = False
            modules_to_add = [
                'employment', 'leaves', 'directorate_leaves', 'files', 'ranks',
                'performance', 'reports', 'accounts', 'backup', 'system_logs'
            ]
        elif permission_level == 'user':
            can_view = True
            can_add = True
            can_edit = False
            can_delete = False
            modules_to_add = [
                'employment', 'leaves', 'directorate_leaves', 'files', 'ranks',
                'performance', 'reports', 'accounts', 'backup', 'system_logs'
            ]
        elif permission_level == 'readonly':
            can_view = True
            can_add = False
            can_edit = False
            can_delete = False
            modules_to_add = [
                'employment', 'leaves', 'directorate_leaves', 'files', 'ranks',
                'performance', 'reports', 'accounts', 'backup', 'system_logs'
            ]
        else:
            can_view = True
            can_add = False
            can_edit = False
            can_delete = False
            modules_to_add = [
                'employment', 'leaves', 'directorate_leaves', 'files', 'ranks',
                'performance', 'reports', 'accounts', 'backup', 'system_logs'
            ]
        
        print(f"   👤 مستخدم عادي - مستوى {permission_level}")
    
    # حذف الصلاحيات الموجودة
    UserPermission.objects.filter(user=user).delete()
    print(f"   🗑️ تم حذف الصلاحيات القديمة")
    
    # إنشاء صلاحيات جديدة
    created_count = 0
    for module_name in modules_to_add:
        if module_name in all_modules:
            pages = all_modules[module_name]
            visible_pages = ','.join(pages)
            
            UserPermission.objects.create(
                user=user,
                module_name=module_name,
                can_view=can_view,
                can_add=can_add,
                can_edit=can_edit,
                can_delete=can_delete,
                visible_pages=visible_pages
            )
            
            created_count += 1
            print(f"   ✅ {module_name}: {len(pages)} صفحة")
    
    print(f"   📊 تم إنشاء {created_count} صلاحية")
    return created_count

def login_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Print debug information
        print(f"Login attempt: username={username}, password={'*' * len(password) if password else 'None'}")

        # Check if user exists
        try:
            user = User.objects.get(username=username)
            print(f"User found: {user.username}, is_active={user.is_active}")
        except User.DoesNotExist:
            print(f"User not found: {username}")
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')
            form = AuthenticationForm()
            return render(request, 'accounts/login.html', {'form': form})

        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()

            # Clear any existing session data
            request.session.flush()

            # Increment login count
            user.login_count += 1
            user.save(update_fields=['login_count'])

            # Perform login
            login(request, user)

            # Load user permissions into session
            reload_user_permissions(request)
            # Force session save
            request.session.save()

            messages.success(request, 'تم تسجيل الدخول بنجاح.')
            return redirect('employees:employee_list')
        else:
            print(f"Form errors: {form.errors}")
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')
    else:
        form = AuthenticationForm()
    return render(request, 'accounts/login.html', {'form': form})

@login_required
def logout_view(request):
    # Clear user_visible_pages from session before logout
    if 'user_visible_pages' in request.session:
        del request.session['user_visible_pages']
        request.session.modified = True
        request.session.save()
        print(f"Logout: Cleared permissions from session for user {request.user.username}")

    # Perform logout
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح.')
    return redirect('accounts:login')

@login_required
def profile_view(request):
    return render(request, 'accounts/profile.html')

@login_required
def instructions_view(request):
    """View for the system instructions page"""
    return render(request, 'accounts/instructions.html')

@login_required
def change_password(request):
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)
            messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
            return redirect('accounts:profile')
        else:
            messages.error(request, 'الرجاء تصحيح الأخطاء أدناه.')
    else:
        form = PasswordChangeForm(request.user)
    return render(request, 'accounts/change_password.html', {'form': form})

@login_required
@user_passes_test(is_admin)
def user_list(request):
    # Handle search
    search_query = request.GET.get('search', '')
    users = User.objects.all().order_by('username')
    
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    # Calculate statistics
    user_stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'admin_users': User.objects.filter(Q(is_admin=True) | Q(is_full_admin=True) | Q(is_superuser=True)).count(),
        'users_with_permissions': User.objects.filter(custom_permissions__isnull=False).distinct().count(),
    }

    # Print debug information
    for user in users:
        print(f"USER LIST - User {user.username}: is_admin={user.is_admin}, is_staff={user.is_staff}, is_full_admin={user.is_full_admin}")

    return render(request, 'accounts/user_list.html', {
        'users': users,
        'user_stats': user_stats,
        'search_query': search_query,
    })

@login_required
@user_passes_test(is_admin)
def user_create(request):
    if request.method == 'POST':
        # Print the POST data for debugging
        print(f"POST data: {request.POST}")

        form = CustomUserCreationForm(request.POST)

        if form.is_valid():
            # Get the cleaned data
            is_full_admin = form.cleaned_data.get('is_full_admin')
            permission_level = form.cleaned_data.get('user_permission_level')

            print(f"Form cleaned data: is_full_admin={is_full_admin}, permission_level={permission_level}")

            # Save the user but don't commit yet
            user = form.save(commit=False)

            # STEP 1: Set user permissions based on form data
            if is_full_admin:
                # User is a full admin
                user.is_admin = True
                user.is_staff = True
                user.is_full_admin = True
            else:
                # User is not a full admin
                user.is_full_admin = False

                if permission_level:
                    # Set user status based on permission level
                    if permission_level == 'admin':
                        user.is_admin = True
                        user.is_staff = True
                    elif permission_level == 'supervisor':
                        user.is_admin = False
                        user.is_staff = True
                    elif permission_level == 'user':
                        user.is_admin = False
                        user.is_staff = False
                else:
                    # Default to regular user if no permission level is selected
                    user.is_admin = False
                    user.is_staff = False

            # Save the user
            user.save()

            print(f"Created user {user.username}: is_admin={user.is_admin}, is_staff={user.is_staff}, is_full_admin={user.is_full_admin}")

            # STEP 2: Create permissions based on user type
            create_user_permissions_automatically(user, is_full_admin, permission_level)

            # Force session update if this is the current user (unlikely but possible)
            if request.user.id == user.id:
                reload_user_permissions(request)
                request.session.save()
                print(f"Updated session for current user {user.username}")

            messages.success(request, 'تم إنشاء المستخدم بنجاح.')
            return redirect('accounts:user_detail', pk=user.pk)
        else:
            print(f"Form errors: {form.errors}")
    else:
        form = CustomUserCreationForm()

    return render(request, 'accounts/user_form.html', {
        'form': form,
        'is_new_user': True
    })

@login_required
@user_passes_test(is_admin)
def user_detail(request, pk):
    user = get_object_or_404(User, pk=pk)
    # Get user permissions
    user_permissions = UserPermission.objects.filter(user=user)

    # Force a database refresh to ensure we have the latest data
    fresh_user = User.objects.get(pk=user.pk)

    # Print debug information
    print(f"USER DETAIL - User {fresh_user.username}: is_admin={fresh_user.is_admin}, is_staff={fresh_user.is_staff}, is_full_admin={fresh_user.is_full_admin}")

    return render(request, 'accounts/user_detail.html', {
        'user_obj': fresh_user,
        'user_permissions': user_permissions
    })

@login_required
@user_passes_test(is_admin)
def admin_change_password(request, pk):
    user = get_object_or_404(User, pk=pk)
    if request.method == 'POST':
        form = AdminPasswordChangeForm(user, request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
            return redirect('accounts:user_detail', pk=user.pk)
    else:
        form = AdminPasswordChangeForm(user)

    return render(request, 'accounts/admin_change_password.html', {
        'form': form,
        'user_obj': user
    })

@login_required
@user_passes_test(is_admin)
def user_update(request, pk):
    user = get_object_or_404(User, pk=pk)

    # Print debug information about the user before any changes
    print(f"BEFORE - User {user.username}: is_admin={user.is_admin}, is_staff={user.is_staff}, is_full_admin={user.is_full_admin}")

    if request.method == 'POST':
        # Print the POST data for debugging
        print(f"POST data: {request.POST}")

        # Create the form with the POST data
        form = CustomUserChangeForm(request.POST, instance=user)

        if form.is_valid():
            # Get the cleaned data
            is_full_admin = form.cleaned_data.get('is_full_admin')
            permission_level = form.cleaned_data.get('user_permission_level')

            print(f"Form cleaned data: is_full_admin={is_full_admin}, permission_level={permission_level}")

            # STEP 1: Update the user object directly in the database
            # This ensures that the changes are immediately visible

            # First, get a fresh copy of the user from the database
            fresh_user = User.objects.get(pk=user.pk)

            # Update the user based on the form data
            if is_full_admin:
                # User is a full admin
                fresh_user.is_admin = True
                fresh_user.is_staff = True
                fresh_user.is_full_admin = True
            else:
                # User is not a full admin
                fresh_user.is_full_admin = False

                if permission_level:
                    # Set user status based on permission level
                    if permission_level == 'admin':
                        fresh_user.is_admin = True
                        fresh_user.is_staff = True
                    elif permission_level == 'supervisor':
                        fresh_user.is_admin = False
                        fresh_user.is_staff = True
                    elif permission_level == 'user':
                        fresh_user.is_admin = False
                        fresh_user.is_staff = False
                    elif permission_level == 'readonly':
                        fresh_user.is_admin = False
                        fresh_user.is_staff = False
                else:
                    # Default to regular user if no permission level is selected
                    fresh_user.is_admin = False
                    fresh_user.is_staff = False

            # Save the user object
            fresh_user.save()

            print(f"Updated user status: is_admin={fresh_user.is_admin}, is_staff={fresh_user.is_staff}, is_full_admin={fresh_user.is_full_admin}")

            # STEP 2: Create permissions automatically
            create_user_permissions_automatically(fresh_user, is_full_admin, permission_level)

            # STEP 3: Update other user fields from the form
            # We need to save the form to update other fields like username, email, etc.
            # But we don't want it to override our permission settings

            # Get a fresh form with the updated user
            updated_form = CustomUserChangeForm(request.POST, instance=fresh_user)
            if updated_form.is_valid():
                # Save the form but exclude permission-related fields
                user_obj = updated_form.save(commit=False)

                # Make sure our permission settings are preserved
                user_obj.is_admin = fresh_user.is_admin
                user_obj.is_staff = fresh_user.is_staff
                user_obj.is_full_admin = fresh_user.is_full_admin

                # Save the user object
                user_obj.save()

                # Force a database update to ensure changes are saved
                User.objects.filter(pk=user.pk).update(
                    is_admin=fresh_user.is_admin,
                    is_staff=fresh_user.is_staff,
                    is_full_admin=fresh_user.is_full_admin
                )

                # Get a fresh copy of the user to verify changes
                final_user = User.objects.get(pk=user.pk)

                print(f"AFTER - User {final_user.username}: is_admin={final_user.is_admin}, is_staff={final_user.is_staff}, is_full_admin={final_user.is_full_admin}")

            # STEP 4: Update session if this is the current user
            if request.user.id == user.pk:
                reload_user_permissions(request)
                request.session.save()
                print(f"Updated session for current user {fresh_user.username}")

            messages.success(request, 'تم تحديث بيانات المستخدم بنجاح.')
            return redirect('accounts:user_detail', pk=user.pk)
        else:
            print(f"Form errors: {form.errors}")
    else:
        form = CustomUserChangeForm(instance=user)

    return render(request, 'accounts/user_form.html', {
        'form': form,
        'user_obj': user
    })

@login_required
@user_passes_test(is_admin)
def user_delete(request, pk):
    user = get_object_or_404(User, pk=pk)

    # Prevent deletion of admin user or superusers
    if user.username == 'admin' or user.is_superuser:
        messages.error(request, 'لا يمكن حذف حساب مدير النظام (admin) أو المستخدمين المميزين.')
        return redirect('accounts:user_list')

    if request.method == 'POST':
        user.delete()
        messages.success(request, 'تم حذف المستخدم بنجاح.')
        return redirect('accounts:user_list')
    return render(request, 'accounts/user_confirm_delete.html', {'user_obj': user})

@login_required
@user_passes_test(is_admin)
def user_visible_pages(request, pk, permission_id):
    """
    View to display visible pages for a user permission in a separate page.
    This is to avoid freezing the system when there are many visible pages.
    """
    user = get_object_or_404(User, pk=pk)
    permission = get_object_or_404(UserPermission, pk=permission_id, user=user)

    # Get the permission form to get all possible pages
    permission_form = UserPermissionForm()
    all_pages_choices = permission_form.get_pages_choices()

    # Get the visible pages for this permission
    visible_pages = []
    if permission.visible_pages:
        visible_pages = permission.visible_pages.split(',')
        # Filter out empty strings
        visible_pages = [page for page in visible_pages if page.strip()]

    # Organize pages by module
    modules = {
        'employment': {'name': 'الكادر', 'pages': []},
        'leaves': {'name': 'الإجراءات', 'pages': []},
        'directorate_leaves': {'name': 'إجازات الموظفين (المديرية)', 'pages': []},
        'files': {'name': 'الملفات', 'pages': []},
        'ranks': {'name': 'الرتب', 'pages': []},
        'performance': {'name': 'التقارير السنوية', 'pages': []},
        'reports': {'name': 'تقارير النظام', 'pages': []},
        'accounts': {'name': 'المستخدمين', 'pages': []},
        'backup': {'name': 'النسخ الاحتياطية', 'pages': []},
        'system_logs': {'name': 'سجل حركات النظام', 'pages': []},
        'disciplinary': {'name': 'العقوبات', 'pages': []},
        'file_management': {'name': 'إدارة الملفات', 'pages': []},
        'employees': {'name': 'بيانات الموظفين', 'pages': []},
    }

    # Map page names to human-readable names
    page_names = {}
    for page_key, page_name in all_pages_choices:
        page_names[page_key] = page_name

    # Add additional page names that might not be in the form
    additional_page_names = {
        'employees:employee_list': 'بيانات الموظفين',
        'employees:calculate_age': 'حساب العمر',
        'employment:department_list': 'الأقسام',
        'employment:position_list': 'المسميات الوظيفية',
        'employment:employee_position_list': 'الحراك الوظيفي',
        'employment:experience_certificate_list': 'شهادة الخبرة',
        'employment:technical_position_list': 'الموقف الفني',
        'employment:actual_service_list': 'الخدمة الفعلية',
        'leaves:unpaid_leave_list': 'إجازات بدون راتب',
        'disciplinary:penalty_list': 'العقوبات',
        'disciplinary:penalty_type_list': 'أنواع العقوبات',
        'leaves:leave_list': 'قائمة الإجازات',
        'leaves:leave_create': 'إضافة إجازة',
        'leaves:leave_balance_list': 'رصيد الإجازات',
        'leaves:leave_reports': 'تقارير الإجازات',
        'directorate_leaves:whatsapp_send': 'إرسال (واتس اب)',
        'file_management:file_movement_list': 'حركة الملف',
        'file_management:file_checkout': 'تسجيل خروج ملف',
        'file_management:file_return_list': 'الملفات المنجزة',
        'ranks:rank_type_list': 'أنواع الرتب',
        'ranks:employee_rank_create': 'إضافة رتبة للموظف',
        'ranks:employee_rank_list': 'رتب الموظفين',
        'performance:performance_list': 'التقارير السنوية',
        'reports:report_dashboard': 'تقارير النظام',
        'accounts:user_list': 'المستخدمين',
        'backup:backup_list': 'النسخ الاحتياطية',
        'system_logs:system_log_list': 'سجل حركات النظام',
        'system_logs:system_error_list': 'سجل أخطاء النظام',
        'system_logs:system_error_detail': 'تفاصيل الخطأ',
    }

    # Update page_names with additional names
    page_names.update(additional_page_names)

    # Organize pages by module
    for page in visible_pages:
        module_name = page.split(':')[0]
        if module_name in modules:
            page_display_name = page_names.get(page, page)
            modules[module_name]['pages'].append({
                'key': page,
                'name': page_display_name
            })

    # Filter out modules with no pages
    active_modules = {k: v for k, v in modules.items() if v['pages']}

    return render(request, 'accounts/user_visible_pages.html', {
        'user_obj': user,
        'permission': permission,
        'modules': active_modules,
        'total_pages': len(visible_pages)
    })

def reload_permissions_view(request):
    """
    View to reload user permissions in the session.
    This is useful when permissions are changed and we want to update the session.
    """
    from django.http import JsonResponse

    # Force a database refresh of the user object to get the latest status
    from django.contrib.auth import get_user_model
    User = get_user_model()
    fresh_user = User.objects.get(pk=request.user.pk)

    # Update the request.user with fresh data
    request.user = fresh_user

    # Clear existing permissions from session
    if 'user_visible_pages' in request.session:
        del request.session['user_visible_pages']
        print(f"RELOAD VIEW: Cleared permissions from session for user {request.user.username}")

    # If user is superuser or full admin, they can see all pages
    if request.user.is_superuser or request.user.is_full_admin:
        # Get all possible pages from the permission form
        from accounts.forms import UserPermissionForm
        permission_form = UserPermissionForm()
        all_pages = [choice[0] for choice in permission_form.get_pages_choices()]
        request.session['user_visible_pages'] = all_pages
        print(f"RELOAD VIEW: Added {len(all_pages)} pages to session for admin user {request.user.username}")
    else:
        # Get user permissions from database
        user_permissions = request.user.custom_permissions.all().select_related()
        visible_pages = []

        for permission in user_permissions:
            if permission.can_view and permission.visible_pages:
                pages = permission.visible_pages.split(',')
                # Filter out empty strings
                pages = [page for page in pages if page.strip()]
                visible_pages.extend(pages)
                print(f"RELOAD VIEW: User {request.user.username} has access to {len(pages)} pages from module {permission.module_name}")

        # Remove duplicates
        visible_pages = list(set(visible_pages))

        # Make sure 'employees:employee_list' is always included
        if 'employees:employee_list' not in visible_pages:
            visible_pages.append('employees:employee_list')
            print(f"RELOAD VIEW: Added employees:employee_list to visible pages for user {request.user.username}")

        request.session['user_visible_pages'] = visible_pages
        print(f"RELOAD VIEW: Added {len(visible_pages)} pages to session for user {request.user.username}")

    # Force session save
    request.session.modified = True
    request.session.save()

    # Check if this is an AJAX request
    if request.GET.get('ajax') == 'true' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': 'تم إعادة تحميل الصلاحيات بنجاح.',
            'visible_pages': request.session.get('user_visible_pages', [])
        })

    # Add success message for non-AJAX requests
    messages.success(request, 'تم إعادة تحميل الصلاحيات بنجاح.')

    # Get the current URL from the referer or use the next parameter
    referer = request.META.get('HTTP_REFERER')
    next_url = request.GET.get('next')

    # If we have a referer and no next parameter, use the referer
    if referer and not next_url:
        return redirect(referer)

    # Otherwise use the next parameter or dashboard
    next_url = next_url or 'dashboard'
    return redirect(next_url)


# ==================== Permission Management Views ====================

@login_required
@user_passes_test(is_admin)
def user_permissions_view(request, user_id):
    """عرض صلاحيات مستخدم معين"""
    
    user = get_object_or_404(User, id=user_id)
    
    # Get all available modules and pages
    form = UserPermissionForm()
    all_pages = form.get_pages_choices()
    
    # Organize pages by module
    modules = {}
    for page_url, page_name in all_pages:
        module_name = page_url.split(':')[0]
        if module_name not in modules:
            modules[module_name] = {
                'name': get_module_display_name(module_name),
                'pages': []
            }
        modules[module_name]['pages'].append({
            'url': page_url,
            'name': page_name
        })
    
    # Get user's current permissions
    user_permissions = {}
    for permission in user.custom_permissions.all():
        visible_pages = permission.visible_pages.split(',') if permission.visible_pages else []
        user_permissions[permission.module_name] = {
            'can_view': permission.can_view,
            'can_add': permission.can_add,
            'can_edit': permission.can_edit,
            'can_delete': permission.can_delete,
            'visible_pages': visible_pages
        }
    
    # Render permission interface
    html = render_to_string('accounts/user_permissions_modal.html', {
        'user': user,
        'modules': modules,
        'user_permissions': user_permissions,
    }, request=request)
    
    return JsonResponse({
        'success': True,
        'html': html
    })


@login_required
@user_passes_test(is_admin)
def user_permissions_page(request, user_id):
    """صفحة إدارة صلاحيات مستخدم معين"""
    
    user = get_object_or_404(User, id=user_id)
    
    # Get all available modules and pages
    form = UserPermissionForm()
    all_pages = form.get_pages_choices()
    
    # Organize pages by module
    modules = {}
    for page_url, page_name in all_pages:
        module_name = page_url.split(':')[0]
        if module_name not in modules:
            modules[module_name] = {
                'name': get_module_display_name(module_name),
                'pages': []
            }
        modules[module_name]['pages'].append({
            'url': page_url,
            'name': page_name
        })
    
    # Get user's current permissions
    user_permissions = {}
    for permission in user.custom_permissions.all():
        visible_pages = permission.visible_pages.split(',') if permission.visible_pages else []
        user_permissions[permission.module_name] = {
            'can_view': permission.can_view,
            'can_add': permission.can_add,
            'can_edit': permission.can_edit,
            'can_delete': permission.can_delete,
            'visible_pages': visible_pages
        }
    
    context = {
        'user': user,
        'modules': modules,
        'user_permissions': user_permissions,
        'page_title': f'إدارة صلاحيات المستخدم: {user.username}',
    }
    
    return render(request, 'accounts/user_permissions_page.html', context)


@login_required
@user_passes_test(is_admin)
@csrf_exempt
def save_user_permissions(request, user_id):
    """حفظ صلاحيات مستخدم"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة الطلب غير صحيحة'})
    
    user = get_object_or_404(User, id=user_id)
    
    try:
        # Parse JSON data from request body
        try:
            permission_data = json.loads(request.body)
            print(f"Received permission data: {permission_data}")
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'بيانات JSON غير صحيحة'})
        
        # Delete existing permissions
        deleted_count = UserPermission.objects.filter(user=user).count()
        UserPermission.objects.filter(user=user).delete()
        print(f"Deleted {deleted_count} existing permissions for user {user.username}")
        
        # Process each module from the received data
        created_permissions = []
        for module_name, module_perms in permission_data.items():
            print(f"Processing module: {module_name}")
            print(f"Module permissions: {module_perms}")
            
            can_view = module_perms.get('can_view', False)
            can_add = module_perms.get('can_add', False)
            can_edit = module_perms.get('can_edit', False)
            can_delete = module_perms.get('can_delete', False)
            visible_pages = module_perms.get('visible_pages', [])
            
            # Convert visible_pages list to comma-separated string
            if isinstance(visible_pages, list):
                visible_pages_str = ','.join(visible_pages)
            else:
                visible_pages_str = str(visible_pages) if visible_pages else ''
            
            print(f"Creating permission for {module_name}: view={can_view}, add={can_add}, edit={can_edit}, delete={can_delete}, pages={visible_pages_str}")
            
            # Create permission for this module
            permission = UserPermission.objects.create(
                user=user,
                module_name=module_name,
                can_view=can_view,
                can_add=can_add,
                can_edit=can_edit,
                can_delete=can_delete,
                visible_pages=visible_pages_str
            )
            created_permissions.append(permission)
            print(f"Created permission ID: {permission.id}")
        
        print(f"Total permissions created: {len(created_permissions)}")
        
        # Update user admin status
        update_user_admin_status(user, created_permissions, request)
        
        # Reload user permissions in session if this is the current user
        if user == request.user:
            reload_user_permissions(request)
        
        return JsonResponse({
            'success': True,
            'message': f'تم حفظ صلاحيات المستخدم {user.username} بنجاح',
            'permissions_count': len(created_permissions)
        })
        
    except Exception as e:
        print(f"Error saving permissions: {str(e)}")
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ الصلاحيات: {str(e)}'
        })


@login_required
@user_passes_test(is_admin)
def toggle_user_status(request, user_id):
    """تفعيل/تعطيل مستخدم"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة الطلب غير صحيحة'})
    
    user = get_object_or_404(User, id=user_id)
    
    # Prevent disabling superuser or admin account
    if user.is_superuser or user.username == 'admin':
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن تعطيل حساب المدير الرئيسي'
        })
    
    try:
        data = json.loads(request.body)
        new_status = data.get('is_active', not user.is_active)
        
        user.is_active = new_status
        user.save()
        
        action = 'تفعيل' if new_status else 'تعطيل'
        
        return JsonResponse({
            'success': True,
            'message': f'تم {action} المستخدم {user.username} بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في تحديث حالة المستخدم: {str(e)}'
        })


@login_required
@user_passes_test(is_admin)
def bulk_permissions_view(request):
    """واجهة إدارة الصلاحيات الجماعية"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة الطلب غير صحيحة'})
    
    try:
        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        
        if not user_ids:
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار أي مستخدمين'})
        
        users = User.objects.filter(id__in=user_ids)
        
        # Get all available modules
        form = UserPermissionForm()
        all_pages = form.get_pages_choices()
        
        # Organize pages by module
        modules = {}
        for page_url, page_name in all_pages:
            module_name = page_url.split(':')[0]
            if module_name not in modules:
                modules[module_name] = {
                    'name': get_module_display_name(module_name),
                    'pages': []
                }
            modules[module_name]['pages'].append({
                'url': page_url,
                'name': page_name
            })
        
        # Render bulk permission interface
        html = render_to_string('accounts/bulk_permissions_modal.html', {
            'users': users,
            'modules': modules,
        }, request=request)
        
        return JsonResponse({
            'success': True,
            'html': html
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في تحميل واجهة الصلاحيات الجماعية: {str(e)}'
        })


@login_required
@user_passes_test(is_admin)
def apply_bulk_permissions(request):
    """تطبيق الصلاحيات الجماعية"""
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'طريقة الطلب غير صحيحة'})
    
    try:
        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        permissions_data = data.get('permissions', {})
        
        if not user_ids:
            return JsonResponse({'success': False, 'message': 'لم يتم اختيار أي مستخدمين'})
        
        users = User.objects.filter(id__in=user_ids)
        updated_count = 0
        
        for user in users:
            # Skip superusers
            if user.is_superuser:
                continue
            
            # Delete existing permissions
            UserPermission.objects.filter(user=user).delete()
            
            # Apply new permissions
            for module_name, module_perms in permissions_data.items():
                if any(module_perms.values()):  # If any permission is granted
                    UserPermission.objects.create(
                        user=user,
                        module_name=module_name,
                        can_view=module_perms.get('can_view', False),
                        can_add=module_perms.get('can_add', False),
                        can_edit=module_perms.get('can_edit', False),
                        can_delete=module_perms.get('can_delete', False),
                        visible_pages=','.join(module_perms.get('visible_pages', []))
                    )
            
            # Update user admin status
            update_user_admin_status(user)
            updated_count += 1
        
        return JsonResponse({
            'success': True,
            'message': f'تم تحديث صلاحيات {updated_count} مستخدم بنجاح'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في تطبيق الصلاحيات الجماعية: {str(e)}'
        })


def get_module_display_name(module_name):
    """الحصول على الاسم المعروض للوحدة"""
    
    module_names = {
        'employees': 'الموظفين',
        'employment': 'التوظيف',
        'leaves': 'الإجازات',
        'directorate_leaves': 'إجازات المديرية',
        'performance': 'التقييم السنوي',
        'reports': 'التقارير',
        'accounts': 'إدارة المستخدمين',
        'file_management': 'إدارة الملفات',
        'ranks': 'الرتب والدرجات',
        'backup': 'النسخ الاحتياطية',
        'disciplinary': 'الإجراءات التأديبية',
        'system_logs': 'سجل النظام',
        'home': 'الصفحة الرئيسية',
        'notifications': 'الإشعارات',
        'announcements': 'الإعلانات',
    }
    
    return module_names.get(module_name, module_name)
