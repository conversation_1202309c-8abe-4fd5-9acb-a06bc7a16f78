from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import Employee

@login_required
def add_qualification(request):
    """View for adding new qualifications to employees"""
    if request.method == 'POST':
        employee_id = request.POST.get('employee_id')
        post_graduate_diploma = request.POST.get('post_graduate_diploma', '').strip()
        masters_degree = request.POST.get('masters_degree', '').strip()
        phd_degree = request.POST.get('phd_degree', '').strip()
        
        try:
            employee = Employee.objects.get(id=employee_id)
            
            # Update qualifications
            if post_graduate_diploma:
                employee.post_graduate_diploma = post_graduate_diploma
            if masters_degree:
                employee.masters_degree = masters_degree
            if phd_degree:
                employee.phd_degree = phd_degree
                
            employee.save()
            
            messages.success(request, f'تم تحديث المؤهلات العلمية للموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_list')
            
        except Employee.DoesNotExist:
            messages.error(request, 'لم يتم العثور على الموظف المحدد.')
    
    return render(request, 'employees/add_qualification.html')

@login_required
def search_employee_for_qualification(request):
    """AJAX view to search for employee by ministry number"""
    ministry_number = request.GET.get('ministry_number', '').strip()
    
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'يرجى إدخال الرقم الوزاري'})
    
    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number,
                'qualification': employee.qualification or '',
                'post_graduate_diploma': employee.post_graduate_diploma or '',
                'masters_degree': employee.masters_degree or '',
                'phd_degree': employee.phd_degree or '',
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})