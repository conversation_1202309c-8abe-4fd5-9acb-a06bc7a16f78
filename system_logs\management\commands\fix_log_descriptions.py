from django.core.management.base import BaseCommand
from system_logs.models import SystemLog

class Command(BaseCommand):
    help = 'Corregir las descripciones de los registros del sistema para que aparezcan en árabe'

    def handle(self, *args, **options):
        updated_count = 0
        
        # Get all logs
        logs = SystemLog.objects.all()
        total_logs = logs.count()
        
        self.stdout.write(self.style.SUCCESS(f'Iniciando actualización de {total_logs} registros...'))
        
        for log in logs:
            # Get module name in Arabic
            module_name = dict(SystemLog.MODULE_CHOICES).get(log.module, log.module)
            
            # Get action name in Arabic
            action_name = dict(SystemLog.LOG_TYPE_CHOICES).get(log.action, log.action)
            
            # Get user name
            user_name = log.user.username if log.user else "مستخدم مجهول"
            
            # Generate simple Arabic description
            if log.action == SystemLog.CREATE:
                description = f'قام المستخدم <strong>{user_name}</strong> بإضافة عنصر جديد في قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.UPDATE:
                description = f'قام المستخدم <strong>{user_name}</strong> بتعديل بيانات في قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.DELETE:
                description = f'قام المستخدم <strong>{user_name}</strong> بحذف عنصر من قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.VIEW:
                description = f'قام المستخدم <strong>{user_name}</strong> بعرض بيانات في قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.LOGIN:
                description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الدخول إلى النظام'
            elif log.action == SystemLog.LOGOUT:
                description = f'قام المستخدم <strong>{user_name}</strong> بتسجيل الخروج من النظام'
            elif log.action == SystemLog.EXPORT:
                description = f'قام المستخدم <strong>{user_name}</strong> بتصدير بيانات من قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.IMPORT:
                description = f'قام المستخدم <strong>{user_name}</strong> باستيراد بيانات إلى قسم <strong>{module_name}</strong>'
            elif log.action == SystemLog.BACKUP:
                description = f'قام المستخدم <strong>{user_name}</strong> بإنشاء نسخة احتياطية من النظام'
            elif log.action == SystemLog.RESTORE:
                description = f'قام المستخدم <strong>{user_name}</strong> باستعادة نسخة احتياطية للنظام'
            else:
                description = f'قام المستخدم <strong>{user_name}</strong> بتنفيذ إجراء <strong>{action_name}</strong> في قسم <strong>{module_name}</strong>'
            
            # Add object info if available
            if log.object_repr:
                description += f' <span class="object-name">({log.object_repr})</span>'
            
            # Add page info if available
            if log.page:
                description += f' - صفحة: <strong>{log.page}</strong>'
            
            # Update log description
            log.description = description
            log.save()
            updated_count += 1
            
            # Print progress every 100 logs
            if updated_count % 100 == 0:
                self.stdout.write(self.style.SUCCESS(f'Actualizados {updated_count} de {total_logs} registros...'))
        
        self.stdout.write(self.style.SUCCESS(f'¡Actualización completada! Se actualizaron {updated_count} registros.'))
