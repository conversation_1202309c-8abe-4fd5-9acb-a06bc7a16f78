{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المدارس - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .school-selector {
        background: linear-gradient(135deg, #f8f9fc 0%, #e3e6f0 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #e3e6f0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .selector-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }
    
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 45px;
        border: 2px solid #d1d3e2;
        border-radius: 8px;
    }
    
    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
        line-height: 41px;
        padding-left: 15px;
        font-size: 1rem;
    }
    
    .select2-container--bootstrap-5 .select2-selection:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    
    .table th {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        padding: 15px 10px;
    }
    
    .table th i {
        margin-left: 8px;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }
    
    .employee-names {
        max-width: 300px;
        word-wrap: break-word;
    }
    
    .employee-list {
        max-height: 150px;
        overflow-y: auto;
    }
    
    .employee-item {
        padding: 2px 0;
        font-size: 0.9rem;
        border-bottom: 1px solid #f1f1f1;
    }
    
    .employee-item:last-child {
        border-bottom: none;
    }
    
    .employee-name {
        color: #495057;
        font-weight: 500;
    }
    
    .count-badge {
        font-size: 1.1rem;
        padding: 8px 12px;
        border-radius: 20px;
    }
    
    .specialization-badge {
        font-size: 0.9rem;
        padding: 6px 12px;
        border-radius: 15px;
    }
    
    .position-badge {
        font-size: 0.85rem;
        padding: 5px 10px;
        border-radius: 12px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .export-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .export-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .no-data-message {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
        background: #f8f9fc;
        border-radius: 15px;
        margin: 20px 0;
    }
    
    .no-data-message i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-school text-primary me-2"></i>تفاصيل المدارس
    </h2>
    <div>
        {% if selected_school %}
        <button type="button" id="refreshBtn" class="btn btn-info me-2">
            <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
        </button>
        <button type="button" id="exportExcelBtn" class="btn export-btn">
            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
        </button>
        {% endif %}
        <a href="{% url 'employment:department_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للأقسام
        </a>
    </div>
</div>

<!-- قسم اختيار المدرسة -->
<div class="school-selector">
    <div class="selector-title">
        <i class="fas fa-search text-primary me-2"></i>اختيار المدرسة
    </div>
    <form method="get" id="schoolForm">
        <div class="row align-items-end">
            <div class="col-md-8">
                <label for="school_select" class="form-label">
                    <i class="fas fa-school me-1"></i>اختر المدرسة
                </label>
                <select name="school_id" id="school_select" class="form-select">
                    <option value="">-- اختر المدرسة --</option>
                    {% for school in schools %}
                        <option value="{{ school.id }}" {% if selected_school_id == school.id|stringformat:"s" %}selected{% endif %}>
                            {{ school.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>عرض التفاصيل
                </button>
            </div>
        </div>
    </form>
</div>

{% if selected_school %}
<!-- إحصائيات المدرسة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ school_data|length }}</div>
            <div class="stats-label">إجمالي التخصصات</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ total_employees }}</div>
            <div class="stats-label">إجمالي الموظفين</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number">{{ selected_school.name|truncatechars:15 }}</div>
            <div class="stats-label">المدرسة المختارة</div>
        </div>
    </div>
</div>

<!-- جدول تفاصيل المدرسة -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table me-2"></i>تفاصيل موظفي {{ selected_school.name }}
        </h6>
    </div>
    <div class="card-body">
        {% if school_data %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="schoolDetailsTable">
                    <thead>
                        <tr>
                            <th width="10%">
                                <i class="fas fa-hashtag"></i>الرقم
                            </th>
                            <th width="25%">
                                <i class="fas fa-school"></i>اسم المدرسة
                            </th>
                            <th width="25%">
                                <i class="fas fa-graduation-cap"></i>التخصص
                            </th>
                            <th width="30%">
                                <i class="fas fa-users"></i>أسماء المعلمين
                            </th>
                            <th width="10%">
                                <i class="fas fa-calculator"></i>العدد
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in school_data %}
                        <tr>
                            <td class="text-center">{{ forloop.counter }}</td>
                            <td>
                                <span class="fw-bold text-primary">
                                    <i class="fas fa-school me-2"></i>{{ item.school_name }}
                                </span>
                            </td>
                            <td>
                                <span class="badge specialization-badge bg-warning text-dark">
                                    <i class="fas fa-graduation-cap me-1"></i>{{ item.specialization }}
                                </span>
                            </td>
                            <td class="employee-names">
                                <div class="employee-list">
                                    {% for employee in item.employees %}
                                        <div class="employee-item">
                                            <i class="fas fa-user text-primary me-1"></i>
                                            <span class="employee-name">{{ employee.employee_name }}</span>
                                            <small class="text-muted ms-2">({{ employee.position_name }})</small>
                                        </div>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge count-badge bg-success">
                                    <i class="fas fa-calculator me-1"></i>{{ item.count }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- ملاحظة توضيحية -->
            <div class="alert alert-info mt-3">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <div>
                        <strong>ملاحظة:</strong> يتم تجميع الموظفين حسب التخصص، حيث يظهر كل تخصص في صف واحد مع العدد الإجمالي لجميع المعلمين في هذا التخصص (بغض النظر عن المسمى الوظيفي).
                    </div>
                </div>
            </div>
        {% else %}
            <div class="no-data-message">
                <i class="fas fa-inbox"></i>
                <h5>لا توجد بيانات</h5>
                <p>لا يوجد موظفون في هذه المدرسة حالياً</p>
            </div>
        {% endif %}
    </div>
</div>

{% else %}
<!-- رسالة عدم اختيار مدرسة -->
<div class="no-data-message">
    <i class="fas fa-search"></i>
    <h5>اختر مدرسة لعرض التفاصيل</h5>
    <p>يرجى اختيار مدرسة من القائمة أعلاه لعرض تفاصيل الموظفين والتخصصات</p>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 for school selection
    $('#school_select').select2({
        theme: 'bootstrap-5',
        placeholder: '-- اختر المدرسة --',
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            },
            placeholder: function() {
                return "ابحث عن المدرسة...";
            }
        }
    });
    
    // Auto submit form when school is selected
    $('#school_select').on('change', function() {
        if ($(this).val()) {
            // Show loading indicator
            showAlert('info', 'جاري تحميل بيانات المدرسة...');
            $('#schoolForm').submit();
        } else {
            // Clear the page if no school selected
            window.location.href = "{% url 'employment:school_details' %}";
        }
    });
    
    // Auto refresh data every 30 seconds if a school is selected
    {% if selected_school %}
    setInterval(function() {
        var currentSchoolId = $('#school_select').val();
        if (currentSchoolId) {
            // Silent refresh - reload current page
            window.location.reload();
        }
    }, 30000); // 30 seconds
    {% endif %}
    
    // Manual refresh functionality
    $('#refreshBtn').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();
        
        // Show loading state
        btn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...');
        btn.prop('disabled', true);
        
        // Reload the page
        setTimeout(function() {
            window.location.reload();
        }, 500);
    });
    
    // Excel Export functionality
    $('#exportExcelBtn').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();
        var schoolId = $('#school_select').val();
        
        if (!schoolId) {
            alert('يرجى اختيار مدرسة أولاً');
            return;
        }
        
        // Show loading state
        btn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...');
        btn.prop('disabled', true);
        
        // Build URL
        var url = "{% url 'employment:export_school_details_to_excel' %}?school_id=" + schoolId;
        
        // Create a temporary link to download the file
        var link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Reset button state after a short delay
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
            
            // Show success message
            showAlert('success', 'تم تصدير البيانات إلى Excel بنجاح!');
        }, 1000);
    });
    
    // Show alert function
    function showAlert(type, message) {
        var alertClass;
        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                break;
            case 'info':
                alertClass = 'alert-info';
                break;
            case 'warning':
                alertClass = 'alert-warning';
                break;
            default:
                alertClass = 'alert-danger';
        }
        
        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Remove existing alerts
        $('.alert').remove();
        
        // Insert alert at the top of the page
        $('main .container-fluid').prepend(alertHtml);
        
        // Auto hide after 3 seconds (except for info alerts which hide after 1 second)
        var hideDelay = type === 'info' ? 1000 : 3000;
        setTimeout(function() {
            $('.alert').fadeOut();
        }, hideDelay);
    }
});
</script>
{% endblock %}