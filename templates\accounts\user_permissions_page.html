{% extends 'base.html' %}
{% load custom_filters %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .permission-badge {
        font-size: 0.75rem;
        margin: 2px;
    }
    
    .user-permissions {
        max-width: 200px;
        overflow: hidden;
    }
    
    .permission-summary {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .permission-count {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: bold;
    }
    
    .user-status-active {
        color: #28a745;
    }
    
    .user-status-inactive {
        color: #dc3545;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stats-item {
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .permission-module {
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .permission-module:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .permission-module.active {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .pages-list {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
    }

    .form-check-sm .form-check-input {
        margin-top: 0.1rem;
    }

    /* Module switch styling */
    .module-switch-container {
        order: 1;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .module-title {
        order: 2;
        text-align: right;
    }

    .module-switch-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #495057;
        margin-right: 0.75rem;
        margin-left: 0.5rem;
        cursor: pointer;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .switch-text {
        white-space: nowrap;
        padding-right: 1rem;
        display: inline-block;
        margin-right: 1rem;
        font-weight: 500;
    }

    .form-switch .form-check-input {
        width: 2.5rem;
        height: 1.25rem;
        background-color: #6c757d;
        border: none;
        border-radius: 2rem;
        transition: all 0.3s ease;
        cursor: pointer;
        margin: 0;
        flex-shrink: 0;
    }

    .form-switch .form-check-input:checked {
        background-color: #198754;
        border-color: #198754;
    }

    .form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
        border-color: #198754;
    }

    .form-switch .form-check-input:checked:focus {
        box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
    }

    .form-switch .form-check-input:hover {
        opacity: 0.8;
    }

    /* Module card states */
    .permission-module.module-enabled {
        border-color: #198754;
        background-color: #f8fff9;
    }

    .permission-module.module-enabled .card-header {
        background-color: #d1e7dd;
        border-bottom-color: #198754;
    }

    .permission-module.module-disabled {
        border-color: #6c757d;
        background-color: #f8f9fa;
        opacity: 0.7;
    }

    .permission-module.module-disabled .card-header {
        background-color: #e9ecef;
        border-bottom-color: #6c757d;
    }

    .permission-module.module-disabled .card-body {
        opacity: 0.5;
        pointer-events: none;
    }

    /* Improved spacing and layout */
    .form-check-input.module-toggle {
        pointer-events: auto !important;
    }

    /* Ensure proper spacing in header */
    .card-header .d-flex {
        gap: 1rem;
    }

    /* Ensure toggle is always clickable */
    .module-toggle {
        cursor: pointer !important;
        pointer-events: auto !important;
    }

    .module-switch-label {
        cursor: pointer !important;
        pointer-events: auto !important;
    }

    /* Animation for module state change */
    .permission-module {
        transition: all 0.4s ease;
    }

    .module-toggle {
        transition: all 0.3s ease;
    }

    .module-toggle:checked {
        transform: scale(1.05);
        animation: togglePulse 0.3s ease;
    }

    @keyframes togglePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1.05); }
    }

    /* Page header styling */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0.5rem;
    }

    .user-info-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        padding: 1rem;
        backdrop-filter: blur(10px);
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
    }

    .save-permissions-btn {
        position: sticky;
        top: 20px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-3">
                        <a href="{% url 'accounts:user_list' %}" class="btn btn-back me-3">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة لقائمة المستخدمين
                        </a>
                        <div>
                            <h1 class="mb-0">
                                <i class="fas fa-user-cog me-2"></i>
                                إدارة صلاحيات المستخدم
                            </h1>
                            <p class="mb-0 opacity-75">تخصيص الصلاحيات والوصول للوحدات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="user-info-card">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-white text-primary rounded-circle me-3">
                                <div class="avatar-title">
                                    {{ user.username|first|upper }}
                                </div>
                            </div>
                            <div>
                                <h5 class="mb-1">{{ user.username }}</h5>
                                <p class="mb-0 opacity-75">
                                    {% if user.first_name or user.last_name %}
                                        {{ user.first_name }} {{ user.last_name }}
                                    {% else %}
                                        {{ user.email|default:"لا يوجد بريد إلكتروني" }}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>
                            القوالب السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-success btn-sm w-100 preset-btn" data-preset="admin">
                                    <i class="fas fa-crown me-1"></i>
                                    مدير كامل
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-primary btn-sm w-100 preset-btn" data-preset="hr">
                                    <i class="fas fa-users me-1"></i>
                                    موارد بشرية
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-info btn-sm w-100 preset-btn" data-preset="finance">
                                    <i class="fas fa-calculator me-1"></i>
                                    مالية
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-warning btn-sm w-100 preset-btn" data-preset="user">
                                    <i class="fas fa-user me-1"></i>
                                    مستخدم عادي
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-secondary btn-sm w-100 preset-btn" data-preset="readonly">
                                    <i class="fas fa-eye me-1"></i>
                                    قراءة فقط
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button class="btn btn-danger btn-sm w-100 preset-btn" data-preset="clear">
                                    <i class="fas fa-times me-1"></i>
                                    مسح الكل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <button class="btn btn-success btn-lg save-permissions-btn" onclick="savePermissions()">
                        <i class="fas fa-save me-2"></i>
                        حفظ الصلاحيات
                    </button>
                </div>
            </div>
        </div>

        <!-- Permissions Content -->
        <div id="permissionContent">
            {% include 'accounts/user_permissions_modal.html' %}
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="d-none position-fixed w-100 h-100" style="top: 0; left: 0; background: rgba(0,0,0,0.7); z-index: 9999; backdrop-filter: blur(5px);">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="text-center text-white">
            <div class="loading-container" style="background: rgba(255,255,255,0.1); padding: 3rem 2rem; border-radius: 15px; backdrop-filter: blur(10px);">
                <div class="spinner-border mb-3" role="status" style="width: 3rem; height: 3rem; border-width: 0.3em;">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h4 style="margin-bottom: 1rem; font-weight: bold;">جاري حفظ الصلاحيات...</h4>
                <p style="margin-bottom: 0; opacity: 0.9;">يرجى الانتظار حتى اكتمال العملية</p>
                <div class="mt-3">
                    <div class="progress" style="height: 4px; background-color: rgba(255,255,255,0.2);">
                        <div class="progress-bar bg-light" role="progressbar" style="width: 100%; animation: progressPulse 1.5s ease-in-out infinite;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes progressPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let currentUserId = {{ user.id }};
let selectedUsers = [];

// Initialize permission management functionality
function initializePermissionManagement() {
    console.log('Initializing permission management...');
    
    // Add event listeners for permission presets
    document.querySelectorAll('.preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const presetName = this.dataset.preset;
            applyPermissionPreset(presetName);
        });
    });
    
    // Add event listeners for module toggles
    document.querySelectorAll('.module-toggle').forEach((toggle, index) => {
        const module = toggle.closest('.permission-module');
        const moduleName = toggle.dataset.module || module.dataset.module;
        
        console.log(`Setting up toggle ${index + 1} for module: ${moduleName}`);
        
        // Remove any existing event listeners to prevent duplicates
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);
        
        // Add new event listener to the cloned element
        newToggle.addEventListener('change', function(e) {
            console.log(`Toggle event fired for ${moduleName}: ${this.checked}`);
            handleModuleToggle(e, moduleName);
        });
        
        // Also add click event listener as backup
        newToggle.addEventListener('click', function(e) {
            console.log(`Toggle clicked for ${moduleName}: ${this.checked}`);
            // Small delay to ensure the checked state is updated
            setTimeout(() => {
                handleModuleToggle(e, moduleName);
            }, 10);
        });
        
        // Initialize visual state based on current state
        updateModuleVisualState(module, newToggle.checked);
    });
    
    // Add event listeners for select all pages buttons
    document.querySelectorAll('.select-all-pages').forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleName = this.dataset.module;
            selectAllPages(moduleName);
        });
    });
    
    // Add event listeners for clear all pages buttons
    document.querySelectorAll('.clear-all-pages').forEach(btn => {
        btn.addEventListener('click', function() {
            const moduleName = this.dataset.module;
            clearAllPages(moduleName);
        });
    });
}

// Handle module toggle change
function handleModuleToggle(event, moduleName) {
    const toggle = event.target;
    const isEnabled = toggle.checked;
    
    console.log(`Module toggle changed: ${moduleName} = ${isEnabled}`);
    console.log(`Toggle element:`, toggle);
    console.log(`Toggle checked state:`, toggle.checked);
    
    // Prevent event bubbling
    event.stopPropagation();
    event.preventDefault();
    
    // Ensure the toggle state is correct
    toggle.checked = isEnabled;
    
    // Toggle module permissions
    toggleModulePermissions(moduleName, isEnabled);
    
    // Force visual update
    const module = toggle.closest('.permission-module');
    if (module) {
        updateModuleVisualState(module, isEnabled);
    }
}

// Apply permission preset
function applyPermissionPreset(presetName) {
    console.log('Applying preset:', presetName);
    
    const presets = {
        'admin': {
            permissions: ['can_view', 'can_add', 'can_edit', 'can_delete'],
            modules: 'all'
        },
        'hr': {
            permissions: ['can_view', 'can_add', 'can_edit'],
            excludeModules: ['backup', 'system_logs']
        },
        'finance': {
            permissions: ['can_view', 'can_add', 'can_edit'],
            excludeModules: ['accounts', 'backup', 'system_logs', 'disciplinary']
        },
        'user': {
            permissions: ['can_view', 'can_add'],
            excludeModules: ['accounts', 'backup', 'system_logs', 'disciplinary']
        },
        'readonly': {
            permissions: ['can_view'],
            excludeModules: ['accounts', 'backup', 'system_logs']
        },
        'clear': {
            permissions: [],
            modules: 'none'
        }
    };
    
    const preset = presets[presetName];
    if (!preset) return;
    
    // Clear all permissions first
    document.querySelectorAll('.permission-module').forEach(module => {
        const moduleName = module.dataset.module;
        const moduleToggle = module.querySelector('.module-toggle');
        
        // Clear all checkboxes
        module.querySelectorAll('input[type="checkbox"]').forEach(cb => {
            cb.checked = false;
        });
        
        // Update module visual state to disabled
        updateModuleVisualState(module, false);
        
        // Skip if preset is 'clear' or module is excluded
        if (preset.modules === 'none' || 
            (preset.excludeModules && preset.excludeModules.includes(moduleName))) {
            return;
        }
        
        // Enable module toggle
        if (moduleToggle) {
            moduleToggle.checked = true;
            updateModuleVisualState(module, true);
        }
        
        // Apply permissions
        preset.permissions.forEach(permission => {
            const checkbox = module.querySelector(`.${permission.replace('can_', 'can-')}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // Select all pages if view permission is granted
        if (preset.permissions.includes('can_view')) {
            module.querySelectorAll('.page-checkbox').forEach(pageCheckbox => {
                pageCheckbox.checked = true;
            });
        }
    });
}

// Toggle module permissions
function toggleModulePermissions(moduleName, enabled) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    console.log(`Toggling module ${moduleName}: ${enabled ? 'enabled' : 'disabled'}`);
    
    // Update module visual state
    updateModuleVisualState(module, enabled);
    
    if (enabled) {
        // Enable view permission by default
        const viewCheckbox = module.querySelector('.can-view');
        if (viewCheckbox) {
            viewCheckbox.checked = true;
            console.log(`Enabled view permission for ${moduleName}`);
        }
        
        // Select all pages
        const pageCheckboxes = module.querySelectorAll('.page-checkbox');
        pageCheckboxes.forEach(cb => {
            cb.checked = true;
        });
        console.log(`Selected ${pageCheckboxes.length} pages for ${moduleName}`);
        
    } else {
        // Disable all permissions except the module toggle
        const checkboxes = module.querySelectorAll('input[type="checkbox"]:not(.module-toggle)');
        checkboxes.forEach(cb => {
            cb.checked = false;
        });
        console.log(`Disabled all permissions for ${moduleName}`);
    }
}

// Update module visual state
function updateModuleVisualState(module, enabled) {
    console.log(`Updating visual state for module: ${module.dataset.module} = ${enabled}`);
    
    if (enabled) {
        module.classList.remove('module-disabled');
        module.classList.add('module-enabled');
        
        // Enable all form elements in the module
        const formElements = module.querySelectorAll('input, button, select, textarea');
        formElements.forEach(element => {
            if (!element.classList.contains('module-toggle')) {
                element.disabled = false;
            }
        });
        
        // Remove disabled styling from card body
        const cardBody = module.querySelector('.card-body');
        if (cardBody) {
            cardBody.style.pointerEvents = 'auto';
            cardBody.style.opacity = '1';
        }
        
    } else {
        module.classList.remove('module-enabled');
        module.classList.add('module-disabled');
        
        // Disable all form elements in the module except the toggle
        const formElements = module.querySelectorAll('input, button, select, textarea');
        formElements.forEach(element => {
            if (!element.classList.contains('module-toggle')) {
                element.disabled = true;
            }
        });
        
        // Add disabled styling to card body
        const cardBody = module.querySelector('.card-body');
        if (cardBody) {
            cardBody.style.pointerEvents = 'none';
            cardBody.style.opacity = '0.5';
        }
    }
}

// Select all pages for a module
function selectAllPages(moduleName) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    module.querySelectorAll('.page-checkbox').forEach(cb => {
        cb.checked = true;
    });
}

// Clear all pages for a module
function clearAllPages(moduleName) {
    const module = document.querySelector(`[data-module="${moduleName}"]`);
    if (!module) return;
    
    module.querySelectorAll('.page-checkbox').forEach(cb => {
        cb.checked = false;
    });
}

// Save permissions
function savePermissions() {
    console.log('Saving permissions for user:', currentUserId);
    
    // Show loading overlay
    document.getElementById('loadingOverlay').classList.remove('d-none');
    
    // Collect all permission data
    const permissionData = {};
    
    document.querySelectorAll('.permission-module').forEach(module => {
        const moduleName = module.dataset.module;
        const moduleToggle = module.querySelector('.module-toggle');
        
        console.log(`Processing module: ${moduleName}`);
        console.log(`Module toggle checked: ${moduleToggle ? moduleToggle.checked : 'no toggle found'}`);
        
        // Skip if module is not enabled
        if (!moduleToggle || !moduleToggle.checked) {
            console.log(`Skipping module ${moduleName} - not enabled`);
            return;
        }
        
        const canView = module.querySelector('.can-view')?.checked || false;
        const canAdd = module.querySelector('.can-add')?.checked || false;
        const canEdit = module.querySelector('.can-edit')?.checked || false;
        const canDelete = module.querySelector('.can-delete')?.checked || false;
        
        permissionData[moduleName] = {
            can_view: canView,
            can_add: canAdd,
            can_edit: canEdit,
            can_delete: canDelete,
            visible_pages: []
        };
        
        // Collect visible pages
        module.querySelectorAll('.page-checkbox:checked').forEach(pageCheckbox => {
            permissionData[moduleName].visible_pages.push(pageCheckbox.value);
        });
        
        console.log(`Module ${moduleName} permissions:`, permissionData[moduleName]);
    });
    
    console.log('Final permission data:', permissionData);
    
    // Check if we have any permissions to save
    if (Object.keys(permissionData).length === 0) {
        console.log('No permissions to save - all modules are disabled');
        // Still send the request to clear all permissions
    }
    
    // Get CSRF token
    let csrfToken = '';
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfInput) {
        csrfToken = csrfInput.value;
    } else {
        // Try to get from cookie
        const csrfCookie = document.cookie.split(';').find(cookie => cookie.trim().startsWith('csrftoken='));
        if (csrfCookie) {
            csrfToken = csrfCookie.split('=')[1];
        }
    }
    
    console.log('CSRF Token:', csrfToken);
    
    // Send AJAX request
    fetch(`/accounts/user/${currentUserId}/permissions/save/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(permissionData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('Save response:', data);
        
        // Hide loading overlay
        document.getElementById('loadingOverlay').classList.add('d-none');
        
        if (data.success) {
            // Show success message in center of screen
            showCenterMessage('success', data.message, data.permissions_count || 0);
            
        } else {
            // Show error message in center of screen
            showCenterMessage('error', data.message || 'حدث خطأ في حفظ الصلاحيات');
        }
    })
    .catch(error => {
        console.error('Error saving permissions:', error);
        
        // Hide loading overlay
        document.getElementById('loadingOverlay').classList.add('d-none');
        
        // Show error message in center of screen
        showCenterMessage('error', `حدث خطأ في الاتصال: ${error.message}`);
    });
}

// Show message in center of screen
function showCenterMessage(type, message, permissionsCount = 0) {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'center-message-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
    `;
    
    // Create message container
    const messageContainer = document.createElement('div');
    messageContainer.className = 'center-message-container';
    messageContainer.style.cssText = `
        background: white;
        padding: 3rem 2rem;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        text-align: center;
        max-width: 500px;
        width: 90%;
        animation: messageSlideIn 0.5s ease-out;
        position: relative;
    `;
    
    // Add animation keyframes
    if (!document.querySelector('#centerMessageStyles')) {
        const style = document.createElement('style');
        style.id = 'centerMessageStyles';
        style.textContent = `
            @keyframes messageSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            
            @keyframes messageSlideOut {
                from {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
            }
            
            .message-slide-out {
                animation: messageSlideOut 0.3s ease-in forwards;
            }
        `;
        document.head.appendChild(style);
    }
    
    let iconClass, iconColor, titleText, redirectDelay;
    
    if (type === 'success') {
        iconClass = 'fas fa-check-circle';
        iconColor = '#28a745';
        titleText = 'تم حفظ الصلاحيات بنجاح!';
        redirectDelay = 3000; // 3 seconds for success
    } else {
        iconClass = 'fas fa-exclamation-triangle';
        iconColor = '#dc3545';
        titleText = 'حدث خطأ في حفظ الصلاحيات';
        redirectDelay = 5000; // 5 seconds for error
    }
    
    // Create message content
    messageContainer.innerHTML = `
        <div style="margin-bottom: 1.5rem;">
            <i class="${iconClass}" style="font-size: 4rem; color: ${iconColor}; margin-bottom: 1rem;"></i>
            <h3 style="color: #333; margin-bottom: 1rem; font-weight: bold;">${titleText}</h3>
            <p style="color: #666; font-size: 1.1rem; margin-bottom: 0;">${message}</p>
            ${type === 'success' && permissionsCount > 0 ? 
                `<p style="color: #28a745; font-weight: bold; margin-top: 0.5rem;">
                    <i class="fas fa-info-circle me-1"></i>
                    تم حفظ ${permissionsCount} صلاحية بنجاح
                </p>` : ''
            }
        </div>
        
        <div style="margin-bottom: 1.5rem;">
            <div class="progress" style="height: 6px; background-color: #e9ecef;">
                <div class="progress-bar ${type === 'success' ? 'bg-success' : 'bg-danger'}" 
                     role="progressbar" style="width: 0%; transition: width 0.1s linear;"></div>
            </div>
            <small style="color: #666; margin-top: 0.5rem; display: block;">
                ${type === 'success' ? 'سيتم توجيهك لصفحة المستخدمين خلال' : 'سيتم إغلاق هذه الرسالة خلال'} 
                <span class="countdown">${Math.ceil(redirectDelay / 1000)}</span> ثانية
            </small>
        </div>
        
        <div>
            ${type === 'success' ? 
                `<button class="btn btn-success me-2" onclick="redirectToUsers()">
                    <i class="fas fa-users me-1"></i>
                    الانتقال لصفحة المستخدمين
                </button>` : ''
            }
            <button class="btn btn-secondary" onclick="closeCenterMessage()">
                <i class="fas fa-times me-1"></i>
                إغلاق
            </button>
        </div>
    `;
    
    // Add to page
    overlay.appendChild(messageContainer);
    document.body.appendChild(overlay);
    
    // Start countdown and progress bar
    const progressBar = messageContainer.querySelector('.progress-bar');
    const countdownElement = messageContainer.querySelector('.countdown');
    let timeLeft = Math.ceil(redirectDelay / 1000);
    
    const countdownInterval = setInterval(() => {
        timeLeft--;
        countdownElement.textContent = timeLeft;
        
        // Update progress bar
        const progress = ((Math.ceil(redirectDelay / 1000) - timeLeft) / Math.ceil(redirectDelay / 1000)) * 100;
        progressBar.style.width = progress + '%';
        
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            if (type === 'success') {
                redirectToUsers();
            } else {
                closeCenterMessage();
            }
        }
    }, 1000);
    
    // Store interval for cleanup
    overlay.countdownInterval = countdownInterval;
}

// Close center message
function closeCenterMessage() {
    const overlay = document.querySelector('.center-message-overlay');
    if (overlay) {
        // Clear countdown interval
        if (overlay.countdownInterval) {
            clearInterval(overlay.countdownInterval);
        }
        
        // Add slide out animation
        const container = overlay.querySelector('.center-message-container');
        container.classList.add('message-slide-out');
        
        // Remove after animation
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.remove();
            }
        }, 300);
    }
}

// Redirect to users page
function redirectToUsers() {
    // Close message first
    closeCenterMessage();
    
    // Add a small delay for smooth transition
    setTimeout(() => {
        window.location.href = '/accounts/';
    }, 300);
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, initializing permission management...');
    initializePermissionManagement();
});
</script>
{% endblock %}