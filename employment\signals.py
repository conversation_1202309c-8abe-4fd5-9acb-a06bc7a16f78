"""
Django signals for employment app
"""
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import ServicePurchase
from .utils import clear_employee_cache


@receiver(post_save, sender=ServicePurchase)
def service_purchase_post_save(sender, instance, created, **kwargs):
    """
    Clear cache when ServicePurchase is created or updated
    """
    clear_employee_cache()


@receiver(post_delete, sender=ServicePurchase)
def service_purchase_post_delete(sender, instance, **kwargs):
    """
    Clear cache when ServicePurchase is deleted
    """
    clear_employee_cache()