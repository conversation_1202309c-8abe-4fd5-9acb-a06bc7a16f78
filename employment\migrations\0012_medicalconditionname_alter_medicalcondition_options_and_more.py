# Generated by Django 5.2 on 2025-05-14 22:17

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0011_medicalcondition'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalConditionName',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='اسم الحالة المرضية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'اسم حالة مرضية',
                'verbose_name_plural': 'أسماء الحالات المرضية',
                'ordering': ['name'],
            },
        ),
        migrations.AlterModelOptions(
            name='medicalcondition',
            options={'ordering': ['-medical_report_date'], 'verbose_name': 'حالة مرضية', 'verbose_name_plural': 'الحالات المرضية'},
        ),
        migrations.RemoveField(
            model_name='medicalcondition',
            name='condition_name',
        ),
        migrations.RemoveField(
            model_name='medicalcondition',
            name='diagnosis_date',
        ),
        migrations.RemoveField(
            model_name='medicalcondition',
            name='resolution_date',
        ),
        migrations.RemoveField(
            model_name='medicalcondition',
            name='severity',
        ),
        migrations.RemoveField(
            model_name='medicalcondition',
            name='status',
        ),
        migrations.AddField(
            model_name='medicalcondition',
            name='medical_report_date',
            field=models.DateField(default=datetime.datetime(2025, 5, 14, 22, 17, 26, 384438, tzinfo=datetime.timezone.utc), verbose_name='تاريخ التقرير الطبي'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='medicalcondition',
            name='condition',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='medical_conditions', to='employment.medicalconditionname', verbose_name='الحالة المرضية'),
            preserve_default=False,
        ),
    ]
