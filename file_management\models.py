from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee

class File(models.Model):
    """Model for employee files"""

    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('archived', _('مؤرشف')),
        ('lost', _('مفقود')),
    ]

    file_number = models.CharField(_('رقم الملف'), max_length=50, unique=True)
    title = models.CharField(_('عنوان الملف'), max_length=255)
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='files',
        verbose_name=_('الموظف')
    )
    status = models.CharField(_('حالة الملف'), max_length=20, choices=STATUS_CHOICES, default='active')
    description = models.TextField(_('وصف الملف'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('ملف')
        verbose_name_plural = _('الملفات')
        ordering = ['file_number']

    def __str__(self):
        return f"{self.file_number} - {self.title}"

class FileMovement(models.Model):
    """Model for tracking employee file movements"""

    STATUS_CHOICES = [
        ('out', _('خارج')),
        ('returned', _('تمت الإعادة')),
    ]

    file = models.ForeignKey(
        File,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name=_('الملف'),
        null=True,
        blank=True
    )
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='file_movements',
        verbose_name=_('الموظف')
    )
    checkout_date = models.DateField(_('تاريخ خروج الملف'))
    return_date = models.DateField(_('تاريخ عودة الملف'), blank=True, null=True)
    action_taken = models.TextField(_('الإجراء المتخذ'), blank=True, null=True)
    status = models.CharField(_('الحالة'), max_length=10, choices=STATUS_CHOICES, default='out')
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('حركة الملف')
        verbose_name_plural = _('حركات الملفات')
        ordering = ['-checkout_date']

    def __str__(self):
        file_info = f"{self.file.file_number} - " if self.file else ""
        return f"{file_info}{self.employee.full_name} - {self.checkout_date}"