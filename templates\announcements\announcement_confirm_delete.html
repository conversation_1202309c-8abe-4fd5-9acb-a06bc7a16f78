{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الإعلان - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .warning-card {
        border-left: 5px solid #dc3545;
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    .announcement-preview {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid #dee2e6;
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 5px;
        min-width: 150px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .danger-zone {
        border: 2px dashed #dc3545;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        background: rgba(220, 53, 69, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Delete Header -->
    <div class="delete-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-exclamation-triangle fa-4x"></i>
            </div>
            <div class="col-md-8">
                <h1 class="mb-2">تأكيد حذف الإعلان</h1>
                <h4 class="mb-0">هل أنت متأكد من حذف هذا الإعلان؟</h4>
            </div>
            <div class="col-md-2">
                <a href="{% url 'announcements:announcement_detail' announcement.pk %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> العودة
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">            
            <!-- Warning Card -->
            <div class="card warning-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle fa-3x text-danger me-3"></i>
                        <div>
                            <h5 class="text-danger mb-2">تحذير مهم!</h5>
                            <p class="mb-0">
                                سيتم حذف الإعلان نهائياً ولن يمكن استرداده. هذا الإجراء لا يمكن التراجع عنه.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Announcement Preview -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-eye"></i> معاينة الإعلان المراد حذفه
                    </h5>
                </div>
                <div class="card-body">
                    <div class="announcement-preview">
                        <div class="d-flex align-items-start mb-3">
                            <i class="fas {{ announcement.type_icon }} fa-2x text-{{ announcement.type_color }} me-3 mt-1"></i>
                            <div class="flex-grow-1">
                                <h4 class="text-primary mb-2">{{ announcement.title }}</h4>
                                <p class="text-muted mb-3">{{ announcement.content|truncatewords:30 }}</p>
                                
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <span class="badge bg-{{ announcement.type_color }}">
                                        {{ announcement.get_announcement_type_display }}
                                    </span>
                                    <span class="badge bg-{{ announcement.priority_color }}">
                                        {{ announcement.get_priority_display }}
                                    </span>
                                    {% if announcement.is_active %}
                                        <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير مفعل</span>
                                    {% endif %}
                                    {% if announcement.show_on_homepage %}
                                        <span class="badge bg-info">الصفحة الرئيسية</span>
                                    {% endif %}
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <small class="text-muted">أضيف بواسطة</small>
                                        <div class="fw-bold">{{ announcement.created_by.get_full_name|default:announcement.created_by.username }}</div>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">تاريخ الإضافة</small>
                                        <div class="fw-bold">{{ announcement.created_at|date:"Y-m-d" }}</div>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">المشاهدات</small>
                                        <div class="fw-bold text-info">{{ announcement.views_count }}</div>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">النقرات</small>
                                        <div class="fw-bold text-success">{{ announcement.clicks_count }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Impact Information -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> تأثير الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">ما سيحدث عند الحذف:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i> سيتم حذف الإعلان نهائياً</li>
                                <li><i class="fas fa-check text-success me-2"></i> سيختفي من الصفحة الرئيسية</li>
                                <li><i class="fas fa-check text-success me-2"></i> سيتم حذف جميع الإحصائيات</li>
                                <li><i class="fas fa-check text-success me-2"></i> سيتم تسجيل العملية في السجل</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-danger">تنبيهات مهمة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> لا يمكن التراجع عن الحذف</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> ستفقد جميع الإحصائيات</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> المستخدمون لن يروا الإعلان</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> الروابط ستصبح غير صالحة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confirmation Form -->
            <div class="danger-zone">
                <h4 class="text-danger mb-3">
                    <i class="fas fa-skull-crossbones"></i> منطقة الخطر
                </h4>
                <p class="text-muted mb-4">
                    للمتابعة، اضغط على زر "حذف الإعلان نهائياً" أدناه.
                    هذا الإجراء لا يمكن التراجع عنه.
                </p>
                
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger btn-action" onclick="return confirmDelete()">
                        <i class="fas fa-trash-alt"></i> حذف الإعلان نهائياً
                    </button>
                </form>
                
                <a href="{% url 'announcements:announcement_detail' announcement.pk %}" class="btn btn-secondary btn-action">
                    <i class="fas fa-times"></i> إلغاء العملية
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    return confirm(
        'هل أنت متأكد تماماً من حذف هذا الإعلان؟\n\n' +
        'العنوان: {{ announcement.title }}\n' +
        'المشاهدات: {{ announcement.views_count }}\n' +
        'النقرات: {{ announcement.clicks_count }}\n\n' +
        'تذكر: هذا الإجراء لا يمكن التراجع عنه!'
    );
}

// Add visual feedback
$(document).ready(function() {
    $('.btn-danger').hover(
        function() {
            $(this).html('<i class="fas fa-skull-crossbones"></i> تأكيد الحذف النهائي');
        },
        function() {
            $(this).html('<i class="fas fa-trash-alt"></i> حذف الإعلان نهائياً');
        }
    );
});
</script>
{% endblock %}