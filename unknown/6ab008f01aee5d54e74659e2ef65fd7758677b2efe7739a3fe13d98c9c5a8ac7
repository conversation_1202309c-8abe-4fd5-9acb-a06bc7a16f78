/**
 * Employee search functionality
 * This script handles searching for employees by ministry number
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Employee search script loaded');
    
    // Find the search button and input field
    const searchButton = document.getElementById('search_employee_btn');
    const ministryNumberInput = document.querySelector('input[name="ministry_number"]');
    const employeeIdInput = document.getElementById('id_employee_id');
    const employeeNameDisplay = document.getElementById('employee_name_display');
    const errorDisplay = document.getElementById('ministry_number_error');
    
    if (searchButton && ministryNumberInput) {
        console.log('Search button and input found');
        
        // Function to search for employee
        const searchEmployee = () => {
            const ministryNumber = ministryNumberInput.value.trim();
            console.log('Searching for ministry number:', ministryNumber);
            
            if (!ministryNumber) {
                if (errorDisplay) {
                    errorDisplay.textContent = 'الرجاء إدخال الرقم الوزاري';
                    errorDisplay.classList.remove('d-none');
                }
                return;
            }
            
            // Clear previous results
            if (errorDisplay) {
                errorDisplay.classList.add('d-none');
            }
            
            // Make AJAX request
            fetch(`/employees/get-by-ministry-number/?ministry_number=${ministryNumber}`)
                .then(response => {
                    console.log('Response received:', response);
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    if (data.success) {
                        // Employee found
                        if (employeeIdInput) {
                            employeeIdInput.value = data.employee.id;
                        }
                        if (employeeNameDisplay) {
                            employeeNameDisplay.innerHTML = '<i class="fas fa-user"></i> <strong>الموظف:</strong> ' + data.employee.full_name;
                            employeeNameDisplay.classList.remove('d-none');
                        }
                    } else {
                        // Error occurred
                        if (errorDisplay) {
                            errorDisplay.textContent = data.error || 'حدث خطأ أثناء البحث';
                            errorDisplay.classList.remove('d-none');
                        }
                        if (employeeNameDisplay) {
                            employeeNameDisplay.classList.add('d-none');
                        }
                        if (employeeIdInput) {
                            employeeIdInput.value = '';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (errorDisplay) {
                        errorDisplay.textContent = 'لم يتم العثور على موظف بهذا الرقم الوزاري';
                        errorDisplay.classList.remove('d-none');
                    }
                    if (employeeNameDisplay) {
                        employeeNameDisplay.classList.add('d-none');
                    }
                    if (employeeIdInput) {
                        employeeIdInput.value = '';
                    }
                });
        };
        
        // Add event listeners
        searchButton.addEventListener('click', function(e) {
            e.preventDefault();
            searchEmployee();
        });
        
        // Search on Enter key
        ministryNumberInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmployee();
            }
        });
    } else {
        console.error('Search button or input not found');
    }
});
