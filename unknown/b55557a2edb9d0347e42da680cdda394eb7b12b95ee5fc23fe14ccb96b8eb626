# Generated by Django 5.2 on 2025-05-01 10:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='internaltransfer',
            name='requested_department',
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='address',
            field=models.TextField(blank=True, null=True, verbose_name='العنوان'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='edit_token',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رمز التعديل'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='first_choice',
            field=models.CharField(default='القسم الحالي', max_length=100, verbose_name='الخيار الأول'),
            preserve_default=False,
        ),
        migrations.Add<PERSON>ield(
            model_name='internaltransfer',
            name='gender',
            field=models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], default='male', max_length=10, verbose_name='الجنس'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='hire_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ التعيين'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='last_position',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='آخر مسمى وظيفي'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='last_rank',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='آخر رتبة'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='ministry_number',
            field=models.CharField(default='000000', max_length=20, verbose_name='الرقم الوزاري'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='qualification',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='المؤهل العلمي'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='second_choice',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الخيار الثاني'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='specialization',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='التخصص'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='third_choice',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الخيار الثالث'),
        ),
        migrations.AddField(
            model_name='internaltransfer',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='internaltransfer',
            name='employee_id',
            field=models.CharField(max_length=20, verbose_name='الرقم الوطني'),
        ),
    ]
