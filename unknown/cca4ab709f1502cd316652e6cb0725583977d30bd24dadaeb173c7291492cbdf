/**
 * JavaScript functions for handling permissions updates
 */

// Function to force reload the page with cache busting
function forceReloadPage() {
    // Add a timestamp to the URL to force a fresh load
    const timestamp = new Date().getTime();
    const currentUrl = window.location.href;
    const separator = currentUrl.indexOf('?') !== -1 ? '&' : '?';
    const newUrl = currentUrl + separator + '_=' + timestamp;
    
    // Reload the page with the new URL
    window.location.href = newUrl;
}

// Function to reload permissions via AJAX
function reloadPermissions() {
    // Create a new XMLHttpRequest
    const xhr = new XMLHttpRequest();
    
    // Configure it to make a GET request to the reload permissions URL
    xhr.open('GET', '/accounts/reload-permissions/?ajax=true', true);
    
    // Set up what happens when the request is successful
    xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
            console.log('Permissions reloaded successfully');
            // Force reload the page to show the updated sidebar
            forceReloadPage();
        } else {
            console.error('Failed to reload permissions');
        }
    };
    
    // Set up what happens in case of error
    xhr.onerror = function() {
        console.error('Request failed');
    };
    
    // Send the request
    xhr.send();
}

// Function to add event listeners to permission forms
function setupPermissionForms() {
    // Add event listeners to all permission forms
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete permission forms
        document.querySelectorAll('.delete-permission-form').forEach(function(form) {
            form.addEventListener('submit', function(e) {
                // Prevent default form submission
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(form);
                
                // Submit the form via fetch API
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        console.log('Permission deleted successfully');
                        // Reload permissions and refresh the page
                        reloadPermissions();
                    } else {
                        console.error('Failed to delete permission');
                        // Submit the form normally if fetch fails
                        form.submit();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Submit the form normally if fetch fails
                    form.submit();
                });
            });
        });
        
        // Handle add permission form in modal
        const modalForm = document.getElementById('modalPermissionForm');
        if (modalForm) {
            modalForm.addEventListener('submit', function(e) {
                // Prevent default form submission
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(modalForm);
                
                // Submit the form via fetch API
                fetch(modalForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        console.log('Permission added successfully');
                        // Reload permissions and refresh the page
                        reloadPermissions();
                    } else {
                        console.error('Failed to add permission');
                        // Submit the form normally if fetch fails
                        modalForm.submit();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Submit the form normally if fetch fails
                    modalForm.submit();
                });
            });
        }
    });
}

// Initialize the permission forms
setupPermissionForms();
