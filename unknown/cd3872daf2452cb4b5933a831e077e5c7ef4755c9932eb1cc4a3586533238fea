{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الموظف - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل الموظف</h2>
    <div>
        <a href="{% url 'employment:employment_update' employment.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للتوظيف
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات التوظيف</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الموظف</th>
                        <td>
                            <a href="{% url 'employees:employee_detail' employment.employee.pk %}">
                                {{ employment.employee.full_name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">القسم</th>
                        <td>
                            <a href="{% url 'employment:department_detail' employment.department.pk %}">
                                {{ employment.department.name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">المسمى الوظيفي</th>
                        <td>{{ employment.position.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الحالة</th>
                        <td>{{ employment.status.get_name_display }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ البداية</th>
                        <td>{{ employment.start_date }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ النهاية</th>
                        <td>{{ employment.end_date|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">حالي</th>
                        <td>
                            {% if employment.is_current %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات الموظف</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الرقم الوزاري</th>
                        <td>{{ employment.employee.ministry_number }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الاسم الكامل</th>
                        <td>{{ employment.employee.full_name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">المؤهل العلمي</th>
                        <td>{{ employment.employee.qualification }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">التخصص</th>
                        <td>{{ employment.employee.specialization }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ التعيين</th>
                        <td>{{ employment.employee.hire_date }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-danger text-white">
                <h6 class="m-0 font-weight-bold">خيارات متقدمة</h6>
            </div>
            <div class="card-body">
                <a href="{% url 'employment:employment_delete' employment.pk %}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف التوظيف
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
