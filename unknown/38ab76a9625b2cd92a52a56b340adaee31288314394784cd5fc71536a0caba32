{% extends 'base.html' %}
{% load static %}

{% block title %}استعادة نسخة احتياطية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>استعادة نسخة احتياطية</h2>
        <a href="{% url 'backup:backup_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">استعادة نسخة احتياطية</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="mb-3">
                    <label class="form-label">نوع الاستعادة</label>
                    <div class="mb-3">
                        {% for radio in form.restore_type %}
                        <div class="form-check">
                            {{ radio.tag }}
                            <label class="form-check-label" for="{{ radio.id_for_label }}">
                                {{ radio.choice_label }}
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.restore_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.restore_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3 backup-select-container">
                    <label for="{{ form.backup.id_for_label }}" class="form-label">اختر النسخة الاحتياطية</label>
                    {{ form.backup }}
                    {% if form.backup.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.backup.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3 backup-file-container" style="display: none;">
                    <label for="{{ form.backup_file.id_for_label }}" class="form-label">ملف النسخة الاحتياطية</label>
                    <div class="input-group">
                        {{ form.backup_file }}
                    </div>
                    {% if form.backup_file.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.backup_file.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.backup_file.help_text }}</div>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <strong>تحذير!</strong> استعادة نسخة احتياطية سيؤدي إلى استبدال جميع البيانات الحالية. هذه العملية لا يمكن التراجع عنها.
                </div>

                <div class="mb-3 form-check">
                    {{ form.confirm }}
                    <label class="form-check-label" for="{{ form.confirm.id_for_label }}">
                        أتفهم أن هذه العملية ستؤدي إلى استبدال قاعدة البيانات الحالية
                    </label>
                    {% if form.confirm.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.confirm.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-undo"></i> استعادة النسخة الاحتياطية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const restoreTypeRadios = document.querySelectorAll('input[name="restore_type"]');
        const backupSelectContainer = document.querySelector('.backup-select-container');
        const backupFileContainer = document.querySelector('.backup-file-container');

        // Function to toggle containers based on selected restore type
        function toggleContainers() {
            const selectedValue = document.querySelector('input[name="restore_type"]:checked').value;

            if (selectedValue === 'from_list') {
                backupSelectContainer.style.display = '';
                backupFileContainer.style.display = 'none';
            } else { // from_file
                backupSelectContainer.style.display = 'none';
                backupFileContainer.style.display = '';
            }
        }

        // Add event listeners to radio buttons
        restoreTypeRadios.forEach(function(radio) {
            radio.addEventListener('change', toggleContainers);
        });

        // Initial toggle based on default selection
        toggleContainers();
    });
</script>
{% endblock %}