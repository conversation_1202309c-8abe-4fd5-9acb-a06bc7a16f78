{% load system_log_tags %}

{% for log in logs %}
<tr>
    <td>{{ log.timestamp|localize_datetime|safe }}</td>
    <td>{{ log.user.username|default:"مجهول" }}</td>
    <td>{{ log.get_module_display }}</td>
    <td>{{ log.get_action_display }}</td>
    <td>{{ log.page }}</td>
    <td class="text-wrap description-cell">
        {{ log.description|default:"-"|safe }}
    </td>
    <td>
        {% if log.operating_system %}
            {% if 'Windows' in log.operating_system %}
                <span class="badge bg-primary" title="{{ log.operating_system }}">
                    <i class="fab fa-windows me-1"></i> {{ log.operating_system }}
                </span>
            {% elif 'macOS' in log.operating_system %}
                <span class="badge bg-secondary" title="{{ log.operating_system }}">
                    <i class="fab fa-apple me-1"></i> {{ log.operating_system }}
                </span>
            {% elif 'Linux' in log.operating_system %}
                <span class="badge bg-danger" title="{{ log.operating_system }}">
                    <i class="fab fa-linux me-1"></i> {{ log.operating_system }}
                </span>
            {% elif 'Android' in log.operating_system %}
                <span class="badge bg-success" title="{{ log.operating_system }}">
                    <i class="fab fa-android me-1"></i> {{ log.operating_system }}
                </span>
            {% elif 'iOS' in log.operating_system %}
                <span class="badge bg-info" title="{{ log.operating_system }}">
                    <i class="fab fa-apple me-1"></i> {{ log.operating_system }}
                </span>
            {% else %}
                <span class="badge bg-secondary" title="{{ log.operating_system }}">
                    <i class="fas fa-desktop me-1"></i> {{ log.operating_system }}
                </span>
            {% endif %}
        {% else %}
            <span class="badge bg-secondary">
                <i class="fas fa-question-circle me-1"></i> غير معروف
            </span>
        {% endif %}
        <br>
        <small class="text-muted mt-1 d-inline-block">
            <i class="fas fa-network-wired me-1"></i> {{ log.ip_address|default:"غير معروف" }}
        </small>
    </td>
</tr>
{% empty %}
<tr>
    <td colspan="7" class="text-center">لا توجد سجلات</td>
</tr>
{% endfor %}
