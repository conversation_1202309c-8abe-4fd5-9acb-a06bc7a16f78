# نظام الموارد البشرية

## متطلبات النظام

- Python 3.8 أو أحدث
- Django 5.2
- جميع المكتبات المذكورة في ملف `requirements.txt`

## تهيئة النظام لأول مرة أو عند الانتقال إلى جهاز جديد

### الطريقة 1: استخدام ملف التهيئة التلقائي (موصى بها)

1. انقر نقرًا مزدوجًا على ملف `تهيئة_النظام.bat` لتثبيت جميع المتطلبات وتهيئة النظام.
2. سيقوم الملف بإنشاء بيئة افتراضية وتثبيت جميع المكتبات اللازمة وإعداد قاعدة البيانات.
3. انتظر حتى تكتمل عملية التهيئة.

### الطريقة 2: تثبيت المتطلبات يدويًا

1. افتح موجه الأوامر (Command Prompt) أو PowerShell.
2. انتقل إلى مجلد المشروع.
3. قم بإنشاء بيئة افتراضية:
   ```
   py -m venv venv
   ```
4. قم بتنشيط البيئة الافتراضية:
   ```
   venv\Scripts\activate
   ```
5. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
6. قم بإعداد قاعدة البيانات:
   ```
   python manage.py migrate
   ```

## تشغيل النظام

### الطريقة 1: استخدام ملف التشغيل التلقائي

1. انقر نقرًا مزدوجًا على ملف `start_hr_system.bat` لتشغيل النظام بالكامل.
2. سيقوم الملف بفحص قاعدة البيانات وإصلاحها إذا لزم الأمر، والتحقق من وجود مستخدم مسؤول، ثم تشغيل الخادم.
3. افتح المتصفح وانتقل إلى `http://localhost:8000/accounts/login/` لتسجيل الدخول.

### الطريقة 2: تشغيل الخادم يدويًا

1. افتح موجه الأوامر (Command Prompt) أو PowerShell.
2. انتقل إلى مجلد المشروع.
3. قم بتنفيذ الأمر التالي:

```
python manage.py runserver
```

4. افتح المتصفح وانتقل إلى `http://localhost:8000/accounts/login/` لتسجيل الدخول.

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## أدوات إضافية

- `تهيئة_النظام.bat`: لتثبيت جميع المتطلبات وتهيئة النظام بشكل كامل.
- `setup_environment.py`: سكريبت بايثون لتهيئة النظام (يستخدمه ملف التهيئة).
- `check_database.bat`: لفحص قاعدة البيانات وإصلاحها إذا لزم الأمر.
- `check_admin.bat`: للتحقق من وجود مستخدم مسؤول وإنشائه إذا لم يكن موجودًا.
- `start_server.bat`: لتشغيل الخادم فقط.
- `تشغيل_السيرفر.bat`: لتشغيل الخادم باللغة العربية.
- `فحص_السيرفر.py`: للتحقق من حالة الخادم.

## المكتبات المستخدمة

يحتوي ملف `requirements.txt` على جميع المكتبات اللازمة لتشغيل النظام، وتشمل:

- **Django**: إطار عمل الويب الأساسي
- **django-crispy-forms**: لتنسيق النماذج
- **openpyxl**: للتعامل مع ملفات Excel
- **pandas**: لمعالجة البيانات
- **reportlab**: لإنشاء ملفات PDF
- **Pillow**: لمعالجة الصور
- **وغيرها من المكتبات الضرورية**

## حقوق النشر

جميع الحقوق محفوظة لمديرية تربية قصبة المفرق - المبرمج أحمد العمري
