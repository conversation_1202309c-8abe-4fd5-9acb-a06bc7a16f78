{% extends 'base.html' %}

{% block title %}اختبار النموذج{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold">اختبار النموذج</h6>
    </div>
    <div class="card-body">
        <form method="post" action="{% url 'employment:employee_position_create' %}">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="ministry_number" class="form-label">الرقم الوزاري</label>
                <input type="text" name="ministry_number" id="ministry_number" class="form-control" required>
            </div>
            
            <div class="mb-3">
                <label for="position" class="form-label">المسمى الوظيفي</label>
                <select name="position" id="position" class="form-control" required>
                    <option value="">-- اختر المسمى الوظيفي --</option>
                    {% for pos in positions %}
                    <option value="{{ pos.id }}">{{ pos.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="mb-3">
                <label for="date_obtained" class="form-label">تاريخ الحصول عليه</label>
                <input type="date" name="date_obtained" id="date_obtained" class="form-control" required>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label">ملاحظات</label>
                <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'employment:employee_position_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
