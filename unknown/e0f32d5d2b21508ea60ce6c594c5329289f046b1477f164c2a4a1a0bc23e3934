/**
 * Common JavaScript functions for employee search by ministry number
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all ministry number input fields with the employee-search class
    const ministryNumberInputs = document.querySelectorAll('.employee-search-input');

    ministryNumberInputs.forEach(input => {
        // Get the associated elements
        const employeeIdInput = document.getElementById(input.dataset.employeeIdTarget);
        const employeeNameDisplay = document.getElementById(input.dataset.employeeNameTarget);
        const searchButton = document.getElementById(input.dataset.searchButtonId);
        const errorDisplay = document.getElementById(input.dataset.errorTarget);

        // Function to search for employee
        const searchEmployee = () => {
            const ministryNumber = input.value.trim();

            if (!ministryNumber) {
                if (errorDisplay) {
                    errorDisplay.textContent = 'الرجاء إدخال الرقم الوزاري';
                    errorDisplay.classList.remove('d-none');
                }
                return;
            }

            // Clear previous results
            if (errorDisplay) {
                errorDisplay.classList.add('d-none');
            }

            // Make AJAX request
            fetch(`/employees/get-by-ministry-number/?ministry_number=${ministryNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Employee found
                        if (employeeIdInput) {
                            employeeIdInput.value = data.employee.id;
                        }
                        if (employeeNameDisplay) {
                            employeeNameDisplay.innerHTML = '<i class="fas fa-user"></i> <strong>الموظف:</strong> ' + data.employee.full_name;
                            employeeNameDisplay.classList.remove('d-none');
                        }
                    } else {
                        // Error occurred
                        if (errorDisplay) {
                            errorDisplay.textContent = data.error || 'حدث خطأ أثناء البحث';
                            errorDisplay.classList.remove('d-none');
                        }
                        if (employeeNameDisplay) {
                            employeeNameDisplay.classList.add('d-none');
                        }
                        if (employeeIdInput) {
                            employeeIdInput.value = '';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (errorDisplay) {
                        errorDisplay.textContent = 'لم يتم العثور على موظف بهذا الرقم الوزاري';
                        errorDisplay.classList.remove('d-none');
                    }
                    if (employeeNameDisplay) {
                        employeeNameDisplay.classList.add('d-none');
                    }
                    if (employeeIdInput) {
                        employeeIdInput.value = '';
                    }
                });
        };

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', function(e) {
                e.preventDefault();
                searchEmployee();
            });
        }

        // Search on Enter key
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmployee();
            }
        });
    });
});
