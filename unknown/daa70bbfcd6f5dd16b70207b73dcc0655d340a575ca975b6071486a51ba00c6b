{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الملف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل الملف</h2>
    <div>
        <a href="{% url 'file_management:file_update' file.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'file_management:file_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لقائمة الملفات
        </a>
    </div>
</div>

<!-- File Details -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات الملف</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th style="width: 200px;">رقم الملف</th>
                        <td>{{ file.file_number }}</td>
                    </tr>
                    <tr>
                        <th>عنوان الملف</th>
                        <td>{{ file.title }}</td>
                    </tr>
                    <tr>
                        <th>الموظف</th>
                        <td>{{ file.employee.full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوزاري</th>
                        <td>{{ file.employee.ministry_number }}</td>
                    </tr>
                    <tr>
                        <th>حالة الملف</th>
                        <td>
                            {% if file.status == 'active' %}
                                <span class="badge bg-success text-white">نشط</span>
                            {% elif file.status == 'archived' %}
                                <span class="badge bg-secondary text-white">مؤرشف</span>
                            {% elif file.status == 'lost' %}
                                <span class="badge bg-danger text-white">مفقود</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>وصف الملف</th>
                        <td>{{ file.description|default:"لا يوجد وصف" }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء</th>
                        <td>{{ file.created_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                    <tr>
                        <th>آخر تحديث</th>
                        <td>{{ file.updated_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- File Movements -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">حركات الملف</h6>
        <a href="{% url 'file_management:file_movement_create' %}" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i> إضافة حركة جديدة
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>تاريخ خروج الملف</th>
                        <th>تاريخ عودة الملف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in file.movements.all %}
                    <tr>
                        <td>{{ movement.employee.full_name }}</td>
                        <td>{{ movement.checkout_date|date:"Y-m-d" }}</td>
                        <td>{{ movement.return_date|date:"Y-m-d"|default:"لم يتم الإرجاع بعد" }}</td>
                        <td>
                            {% if movement.status == 'out' %}
                                <span class="badge bg-warning text-dark">خارج</span>
                            {% elif movement.status == 'returned' %}
                                <span class="badge bg-success text-white">تمت الإعادة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'file_management:file_movement_detail' movement.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if movement.status == 'out' %}
                                <a href="{% url 'file_management:file_return' movement.id %}" class="btn btn-success btn-sm">
                                    <i class="fas fa-undo"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا توجد حركات لهذا الملف</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
