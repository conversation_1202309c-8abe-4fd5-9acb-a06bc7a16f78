from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from .models import PerformanceEvaluation

@admin.register(PerformanceEvaluation)
class PerformanceEvaluationAdmin(ImportExportModelAdmin):
    list_display = ('employee', 'year', 'score', 'max_score', 'percentage', 'evaluator')
    list_filter = ('year',)
    search_fields = ('employee__full_name', 'employee__ministry_number', 'evaluator', 'comments')
