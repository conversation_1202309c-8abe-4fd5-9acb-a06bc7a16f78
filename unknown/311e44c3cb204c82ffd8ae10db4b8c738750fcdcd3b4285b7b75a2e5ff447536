from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import JsonResponse
from .models import PenaltyType, Penalty
from .forms import PenaltyTypeForm, PenaltyForm
from employees.models import Employee

@login_required
def penalty_list(request):
    search_query = request.GET.get('search', '')
    year_filter = request.GET.get('year', '')
    penalty_type_filter = request.GET.get('penalty_type', '')

    # Base queryset
    penalties = Penalty.objects.all()

    # Apply search filter
    if search_query:
        penalties = penalties.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(decision_number__icontains=search_query)
        )

    # Apply year filter
    if year_filter and year_filter != 'all':
        penalties = penalties.filter(date__year=year_filter)

    # Apply penalty type filter
    if penalty_type_filter and penalty_type_filter != 'all':
        penalties = penalties.filter(penalty_type_id=penalty_type_filter)

    # Get unique years for the filter
    years = Penalty.objects.dates('date', 'year', order='DESC').values_list('date__year', flat=True)

    # Get penalty types for the filter
    penalty_types = PenaltyType.objects.all()

    # Count unique employees with penalties
    employee_count = penalties.values('employee').distinct().count()

    return render(request, 'disciplinary/penalty_list.html', {
        'penalties': penalties,
        'search_query': search_query,
        'years': years,
        'year_filter': year_filter,
        'penalty_types': penalty_types,
        'penalty_type_filter': penalty_type_filter,
        'employee_count': employee_count
    })

@login_required
def penalty_create(request):
    if request.method == 'POST':
        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'disciplinary/penalty_form.html', {'form': PenaltyForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'disciplinary/penalty_form.html', {'form': PenaltyForm(post_data)})

        # Create form with modified POST data
        form = PenaltyForm(post_data)

        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'disciplinary/penalty_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'disciplinary/penalty_form.html', {'form': form})

                # Create a new penalty
                penalty = form.save(commit=False)
                penalty.employee = employee
                penalty.save()

                messages.success(request, 'تم إضافة العقوبة بنجاح.')
                return redirect('disciplinary:penalty_detail', pk=penalty.pk)

            except Exception as e:
                print('Error saving penalty:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ العقوبة: {str(e)}')
        else:
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        form = PenaltyForm()
    return render(request, 'disciplinary/penalty_form.html', {'form': form})

@login_required
def penalty_detail(request, pk):
    penalty = get_object_or_404(Penalty, pk=pk)
    return render(request, 'disciplinary/penalty_detail.html', {'penalty': penalty})

@login_required
def penalty_update(request, pk):
    penalty = get_object_or_404(Penalty, pk=pk)
    if request.method == 'POST':
        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'disciplinary/penalty_form.html', {'form': PenaltyForm(post_data), 'penalty': penalty})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'disciplinary/penalty_form.html', {'form': PenaltyForm(post_data), 'penalty': penalty})

        # Create form with modified POST data
        form = PenaltyForm(post_data, instance=penalty)
        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'disciplinary/penalty_form.html', {'form': form, 'penalty': penalty})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'disciplinary/penalty_form.html', {'form': form, 'penalty': penalty})

                # Update the penalty
                updated_penalty = form.save(commit=False)
                updated_penalty.employee = employee
                updated_penalty.save()

                messages.success(request, 'تم تحديث العقوبة بنجاح.')
                return redirect('disciplinary:penalty_detail', pk=penalty.pk)
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث العقوبة: {str(e)}')
        else:
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        # Initialize form with employee data for the template
        form = PenaltyForm(instance=penalty, initial={
            'ministry_number': penalty.employee.ministry_number,
            'employee_name': penalty.employee.full_name,
            'employee_id': penalty.employee.id
        })
    return render(request, 'disciplinary/penalty_form.html', {'form': form, 'penalty': penalty})

@login_required
def penalty_delete(request, pk):
    penalty = get_object_or_404(Penalty, pk=pk)
    if request.method == 'POST':
        penalty.delete()
        messages.success(request, 'تم حذف العقوبة بنجاح.')
        return redirect('disciplinary:penalty_list')
    return render(request, 'disciplinary/penalty_confirm_delete.html', {'penalty': penalty})

@login_required
def penalty_type_list(request):
    penalty_types = PenaltyType.objects.all()
    return render(request, 'disciplinary/penalty_type_list.html', {'penalty_types': penalty_types})

@login_required
def penalty_type_create(request):
    if request.method == 'POST':
        form = PenaltyTypeForm(request.POST)
        if form.is_valid():
            penalty_type = form.save()
            messages.success(request, 'تم إضافة نوع العقوبة بنجاح.')
            return redirect('disciplinary:penalty_type_list')
    else:
        form = PenaltyTypeForm()
    return render(request, 'disciplinary/penalty_type_form.html', {'form': form})

@login_required
def penalty_type_update(request, pk):
    penalty_type = get_object_or_404(PenaltyType, pk=pk)
    if request.method == 'POST':
        form = PenaltyTypeForm(request.POST, instance=penalty_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع العقوبة بنجاح.')
            return redirect('disciplinary:penalty_type_list')
    else:
        form = PenaltyTypeForm(instance=penalty_type)
    return render(request, 'disciplinary/penalty_type_form.html', {'form': form, 'penalty_type': penalty_type})

@login_required
def penalty_type_delete(request, pk):
    penalty_type = get_object_or_404(PenaltyType, pk=pk)
    if request.method == 'POST':
        penalty_type.delete()
        messages.success(request, 'تم حذف نوع العقوبة بنجاح.')
        return redirect('disciplinary:penalty_type_list')
    return render(request, 'disciplinary/penalty_type_confirm_delete.html', {'penalty_type': penalty_type})

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
