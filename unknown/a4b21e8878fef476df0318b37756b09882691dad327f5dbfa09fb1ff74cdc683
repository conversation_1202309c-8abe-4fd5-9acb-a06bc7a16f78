from django import forms
from .models import Leave, LeaveType
from employees.models import Employee

class UnpaidLeaveForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Original employee field but hidden
    employee = forms.ModelChoiceField(
        queryset=Employee.objects.all().order_by('full_name'),
        label='الموظف',
        widget=forms.Select(attrs={'class': 'select2', 'style': 'display: none;'})
    )

    class Meta:
        model = Leave
        fields = ['employee', 'start_date', 'end_date', 'reason']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'reason': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set leave_type to unpaid by default
        try:
            unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)
            self.instance.leave_type = unpaid_leave_type
        except LeaveType.DoesNotExist:
            pass
