{% extends 'base.html' %}
{% load static %}

{% block title %}{% if file %}تعديل ملف{% else %}إضافة ملف جديد{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if file %}تعديل ملف{% else %}إضافة ملف جديد{% endif %}</h2>
    <a href="{% url 'file_management:file_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لقائمة الملفات
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات الملف</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger alert-permanent">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.file_number.id_for_label }}" class="form-label">رقم الملف</label>
                {{ form.file_number }}
                {% if form.file_number.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.file_number.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.title.id_for_label }}" class="form-label">عنوان الملف</label>
                {{ form.title }}
                {% if form.title.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.title.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.employee.id_for_label }}" class="form-label">الموظف</label>
                {{ form.employee }}
                {% if form.employee.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.employee.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.status.id_for_label }}" class="form-label">حالة الملف</label>
                {{ form.status }}
                {% if form.status.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.status.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">وصف الملف</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
