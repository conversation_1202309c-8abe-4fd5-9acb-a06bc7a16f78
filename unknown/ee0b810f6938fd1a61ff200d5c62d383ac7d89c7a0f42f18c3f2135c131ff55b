{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل رتبة الموظف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل رتبة الموظف</h2>
    <div>
        <a href="{% url 'ranks:employee_rank_update' employee_rank.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لقائمة رتب الموظفين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات الرتبة</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الموظف</th>
                        <td>
                            <a href="{% url 'employees:employee_detail' employee_rank.employee.pk %}">
                                {{ employee_rank.employee.full_name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">الرقم الوزاري</th>
                        <td>{{ employee_rank.employee.ministry_number }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">نوع الرتبة</th>
                        <td>{{ employee_rank.rank_type.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الحصول عليها</th>
                        <td>{{ employee_rank.date_obtained|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">ملاحظات</th>
                        <td>{{ employee_rank.notes|default:"-" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">رتب الموظف الأخرى</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>نوع الرتبة</th>
                                <th>تاريخ الحصول عليها</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rank in employee_rank.employee.ranks.all %}
                            {% if rank.id != employee_rank.id %}
                            <tr>
                                <td>{{ rank.rank_type.name }}</td>
                                <td>{{ rank.date_obtained|date:"Y-m-d" }}</td>
                                <td>
                                    <a href="{% url 'ranks:employee_rank_detail' rank.pk %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center">لا يوجد رتب أخرى لهذا الموظف</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
