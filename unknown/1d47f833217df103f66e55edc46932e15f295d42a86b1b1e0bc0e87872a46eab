# Generated by Django 5.2 on 2025-05-01 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0003_internaltransfer_actual_service'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('internal_transfer_enabled', models.BooleanField(default=True, verbose_name='تفعيل صفحة النقل الداخلي')),
                ('internal_transfer_message', models.TextField(default='لقد انتهت فترة تقديم طلبات النقل الداخلي', verbose_name='رسالة صفحة النقل الداخلي عند التعطيل')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
    ]
