from django.urls import path
from . import views

app_name = 'file_management'

urlpatterns = [
    # AJAX endpoints
    path('get-employee/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),

    # File movement URLs
    path('movements/', views.file_movement_list, name='file_movement_list'),
    path('movements/<int:pk>/', views.file_movement_detail, name='file_movement_detail'),
    path('movements/add/', views.file_movement_create, name='file_movement_create'),
    path('movements/<int:pk>/edit/', views.file_movement_update, name='file_movement_update'),
    path('movements/<int:pk>/delete/', views.file_movement_delete, name='file_movement_delete'),
    path('checkout/', views.file_checkout, name='file_checkout'),
    path('completed/', views.file_return_list, name='file_return_list'),
    path('movements/<int:pk>/return/', views.file_return, name='file_return'),

    # File URLs
    path('', views.file_list, name='file_list'),
    path('<int:pk>/', views.file_detail, name='file_detail'),
    path('add/', views.file_create, name='file_create'),
    path('<int:pk>/edit/', views.file_update, name='file_update'),
    path('<int:pk>/delete/', views.file_delete, name='file_delete'),
]
