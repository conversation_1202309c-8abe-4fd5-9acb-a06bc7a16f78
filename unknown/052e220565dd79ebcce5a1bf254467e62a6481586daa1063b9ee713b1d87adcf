{% extends 'base.html' %}
{% load static %}

{% block title %}استيراد وتصدير بيانات الموظفين - نظام شؤون الموظفين{% endblock %}

{% block styles %}
<style>
    /* تنسيقات عامة */
    .content-wrapper, .container-fluid {
        padding: 0 !important;
        margin: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .page-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        color: #4e73df;
        font-weight: bold;
        border-bottom: 2px solid #4e73df;
        padding-bottom: 0.5rem;
        display: inline-block;
        margin-bottom: 1.5rem;
    }

    .card {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
        margin-bottom: 2rem;
    }

    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 1rem 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* تنسيقات قسم الاستيراد */
    .import-steps {
        counter-reset: step-counter;
        margin-bottom: 2rem;
    }

    .import-step {
        position: relative;
        padding-right: 3rem;
        margin-bottom: 1.5rem;
        min-height: 2.5rem;
    }

    .import-step:before {
        content: counter(step-counter);
        counter-increment: step-counter;
        position: absolute;
        right: 0;
        top: 0;
        width: 2.5rem;
        height: 2.5rem;
        background-color: #4e73df;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .import-form {
        background-color: #f8f9fc;
        border: 2px solid #d1d3e2;
        border-radius: 0.5rem;
        padding: 1.5rem;
        transition: all 0.3s;
    }

    .import-form:hover {
        border-color: #4e73df;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(78, 115, 223, 0.1);
    }

    .template-preview {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        overflow-x: auto;
    }

    /* تنسيقات قسم التصدير */
    .export-options {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .export-option {
        flex: 1;
        min-width: 250px;
        background-color: #fff;
        border-radius: 0.5rem;
        border: 2px solid #e3e6f0;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s;
    }

    .export-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .export-option.excel {
        border-color: #1cc88a;
    }

    .export-option.excel:hover {
        background-color: #f0fff8;
    }

    .export-option.pdf {
        border-color: #e74a3b;
    }

    .export-option.pdf:hover {
        background-color: #fff5f5;
    }

    .export-option i {
        font-size: 4rem;
        margin-bottom: 1rem;
        display: block;
    }

    .export-option.excel i {
        color: #1cc88a;
    }

    .export-option.pdf i {
        color: #e74a3b;
    }

    /* تنسيقات الجداول */
    .table-preview {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1.5rem;
    }

    .table-preview th, .table-preview td {
        padding: 0.75rem;
        border: 1px solid #e3e6f0;
        text-align: center;
    }

    .table-preview th {
        background-color: #f8f9fc;
        font-weight: bold;
    }

    /* تنسيقات التنبيهات */
    .alert-info {
        background-color: #e8f4fd;
        border-color: #b8daff;
        margin-bottom: 1.5rem;
    }

    /* تنسيقات الإحصائيات */
    .stats-card {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .stats-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e3e6f0;
    }

    .stats-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .stats-value {
        font-weight: bold;
        color: #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- رأس الصفحة -->
    <div class="page-header d-flex justify-content-between align-items-center">
        <h3 class="font-weight-bold text-primary mb-0">استيراد وتصدير بيانات الموظفين</h3>
        <div>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right mr-2"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- قسم الاستيراد -->
        <div class="col-lg-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="m-0 font-weight-bold text-primary">استيراد بيانات الموظفين</h5>
                    <a href="{% static 'templates/employee_template.xlsx' %}" class="btn btn-success" download>
                        <i class="fas fa-download mr-2"></i> تحميل قالب الاستيراد
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6 class="font-weight-bold mb-3">خطوات الاستيراد:</h6>
                            <div class="import-steps">
                                <div class="import-step">
                                    <h6 class="font-weight-bold">تحميل قالب الاستيراد</h6>
                                    <p>قم بتحميل قالب ملف Excel الجاهز من خلال الضغط على زر "تحميل قالب الاستيراد".</p>
                                </div>
                                <div class="import-step">
                                    <h6 class="font-weight-bold">تعبئة البيانات</h6>
                                    <p>قم بتعبئة بيانات الموظفين في القالب مع الالتزام بالأعمدة المحددة.</p>
                                </div>
                                <div class="import-step">
                                    <h6 class="font-weight-bold">رفع الملف</h6>
                                    <p>قم برفع الملف بعد تعبئته من خلال النموذج أدناه.</p>
                                </div>
                            </div>

                            <div class="alert alert-info alert-permanent">
                                <h6 class="alert-heading font-weight-bold">ملاحظات هامة:</h6>
                                <ul class="mb-0">
                                    <li>تأكد من تعبئة جميع الحقول الإلزامية (الرقم الوزاري، الاسم الكامل، القسم، تاريخ التعيين، الجنس).</li>
                                    <li>تأكد من صحة تنسيق التواريخ (YYYY-MM-DD).</li>
                                    <li>تأكد من أن قيمة الجنس هي "ذكر" أو "انثى" فقط.</li>
                                    <li>سيتم تجاهل الموظفين الموجودين مسبقاً (بنفس الرقم الوزاري).</li>
                                </ul>
                            </div>

                            <form method="post" enctype="multipart/form-data" class="import-form">
                                {% csrf_token %}
                                <div class="row align-items-end">
                                    <div class="col-md-8">
                                        <label for="excel_file" class="form-label font-weight-bold">اختر ملف Excel</label>
                                        <input class="form-control form-control-lg" type="file" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-file-import mr-2"></i> استيراد البيانات
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="col-lg-6">
                            <h6 class="font-weight-bold mb-3">نموذج قالب الاستيراد:</h6>
                            <div class="template-preview">
                                <table class="table-preview">
                                    <thead>
                                        <tr>
                                            <th>الرقم الوزاري</th>
                                            <th>الرقم الوطني</th>
                                            <th>الاسم الكامل</th>
                                            <th>المؤهل العلمي</th>
                                            <th>التخصص</th>
                                            <th>تاريخ التعيين</th>
                                            <th>القسم</th>
                                            <th>تاريخ الميلاد</th>
                                            <th>العنوان</th>
                                            <th>رقم الهاتف</th>
                                            <th>الجنس</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>123456</td>
                                            <td>9801234567</td>
                                            <td>اسم موظف نموذجي</td>
                                            <td>بكالوريوس</td>
                                            <td>علوم حاسوب</td>
                                            <td>2020-01-01</td>
                                            <td>قسم تكنولوجيا المعلومات</td>
                                            <td>1990-05-10</td>
                                            <td>عمان - الأردن</td>
                                            <td>0777123456</td>
                                            <td>ذكر</td>
                                        </tr>
                                        <tr>
                                            <td>789012</td>
                                            <td>9876543210</td>
                                            <td>اسم موظف نموذجي آخر</td>
                                            <td>ماجستير</td>
                                            <td>إدارة أعمال</td>
                                            <td>2019-05-15</td>
                                            <td>قسم الإدارة</td>
                                            <td>1985-12-20</td>
                                            <td>إربد - الأردن</td>
                                            <td>0799876543</td>
                                            <td>انثى</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التصدير -->
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">تصدير بيانات الموظفين</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <h6 class="font-weight-bold mb-3">خيارات التصدير:</h6>
                            <div class="export-options">
                                <div class="export-option excel">
                                    <i class="fas fa-file-excel"></i>
                                    <h5>تصدير إلى Excel</h5>
                                    <p>تصدير جميع بيانات الموظفين بصيغة Excel</p>
                                    <a href="{% url 'employees:employee_import_export' %}?export=excel" class="btn btn-success btn-lg">
                                        <i class="fas fa-download mr-2"></i> تنزيل Excel
                                    </a>
                                </div>

                                <div class="export-option pdf">
                                    <i class="fas fa-file-pdf"></i>
                                    <h5>تصدير إلى PDF</h5>
                                    <p>تصدير جميع بيانات الموظفين بصيغة PDF</p>
                                    <a href="{% url 'employees:employee_import_export' %}?export=pdf" class="btn btn-danger btn-lg">
                                        <i class="fas fa-download mr-2"></i> تنزيل PDF
                                    </a>
                                </div>
                            </div>

                            <h6 class="font-weight-bold mb-3">ميزات التصدير:</h6>
                            <ul>
                                <li>تصدير جميع بيانات الموظفين دفعة واحدة.</li>
                                <li>تضمين جميع المعلومات الأساسية بما فيها الجنس والمؤهلات العلمية.</li>
                                <li>تنسيق ملائم للطباعة والمشاركة.</li>
                                <li>إمكانية التعديل على ملف Excel بعد التصدير.</li>
                            </ul>
                        </div>

                        <div class="col-lg-4">
                            <div class="stats-card">
                                <h6 class="font-weight-bold mb-3">إحصائيات الموظفين:</h6>
                                <div class="stats-item">
                                    <span>إجمالي عدد الموظفين:</span>
                                    <span class="stats-value">{{ employees_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الموظفين الذكور:</span>
                                    <span class="stats-value">{{ male_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الموظفات الإناث:</span>
                                    <span class="stats-value">{{ female_count|default:"0" }}</span>
                                </div>
                                <div class="stats-item">
                                    <span>عدد الأقسام:</span>
                                    <span class="stats-value">{{ departments_count|default:"0" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين تجربة المستخدم عند رفع الملف
        const fileInput = document.getElementById('excel_file');
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const fileName = e.target.files[0].name;
                    const fileLabel = document.querySelector('label[for="excel_file"]');
                    if (fileLabel) {
                        fileLabel.textContent = 'الملف المختار: ' + fileName;
                    }
                }
            });
        }
    });
</script>
{% endblock %}
