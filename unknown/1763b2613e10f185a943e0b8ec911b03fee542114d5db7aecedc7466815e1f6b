{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المغادرات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة المغادرات</h2>
    <div>
        <a href="{% url 'leaves:departure_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مغادرة جديدة
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع المغادرات</h6>
        <form class="d-flex" method="get">
            <input class="form-control me-2" type="search" placeholder="بحث..." name="search">
            <button class="btn btn-outline-primary" type="submit">بحث</button>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع المغادرة</th>
                        <th>التاريخ</th>
                        <th>من الساعة</th>
                        <th>إلى الساعة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for departure in departures %}
                    <tr>
                        <td>
                            <a href="{% url 'employees:employee_detail' departure.employee.pk %}">
                                {{ departure.employee.full_name }}
                            </a>
                        </td>
                        <td>
                            {% if departure.departure_type == 'personal' %}
                                <span class="badge bg-info">شخصية</span>
                            {% else %}
                                <span class="badge bg-primary">رسمية</span>
                            {% endif %}
                        </td>
                        <td>{{ departure.date }}</td>
                        <td>{{ departure.time_from }}</td>
                        <td>{{ departure.time_to }}</td>
                        <td>
                            {% if departure.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif departure.status == 'approved' %}
                                <span class="badge bg-success">موافق عليها</span>
                            {% elif departure.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوضة</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="#" class="btn btn-info btn-sm view-departure" data-id="{{ departure.pk }}">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-warning btn-sm edit-departure" data-id="{{ departure.pk }}">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-danger btn-sm delete-departure" data-id="{{ departure.pk }}">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">لا يوجد مغادرات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add event listeners for buttons
    document.addEventListener('DOMContentLoaded', function() {
        const viewButtons = document.querySelectorAll('.view-departure');
        const editButtons = document.querySelectorAll('.edit-departure');
        const deleteButtons = document.querySelectorAll('.delete-departure');
        
        viewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                // Implement view functionality
                alert('عرض المغادرة: ' + id);
            });
        });
        
        editButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                // Implement edit functionality
                alert('تعديل المغادرة: ' + id);
            });
        });
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذه المغادرة؟')) {
                    // Implement delete functionality
                    alert('تم حذف المغادرة: ' + id);
                }
            });
        });
    });
</script>
{% endblock %}
