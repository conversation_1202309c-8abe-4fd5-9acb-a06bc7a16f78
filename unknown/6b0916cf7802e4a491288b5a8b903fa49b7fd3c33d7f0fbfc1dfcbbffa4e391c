# Generated by Django 5.2 on 2025-05-01 09:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='InternalTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_name', models.Char<PERSON>ield(max_length=100, verbose_name='اسم الموظف')),
                ('employee_id', models.CharField(max_length=20, verbose_name='الرقم الوظيفي')),
                ('current_department', models.CharField(max_length=100, verbose_name='القسم الحالي')),
                ('requested_department', models.CharField(max_length=100, verbose_name='القسم المطلوب')),
                ('reason', models.TextField(verbose_name='سبب النقل')),
                ('phone_number', models.<PERSON>r<PERSON><PERSON>(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'طلب نقل داخلي',
                'verbose_name_plural': 'طلبات النقل الداخلي',
                'ordering': ['-created_at'],
            },
        ),
    ]
