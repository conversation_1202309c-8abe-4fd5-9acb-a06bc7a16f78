from django import template

register = template.Library()

@register.filter
def get_item(positions_list, index):
    """Get an item from a list by index"""
    try:
        return positions_list[index]
    except (IndexError, TypeError):
        return None

@register.filter
def get_next_date(position):
    """Get the next date for a position"""
    from employment.models import EmployeePosition
    
    try:
        # Get the next position by date
        next_position = EmployeePosition.objects.filter(
            employee=position.employee,
            date_obtained__gt=position.date_obtained
        ).order_by('date_obtained').first()
        
        if next_position:
            return next_position.date_obtained
        return None
    except Exception:
        return None
