# Generated by Django 5.2 on 2025-04-09 06:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('checkout_date', models.DateField(verbose_name='تاريخ خروج الملف')),
                ('return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ عودة الملف')),
                ('action_taken', models.TextField(blank=True, null=True, verbose_name='الإجراء المتخذ')),
                ('status', models.CharField(choices=[('out', 'خارج'), ('returned', 'تمت الإعادة')], default='out', max_length=10, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='file_movements', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حركة الملف',
                'verbose_name_plural': 'حركات الملفات',
                'ordering': ['-checkout_date'],
            },
        ),
    ]
