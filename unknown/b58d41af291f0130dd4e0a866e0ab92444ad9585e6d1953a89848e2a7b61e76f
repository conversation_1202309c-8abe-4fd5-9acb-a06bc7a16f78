from django import forms
from .models import PerformanceEvaluation
from employees.models import Employee

class PerformanceEvaluationForm(forms.ModelForm):
    employee = forms.ModelChoiceField(
        queryset=Employee.objects.all().order_by('full_name'),
        label='الموظف',
        widget=forms.Select(attrs={'class': 'select2'})
    )

    class Meta:
        model = PerformanceEvaluation
        fields = ['employee', 'year', 'score', 'max_score', 'evaluator', 'comments']
        widgets = {
            'comments': forms.Textarea(attrs={'rows': 3}),
        }

class PerformanceImportForm(forms.Form):
    excel_file = forms.FileField(
        label='ملف Excel',
        help_text='يجب أن يحتوي الملف على الأعمدة المطلوبة'
    )
