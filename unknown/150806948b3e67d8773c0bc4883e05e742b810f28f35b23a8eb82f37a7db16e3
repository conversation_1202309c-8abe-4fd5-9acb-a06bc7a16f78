# Generated by Django 5.2 on 2025-04-14 11:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('module', models.CharField(choices=[('employees', 'Employees'), ('employment', 'Employment'), ('leaves', 'Leaves'), ('performance', 'Performance'), ('reports', 'Reports'), ('accounts', 'Accounts'), ('backup', 'Backup'), ('disciplinary', 'Disciplinary'), ('files', 'Files'), ('ranks', 'Ranks'), ('system', 'System')], max_length=20, verbose_name='Module')),
                ('action', models.Char<PERSON>ield(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('view', 'View'), ('login', 'Login'), ('logout', 'Logout'), ('export', 'Export'), ('import', 'Import'), ('backup', 'Backup'), ('restore', 'Restore'), ('other', 'Other')], max_length=20, verbose_name='Action')),
                ('page', models.CharField(max_length=255, verbose_name='Page')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('object_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='Object ID')),
                ('object_repr', models.CharField(blank=True, max_length=255, null=True, verbose_name='Object Representation')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Log',
                'verbose_name_plural': 'System Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
