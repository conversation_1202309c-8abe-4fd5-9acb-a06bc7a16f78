{% extends 'base.html' %}
{% load static %}

{% block title %}{% if penalty %}تعديل عقوبة{% else %}إضافة عقوبة{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% if penalty %}تعديل عقوبة{% else %}إضافة عقوبة{% endif %}</h1>
        <a href="{% url 'disciplinary:penalty_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% if penalty %}تعديل عقوبة{% else %}إضافة عقوبة جديدة{% endif %}</h6>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}

                {{ form.employee }}
                <input type="hidden" name="employee_id" id="id_employee_id" value="">

                <div class="mb-3">
                    <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                    <div class="input-group">
                        {{ form.ministry_number }}
                        <button class="btn btn-primary" type="button" id="search_employee_btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                    <div id="ministry_number_error" class="invalid-feedback d-none"></div>
                    {% if form.ministry_number.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.ministry_number.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                    {{ form.employee_name }}
                    {% if form.employee_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee_name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.penalty_type.id_for_label }}" class="form-label">نوع العقوبة</label>
                    {{ form.penalty_type }}
                    {% if form.penalty_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.penalty_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.date.id_for_label }}" class="form-label">التاريخ</label>
                    {{ form.date }}
                    {% if form.date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.decision_number.id_for_label }}" class="form-label">رقم القرار</label>
                    {{ form.decision_number }}
                    {% if form.decision_number.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.decision_number.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.decision_date.id_for_label }}" class="form-label">تاريخ القرار</label>
                    {{ form.decision_date }}
                    {% if form.decision_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.decision_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/disciplinary/get-employee/?ministry_number=${ministryNumber}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        if (response.status === 403) {
                            // Redirect to login page if not authenticated
                            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                            throw new Error('يجب تسجيل الدخول للمتابعة');
                        }
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    // Check if data is successful and employee data exists
                    if (!data.success) {
                        throw new Error(data.error || 'بيانات الموظف غير متوفرة');
                    }

                    if (!data.employee) {
                        throw new Error('بيانات الموظف غير متوفرة');
                    }

                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement && selectElement.options) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.employee.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    } else if (selectElement) {
                        // If options is undefined but selectElement exists, create a new option
                        const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                        selectElement.appendChild(newOption);
                    }
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput && ministryNumberInput.value && employeeNameDisplay && !employeeNameDisplay.value) {
            searchEmployee();
        }

        // Add form submission handler
        const form = document.querySelector('form');
        const saveButton = document.querySelector('button[type="submit"]');

        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Form submitted');

                // Check if employee is selected
                if (!employeeIdInput || !employeeIdInput.value) {
                    e.preventDefault();
                    alert('الرجاء اختيار موظف أولاً');
                    return false;
                }

                // Disable the save button to prevent double submission
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                }

                // Make sure the employee ID is set in the form
                if (employeeIdInput && employeeIdInput.value) {
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == employeeIdInput.value) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(employeeNameDisplay.value, employeeIdInput.value, true, true);
                            selectElement.appendChild(newOption);
                        }
                    }
                }

                return true;
            });
        }
    });
</script>
{% endblock %}
