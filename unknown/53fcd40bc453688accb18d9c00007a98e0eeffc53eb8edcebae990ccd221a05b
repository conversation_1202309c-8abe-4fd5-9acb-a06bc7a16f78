{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحليلات البيانات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-primary fw-bold">
            <i class="fas fa-chart-line me-2"></i>لوحة تحليلات البيانات
        </h1>
        <div>
            <a href="#" class="btn btn-sm btn-primary shadow-sm" onclick="window.print()">
                <i class="fas fa-download fa-sm text-white me-1"></i> تصدير التقرير
            </a>
            <button id="refreshData" class="btn btn-sm btn-outline-primary shadow-sm ms-2">
                <i class="fas fa-sync-alt fa-sm me-1"></i> تحديث البيانات
            </button>
        </div>
    </div>

    <!-- Overview Cards -->
    <div class="row mb-4">
        <!-- Total Employees Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي الموظفين</div>
                            <div class="h3 mb-0 fw-bold text-gray-800 d-flex align-items-center">
                                {{ total_employees }}
                                <span class="ms-2 fs-6 text-success">
                                    <i class="fas fa-arrow-up me-1"></i>{{ employee_growth }}%
                                </span>
                            </div>
                            <div class="text-muted small mt-2">مقارنة بالشهر السابق</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                <i class="fas fa-users fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">الأقسام</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ total_departments }}</div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span class="text-muted small">إجمالي الأقسام</span>
                                <span class="badge bg-success rounded-pill">
                                    <i class="fas fa-check-circle me-1"></i>{{ departments_with_employees }} مشغول
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                <i class="fas fa-building fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Positions Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-info shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">المسميات الوظيفية</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ total_positions }}</div>
                            <div class="text-muted small mt-2">{{ positions_with_employees }} مسمى وظيفي مشغول</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                <i class="fas fa-briefcase fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Leaves Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-warning shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">الإجازات النشطة</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ active_leaves }}</div>
                            <div class="text-muted small mt-2">{{ leave_percentage }}% من الموظفين</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                                <i class="fas fa-calendar-alt fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Gender Distribution Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100 rounded-3">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-white">
                    <h6 class="m-0 fw-bold text-primary">توزيع الموظفين حسب الجنس</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="genderDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="genderDropdown">
                            <div class="dropdown-header">خيارات الرسم البياني:</div>
                            <a class="dropdown-item chart-type" href="#" data-chart="gender" data-type="pie">رسم دائري</a>
                            <a class="dropdown-item chart-type" href="#" data-chart="gender" data-type="doughnut">رسم حلقي</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" onclick="exportChart('genderChart', 'توزيع الموظفين حسب الجنس')">تصدير كصورة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="genderChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="me-2">
                            <i class="fas fa-circle text-primary"></i> ذكور
                        </span>
                        <span class="me-2">
                            <i class="fas fa-circle text-success"></i> إناث
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Qualification Distribution Chart -->
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card shadow h-100 rounded-3">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-white">
                    <h6 class="m-0 fw-bold text-primary">توزيع الموظفين حسب المؤهل العلمي</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="qualificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="qualificationDropdown">
                            <div class="dropdown-header">خيارات الرسم البياني:</div>
                            <a class="dropdown-item chart-type" href="#" data-chart="qualification" data-type="bar">رسم شريطي</a>
                            <a class="dropdown-item chart-type" href="#" data-chart="qualification" data-type="horizontalBar">رسم شريطي أفقي</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" onclick="exportChart('qualificationChart', 'توزيع الموظفين حسب المؤهل العلمي')">تصدير كصورة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="qualificationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Charts Row -->
    <div class="row mb-4">
        <!-- Position Distribution Chart -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card shadow h-100 rounded-3">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-white">
                    <h6 class="m-0 fw-bold text-primary">توزيع الموظفين حسب المسمى الوظيفي</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="positionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="positionDropdown">
                            <div class="dropdown-header">خيارات الرسم البياني:</div>
                            <a class="dropdown-item chart-type" href="#" data-chart="position" data-type="bar">رسم شريطي</a>
                            <a class="dropdown-item chart-type" href="#" data-chart="position" data-type="horizontalBar">رسم شريطي أفقي</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" onclick="exportChart('positionChart', 'توزيع الموظفين حسب المسمى الوظيفي')">تصدير كصورة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="positionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Growth Chart -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow h-100 rounded-3">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-white">
                    <h6 class="m-0 fw-bold text-primary">نمو عدد الموظفين</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="growthDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="growthDropdown">
                            <div class="dropdown-header">خيارات الرسم البياني:</div>
                            <a class="dropdown-item chart-type" href="#" data-chart="growth" data-type="line">رسم خطي</a>
                            <a class="dropdown-item chart-type" href="#" data-chart="growth" data-type="bar">رسم شريطي</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" onclick="exportChart('growthChart', 'نمو عدد الموظفين')">تصدير كصورة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="growthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow rounded-3">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-white">
                    <h6 class="m-0 fw-bold text-primary">توزيع الموظفين حسب الأقسام</h6>
                    <div>
                        <input type="text" id="departmentSearch" class="form-control form-control-sm" placeholder="بحث..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered" id="departmentTable">
                            <thead class="table-light">
                                <tr>
                                    <th>القسم</th>
                                    <th>عدد الموظفين</th>
                                    <th>النسبة المئوية</th>
                                    <th>توزيع الجنس (ذكور/إناث)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in departments_data %}
                                <tr>
                                    <td>{{ dept.name }}</td>
                                    <td>{{ dept.count }}</td>
                                    <td>{{ dept.percentage }}%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ dept.male_percentage }}%"
                                                aria-valuenow="{{ dept.male_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                                {{ dept.male_count }} ({{ dept.male_percentage }}%)
                                            </div>
                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ dept.female_percentage }}%"
                                                aria-valuenow="{{ dept.female_percentage }}" aria-valuemin="0" aria-valuemax="100">
                                                {{ dept.female_count }} ({{ dept.female_percentage }}%)
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Chart.js Plugins -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

<script>
    // Register Chart.js plugins
    Chart.register(ChartDataLabels);

    // Set Chart.js defaults
    Chart.defaults.font.family = 'Tajawal, sans-serif';
    Chart.defaults.font.size = 14;
    Chart.defaults.color = '#666';
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    Chart.defaults.plugins.tooltip.padding = 10;
    Chart.defaults.plugins.tooltip.cornerRadius = 6;
    Chart.defaults.plugins.tooltip.titleFont = { weight: 'bold' };
    Chart.defaults.plugins.datalabels.color = '#fff';
    Chart.defaults.plugins.datalabels.font = { weight: 'bold' };
    Chart.defaults.plugins.datalabels.formatter = (value, ctx) => {
        const dataset = ctx.chart.data.datasets[ctx.datasetIndex];
        const total = dataset.data.reduce((acc, data) => acc + data, 0);
        const percentage = Math.round((value / total) * 100) + '%';
        return percentage;
    };

    // Parse data from Django
    const genderData = JSON.parse('{{ gender_data|safe }}');
    const qualificationData = JSON.parse('{{ qualification_data|safe }}');
    const positionData = JSON.parse('{{ position_data|safe }}');
    const growthData = JSON.parse('{{ growth_data|safe }}');

    // Chart instances
    let genderChart, qualificationChart, positionChart, growthChart;

    // Create charts when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        createGenderChart('pie');
        createQualificationChart('bar');
        createPositionChart('horizontalBar');
        createGrowthChart('line');

        // Initialize search functionality for department table
        initDepartmentSearch();

        // Handle chart type changes
        document.querySelectorAll('.chart-type').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const chart = this.getAttribute('data-chart');
                const type = this.getAttribute('data-type');

                if (chart === 'gender') {
                    genderChart.destroy();
                    createGenderChart(type);
                } else if (chart === 'qualification') {
                    qualificationChart.destroy();
                    createQualificationChart(type);
                } else if (chart === 'position') {
                    positionChart.destroy();
                    createPositionChart(type);
                } else if (chart === 'growth') {
                    growthChart.destroy();
                    createGrowthChart(type);
                }
            });
        });

        // Handle refresh button
        document.getElementById('refreshData').addEventListener('click', function() {
            location.reload();
        });
    });

    // Create gender distribution chart
    function createGenderChart(type) {
        const ctx = document.getElementById('genderChart').getContext('2d');

        // Prepare labels with Arabic names
        const labels = genderData.labels.map(label =>
            genderData.label_names[label] || label
        );

        genderChart = new Chart(ctx, {
            type: type,
            data: {
                labels: labels,
                datasets: [{
                    data: genderData.data,
                    backgroundColor: ['rgba(0, 123, 255, 0.8)', 'rgba(40, 167, 69, 0.8)'],
                    borderColor: ['rgba(0, 123, 255, 1)', 'rgba(40, 167, 69, 1)'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20
                        }
                    },
                    title: {
                        display: false,
                        text: 'توزيع الموظفين حسب الجنس'
                    },
                    datalabels: {
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 14
                        },
                        formatter: (value, ctx) => {
                            const dataset = ctx.chart.data.datasets[ctx.datasetIndex];
                            const total = dataset.data.reduce((acc, data) => acc + data, 0);
                            const percentage = Math.round((value / total) * 100) + '%';
                            return percentage;
                        }
                    }
                }
            }
        });
    }

    // Create qualification distribution chart
    function createQualificationChart(type) {
        const ctx = document.getElementById('qualificationChart').getContext('2d');

        qualificationChart = new Chart(ctx, {
            type: type === 'horizontalBar' ? 'bar' : type, // 'horizontalBar' is deprecated in Chart.js v3
            data: {
                labels: qualificationData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: qualificationData.data,
                    backgroundColor: 'rgba(23, 162, 184, 0.8)',
                    borderColor: 'rgba(23, 162, 184, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: type === 'horizontalBar' ? 'y' : 'x',
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: false,
                        text: 'توزيع الموظفين حسب المؤهل العلمي'
                    },
                    datalabels: {
                        color: '#fff',
                        anchor: 'end',
                        align: 'start',
                        formatter: (value) => value
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Create position distribution chart
    function createPositionChart(type) {
        const ctx = document.getElementById('positionChart').getContext('2d');

        positionChart = new Chart(ctx, {
            type: type === 'horizontalBar' ? 'bar' : type, // 'horizontalBar' is deprecated in Chart.js v3
            data: {
                labels: positionData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: positionData.data,
                    backgroundColor: 'rgba(102, 16, 242, 0.8)',
                    borderColor: 'rgba(102, 16, 242, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: type === 'horizontalBar' ? 'y' : 'x',
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: false,
                        text: 'توزيع الموظفين حسب المسمى الوظيفي'
                    },
                    datalabels: {
                        color: '#fff',
                        anchor: 'end',
                        align: 'start',
                        formatter: (value) => value
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Create employee growth chart
    function createGrowthChart(type) {
        const ctx = document.getElementById('growthChart').getContext('2d');

        growthChart = new Chart(ctx, {
            type: type,
            data: {
                labels: growthData.labels,
                datasets: [{
                    label: 'عدد الموظفين',
                    data: growthData.data,
                    backgroundColor: 'rgba(255, 193, 7, 0.5)',
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: type === 'line'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: false,
                        text: 'نمو عدد الموظفين'
                    },
                    datalabels: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Initialize department table search
    function initDepartmentSearch() {
        const searchInput = document.getElementById('departmentSearch');
        const table = document.getElementById('departmentTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        searchInput.addEventListener('keyup', function() {
            const term = searchInput.value.toLowerCase();

            for (let i = 0; i < rows.length; i++) {
                const deptName = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                if (deptName.indexOf(term) > -1) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        });
    }

    // Export chart as image
    function exportChart(chartId, title) {
        const canvas = document.getElementById(chartId);
        const image = canvas.toDataURL('image/png');

        // Create temporary link and trigger download
        const link = document.createElement('a');
        link.href = image;
        link.download = title + '.png';
        link.click();
    }
</script>
{% endblock %}
