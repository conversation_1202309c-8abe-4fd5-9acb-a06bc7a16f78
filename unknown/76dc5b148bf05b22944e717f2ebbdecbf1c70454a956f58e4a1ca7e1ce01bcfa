# Generated by Django 5.2 on 2025-05-12 16:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0009_alter_technicalposition_notes'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExcessEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='سبب اعتبار الموظف زائد في القسم', verbose_name='سبب الزيادة')),
                ('status', models.CharField(choices=[('pending', 'قيد المعالجة'), ('resolved', 'تمت المعالجة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('resolution_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات المعالجة')),
                ('resolution_date', models.DateField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='excess_employees', to='employment.department', verbose_name='القسم الحالي')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='excess_records', to='employees.employee', verbose_name='الموظف')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='excess_employees', to='employment.position', verbose_name='المسمى الوظيفي')),
            ],
            options={
                'verbose_name': 'موظف زائد',
                'verbose_name_plural': 'الموظفين الزوائد',
                'ordering': ['-created_at'],
            },
        ),
    ]
