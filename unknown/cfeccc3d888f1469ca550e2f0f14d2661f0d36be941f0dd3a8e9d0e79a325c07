{% extends 'base.html' %}
{% load static %}

{% block title %}{% if penalty_type %}تعديل نوع عقوبة{% else %}إضافة نوع عقوبة{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% if penalty_type %}تعديل نوع عقوبة{% else %}إضافة نوع عقوبة{% endif %}</h1>
        <a href="{% url 'disciplinary:penalty_type_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% if penalty_type %}تعديل نوع عقوبة{% else %}إضافة نوع عقوبة جديد{% endif %}</h6>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}

                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">الاسم</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
