from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0001_initial'),
        ('disciplinary', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='penalty',
            options={'ordering': ['-date'], 'verbose_name': 'عقوبة', 'verbose_name_plural': 'العقوبات'},
        ),
        migrations.AlterModelOptions(
            name='penaltytype',
            options={'ordering': ['name'], 'verbose_name': 'نوع العقوبة', 'verbose_name_plural': 'أنواع العقوبات'},
        ),
        migrations.AlterField(
            model_name='penalty',
            name='date',
            field=models.DateField(verbose_name='التاريخ'),
        ),
        migrations.AlterField(
            model_name='penalty',
            name='decision_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ القرار'),
        ),
        migrations.AlterField(
            model_name='penalty',
            name='decision_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم القرار'),
        ),
        migrations.AlterField(
            model_name='penalty',
            name='description',
            field=models.TextField(verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='penalty',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disciplinary_penalties', to='employees.employee', verbose_name='الموظف'),
        ),
        migrations.AlterField(
            model_name='penalty',
            name='penalty_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='penalties', to='disciplinary.penaltytype', verbose_name='نوع العقوبة'),
        ),
        migrations.AlterField(
            model_name='penaltytype',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='penaltytype',
            name='name',
            field=models.CharField(max_length=100, verbose_name='الاسم'),
        ),
        migrations.CreateModel(
            name='UnpaidLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('reason', models.TextField(blank=True, null=True, verbose_name='سبب الإجازة')),
                ('decision_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم القرار')),
                ('decision_date', models.DateField(blank=True, null=True, verbose_name='تاريخ القرار')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='unpaid_leaves', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إجازة بدون راتب',
                'verbose_name_plural': 'إجازات بدون راتب',
                'ordering': ['-start_date'],
            },
        ),
    ]
