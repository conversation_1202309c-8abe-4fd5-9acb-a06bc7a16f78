{% extends 'base.html' %}
{% load static %}

{% block title %}خطأ في النظام - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="text-center">
        <div class="error mx-auto" data-text="{{ error_code|default:'Error' }}">{{ error_code|default:"Error" }}</div>
        <p class="lead text-gray-800 mb-4">{{ error_title|default:"حدث خطأ" }}</p>
        <p class="text-gray-500 mb-0">{{ error_message|default:"حدث خطأ غير متوقع في النظام. يرجى المحاولة مرة أخرى أو الاتصال بمسؤول النظام." }}</p>
        <a href="{% url 'employees:employee_list' %}" class="btn btn-primary mt-4">
            <i class="fas fa-arrow-left"></i> العودة إلى الصفحة الرئيسية
        </a>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .error {
        color: #5a5c69;
        font-size: 7rem;
        position: relative;
        line-height: 1;
        width: 12.5rem;
        margin: 2rem auto;
    }

    .error:before {
        content: attr(data-text);
        position: absolute;
        left: -2px;
        top: 0;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        opacity: 0.8;
        z-index: -1;

        /* Color based on error code */
        {% if error_code == '400' %}
        color: #36b9cc; /* Cyan */
        {% elif error_code == '403' %}
        color: #4e73df; /* Blue */
        {% elif error_code == '404' %}
        color: #f6c23e; /* Yellow */
        {% elif error_code == '500' %}
        color: #e74a3b; /* Red */
        {% else %}
        color: #e74a3b; /* Default: Red */
        {% endif %}
    }
</style>
{% endblock %}
