#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف للتحقق من حالة السيرفر
هذا الملف سيقوم بالتحقق من حالة السيرفر وإظهار رسالة مناسبة
"""

import http.client
import time
import sys
import platform
import webbrowser

# تعريف الألوان للطباعة في الطرفية
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_success(message):
    print(f"{Colors.GREEN}{message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}{message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}{message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}{message}{Colors.ENDC}")

def check_server():
    """التحقق من حالة السيرفر"""
    print_info("جاري التحقق من حالة السيرفر...")
    
    # عدد المحاولات
    max_attempts = 5
    
    for attempt in range(max_attempts):
        try:
            # محاولة الاتصال بالسيرفر
            conn = http.client.HTTPConnection("127.0.0.1", 8000)
            conn.request("GET", "/")
            response = conn.getresponse()
            
            # إذا نجح الاتصال
            print_success(f"تم تشغيل السيرفر بنجاح! الحالة: {response.status} {response.reason}")
            print_info("يمكنك الآن الوصول إلى النظام عبر الرابط: http://127.0.0.1:8000/")
            
            # سؤال المستخدم إذا كان يريد فتح المتصفح
            if platform.system() == "Windows":
                open_browser = input("هل تريد فتح المتصفح الآن؟ (نعم/لا): ").strip().lower()
                if open_browser in ["نعم", "y", "yes"]:
                    webbrowser.open("http://127.0.0.1:8000/")
            
            return True
        except Exception as e:
            # إذا فشل الاتصال
            if attempt < max_attempts - 1:
                print_warning(f"المحاولة {attempt+1}/{max_attempts}: لم يتم الاتصال بالسيرفر بعد. جاري المحاولة مرة أخرى...")
                time.sleep(2)
            else:
                print_error(f"لم يتم تشغيل السيرفر بنجاح بعد {max_attempts} محاولات.")
                print_error(f"قد تكون هناك مشكلة في تشغيل السيرفر. الخطأ: {e}")
                print_info("تأكد من تشغيل السيرفر باستخدام الأمر: python manage.py runserver")
                return False

if __name__ == "__main__":
    check_server()
    
    # إبقاء النافذة مفتوحة في ويندوز
    if platform.system() == "Windows":
        print("\nاضغط على Enter للخروج...")
        input()
