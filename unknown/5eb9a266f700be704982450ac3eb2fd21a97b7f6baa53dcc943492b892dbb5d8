# Generated by Django 5.2 on 2025-04-14 11:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='RankType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Rank Type',
                'verbose_name_plural': 'Rank Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeRank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_obtained', models.DateField(verbose_name='Date Obtained')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ranks', to='employees.employee')),
                ('rank_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_ranks', to='ranks.ranktype')),
            ],
            options={
                'verbose_name': 'Employee Rank',
                'verbose_name_plural': 'Employee Ranks',
                'ordering': ['-date_obtained'],
            },
        ),
    ]
