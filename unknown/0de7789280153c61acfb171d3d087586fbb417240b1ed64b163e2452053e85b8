{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الإجازة بدون راتب - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل الإجازة بدون راتب</h1>
        <div>
            <a href="{% url 'leaves:unpaid_leave_update' leave.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{% url 'leaves:unpaid_leave_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات الإجازة بدون راتب</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">الموظف</th>
                            <td>
                                <a href="{% url 'employees:employee_detail' leave.employee.pk %}">
                                    {{ leave.employee.full_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th class="bg-light">الرقم الوزاري</th>
                            <td>{{ leave.employee.ministry_number }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">نوع الإجازة</th>
                            <td>{{ leave.leave_type.get_name_display }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ البداية</th>
                            <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">تاريخ النهاية</th>
                            <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">عدد الأيام</th>
                            <td>{{ leave.days_count }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">الحالة</th>
                            <td>
                                {% if leave.status == 'approved' %}
                                <span class="badge bg-success">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'pending' %}
                                <span class="badge bg-warning text-dark">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">{{ leave.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ leave.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ الإنشاء</th>
                            <td>{{ leave.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">السبب</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ leave.reason|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
