# Generated by Django 5.2 on 2025-04-13 05:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TechnicalPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specialization', models.CharField(max_length=255, verbose_name='التخصص')),
                ('vacancies', models.PositiveIntegerField(verbose_name='عدد الشواغر')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'الموقف الفني',
                'verbose_name_plural': 'المواقف الفنية',
                'ordering': ['specialization'],
            },
        ),
    ]
