# Generated by Django 5.2 on 2025-04-09 05:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PenaltyType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Penalty Type',
                'verbose_name_plural': 'Penalty Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Penalty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('description', models.TextField(verbose_name='Description')),
                ('decision_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='Decision Number')),
                ('decision_date', models.DateField(blank=True, null=True, verbose_name='Decision Date')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disciplinary_penalties', to='employees.employee')),
                ('penalty_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='penalties', to='disciplinary.penaltytype')),
            ],
            options={
                'verbose_name': 'Penalty',
                'verbose_name_plural': 'Penalties',
                'ordering': ['-date'],
            },
        ),
    ]
