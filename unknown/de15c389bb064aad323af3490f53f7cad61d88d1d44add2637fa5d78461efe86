{% extends 'base.html' %}
{% load static %}

{% block title %}تغيير كلمة المرور للمستخدم {{ user_obj.username }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تغيير كلمة المرور للمستخدم: {{ user_obj.username }}</h2>
    <a href="{% url 'accounts:user_detail' user_obj.pk %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تغيير كلمة المرور</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="mb-3">
                <label for="{{ form.new_password1.id_for_label }}" class="form-label">كلمة المرور الجديدة</label>
                {{ form.new_password1 }}
                {% if form.new_password1.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.new_password1.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                {% if form.new_password1.help_text %}
                <div class="form-text text-muted small">
                    {{ form.new_password1.help_text|safe }}
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.new_password2.id_for_label }}" class="form-label">تأكيد كلمة المرور</label>
                {{ form.new_password2 }}
                {% if form.new_password2.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.new_password2.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
