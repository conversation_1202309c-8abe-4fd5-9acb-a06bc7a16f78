from django.shortcuts import redirect, render
from django.urls import resolve
from django.contrib import messages

class UserPermissionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip middleware for non-authenticated users
        if not request.user.is_authenticated:
            return self.get_response(request)

        # Skip middleware for superuser or full admin users (they have access to everything)
        if request.user.is_superuser or request.user.is_full_admin:
            print(f"Middleware: Skipping permission check for {request.user.username} (superuser={request.user.is_superuser}, full_admin={request.user.is_full_admin})")
            # Always update user_visible_pages in session for admins
            # This ensures the middleware always has the latest permissions
            from accounts.forms import UserPermissionForm
            permission_form = UserPermissionForm()
            all_pages = [choice[0] for choice in permission_form.get_pages_choices()]
            request.session['user_visible_pages'] = all_pages
            request.session.modified = True
            print(f"Middleware: Added {len(all_pages)} pages to session for admin user {request.user.username}")
            return self.get_response(request)

        # Skip for login, logout, dashboard, and static URLs
        if request.path.startswith('/admin/') or request.path.startswith('/accounts/login/') or \
           request.path.startswith('/accounts/logout/') or request.path.startswith('/static/') or \
           request.path == '/' or request.path == '/dashboard/':
            return self.get_response(request)

        # Get current URL name
        try:
            current_url_name = resolve(request.path_info).url_name
            current_namespace = resolve(request.path_info).namespace
            current_view_name = f"{current_namespace}:{current_url_name}" if current_namespace else current_url_name
        except:
            # If URL cannot be resolved, allow access
            return self.get_response(request)

        # Check if user has permission to access this page
        has_permission = False

        # Get user permissions from session if available
        user_visible_pages = request.session.get('user_visible_pages', [])

        # Debug output
        print(f"Checking permissions for user {request.user.username} to access {current_view_name}")
        print(f"User has {len(user_visible_pages)} visible pages in session")

        # If the page is in the user's visible pages, grant permission
        if current_view_name in user_visible_pages:
            has_permission = True
            print(f"Permission granted for {current_view_name} from session")
        else:
            # If not in session, check database permissions
            user_permissions = request.user.custom_permissions.all()
            print(f"User has {user_permissions.count()} permission entries in database")

            for permission in user_permissions:
                # Check if user has permission for this module
                if permission.can_view:
                    # Check if the page is in the visible pages list
                    if permission.visible_pages:
                        visible_pages = permission.visible_pages.split(',')
                        print(f"Module {permission.module_name} has {len(visible_pages)} visible pages")
                        if current_view_name in visible_pages:
                            has_permission = True
                            print(f"Permission granted for {current_view_name} from database")

                            # Update session
                            if 'user_visible_pages' not in request.session:
                                request.session['user_visible_pages'] = []
                            if current_view_name not in request.session['user_visible_pages']:
                                request.session['user_visible_pages'].append(current_view_name)
                                request.session.modified = True

                            break

        # If user doesn't have permission, redirect to access denied page
        if not has_permission and current_view_name != 'dashboard':
            print(f"Permission denied for {request.user.username} to access {current_view_name}")
            # Instead of showing a message with the page name, render a custom access denied page
            return render(request, 'accounts/access_denied.html', status=403)

        return self.get_response(request)
