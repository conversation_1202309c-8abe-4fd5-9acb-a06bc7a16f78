{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة حالات التوظيف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة حالات التوظيف</h2>
    <div>
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStatusModal">
            <i class="fas fa-plus"></i> إضافة حالة جديدة
        </a>
        <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للتوظيف
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع حالات التوظيف</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الاسم</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for status in statuses %}
                    <tr>
                        <td>{{ status.get_name_display }}</td>
                        <td>{{ status.description|default:"-" }}</td>
                        <td>
                            <button class="btn btn-warning btn-sm edit-status" data-id="{{ status.pk }}" data-name="{{ status.name }}" data-description="{{ status.description|default:'' }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm delete-status" data-id="{{ status.pk }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center">لا يوجد حالات توظيف</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Status Modal -->
<div class="modal fade" id="addStatusModal" tabindex="-1" aria-labelledby="addStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStatusModalLabel">إضافة حالة توظيف جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employment:status_create' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم</label>
                        <select class="form-select" id="name" name="name" required>
                            <option value="">اختر الحالة</option>
                            <option value="permanent">دائم</option>
                            <option value="temporary">مؤقت</option>
                            <option value="contract">عقد</option>
                            <option value="probation">تحت التجربة</option>
                            <option value="part_time">دوام جزئي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Status Modal -->
<div class="modal fade" id="editStatusModal" tabindex="-1" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStatusModalLabel">تعديل حالة توظيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employment:status_update' 0 %}" id="editStatusForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">الاسم</label>
                        <select class="form-select" id="edit_name" name="name" required>
                            <option value="permanent">دائم</option>
                            <option value="temporary">مؤقت</option>
                            <option value="contract">عقد</option>
                            <option value="probation">تحت التجربة</option>
                            <option value="part_time">دوام جزئي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit status
        const editButtons = document.querySelectorAll('.edit-status');
        const editForm = document.getElementById('editStatusForm');
        const editNameSelect = document.getElementById('edit_name');
        const editDescriptionInput = document.getElementById('edit_description');
        
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');
                
                // Update form action URL
                editForm.action = editForm.action.replace(/\/\d+\/$/, `/${id}/`);
                
                // Set form values
                editNameSelect.value = name;
                editDescriptionInput.value = description;
                
                // Show modal
                const editModal = new bootstrap.Modal(document.getElementById('editStatusModal'));
                editModal.show();
            });
        });
        
        // Delete status
        const deleteButtons = document.querySelectorAll('.delete-status');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذه الحالة؟')) {
                    window.location.href = `/employment/status/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}
