#!/usr/bin/env python
"""
A standalone script to open a folder selection dialog and print the selected path.
This script is meant to be called from the command line.
"""

import easygui
import os
import sys

def select_folder():
    """Open a folder selection dialog and print the selected folder path."""
    # Open the folder dialog
    folder_path = easygui.diropenbox(
        msg="اختر مجلد حفظ النسخة الاحتياطية",
        title="اختيار مجلد",
        default=os.path.expanduser("~")
    )

    # Print the selected path to stdout
    if folder_path:
        print(folder_path)
    else:
        print("")

if __name__ == "__main__":
    select_folder()
