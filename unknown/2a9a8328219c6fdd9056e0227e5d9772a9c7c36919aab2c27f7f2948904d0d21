from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee

class RankType(models.Model):
    """Model for storing rank types"""
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Rank Type')
        verbose_name_plural = _('Rank Types')
        ordering = ['name']

    def __str__(self):
        return self.name

class EmployeeRank(models.Model):
    """Model for storing employee ranks"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='ranks')
    rank_type = models.ForeignKey(RankType, on_delete=models.CASCADE, related_name='employee_ranks')
    date_obtained = models.DateField(_('Date Obtained'))
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee Rank')
        verbose_name_plural = _('Employee Ranks')
        ordering = ['-date_obtained']

    def __str__(self):
        return f"{self.employee.full_name} - {self.rank_type.name}"
