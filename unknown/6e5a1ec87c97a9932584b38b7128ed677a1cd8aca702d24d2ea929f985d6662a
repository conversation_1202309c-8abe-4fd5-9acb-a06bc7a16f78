from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from django.http import JsonResponse
from .models import LeaveType, Leave
from .forms_unpaid import UnpaidLeaveForm
from employees.models import Employee

@login_required
def unpaid_leave_list(request):
    search_query = request.GET.get('search', '')
    try:
        unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)
        if search_query:
            leaves = Leave.objects.filter(
                leave_type=unpaid_leave_type
            ).filter(
                Q(employee__full_name__icontains=search_query) |
                Q(employee__ministry_number__icontains=search_query) |
                Q(reason__icontains=search_query)
            )
        else:
            leaves = Leave.objects.filter(leave_type=unpaid_leave_type)
    except LeaveType.DoesNotExist:
        leaves = Leave.objects.none()

    return render(request, 'leaves/unpaid_leave_list.html', {
        'leaves': leaves,
        'search_query': search_query
    })

@login_required
def unpaid_leave_create(request):
    if request.method == 'POST':
        form = UnpaidLeaveForm(request.POST)
        if form.is_valid():
            leave = form.save(commit=False)

            # Get employee from hidden field
            employee_id = form.cleaned_data.get('employee_id')
            if not employee_id:
                # Try to get employee by ministry number
                ministry_number = form.cleaned_data.get('ministry_number')
                try:
                    employee = Employee.objects.get(ministry_number=ministry_number)
                    leave.employee = employee
                except Employee.DoesNotExist:
                    form.add_error('ministry_number', 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                    return render(request, 'leaves/unpaid_leave_form.html', {'form': form})
            else:
                try:
                    employee = Employee.objects.get(id=employee_id)
                    leave.employee = employee
                except Employee.DoesNotExist:
                    form.add_error('ministry_number', 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/unpaid_leave_form.html', {'form': form})

            # Set leave type to unpaid
            try:
                unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)
                leave.leave_type = unpaid_leave_type
            except LeaveType.DoesNotExist:
                messages.error(request, 'نوع الإجازة "بدون راتب" غير موجود. يرجى إضافته أولاً.')
                return redirect('leaves:leave_type_list')

            # Calculate days count automatically
            delta = leave.end_date - leave.start_date
            leave.days_count = delta.days + 1

            # Set status to approved by default
            leave.status = 'approved'
            leave.save()

            messages.success(request, 'تم إضافة الإجازة بدون راتب بنجاح.')
            return redirect('leaves:unpaid_leave_detail', pk=leave.pk)
    else:
        form = UnpaidLeaveForm()
    return render(request, 'leaves/unpaid_leave_form.html', {'form': form})

@login_required
def unpaid_leave_detail(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    # Ensure it's an unpaid leave
    try:
        unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)
        if leave.leave_type != unpaid_leave_type:
            messages.error(request, 'هذه ليست إجازة بدون راتب.')
            return redirect('leaves:leave_detail', pk=leave.pk)
    except LeaveType.DoesNotExist:
        pass

    return render(request, 'leaves/unpaid_leave_detail.html', {'leave': leave})

@login_required
def unpaid_leave_update(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    # Ensure it's an unpaid leave
    try:
        unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)
        if leave.leave_type != unpaid_leave_type:
            messages.error(request, 'هذه ليست إجازة بدون راتب.')
            return redirect('leaves:leave_update', pk=leave.pk)
    except LeaveType.DoesNotExist:
        pass

    if request.method == 'POST':
        form = UnpaidLeaveForm(request.POST, instance=leave)
        if form.is_valid():
            # Get employee from hidden field or use existing one
            employee_id = form.cleaned_data.get('employee_id')
            if employee_id:
                try:
                    employee = Employee.objects.get(id=employee_id)
                    leave.employee = employee
                except Employee.DoesNotExist:
                    form.add_error('ministry_number', 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/unpaid_leave_form.html', {'form': form, 'leave': leave})
            else:
                # Try to get employee by ministry number
                ministry_number = form.cleaned_data.get('ministry_number')
                if ministry_number:
                    try:
                        employee = Employee.objects.get(ministry_number=ministry_number)
                        leave.employee = employee
                    except Employee.DoesNotExist:
                        form.add_error('ministry_number', 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                        return render(request, 'leaves/unpaid_leave_form.html', {'form': form, 'leave': leave})

            updated_leave = form.save(commit=False)
            # Ensure leave type is still unpaid
            updated_leave.leave_type = unpaid_leave_type

            # Calculate days count automatically
            delta = updated_leave.end_date - updated_leave.start_date
            updated_leave.days_count = delta.days + 1

            # Ensure status remains approved
            updated_leave.status = 'approved'

            updated_leave.save()
            messages.success(request, 'تم تحديث الإجازة بدون راتب بنجاح.')
            return redirect('leaves:unpaid_leave_detail', pk=leave.pk)
    else:
        # Pre-fill the form with employee data
        initial_data = {
            'ministry_number': leave.employee.ministry_number,
            'employee_name': leave.employee.full_name,
            'employee_id': leave.employee.id
        }
        form = UnpaidLeaveForm(instance=leave, initial=initial_data)

    return render(request, 'leaves/unpaid_leave_form.html', {'form': form, 'leave': leave})


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'error': 'الرجاء إدخال الرقم الوزاري'}, status=400)

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'id': employee.id,
            'full_name': employee.full_name,
            'ministry_number': employee.ministry_number,
            'success': True
        })
    except Employee.DoesNotExist:
        return JsonResponse({'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'}, status=404)
