{% extends 'home/base.html' %}

{% block title %}تم تقديم الطلب بنجاح - النقل الداخلي{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0"><i class="fas fa-check-circle me-2"></i> تم تقديم الطلب بنجاح</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="rounded-circle bg-success d-inline-flex p-4 mb-4" style="width: 100px; height: 100px; justify-content: center; align-items: center;">
                            <i class="fas fa-check fa-4x text-white"></i>
                        </div>
                        <h4>تم تقديم طلب النقل الداخلي بنجاح</h4>
                        <p class="lead">سيتم تخزين طلبك وخيارات النقل وترتيبها حسب الخدمة الفعلية ولحين إجراء النقل الداخلي.</p>
                    </div>

                    <div class="alert alert-info">
                        <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i> معلومات هامة</h5>
                        <p>يمكنك تعديل طلبك في أي وقت خلال فترة النقل الداخلي من خلال الرابط أدناه:</p>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" value="{{ request.scheme }}://{{ request.get_host }}{% url 'home:internal_transfer_edit' token=transfer.edit_token %}" id="edit_link" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyEditLink()">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                        <p class="mb-0">
                            <strong>ملاحظة:</strong> احتفظ بهذا الرابط لتتمكن من تعديل طلبك لاحقاً. لن تتمكن من استرجاع هذا الرابط مرة أخرى.
                        </p>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">ملخص الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> {{ transfer.employee_name }}</p>
                                    <p><strong>الرقم الوزاري:</strong> {{ transfer.ministry_number }}</p>
                                    <p><strong>القسم الحالي:</strong> {{ transfer.current_department }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>الخيار الأول:</strong> {{ transfer.first_choice }}</p>
                                    {% if transfer.second_choice %}
                                    <p><strong>الخيار الثاني:</strong> {{ transfer.second_choice }}</p>
                                    {% endif %}
                                    {% if transfer.third_choice %}
                                    <p><strong>الخيار الثالث:</strong> {{ transfer.third_choice }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{% url 'home:internal_transfer_edit' token=transfer.edit_token %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i> تعديل الطلب
                        </a>
                        <a href="{% url 'home:home' %}" class="btn btn-secondary">
                            <i class="fas fa-home me-2"></i> العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function copyEditLink() {
        var copyText = document.getElementById("edit_link");
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");

        // Show copied message
        var button = document.querySelector("button[onclick='copyEditLink()']");
        var originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }
</script>
{% endblock %}
