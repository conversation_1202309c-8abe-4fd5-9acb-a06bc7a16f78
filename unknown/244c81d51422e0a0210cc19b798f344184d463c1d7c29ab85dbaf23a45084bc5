{% extends 'base.html' %}
{% load static %}

{% block title %}استيراد التقارير السنوية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>استيراد التقارير السنوية</h2>
    <a href="{% url 'performance:performance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير السنوية
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">استيراد التقارير السنوية من ملف Excel</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-info alert-permanent">
            <h5 class="alert-heading">تعليمات الاستيراد</h5>
            <p>يجب أن يحتوي ملف Excel على الأعمدة التالية:</p>
            <ul>
                <li>الرقم الوزاري: الرقم الوزاري للموظف</li>
                <li>الاسم الرباعي: اسم الموظف الرباعي (اختياري، للتحقق فقط)</li>
                <li>العلامة: علامة التقرير السنوي</li>
                <li>السنة: سنة التقرير السنوي</li>
            </ul>
            <p>ملاحظة: إذا كان الموظف موجود بالفعل في النظام وله تقرير سنوي للسنة المحددة، سيتم تحديث التقرير الموجود.</p>
        </div>

        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.excel_file.id_for_label }}" class="form-label">ملف Excel</label>
                {{ form.excel_file }}
                {% if form.excel_file.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.excel_file.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">{{ form.excel_file.help_text }}</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-import"></i> استيراد
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تنزيل قالب استيراد التقارير السنوية</h6>
    </div>
    <div class="card-body">
        <p>يمكنك تنزيل قالب Excel فارغ لاستخدامه في إعداد بيانات التقارير السنوية للاستيراد:</p>
        <a href="#" class="btn btn-success">
            <i class="fas fa-download"></i> تنزيل قالب التقارير السنوية
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
