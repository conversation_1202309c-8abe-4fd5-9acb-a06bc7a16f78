from django import forms
from .models import RankType, EmployeeRank
from employees.models import Employee

class RankTypeForm(forms.ModelForm):
    class Meta:
        model = RankType
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class EmployeeRankForm(forms.Form):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Rank type selection
    rank_type = forms.ModelChoiceField(
        queryset=RankType.objects.all(),
        label='نوع الرتبة',
        empty_label='اختر نوع الرتبة',
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    # Date obtained
    date_obtained = forms.DateField(
        label='تاريخ الحصول عليها',
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    # Notes
    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
    )

class EmployeeRankImportForm(forms.Form):
    excel_file = forms.FileField(
        label='ملف Excel',
        help_text='يجب أن يحتوي الملف على الأعمدة التالية: الرقم الوزاري، نوع الرتبة، تاريخ الحصول عليها، ملاحظات'
    )

    rank_type_mapping = forms.ModelChoiceField(
        queryset=RankType.objects.all(),
        label='نوع الرتبة الافتراضي',
        help_text='سيتم استخدام هذا النوع إذا لم يتم تحديد نوع الرتبة في الملف',
        required=False
    )
