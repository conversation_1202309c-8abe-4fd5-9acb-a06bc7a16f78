# Generated by Django 5.2 on 2025-04-16 11:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('file_management', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='File',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الملف')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان الملف')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('archived', 'مؤرشف'), ('lost', 'مفقود')], default='active', max_length=20, verbose_name='حالة الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الملف')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ملف',
                'verbose_name_plural': 'الملفات',
                'ordering': ['file_number'],
            },
        ),
        migrations.AddField(
            model_name='filemovement',
            name='file',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='file_management.file', verbose_name='الملف'),
            preserve_default=False,
        ),
    ]
