{% extends 'base.html' %}
{% load static %}

{% block title %}إرسا<PERSON> (واتس اب) - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>إرسال رصيد الإجازات عبر واتس اب</h2>
    <div>
        <a href="{% url 'directorate_leaves:leave_reports' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لتقارير الإجازات
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">قائمة الموظفين</h6>
        <div class="d-flex">
            <form class="d-flex me-2" method="get">
                <input type="hidden" name="year" value="{{ selected_year }}">
                <input type="hidden" name="department" value="{{ selected_department.id|default:'' }}">
                <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">بحث</button>
            </form>
            <form class="d-flex me-2" method="get">
                <input type="hidden" name="search" value="{{ search_query }}">
                <input type="hidden" name="department" value="{{ selected_department.id|default:'' }}">
                <select class="form-select me-2" name="year" onchange="this.form.submit()">
                    {% for year in years %}
                    <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
                    {% endfor %}
                </select>
            </form>
            <form class="d-flex" method="get">
                <input type="hidden" name="search" value="{{ search_query }}">
                <input type="hidden" name="year" value="{{ selected_year }}">
                <select class="form-select me-2" name="department" onchange="this.form.submit()">
                    <option value="">-- جميع الأقسام --</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if selected_department.id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                    {% endfor %}
                </select>
            </form>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3 d-flex gap-2">
            <button id="send-to-all-departments" class="btn btn-primary" data-year="{{ selected_year }}">
                <i class="fab fa-whatsapp"></i> إرسال لجميع أقسام المديرية
            </button>

            {% if selected_department %}
            <button id="send-to-all" class="btn btn-success" data-department-id="{{ selected_department.id }}" data-year="{{ selected_year }}">
                <i class="fab fa-whatsapp"></i> إرسال لجميع موظفي قسم {{ selected_department.name }}
            </button>
            {% endif %}

            <div id="progress-container" class="mt-2 d-none flex-grow-1">
                <div class="progress">
                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="progress-text" class="text-center mt-1">جاري الإرسال: <span id="current-count">0</span> من <span id="total-count">0</span></div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الرقم الوزاري</th>
                        <th>الاسم الرباعي</th>
                        <th>السنة</th>
                        <th>إرسال واتس اب</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.ministry_number }}</td>
                        <td>
                            <a href="{% url 'employees:employee_detail' employee.pk %}">
                                {{ employee.full_name }}
                            </a>
                        </td>
                        <td>{{ selected_year }}</td>
                        <td>
                            <button class="btn btn-success btn-sm send-whatsapp" data-employee-id="{{ employee.id }}" data-year="{{ selected_year }}" title="إرسال رصيد الإجازات عبر واتس اب">
                                <i class="fab fa-whatsapp"></i> إرسال
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">لا يوجد موظفين لديهم أرصدة إجازات في هذه السنة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle individual WhatsApp button click
        const whatsappButtons = document.querySelectorAll('.send-whatsapp');
        whatsappButtons.forEach(button => {
            button.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-employee-id');
                const year = this.getAttribute('data-year');

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                this.disabled = true;

                // Make AJAX request to get WhatsApp URL
                fetch(`/directorate_leaves/get-leave-balance-whatsapp/${employeeId}/?year=${year}`)
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        this.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال';
                        this.disabled = false;

                        if (data.success) {
                            // Encode the message for URL
                            const encodedMessage = encodeURIComponent(data.message);
                            const whatsappUrl = `https://wa.me/${data.phone}?text=${encodedMessage}`;

                            // Open WhatsApp in a new tab
                            window.open(whatsappUrl, '_blank');
                        } else {
                            // Show error message
                            alert(data.error || 'حدث خطأ أثناء إنشاء رابط واتس اب');
                        }
                    })
                    .catch(error => {
                        // Reset button state and show error
                        this.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال';
                        this.disabled = false;
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                        console.error('Error:', error);
                    });
            });
        });

        // Handle send to all button click
        const sendToAllButton = document.getElementById('send-to-all');
        if (sendToAllButton) {
            sendToAllButton.addEventListener('click', function() {
                const departmentId = this.getAttribute('data-department-id');
                const year = this.getAttribute('data-year');

                if (!departmentId) {
                    alert('الرجاء اختيار قسم أولاً');
                    return;
                }

                // Confirm before sending to all employees
                if (!confirm('هل أنت متأكد من إرسال رصيد الإجازات لجميع موظفي هذا القسم؟')) {
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                this.disabled = true;

                // Show progress container
                const progressContainer = document.getElementById('progress-container');
                progressContainer.classList.remove('d-none');

                // Make AJAX request to get all employees in the department
                fetch(`/directorate_leaves/get-department-employees-whatsapp/?department_id=${departmentId}&year=${year}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const employees = data.employees;
                            const totalEmployees = employees.length;

                            // Update progress bar
                            const progressBar = document.getElementById('progress-bar');
                            const progressText = document.getElementById('progress-text');
                            const currentCount = document.getElementById('current-count');
                            const totalCount = document.getElementById('total-count');

                            totalCount.textContent = totalEmployees;

                            // Function to send WhatsApp messages sequentially
                            const sendMessages = async () => {
                                for (let i = 0; i < employees.length; i++) {
                                    const employee = employees[i];

                                    // Update progress
                                    const progress = ((i + 1) / totalEmployees) * 100;
                                    progressBar.style.width = `${progress}%`;
                                    currentCount.textContent = i + 1;

                                    // Open WhatsApp in a new tab
                                    window.open(employee.whatsapp_url, '_blank');

                                    // Wait for a short delay to avoid browser blocking
                                    if (i < employees.length - 1) {
                                        await new Promise(resolve => setTimeout(resolve, 1000));
                                    }
                                }

                                // Reset button state
                                sendToAllButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع موظفي قسم ' + data.department_name;
                                sendToAllButton.disabled = false;

                                // Show success message
                                alert(`تم إرسال ${totalEmployees} رسالة بنجاح`);
                            };

                            // Start sending messages
                            sendMessages();
                        } else {
                            // Reset button state
                            sendToAllButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع موظفي القسم';
                            sendToAllButton.disabled = false;

                            // Hide progress container
                            progressContainer.classList.add('d-none');

                            // Show error message
                            alert(data.error || 'حدث خطأ أثناء جلب بيانات الموظفين');
                        }
                    })
                    .catch(error => {
                        // Reset button state
                        sendToAllButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع موظفي القسم';
                        sendToAllButton.disabled = false;

                        // Hide progress container
                        progressContainer.classList.add('d-none');

                        // Show error message
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                        console.error('Error:', error);
                    });
            });
        }

        // Handle send to all departments button click
        const sendToAllDepartmentsButton = document.getElementById('send-to-all-departments');
        if (sendToAllDepartmentsButton) {
            sendToAllDepartmentsButton.addEventListener('click', function() {
                const year = this.getAttribute('data-year');

                // Confirm before sending to all departments
                if (!confirm('هل أنت متأكد من إرسال رصيد الإجازات لجميع موظفي أقسام المديرية؟')) {
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                this.disabled = true;

                // Disable other buttons
                if (sendToAllButton) {
                    sendToAllButton.disabled = true;
                }

                // Show progress container
                const progressContainer = document.getElementById('progress-container');
                progressContainer.classList.remove('d-none');

                // Make AJAX request to get all employees in all departments
                fetch(`/directorate_leaves/get-all-directorate-departments-whatsapp/?year=${year}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const departments = data.departments;
                            const totalEmployees = data.total_employees;
                            const totalDepartments = data.total_departments;

                            // Update progress bar
                            const progressBar = document.getElementById('progress-bar');
                            const progressText = document.getElementById('progress-text');
                            const currentCount = document.getElementById('current-count');
                            const totalCount = document.getElementById('total-count');

                            totalCount.textContent = totalEmployees;
                            let processedEmployees = 0;

                            // Function to send WhatsApp messages sequentially for all departments
                            const sendAllMessages = async () => {
                                for (let d = 0; d < departments.length; d++) {
                                    const department = departments[d];
                                    const employees = department.employees;

                                    // Update progress text to show current department
                                    progressText.innerHTML = `جاري الإرسال: <span id="current-count">${processedEmployees}</span> من <span id="total-count">${totalEmployees}</span> (قسم ${department.name})`;

                                    for (let i = 0; i < employees.length; i++) {
                                        const employee = employees[i];
                                        processedEmployees++;

                                        // Update progress
                                        const progress = (processedEmployees / totalEmployees) * 100;
                                        progressBar.style.width = `${progress}%`;
                                        document.getElementById('current-count').textContent = processedEmployees;

                                        // Open WhatsApp in a new tab
                                        window.open(employee.whatsapp_url, '_blank');

                                        // Wait for a short delay to avoid browser blocking
                                        if (!(d === departments.length - 1 && i === employees.length - 1)) {
                                            await new Promise(resolve => setTimeout(resolve, 1000));
                                        }
                                    }
                                }

                                // Reset button states
                                sendToAllDepartmentsButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع أقسام المديرية';
                                sendToAllDepartmentsButton.disabled = false;
                                if (sendToAllButton) {
                                    sendToAllButton.disabled = false;
                                }

                                // Show success message
                                alert(`تم إرسال ${totalEmployees} رسالة لموظفي ${totalDepartments} قسم بنجاح`);
                            };

                            // Start sending messages
                            sendAllMessages();
                        } else {
                            // Reset button states
                            sendToAllDepartmentsButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع أقسام المديرية';
                            sendToAllDepartmentsButton.disabled = false;
                            if (sendToAllButton) {
                                sendToAllButton.disabled = false;
                            }

                            // Hide progress container
                            progressContainer.classList.add('d-none');

                            // Show error message
                            alert(data.error || 'حدث خطأ أثناء جلب بيانات الأقسام');
                        }
                    })
                    .catch(error => {
                        // Reset button states
                        sendToAllDepartmentsButton.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال لجميع أقسام المديرية';
                        sendToAllDepartmentsButton.disabled = false;
                        if (sendToAllButton) {
                            sendToAllButton.disabled = false;
                        }

                        // Hide progress container
                        progressContainer.classList.add('d-none');

                        // Show error message
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                        console.error('Error:', error);
                    });
            });
        }
    });
</script>
{% endblock %}
