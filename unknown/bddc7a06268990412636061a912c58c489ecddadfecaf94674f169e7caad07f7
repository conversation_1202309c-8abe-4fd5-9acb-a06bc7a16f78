{% extends 'base.html' %}
{% load static %}

{% block title %}حذف موظف زائد{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .delete-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 15px 20px;
        font-weight: bold;
        color: #495057;
    }
    
    .delete-card .card-body {
        padding: 20px;
    }
    
    .warning-icon {
        font-size: 48px;
        color: #dc3545;
        margin-bottom: 20px;
    }
    
    .detail-row {
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }
    
    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .detail-label {
        font-weight: bold;
        color: #495057;
    }
    
    .detail-value {
        color: #212529;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title mb-4">حذف موظف زائد</h1>
            
            <div class="delete-card">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
                </div>
                <div class="card-body text-center">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h4 class="mb-4">هل أنت متأكد من حذف هذا الموظف الزائد؟</h4>
                    <p class="text-danger mb-4">هذا الإجراء لا يمكن التراجع عنه.</p>
                    
                    <div class="row justify-content-center mb-4">
                        <div class="col-md-8">
                            <div class="detail-row">
                                <div class="detail-label">الرقم الوزاري</div>
                                <div class="detail-value">{{ excess_employee.employee.ministry_number }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">اسم الموظف</div>
                                <div class="detail-value">{{ excess_employee.employee.full_name }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">القسم الحالي</div>
                                <div class="detail-value">{{ excess_employee.current_department.name }}</div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">المسمى الوظيفي</div>
                                <div class="detail-value">{{ excess_employee.position.name }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-center">
                            <a href="{% url 'employment:excess_employee_detail' excess_employee.id %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
