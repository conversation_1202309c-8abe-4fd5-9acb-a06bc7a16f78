from accounts.forms import UserPer<PERSON>Form

def user_permissions(request):
    """
    Context processor to add user permissions to all templates
    """
    context = {
        'user_visible_pages': [],
    }

    if request.user.is_authenticated:
        # Set admin flags in context
        if request.user.is_superuser or request.user.is_full_admin:
            context['is_admin'] = True
            context['is_full_admin'] = True
        elif request.user.is_admin:
            context['is_admin'] = True

        # Get all possible pages from the permission form
        permission_form = UserPermissionForm()
        all_pages = [choice[0] for choice in permission_form.get_pages_choices()]

        # Check if we already have pages in session
        if 'user_visible_pages' in request.session:
            context['user_visible_pages'] = request.session['user_visible_pages']
            print(f"Context processor: Using cached pages for user {request.user.username}: {len(request.session['user_visible_pages'])} pages")
            return context

        # If user is superuser, full admin, or regular admin, they can see all pages
        if request.user.is_superuser or request.user.is_full_admin or request.user.is_admin:
            # Update both context and session
            context['user_visible_pages'] = all_pages
            request.session['user_visible_pages'] = all_pages
            request.session.modified = True
            request.session.save()

            # Log the number of pages for debugging
            print(f"Context processor: Admin user {request.user.username} has access to {len(all_pages)} pages")
        else:
            # Get fresh data from the database for non-admin users
            user_permissions = request.user.custom_permissions.all()
            visible_pages = []

            for permission in user_permissions:
                if permission.can_view and permission.visible_pages:
                    pages = permission.visible_pages.split(',')
                    # Filter out empty strings
                    pages = [page for page in pages if page.strip()]
                    visible_pages.extend(pages)
                    # Log the pages for debugging
                    print(f"Context processor: User {request.user.username} has access to {len(pages)} pages from module {permission.module_name}")

            # Remove duplicates
            visible_pages = list(set(visible_pages))

            # Make sure 'employees:employee_list' is always included
            if 'employees:employee_list' not in visible_pages:
                visible_pages.append('employees:employee_list')
                print(f"Context processor: Added employees:employee_list to visible pages for user {request.user.username}")

            # Update both context and session
            context['user_visible_pages'] = visible_pages
            request.session['user_visible_pages'] = visible_pages
            request.session.modified = True
            request.session.save()

            # Log the total number of pages for debugging
            print(f"Context processor: User {request.user.username} has access to a total of {len(visible_pages)} pages")

    return context
