Watching for file changes with StatR<PERSON>ader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\checks\urls.py", line 136, in check_custom_error_handlers
    handler = resolver.resolve_error_handler(status_code)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 732, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\hr_system\urls.py", line 36, in <module>
    path('employment/', include('employment.urls')),
                        ~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\employment\urls.py", line 2, in <module>
    from . import views
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\employment\views.py", line 10, in <module>
    import docx
ModuleNotFoundError: No module named 'docx'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
    ~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\management\commands\runserver.py", line 134, in inner_run
    self.check(**check_kwargs)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\management\base.py", line 492, in check
    all_issues = checks.run_checks(
        app_configs=app_configs,
    ...<2 lines>...
        databases=databases,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\checks\registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\checks\urls.py", line 138, in check_custom_error_handlers
    path = getattr(resolver.urlconf_module, "handler%s" % status_code)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\hr_system\urls.py", line 36, in <module>
    path('employment/', include('employment.urls')),
                        ~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\employment\urls.py", line 2, in <module>
    from . import views
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\employment\views.py", line 10, in <module>
    import docx
ModuleNotFoundError: No module named 'docx'
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\hr_system\views.py changed, reloading.
Performing system checks...

Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 26, 2025 - 20:30:04
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\forms.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:01:59
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\models.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:03:04
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\forms.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:26:14
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\forms.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:27:49
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:28:08
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\middleware.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:28:21
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:28:56
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\middleware.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:39:06
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\middleware.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:39:45
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\middleware.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:39:52
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\forms.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:40:08
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\forms.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:40:29
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:40:49
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:49:00
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:49:18
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:49:38
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:49:55
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:50:15
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 27, 2025 - 22:50:32
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:22:54
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:24:12
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:24:27
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:24:46
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:24:57
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:34:22
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:35:49
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:36:08
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:36:28
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:36:41
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:48:38
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:48:52
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:49:04
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\urls.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:49:21
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
    ~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\management\commands\runserver.py", line 134, in inner_run
    self.check(**check_kwargs)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\management\base.py", line 492, in check
    all_issues = checks.run_checks(
        app_configs=app_configs,
    ...<2 lines>...
        databases=databases,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\checks\registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\checks\urls.py", line 136, in check_custom_error_handlers
    handler = resolver.resolve_error_handler(status_code)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 732, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\hr_system\urls.py", line 40, in <module>
    path('accounts/', include('accounts.urls')),
                      ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\urls.py", line 17, in <module>
    path('reload-permissions/', views.reload_permissions_view, name='reload_permissions'),
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'accounts.views' has no attribute 'reload_permissions_view'
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:50:03
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:50:48
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:50:59
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:51:17
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\HR-SYSTEM- VSCode\accounts\views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
April 28, 2025 - 18:51:33
Django version 5.2, using settings 'hr_system.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CTRL-BREAK.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
