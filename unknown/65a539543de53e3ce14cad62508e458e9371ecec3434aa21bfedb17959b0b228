from django.shortcuts import render

def about(request):
    """View for the about page"""
    return render(request, 'about.html')

def handler400(request, exception=None):
    """Vista para manejar errores 400 (Bad Request)"""
    context = {
        'error_code': '400',
        'error_title': 'طلب غير صحيح',
        'error_message': 'الطلب الذي قمت به غير صحيح أو غير مكتمل.'
    }
    # Print for debugging
    print(f"Handler400 called with context: {context}")
    return render(request, 'errors/error.html', context, status=400)

def handler403(request, exception=None):
    """Vista para manejar errores 403 (Forbidden)"""
    context = {
        'error_code': '403',
        'error_title': 'غير مصرح بالوصول',
        'error_message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'
    }
    # Print for debugging
    print(f"Handler403 called with context: {context}")
    return render(request, 'errors/error.html', context, status=403)

def handler404(request, exception=None):
    """Vista para manejar errores 404 (Not Found)"""
    context = {
        'error_code': '404',
        'error_title': 'الصفحة غير موجودة',
        'error_message': 'يبدو أن الصفحة التي تبحث عنها غير موجودة أو تم نقلها.'
    }
    # Print for debugging
    print(f"Handler404 called with context: {context}")
    return render(request, 'errors/error.html', context, status=404)

def handler500(request):
    """Vista para manejar errores 500 (Server Error)"""
    context = {
        'error_code': '500',
        'error_title': 'خطأ في النظام',
        'error_message': 'حدث خطأ غير متوقع في النظام. يرجى المحاولة مرة أخرى أو الاتصال بمسؤول النظام.'
    }
    # Print for debugging
    print(f"Handler500 called with context: {context}")
    return render(request, 'errors/error.html', context, status=500)
