{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">استيراد أرقام الهوية</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5><i class="fas fa-info-circle"></i> تعليمات الاستيراد</h5>
                        <p>يجب أن يحتوي ملف الإكسل على العمودين التاليين:</p>
                        <ul>
                            <li><strong>الرقم الوزاري</strong>: الرقم الوزاري للموظف</li>
                            <li><strong>رقم الهوية</strong>: رقم الهوية للموظف</li>
                        </ul>
                        <p>ملاحظات:</p>
                        <ul>
                            <li>يجب أن تكون أسماء الأعمدة بالضبط كما هو مذكور أعلاه.</li>
                            <li>إذا كان الموظف لديه بيانات تعريفية بالفعل، سيتم تحديث رقم الهوية فقط.</li>
                            <li>إذا لم يكن لدى الموظف بيانات تعريفية، سيتم إنشاء سجل جديد مع رقم الهوية المحدد.</li>
                        </ul>
                    </div>

                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            {{ form.errors }}
                        </div>
                        {% endif %}

                        <div class="form-group">
                            <label for="{{ form.excel_file.id_for_label }}">{{ form.excel_file.label }}</label>
                            {{ form.excel_file }}
                            {% if form.excel_file.help_text %}
                            <small class="form-text text-muted">{{ form.excel_file.help_text }}</small>
                            {% endif %}
                            {% if form.excel_file.errors %}
                            <div class="invalid-feedback d-block">{{ form.excel_file.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import"></i> استيراد
                            </button>
                            <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">نموذج ملف الإكسل</h6>
                </div>
                <div class="card-body">
                    <p>يمكنك تحميل نموذج ملف الإكسل من هنا:</p>
                    <a href="#" class="btn btn-success mb-3" id="downloadTemplateBtn">
                        <i class="fas fa-download"></i> تحميل نموذج ملف الإكسل
                    </a>

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-dark">
                                <tr>
                                    <th>الرقم الوزاري</th>
                                    <th>رقم الهوية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>123456</td>
                                    <td>9871234567</td>
                                </tr>
                                <tr>
                                    <td>789012</td>
                                    <td>9877654321</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Function to generate and download Excel template
    document.getElementById('downloadTemplateBtn').addEventListener('click', function(e) {
        e.preventDefault();
        
        // Create a table with headers
        let table = '<table>';
        table += '<tr><th>الرقم الوزاري</th><th>رقم الهوية</th></tr>';
        table += '<tr><td>123456</td><td>9871234567</td></tr>';
        table += '<tr><td>789012</td><td>9877654321</td></tr>';
        table += '</table>';
        
        // Convert table to a downloadable format
        let uri = 'data:application/vnd.ms-excel;base64,';
        let template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>نموذج استيراد أرقام الهوية</x:Name><x:WorksheetOptions><x:DisplayDirections/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><div dir="rtl">' + table + '</div></body></html>';
        
        // Create download link
        let link = document.createElement('a');
        link.href = uri + window.btoa(unescape(encodeURIComponent(template)));
        link.download = 'نموذج_استيراد_أرقام_الهوية.xls';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
</script>
{% endblock %}
