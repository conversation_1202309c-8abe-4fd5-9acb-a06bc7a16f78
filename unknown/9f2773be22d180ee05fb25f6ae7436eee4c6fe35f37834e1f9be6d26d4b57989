{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة موقف فني جديد - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>إضافة موقف فني جديد</h2>
    <a href="{% url 'employment:technical_position_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات الموقف الفني الجديد</h6>
            </div>
            <div class="card-body">
                <form method="post" id="technicalPositionForm">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="id_specialization" class="form-label">التخصص <span class="text-danger">*</span></label>
                        {{ form.specialization }}
                    </div>

                    <div class="mb-3">
                        <label for="id_gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                        {{ form.gender }}
                    </div>

                    <div class="mb-3">
                        <label for="id_vacancies" class="form-label">عدد الشواغر <span class="text-danger">*</span></label>
                        {{ form.vacancies }}
                        <small class="form-text text-muted">أدخل عدد الشواغر المطلوبة</small>
                    </div>

                    <div id="departmentsContainer">
                        <!-- هنا سيتم إضافة حقول الأقسام والمبررات ديناميكياً -->
                    </div>

                    <div class="alert alert-info mb-3" id="departmentsInfo" style="display:none;">
                        <i class="fas fa-info-circle"></i>
                        يرجى اختيار الأقسام المدرسية وإدخال المبرر لكل شاغر
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'employment:technical_position_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const vacanciesInput = document.getElementById('id_vacancies');
        const departmentsContainer = document.getElementById('departmentsContainer');
        const departmentsInfo = document.getElementById('departmentsInfo');
        const form = document.getElementById('technicalPositionForm');

        // قائمة الأقسام المدرسية
        const schoolDepartments = [
            {% for dept in school_departments %}
                { id: {{ dept.id }}, name: "{{ dept.name }}" },
            {% endfor %}
        ];

        // دالة لإنشاء حقول الأقسام والمبررات
        function createDepartmentFields() {
            const vacanciesCount = parseInt(vacanciesInput.value) || 0;

            // إفراغ الحاوية
            departmentsContainer.innerHTML = '';

            if (vacanciesCount > 0) {
                departmentsInfo.style.display = 'block';

                // إنشاء حقل لكل شاغر
                for (let i = 0; i < vacanciesCount; i++) {
                    const departmentGroup = document.createElement('div');
                    departmentGroup.className = 'card mb-3 p-3 border-primary';

                    const groupTitle = document.createElement('h6');
                    groupTitle.className = 'mb-3 text-primary';
                    groupTitle.textContent = `الشاغر رقم ${i + 1}`;
                    departmentGroup.appendChild(groupTitle);

                    // إنشاء حقل القسم
                    const departmentDiv = document.createElement('div');
                    departmentDiv.className = 'mb-3';

                    const departmentLabel = document.createElement('label');
                    departmentLabel.className = 'form-label';
                    departmentLabel.textContent = 'القسم ';
                    departmentLabel.appendChild(document.createElement('span')).className = 'text-danger';
                    departmentLabel.lastChild.textContent = '*';

                    const departmentSelect = document.createElement('select');
                    departmentSelect.className = 'form-control';
                    departmentSelect.name = `department_${i}`;
                    departmentSelect.id = `id_department_${i}`;
                    departmentSelect.required = true;

                    // إضافة خيار فارغ
                    const emptyOption = document.createElement('option');
                    emptyOption.value = '';
                    emptyOption.textContent = 'اختر القسم';
                    departmentSelect.appendChild(emptyOption);

                    // إضافة خيارات الأقسام المدرسية
                    schoolDepartments.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.name;
                        departmentSelect.appendChild(option);
                    });

                    departmentDiv.appendChild(departmentLabel);
                    departmentDiv.appendChild(departmentSelect);
                    departmentGroup.appendChild(departmentDiv);

                    // إنشاء حقل المبرر
                    const reasonDiv = document.createElement('div');
                    reasonDiv.className = 'mb-3';

                    const reasonLabel = document.createElement('label');
                    reasonLabel.className = 'form-label';
                    reasonLabel.textContent = 'المبرر ';
                    reasonLabel.appendChild(document.createElement('span')).className = 'text-danger';
                    reasonLabel.lastChild.textContent = '*';

                    const reasonTextarea = document.createElement('textarea');
                    reasonTextarea.className = 'form-control';
                    reasonTextarea.name = `reason_${i}`;
                    reasonTextarea.id = `id_reason_${i}`;
                    reasonTextarea.rows = 2;
                    reasonTextarea.required = true;
                    reasonTextarea.placeholder = 'أدخل المبرر من هذا الشاغر';

                    reasonDiv.appendChild(reasonLabel);
                    reasonDiv.appendChild(reasonTextarea);
                    departmentGroup.appendChild(reasonDiv);

                    departmentsContainer.appendChild(departmentGroup);
                }
            } else {
                departmentsInfo.style.display = 'none';
            }
        }

        // إضافة مستمع حدث لتغيير عدد الشواغر
        if (vacanciesInput) {
            vacanciesInput.addEventListener('input', createDepartmentFields);
            // التحقق الأولي
            createDepartmentFields();
        }

        // التحقق من النموذج قبل الإرسال
        form.addEventListener('submit', function(e) {
            const vacanciesCount = parseInt(vacanciesInput.value) || 0;

            if (vacanciesCount > 0) {
                // التحقق من اختيار جميع الأقسام وإدخال المبررات
                let isValid = true;
                const selectedDepartments = new Set();

                for (let i = 0; i < vacanciesCount; i++) {
                    const departmentSelect = document.getElementById(`id_department_${i}`);
                    const reasonTextarea = document.getElementById(`id_reason_${i}`);

                    if (!departmentSelect.value) {
                        departmentSelect.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        departmentSelect.classList.remove('is-invalid');

                        // إضافة القسم إلى القائمة (بدون التحقق من التكرار)
                        selectedDepartments.add(departmentSelect.value);
                    }

                    if (!reasonTextarea.value.trim()) {
                        reasonTextarea.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        reasonTextarea.classList.remove('is-invalid');
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة.');
                }
            }
        });
    });
</script>
{% endblock %}
