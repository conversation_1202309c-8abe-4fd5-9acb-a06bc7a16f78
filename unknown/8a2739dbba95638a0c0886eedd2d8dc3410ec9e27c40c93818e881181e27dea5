# Generated by Django 5.2 on 2025-04-07 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255, verbose_name='Title')),
                ('report_type', models.Char<PERSON>ield(choices=[('attendance', 'Attendance'), ('employment', 'Employment'), ('employee', 'Employee'), ('department', 'Department'), ('leave', 'Leave')], max_length=20, verbose_name='Report Type')),
                ('parameters', models.JSONField(blank=True, null=True, verbose_name='Parameters')),
                ('file', models.FileField(blank=True, null=True, upload_to='reports/', verbose_name='File')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Report',
                'verbose_name_plural': 'Reports',
                'ordering': ['-created_at'],
            },
        ),
    ]
