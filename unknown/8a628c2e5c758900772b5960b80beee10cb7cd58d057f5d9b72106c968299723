{% extends 'base.html' %}
{% load static %}

{% block title %}تسجيل الدخول - مديرية التربية والتعليم للواء قصبة المفرق - قسم شؤون الموظفين{% endblock %}

{% block body_class %}login-page{% endblock %}

{% block content_login %}
<div class="container mb-5 pb-4">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-lg border-0 rounded-lg mt-5 login-card fade-in">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-2 text-white">مديرية التربية والتعليم للواء قصبة المفرق</h3>
                    <h5 class="mb-0 text-white font-weight-bold mx-auto">قسم شؤون الموظفين</h5>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                    <div class="alert alert-danger">
                        {% for message in messages %}
                            {{ message }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="id_username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" name="username" id="id_username" class="form-control {% if form.username.errors %}is-invalid{% endif %}" required>
                            </div>
                            {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="id_password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" name="password" id="id_password" class="form-control {% if form.password.errors %}is-invalid{% endif %}" required>
                            </div>
                            {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                            <label class="form-check-label" for="rememberMe">تذكرني</label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary pulse">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Focus on username field
        document.getElementById('id_username').focus();
    });
</script>
{% endblock %}