{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق البطاقات */
    .dashboard-card {
        border-right: 4px solid;
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        background-color: white;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: block;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0;
    }

    /* تنسيق الأيقونات */
    .dashboard-card .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }

    .dashboard-card .icon-circle i {
        font-size: 1.8rem;
    }

    /* ألوان البطاقات */
    .card-primary {
        border-color: #4e73df;
    }

    .card-primary .text-xs {
        color: #4e73df;
    }

    .card-primary .icon-circle {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }

    .card-success {
        border-color: #1cc88a;
    }

    .card-success .text-xs {
        color: #1cc88a;
    }

    .card-success .icon-circle {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .card-info {
        border-color: #36b9cc;
    }

    .card-info .text-xs {
        color: #36b9cc;
    }

    .card-info .icon-circle {
        background-color: rgba(54, 185, 204, 0.1);
        color: #36b9cc;
    }

    .card-warning {
        border-color: #f6c23e;
    }

    .card-warning .text-xs {
        color: #f6c23e;
    }

    .card-warning .icon-circle {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    /* تنسيق الظهور التدريجي */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out forwards;
        opacity: 0;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>لوحة التحكم</h2>
</div>

<div class="row">
    <!-- Row 1 - Main Stats -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الموظفين</span>
                        <div class="h3 text-dark">{{ employee_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-success fade-in" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الأقسام</span>
                        <div class="h3 text-dark">{{ department_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-info fade-in" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الإجازات الحالية</span>
                        <div class="h3 text-dark">{{ active_leaves_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-calendar"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.4s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">المستخدمين</span>
                        <div class="h3 text-dark">{{ user_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 2 - Additional Stats -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-primary fade-in" style="animation-delay: 0.5s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الرتب</span>
                        <div class="h3 text-dark">{{ rank_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-medal"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-success fade-in" style="animation-delay: 0.6s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الملفات</span>
                        <div class="h3 text-dark">{{ file_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-folder"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-info fade-in" style="animation-delay: 0.7s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">الإجازات بدون راتب</span>
                        <div class="h3 text-dark">{{ unpaid_leave_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card card-warning fade-in" style="animation-delay: 0.8s;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <span class="text-xs text-uppercase">سجل حركات النظام</span>
                        <div class="h3 text-dark">{{ system_log_count }}</div>
                    </div>
                    <div class="icon-circle">
                        <i class="fas fa-history"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 3 - Recent Data Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 0.9s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">آخر الموظفين المضافين</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الرقم الوزاري</th>
                                <th>الاسم الكامل</th>
                                <th>القسم</th>
                                <th>تاريخ التعيين</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in recent_employees %}
                            <tr>
                                <td>{{ employee.ministry_number }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employee.pk %}">
                                        {{ employee.full_name }}
                                    </a>
                                </td>
                                <td>{{ employee.school }}</td>
                                <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">لا يوجد موظفين</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 1.0s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">آخر الإجازات</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الإجازة</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in recent_leaves %}
                            <tr>
                                <td>{{ leave.employee.full_name }}</td>
                                <td>{{ leave.leave_type }}</td>
                                <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                                <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                        <span class="badge bg-success">موافق عليها</span>
                                    {% elif leave.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">لا يوجد إجازات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 4 - Additional Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 1.1s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">آخر حركات الملفات</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الملف</th>
                                <th>العنوان</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_file_movements %}
                            <tr>
                                <td>-</td>
                                <td>
                                    {{ movement.employee.full_name }}
                                </td>
                                <td>{{ movement.status }}</td>
                                <td>{{ movement.created_at|date:"Y-m-d" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد حركات ملفات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 1.2s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">آخر الرتب المضافة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الرتبة</th>
                                <th>تاريخ الرتبة</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rank in recent_ranks %}
                            <tr>
                                <td>{{ rank.employee.full_name }}</td>
                                <td>{{ rank.rank_type.name }}</td>
                                <td>{{ rank.date_obtained|date:"Y-m-d" }}</td>
                                <td>{{ rank.notes|truncatechars:30 }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد رتب مضافة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 5 - Leave Types -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 1.3s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">أنواع الإجازات المتاحة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for leave_type in leave_types %}
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">{{ leave_type.get_name_display }}</h5>
                                <p class="card-text small text-muted">{{ leave_type.description|default:"لا يوجد وصف"|truncatechars:100 }}</p>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center">
                        <p>لا توجد أنواع إجازات متاحة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Row 6 - Quick Links -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4 fade-in" style="animation-delay: 1.4s;">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">روابط سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'employees:employee_list' %}" class="btn btn-primary btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-users me-2"></i> بيانات الموظفين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'leaves:leave_list' %}" class="btn btn-success btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-calendar-alt me-2"></i> الإجازات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-info btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-folder me-2"></i> الملفات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-warning btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-medal me-2"></i> الرتب
                        </a>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'performance:performance_list' %}" class="btn btn-danger btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-file-alt me-2"></i> التقارير السنوية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-pie me-2"></i> تقارير النظام
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'disciplinary:penalty_list' %}" class="btn btn-dark btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-calendar-times me-2"></i> الإجراءات التأديبية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'system_logs:system_log_list' %}" class="btn btn-primary btn-block d-flex align-items-center justify-content-center py-3">
                            <i class="fas fa-history me-2"></i> سجل حركات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
