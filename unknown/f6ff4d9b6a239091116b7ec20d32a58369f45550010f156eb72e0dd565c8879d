#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف لتشغيل نظام شؤون الموظفين مع التحقق من وجود جميع المكتبات والملحقات اللازمة
هذا الملف سيقوم بالخطوات التالية:
1. التحقق من وجود جميع المكتبات المطلوبة وتثبيتها إذا لم تكن موجودة
2. التحقق من قاعدة البيانات وتطبيق الترحيلات إذا لزم الأمر
3. تشغيل السيرفر
4. فتح المتصفح تلقائياً على صفحة النظام
"""

import os
import sys
import subprocess
import platform
import time
import webbrowser
import importlib
try:
    import pkg_resources
except ImportError:
    # إذا لم تكن مكتبة pkg_resources متوفرة، نستخدم طريقة بديلة
    pkg_resources = None

# تعريف الألوان للطباعة في الطرفية
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(message):
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_success(message):
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}ℹ {message}{Colors.ENDC}")

def run_command(command, shell=True, capture_output=True):
    """تنفيذ أمر وإرجاع النتيجة وحالة النجاح"""
    try:
        if capture_output:
            result = subprocess.run(
                command,
                shell=shell,
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            return True, result.stdout
        else:
            result = subprocess.run(
                command,
                shell=shell,
                check=True
            )
            return True, None
    except subprocess.CalledProcessError as e:
        if capture_output:
            return False, e.stderr
        return False, str(e)

def check_python():
    """التحقق من وجود بايثون وإصداره"""
    print_header("التحقق من تثبيت بايثون")

    python_version = platform.python_version()
    print_success(f"بايثون {python_version} مثبت")

    # التحقق من أن الإصدار 3.8 أو أعلى
    major, minor, *_ = python_version.split(".")
    if int(major) < 3 or (int(major) == 3 and int(minor) < 8):
        print_warning(f"بايثون {python_version} مثبت، لكن يُنصح باستخدام الإصدار 3.8 أو أعلى")

    return True

def check_and_install_requirements():
    """التحقق من وجود جميع المكتبات المطلوبة وتثبيتها إذا لم تكن موجودة"""
    print_header("التحقق من المكتبات المطلوبة")

    # التحقق من وجود ملف requirements.txt
    if not os.path.exists('requirements.txt'):
        print_error("ملف requirements.txt غير موجود")
        return False

    # قراءة المكتبات المطلوبة
    required_packages = []
    with open('requirements.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # التعامل مع المكتبات التي تحتوي على إصدار محدد
            if '==' in line:
                package_name, version = line.split('==', 1)
                required_packages.append((package_name.lower(), version))
            else:
                required_packages.append((line.lower(), None))

    # استخدام pip لعرض المكتبات المثبتة
    success, output = run_command("py -m pip list --format=json")

    # إذا فشل الأمر، نستخدم طريقة بديلة
    if not success:
        print_warning("لم يتم التمكن من الحصول على قائمة المكتبات المثبتة")
        print_info("سيتم محاولة تثبيت جميع المكتبات المطلوبة")

        # تثبيت المكتبات المطلوبة
        print_info("جاري تثبيت المكتبات المطلوبة...")
        success, output = run_command("py -m pip install -r requirements.txt")
        if not success:
            print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
            return False

        print_success("تم تثبيت المكتبات المطلوبة بنجاح")
        return True

    # محاولة تحليل مخرجات pip list
    try:
        import json
        installed_packages = json.loads(output)
        installed_packages = {pkg['name'].lower(): pkg['version'] for pkg in installed_packages}
    except Exception as e:
        print_warning(f"لم يتم التمكن من تحليل قائمة المكتبات المثبتة: {e}")
        print_info("سيتم محاولة تثبيت جميع المكتبات المطلوبة")

        # تثبيت المكتبات المطلوبة
        print_info("جاري تثبيت المكتبات المطلوبة...")
        success, output = run_command("py -m pip install -r requirements.txt")
        if not success:
            print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
            return False

        print_success("تم تثبيت المكتبات المطلوبة بنجاح")
        return True

    # التحقق من كل مكتبة مطلوبة
    missing_packages = []
    outdated_packages = []

    for package_name, required_version in required_packages:
        # تخطي التعليقات والأسطر الفارغة
        if not package_name or package_name.startswith('#'):
            continue

        # التعامل مع المكتبات التي تحتوي على تعليق
        if '#' in package_name:
            package_name = package_name.split('#')[0].strip()

        # التحقق من وجود المكتبة
        if package_name.lower() not in installed_packages:
            missing_packages.append((package_name, required_version))
            continue

        # التحقق من الإصدار إذا كان محدداً
        if required_version:
            installed_version = installed_packages[package_name.lower()]
            if installed_version != required_version:
                outdated_packages.append((package_name, installed_version, required_version))

    # عرض النتائج
    if not missing_packages and not outdated_packages:
        print_success("جميع المكتبات المطلوبة مثبتة بالإصدارات الصحيحة")
        return True

    # تثبيت المكتبات المفقودة أو تحديث المكتبات القديمة
    if missing_packages or outdated_packages:
        print_info("جاري تثبيت/تحديث المكتبات المطلوبة...")
        success, output = run_command("pip install -r requirements.txt")
        if not success:
            print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
            return False

        print_success("تم تثبيت/تحديث جميع المكتبات المطلوبة بنجاح")
        return True

def check_database():
    """التحقق من قاعدة البيانات وتطبيق الترحيلات إذا لزم الأمر"""
    print_header("التحقق من قاعدة البيانات")

    # التحقق من وجود ملف قاعدة البيانات
    if os.path.exists("db.sqlite3"):
        print_success("ملف قاعدة البيانات موجود")
    else:
        print_warning("ملف قاعدة البيانات غير موجود، سيتم إنشاؤه أثناء الترحيلات")

    # تطبيق الترحيلات
    print_info("جاري تطبيق الترحيلات...")
    success, output = run_command("python manage.py migrate")
    if not success:
        print_error(f"فشل في تطبيق الترحيلات: {output}")
        return False

    print_success("تم التحقق من قاعدة البيانات وتطبيق الترحيلات بنجاح")
    return True

def run_server():
    """تشغيل السيرفر"""
    print_header("تشغيل السيرفر")

    # تشغيل السيرفر في خلفية العملية
    if platform.system() == "Windows":
        subprocess.Popen(
            "py manage.py runserver",
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
    else:
        subprocess.Popen(
            "python manage.py runserver",
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

    # الانتظار قليلاً للتأكد من بدء تشغيل السيرفر
    print_info("جاري بدء تشغيل السيرفر...")
    time.sleep(2)

    # التحقق من حالة السيرفر
    max_attempts = 5
    for attempt in range(max_attempts):
        try:
            # محاولة الاتصال بالسيرفر
            import http.client
            conn = http.client.HTTPConnection("127.0.0.1", 8000)
            conn.request("GET", "/")
            response = conn.getresponse()

            # إذا نجح الاتصال
            print_success(f"تم تشغيل السيرفر بنجاح! الحالة: {response.status} {response.reason}")
            return True
        except Exception as e:
            # إذا فشل الاتصال
            if attempt < max_attempts - 1:
                print_info(f"المحاولة {attempt+1}/{max_attempts}: لم يتم الاتصال بالسيرفر بعد. جاري المحاولة مرة أخرى...")
                time.sleep(2)
            else:
                print_error(f"فشل في الاتصال بالسيرفر بعد {max_attempts} محاولات. الخطأ: {e}")
                return False

    return False

def open_browser():
    """فتح المتصفح على صفحة النظام"""
    print_header("فتح المتصفح")

    try:
        url = "http://127.0.0.1:8000/"
        webbrowser.open(url)
        print_success(f"تم فتح المتصفح على الرابط: {url}")
        return True
    except Exception as e:
        print_error(f"فشل في فتح المتصفح: {e}")
        print_info(f"يمكنك الوصول إلى النظام يدوياً عبر الرابط: http://127.0.0.1:8000/")
        return False

def main():
    """الدالة الرئيسية"""
    print_header("بدء تشغيل نظام شؤون الموظفين")

    # التحقق من بايثون
    if not check_python():
        sys.exit(1)

    # التحقق من المكتبات المطلوبة
    if not check_and_install_requirements():
        sys.exit(1)

    # التحقق من قاعدة البيانات
    if not check_database():
        sys.exit(1)

    # تشغيل السيرفر
    if not run_server():
        sys.exit(1)

    # فتح المتصفح
    open_browser()

    print_header("تم تشغيل النظام بنجاح")
    print_info("يمكنك الوصول إلى النظام عبر الرابط: http://127.0.0.1:8000/")
    print_info("لإيقاف السيرفر، اضغط على Ctrl+C في نافذة السيرفر")

    # إبقاء النافذة مفتوحة في ويندوز
    if platform.system() == "Windows":
        print("\nاضغط على Enter للخروج...")
        input()

if __name__ == "__main__":
    main()
