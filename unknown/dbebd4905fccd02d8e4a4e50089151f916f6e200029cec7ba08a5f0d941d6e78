{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الملفات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة الملفات</h2>
    <div>
        <a href="{% url 'file_management:file_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة ملف جديد
        </a>
        <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-info">
            <i class="fas fa-exchange-alt"></i> حركات الملفات
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بحث</h6>
    </div>
    <div class="card-body">
        <form method="get" class="mb-0">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="ابحث برقم الملف، العنوان، اسم الموظف أو الرقم الوزاري" value="{{ search_query }}">
                <div class="input-group-append">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Files Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">الملفات</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>رقم الملف</th>
                        <th>عنوان الملف</th>
                        <th>الموظف</th>
                        <th>الرقم الوزاري</th>
                        <th>حالة الملف</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for file in files %}
                    <tr>
                        <td>{{ file.file_number }}</td>
                        <td>{{ file.title }}</td>
                        <td>{{ file.employee.full_name }}</td>
                        <td>{{ file.employee.ministry_number }}</td>
                        <td>
                            {% if file.status == 'active' %}
                                <span class="badge bg-success text-white">نشط</span>
                            {% elif file.status == 'archived' %}
                                <span class="badge bg-secondary text-white">مؤرشف</span>
                            {% elif file.status == 'lost' %}
                                <span class="badge bg-danger text-white">مفقود</span>
                            {% endif %}
                        </td>
                        <td>{{ file.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'file_management:file_detail' file.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'file_management:file_update' file.id %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'file_management:file_delete' file.id %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد ملفات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
