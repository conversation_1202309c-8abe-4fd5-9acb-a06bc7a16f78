#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف للتحقق من المكتبات المطلوبة وتثبيتها
هذا الملف سيقوم بالتحقق من وجود جميع المكتبات المطلوبة وتثبيتها إذا لم تكن موجودة
"""

import os
import sys
import subprocess
import platform
try:
    import pkg_resources
except ImportError:
    # إذا لم تكن مكتبة pkg_resources متوفرة، نستخدم طريقة بديلة
    pkg_resources = None

# تعريف الألوان للطباعة في الطرفية
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(message):
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_success(message):
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def print_error(message):
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def print_info(message):
    print(f"{Colors.BLUE}ℹ {message}{Colors.ENDC}")

def run_command(command, shell=True):
    """تنفيذ أمر وإرجاع النتيجة وحالة النجاح"""
    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python():
    """التحقق من وجود بايثون وإصداره"""
    print_header("التحقق من تثبيت بايثون")

    python_version = platform.python_version()
    print_success(f"بايثون {python_version} مثبت")

    # التحقق من أن الإصدار 3.8 أو أعلى
    major, minor, *_ = python_version.split(".")
    if int(major) < 3 or (int(major) == 3 and int(minor) < 8):
        print_warning(f"بايثون {python_version} مثبت، لكن يُنصح باستخدام الإصدار 3.8 أو أعلى")

    return True

def check_and_install_requirements():
    """التحقق من وجود جميع المكتبات المطلوبة وتثبيتها إذا لم تكن موجودة"""
    print_header("التحقق من المكتبات المطلوبة")

    # التحقق من وجود ملف requirements.txt
    if not os.path.exists('requirements.txt'):
        print_error("ملف requirements.txt غير موجود")
        return False

    # قراءة المكتبات المطلوبة
    required_packages = []
    with open('requirements.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # التعامل مع المكتبات التي تحتوي على إصدار محدد
            if '==' in line:
                package_name, version = line.split('==', 1)
                required_packages.append((package_name.lower(), version))
            else:
                required_packages.append((line.lower(), None))

    # استخدام pip لعرض المكتبات المثبتة
    success, output = run_command("py -m pip list --format=json")

    # إذا فشل الأمر، نستخدم طريقة بديلة
    if not success:
        print_warning("لم يتم التمكن من الحصول على قائمة المكتبات المثبتة")
        print_info("سيتم محاولة تثبيت جميع المكتبات المطلوبة")

        # تثبيت المكتبات المطلوبة
        print_info("جاري تثبيت المكتبات المطلوبة...")
        success, output = run_command("py -m pip install -r requirements.txt")
        if not success:
            print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
            return False

        print_success("تم تثبيت المكتبات المطلوبة بنجاح")
        return True

    # محاولة تحليل مخرجات pip list
    try:
        import json
        installed_packages = json.loads(output)
        installed_packages = {pkg['name'].lower(): pkg['version'] for pkg in installed_packages}
    except Exception as e:
        print_warning(f"لم يتم التمكن من تحليل قائمة المكتبات المثبتة: {e}")
        print_info("سيتم محاولة تثبيت جميع المكتبات المطلوبة")

        # تثبيت المكتبات المطلوبة
        print_info("جاري تثبيت المكتبات المطلوبة...")
        success, output = run_command("py -m pip install -r requirements.txt")
        if not success:
            print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
            return False

        print_success("تم تثبيت المكتبات المطلوبة بنجاح")
        return True

    # التحقق من كل مكتبة مطلوبة
    missing_packages = []
    outdated_packages = []

    for package_name, required_version in required_packages:
        # تخطي التعليقات والأسطر الفارغة
        if not package_name or package_name.startswith('#'):
            continue

        # التعامل مع المكتبات التي تحتوي على تعليق
        if '#' in package_name:
            package_name = package_name.split('#')[0].strip()

        # التحقق من وجود المكتبة
        if package_name.lower() not in installed_packages:
            missing_packages.append((package_name, required_version))
            continue

        # التحقق من الإصدار إذا كان محدداً
        if required_version:
            installed_version = installed_packages[package_name.lower()]
            if installed_version != required_version:
                outdated_packages.append((package_name, installed_version, required_version))

    # عرض النتائج
    if not missing_packages and not outdated_packages:
        print_success("جميع المكتبات المطلوبة مثبتة بالإصدارات الصحيحة")
        return True

    # عرض المكتبات المفقودة
    if missing_packages:
        print_warning(f"المكتبات المفقودة ({len(missing_packages)}):")
        for package_name, required_version in missing_packages:
            version_str = f" (الإصدار {required_version})" if required_version else ""
            print(f"  - {package_name}{version_str}")

    # عرض المكتبات القديمة
    if outdated_packages:
        print_warning(f"المكتبات القديمة ({len(outdated_packages)}):")
        for package_name, installed_version, required_version in outdated_packages:
            print(f"  - {package_name}: المثبت {installed_version}، المطلوب {required_version}")

    # سؤال المستخدم إذا كان يريد تثبيت/تحديث المكتبات
    if platform.system() == "Windows":
        install = input("\nهل تريد تثبيت/تحديث المكتبات المطلوبة؟ (نعم/لا): ").strip().lower()
        if install not in ["نعم", "y", "yes"]:
            print_info("لم يتم تثبيت/تحديث المكتبات المطلوبة")
            return False

    # تثبيت المكتبات المفقودة أو تحديث المكتبات القديمة
    print_info("جاري تثبيت/تحديث المكتبات المطلوبة...")
    success, output = run_command("pip install -r requirements.txt")
    if not success:
        print_error(f"فشل في تثبيت المكتبات المطلوبة: {output}")
        return False

    print_success("تم تثبيت/تحديث جميع المكتبات المطلوبة بنجاح")
    return True

def main():
    """الدالة الرئيسية"""
    print_header("التحقق من المكتبات المطلوبة لنظام شؤون الموظفين")

    # التحقق من بايثون
    if not check_python():
        sys.exit(1)

    # التحقق من المكتبات المطلوبة
    if not check_and_install_requirements():
        sys.exit(1)

    print_header("اكتمل التحقق من المكتبات المطلوبة")
    print_success("جميع المكتبات المطلوبة مثبتة بالإصدارات الصحيحة")
    print_info("يمكنك الآن تشغيل النظام باستخدام الأمر: python تشغيل_النظام.py")

    # إبقاء النافذة مفتوحة في ويندوز
    if platform.system() == "Windows":
        print("\nاضغط على Enter للخروج...")
        input()

if __name__ == "__main__":
    main()
