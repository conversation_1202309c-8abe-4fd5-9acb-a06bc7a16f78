{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الإجازة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل الإجازة</h2>
    <div>
        <a href="{% url 'leaves:leave_update' leave.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات الإجازة</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الموظف</th>
                        <td>
                            <a href="{% url 'employees:employee_detail' leave.employee.pk %}">
                                {{ leave.employee.full_name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">نوع الإجازة</th>
                        <td>{{ leave.leave_type.get_name_display }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">من تاريخ</th>
                        <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">إلى تاريخ</th>
                        <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">عدد الأيام</th>
                        <td>{{ leave.days_count }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الحالة</th>
                        <td>
                            {% if leave.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status == 'approved' %}
                                <span class="badge bg-success">موافق عليها</span>
                            {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوضة</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">السبب</th>
                        <td>{{ leave.reason|default:"-" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">رصيد الإجازات</h6>
            </div>
            <div class="card-body">
                {% if balance %}
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">نوع الإجازة</th>
                        <td>{{ balance.leave_type.get_name_display }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">السنة</th>
                        <td>{{ balance.year }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الرصيد الأولي</th>
                        <td>{{ balance.initial_balance }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الرصيد المستخدم</th>
                        <td>{{ balance.used_balance }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الرصيد المتبقي</th>
                        <td>{{ balance.remaining_balance }}</td>
                    </tr>
                </table>
                {% else %}
                <div class="alert alert-info">
                    لا يوجد رصيد إجازات لهذا الموظف لهذا النوع من الإجازات.
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-danger text-white">
                <h6 class="m-0 font-weight-bold">خيارات متقدمة</h6>
            </div>
            <div class="card-body">
                <a href="{% url 'leaves:leave_delete' leave.pk %}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف الإجازة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
