{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة تقرير سنوي جديد - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>إضافة تقرير سنوي جديد</h2>
    <a href="{% url 'performance:performance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير السنوية
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات التقرير السنوي</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <input type="hidden" name="employee_id" id="id_employee_id" value="">

            <div class="mb-3">
                <label for="ministry_number_input" class="form-label">الرقم الوزاري</label>
                <div class="input-group">
                    <input type="text" name="ministry_number" id="ministry_number_input" class="form-control" required>
                    <button class="btn btn-primary" type="button" id="search_employee_btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>

            <div class="mb-3">
                <label for="employee_name_display" class="form-label">اسم الموظف</label>
                <input type="text" name="employee_name" id="employee_name_display" class="form-control" readonly>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="id_year" class="form-label">السنة</label>
                    <input type="number" name="year" id="id_year" class="form-control" value="{{ form.year.value|default:2025 }}" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="id_score" class="form-label">الدرجة</label>
                    <input type="number" name="score" id="id_score" class="form-control" step="0.01" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="id_notes" class="form-label">ملاحظات</label>
                <textarea name="notes" id="id_notes" class="form-control" rows="3"></textarea>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/employees/get-by-ministry-number/?ministry_number=${ministryNumber}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Response data:', data);

                    if (data.success) {
                        // Update form fields
                        employeeNameDisplay.value = data.employee.full_name;
                        employeeIdInput.value = data.employee.id;
                    } else {
                        alert(data.error || 'لم يتم العثور على موظف بهذا الرقم الوزاري');
                        employeeNameDisplay.value = '';
                        employeeIdInput.value = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث');
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }
    });
</script>
{% endblock %}
