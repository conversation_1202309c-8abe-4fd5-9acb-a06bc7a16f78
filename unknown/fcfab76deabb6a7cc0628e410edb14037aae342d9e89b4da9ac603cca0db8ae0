from django import forms
from .models import Report
from employees.models import Employee
from employment.models import Department, Position, TechnicalPosition

# Import gender choices from Employee model
GENDER_CHOICES = [('', 'الكل')] + list(Employee.GENDER_CHOICES)

class ReportForm(forms.ModelForm):
    class Meta:
        model = Report
        fields = ['title', 'report_type', 'parameters']
        widgets = {
            'parameters': forms.HiddenInput(),
        }

class AttendanceReportForm(forms.Form):
    start_date = forms.DateField(
        label='تاريخ البداية',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    end_date = forms.DateField(
        label='تاريخ النهاية',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'select2'})
    )

class EmploymentReportForm(forms.Form):
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'select2'})
    )
    status = forms.ChoiceField(
        label='حالة التوظيف',
        choices=[
            ('', 'الكل'),
            ('permanent', 'دائم'),
            ('temporary', 'مؤقت'),
            ('contract', 'عقد'),
        ],
        required=False
    )
    is_current = forms.BooleanField(
        label='الموظفين الحاليين فقط',
        required=False
    )

class EmployeeReportForm(forms.Form):
    employee = forms.ModelChoiceField(
        label='الموظف',
        queryset=Employee.objects.all().order_by('full_name'),
        widget=forms.Select(attrs={'class': 'select2'})
    )
    include_personal_info = forms.BooleanField(
        label='تضمين المعلومات الشخصية',
        required=False,
        initial=True
    )
    include_employment_info = forms.BooleanField(
        label='تضمين معلومات التوظيف',
        required=False,
        initial=True
    )
    include_leaves = forms.BooleanField(
        label='تضمين الإجازات',
        required=False,
        initial=True
    )
    include_performance = forms.BooleanField(
        label='تضمين تقييم الأداء',
        required=False,
        initial=True
    )

class DepartmentReportForm(forms.Form):
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.all().order_by('name'),
        widget=forms.Select(attrs={'class': 'select2'})
    )

class StaffReportForm(forms.Form):
    gender = forms.ChoiceField(
        label='الجنس',
        choices=GENDER_CHOICES,
        required=False
    )
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'select2'})
    )

class LeaveReportForm(forms.Form):
    start_date = forms.DateField(
        label='تاريخ البداية',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    end_date = forms.DateField(
        label='تاريخ النهاية',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    department = forms.ModelChoiceField(
        label='القسم',
        queryset=Department.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'select2'})
    )
    leave_type = forms.ChoiceField(
        label='نوع الإجازة',
        choices=[
            ('', 'الكل'),
            ('annual', 'سنوية'),
            ('sick', 'مرضية'),
            ('medical_committee', 'لجان طبية'),
            ('casual', 'عرضية'),
            ('personal_departure', 'مغادرة خاصة'),
            ('official_departure', 'مغادرة رسمية'),
        ],
        required=False
    )


class TechnicalPositionReportForm(forms.Form):
    gender = forms.ChoiceField(
        label='الجنس',
        choices=GENDER_CHOICES,
        required=False
    )


class PositionReportForm(forms.Form):
    position = forms.ModelChoiceField(
        label='المسمى الوظيفي',
        queryset=Position.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'select2'})
    )
    gender = forms.ChoiceField(
        label='الجنس',
        choices=GENDER_CHOICES,
        required=False
    )


class SpecializationReportForm(forms.Form):
    # Get unique specializations from employees
    specialization_choices = [('', 'الكل')]
    try:
        specializations = Employee.objects.values_list('specialization', flat=True).distinct().order_by('specialization')
        specialization_choices += [(spec, spec) for spec in specializations if spec]
    except:
        pass

    specialization = forms.ChoiceField(
        label='التخصص',
        choices=specialization_choices,
        widget=forms.Select(attrs={'class': 'select2'})
    )
    gender = forms.ChoiceField(
        label='الجنس',
        choices=GENDER_CHOICES,
        required=False
    )
