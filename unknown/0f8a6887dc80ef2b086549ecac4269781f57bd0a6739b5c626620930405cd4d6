{% extends 'base.html' %}
{% load static %}

{% block title %}الصفحات المرئية للمستخدم{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">الصفحات المرئية للمستخدم</h1>
        <a href="{% url 'accounts:user_detail' user_obj.pk %}" class="btn btn-primary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل المستخدم
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المستخدم</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم المستخدم:</strong> {{ user_obj.username }}</p>
                            <p><strong>الاسم الكامل:</strong> {{ user_obj.get_full_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>القسم:</strong> 
                                {% if permission.module_name == 'employment' %}الكادر
                                {% elif permission.module_name == 'leaves' %}الإجراءات
                                {% elif permission.module_name == 'directorate_leaves' %}إجازات الموظفين (المديرية)
                                {% elif permission.module_name == 'files' %}الملفات
                                {% elif permission.module_name == 'ranks' %}الرتب
                                {% elif permission.module_name == 'performance' %}التقارير السنوية
                                {% elif permission.module_name == 'reports' %}تقارير النظام
                                {% elif permission.module_name == 'accounts' %}المستخدمين
                                {% elif permission.module_name == 'backup' %}النسخ الاحتياطية
                                {% elif permission.module_name == 'system_logs' %}سجل حركات النظام
                                {% elif permission.module_name == 'disciplinary' %}العقوبات
                                {% elif permission.module_name == 'file_management' %}إدارة الملفات
                                {% elif permission.module_name == 'employees' %}بيانات الموظفين
                                {% else %}{{ permission.module_name }}
                                {% endif %}
                            </p>
                            <p><strong>مستوى الصلاحية:</strong>
                                {% if permission.can_delete %}
                                <span class="badge bg-primary">مدير (عرض - اضافة - تعديل - حذف)</span>
                                {% elif permission.can_edit %}
                                <span class="badge bg-info">مشرف (عرض - اضافة - تعديل)</span>
                                {% elif permission.can_add %}
                                <span class="badge bg-success">مستخدم عادي (عرض - اضافة)</span>
                                {% elif permission.can_view %}
                                <span class="badge bg-secondary">عرض فقط</span>
                                {% else %}
                                <span class="badge bg-danger">غير محدد</span>
                                {% endif %}
                            </p>
                            <p><strong>عدد الصفحات المرئية:</strong> <span class="badge bg-primary">{{ total_pages }}</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الصفحات المرئية</h6>
                </div>
                <div class="card-body">
                    {% if modules %}
                    <div class="accordion" id="pagesAccordion">
                        {% for module_key, module_data in modules.items %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="{{ module_key }}-heading">
                                <button class="accordion-button {% if forloop.first %}{% else %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#{{ module_key }}-collapse" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="{{ module_key }}-collapse">
                                    {{ module_data.name }} <span class="badge bg-primary ms-2">{{ module_data.pages|length }}</span>
                                </button>
                            </h2>
                            <div id="{{ module_key }}-collapse" class="accordion-collapse collapse {% if forloop.first %}show{% endif %}" aria-labelledby="{{ module_key }}-heading" data-bs-parent="#pagesAccordion">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>#</th>
                                                    <th>اسم الصفحة</th>
                                                    <th>المفتاح</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for page in module_data.pages %}
                                                <tr>
                                                    <td>{{ forloop.counter }}</td>
                                                    <td>{{ page.name }}</td>
                                                    <td><code>{{ page.key }}</code></td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد صفحات مرئية لهذا المستخدم</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
