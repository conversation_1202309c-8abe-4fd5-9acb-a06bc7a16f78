from django.core.management.base import BaseCommand
from system_logs.models import SystemLog

class Command(BaseCommand):
    help = 'تحديث أوصاف سجلات النظام لتكون باللغة العربية'

    def handle(self, *args, **options):
        updated_count = 0
        
        # Get all logs
        logs = SystemLog.objects.all()
        total_logs = logs.count()
        
        self.stdout.write(self.style.SUCCESS(f'بدء تحديث {total_logs} سجل...'))
        
        for log in logs:
            # Get module name in Arabic
            module_name = dict(SystemLog.MODULE_CHOICES).get(log.module, log.module)
            
            # Get action name in Arabic
            action_name = dict(SystemLog.LOG_TYPE_CHOICES).get(log.action, log.action)
            
            # Get user name
            user_name = log.user.username if log.user else "مستخدم مجهول"
            
            # Generate simple Arabic description
            if log.action == SystemLog.CREATE:
                description = f'قام المستخدم {user_name} بإضافة عنصر جديد في قسم {module_name}'
            elif log.action == SystemLog.UPDATE:
                description = f'قام المستخدم {user_name} بتعديل بيانات في قسم {module_name}'
            elif log.action == SystemLog.DELETE:
                description = f'قام المستخدم {user_name} بحذف عنصر من قسم {module_name}'
            elif log.action == SystemLog.VIEW:
                description = f'قام المستخدم {user_name} بعرض بيانات في قسم {module_name}'
            elif log.action == SystemLog.LOGIN:
                description = f'قام المستخدم {user_name} بتسجيل الدخول إلى النظام'
            elif log.action == SystemLog.LOGOUT:
                description = f'قام المستخدم {user_name} بتسجيل الخروج من النظام'
            elif log.action == SystemLog.EXPORT:
                description = f'قام المستخدم {user_name} بتصدير بيانات من قسم {module_name}'
            elif log.action == SystemLog.IMPORT:
                description = f'قام المستخدم {user_name} باستيراد بيانات إلى قسم {module_name}'
            elif log.action == SystemLog.BACKUP:
                description = f'قام المستخدم {user_name} بإنشاء نسخة احتياطية من النظام'
            elif log.action == SystemLog.RESTORE:
                description = f'قام المستخدم {user_name} باستعادة نسخة احتياطية للنظام'
            else:
                description = f'قام المستخدم {user_name} بتنفيذ إجراء {action_name} في قسم {module_name}'
            
            # Add object info if available
            if log.object_repr:
                description += f' ({log.object_repr})'
                
            # Add page info if available
            if log.page:
                description += f' - صفحة: {log.page}'
            
            # Update log description
            log.description = description
            log.save()
            updated_count += 1
            
            # Show progress
            if updated_count % 10 == 0:
                self.stdout.write(f'تم تحديث {updated_count} من {total_logs} سجل...')
        
        self.stdout.write(self.style.SUCCESS(f'تم تحديث {updated_count} من سجلات حركات النظام بنجاح.'))
