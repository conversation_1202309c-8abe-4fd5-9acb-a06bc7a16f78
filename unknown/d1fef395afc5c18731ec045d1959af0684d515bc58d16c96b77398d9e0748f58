{% extends 'base.html' %}
{% load static %}

{% block title %}تسجيل عودة ملف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تسجيل عودة ملف</h1>
        <a href="{% url 'file_management:file_movement_detail' file_movement.pk %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للتفاصيل
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تسجيل عودة ملف موظف</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <h5>معلومات الملف</h5>
                <p>
                    <strong>الموظف:</strong> {{ file_movement.employee.full_name }}<br>
                    <strong>الرقم الوزاري:</strong> {{ file_movement.employee.ministry_number }}<br>
                    <strong>تاريخ خروج الملف:</strong> {{ file_movement.checkout_date|date:"Y-m-d" }}
                </p>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}

                <div class="mb-3">
                    <label for="{{ form.return_date.id_for_label }}" class="form-label">تاريخ عودة الملف</label>
                    {{ form.return_date }}
                    {% if form.return_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.return_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.action_taken.id_for_label }}" class="form-label">الإجراء المتخذ</label>
                    {{ form.action_taken }}
                    {% if form.action_taken.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.action_taken.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.notes.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import"></i> تسجيل عودة الملف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
