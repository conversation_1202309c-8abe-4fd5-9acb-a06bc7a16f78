// Custom JavaScript for HR System

document.addEventListener('DOMContentLoaded', function() {
    // Toggle sidebar on mobile
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const sidebar = document.getElementById('sidebarMenu');
    const mainContent = document.querySelector('.main-content');

    if (sidebarToggleBtn && sidebar) {
        // Toggle sidebar when button is clicked
        sidebarToggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            document.body.classList.toggle('sidebar-open');
        });

        // Close sidebar when clicking outside of it
        document.addEventListener('click', function(event) {
            const isClickInsideSidebar = sidebar.contains(event.target);
            const isClickOnToggleBtn = sidebarToggleBtn.contains(event.target);

            if (!isClickInsideSidebar && !isClickOnToggleBtn && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });

        // Close sidebar when window is resized to desktop size
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });
    }

    // Handle responsive tables
    const tables = document.querySelectorAll('.table');
    tables.forEach(function(table) {
        if (!table.closest('.table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });

    // Add responsive classes to all tables
    document.querySelectorAll('.table').forEach(function(table) {
        // Add wrap-text class to description columns
        const descriptionCells = table.querySelectorAll('td:nth-child(n+3):nth-child(-n+4)');
        descriptionCells.forEach(cell => {
            if (cell.textContent.length > 50) {
                cell.classList.add('wrap-text');
            }
        });

        // Make sure DataTables are responsive
        if (table.classList.contains('dataTable')) {
            table.classList.add('table-responsive-xl');
        }
    });

    // Handle window resize for responsive elements
    function handleResponsiveElements() {
        // Adjust table container width on small screens
        if (window.innerWidth < 768) {
            document.querySelectorAll('.table-responsive').forEach(function(container) {
                container.style.maxWidth = (window.innerWidth - 30) + 'px';
            });
        } else {
            document.querySelectorAll('.table-responsive').forEach(function(container) {
                container.style.maxWidth = '';
            });
        }

        // Fix modal positioning
        document.querySelectorAll('.modal-dialog').forEach(function(dialog) {
            if (window.innerWidth < 576) {
                dialog.style.margin = '10px';
                dialog.style.maxWidth = (window.innerWidth - 20) + 'px';
            } else {
                dialog.style.margin = '';
                dialog.style.maxWidth = '';
            }
        });
    }

    // Run on load and on resize
    handleResponsiveElements();
    window.addEventListener('resize', handleResponsiveElements);

    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input:not([type="checkbox"]):not([type="radio"]), select, textarea');
    formControls.forEach(function(element) {
        element.classList.add('form-control');
    });

    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(function(element) {
        element.classList.add('form-check-input');
    });

    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(function(element) {
        element.classList.add('form-check-input');
    });

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Add close button to alerts but don't auto-hide them
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        // Add permanent class to all alerts to prevent auto-hiding
        alert.classList.add('alert-permanent');

        // Add a close button if it doesn't exist
        if (!alert.querySelector('.btn-close')) {
            const closeButton = document.createElement('button');
            closeButton.type = 'button';
            closeButton.className = 'btn-close';
            closeButton.setAttribute('data-bs-dismiss', 'alert');
            closeButton.setAttribute('aria-label', 'Close');
            alert.appendChild(closeButton);
        }
    });

    // Add animation classes to elements
    const fadeInElements = document.querySelectorAll('.card, .alert');
    fadeInElements.forEach(function(element) {
        element.classList.add('fade-in');
    });

    // Date range picker initialization
    const startDateInputs = document.querySelectorAll('input[type="date"][id*="start"]');
    const endDateInputs = document.querySelectorAll('input[type="date"][id*="end"]');

    startDateInputs.forEach(function(startInput) {
        const formGroup = startInput.closest('.row');
        if (formGroup) {
            const endInput = formGroup.querySelector('input[type="date"][id*="end"]');
            if (endInput) {
                startInput.addEventListener('change', function() {
                    if (this.value) {
                        endInput.min = this.value;
                    }
                });

                endInput.addEventListener('change', function() {
                    if (this.value) {
                        startInput.max = this.value;
                    }
                });
            }
        }
    });

    // Calculate leave days
    const leaveStartDateInput = document.getElementById('id_start_date');
    const leaveEndDateInput = document.getElementById('id_end_date');
    const leaveDaysCountInput = document.getElementById('id_days_count');

    if (leaveStartDateInput && leaveEndDateInput && leaveDaysCountInput) {
        function calculateDays() {
            if (leaveStartDateInput.value && leaveEndDateInput.value) {
                const startDate = new Date(leaveStartDateInput.value);
                const endDate = new Date(leaveEndDateInput.value);
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

                if (diffDays > 0) {
                    leaveDaysCountInput.value = diffDays;
                }
            }
        }

        leaveStartDateInput.addEventListener('change', calculateDays);
        leaveEndDateInput.addEventListener('change', calculateDays);
    }

    // Toggle password visibility
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(function(field) {
        const parent = field.parentElement;
        if (parent && parent.classList.contains('input-group')) {
            const toggleButton = document.createElement('button');
            toggleButton.type = 'button';
            toggleButton.className = 'btn btn-outline-secondary';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
            toggleButton.title = 'إظهار كلمة المرور';

            parent.appendChild(toggleButton);

            toggleButton.addEventListener('click', function() {
                const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
                field.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
                this.title = type === 'password' ? 'إظهار كلمة المرور' : 'إخفاء كلمة المرور';
            });
        }
    });

    // Print button functionality
    const printButtons = document.querySelectorAll('.btn-print');
    printButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });
    });

    // Confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete-confirm');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                e.preventDefault();
            }
        });
    });

    // Add hover effect to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
        });
    });

    // Add active class to current page link
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar-link');

    navLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/') {
            link.classList.add('active');
        } else if (href === '/' && currentPath === '/') {
            link.classList.add('active');
        }
    });

    // Employee search autocomplete
    const employeeSearchInput = document.getElementById('employeeSearch');
    if (employeeSearchInput) {
        employeeSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length >= 2) {
                fetch(`/employees/search/?term=${searchTerm}`)
                    .then(response => response.json())
                    .then(data => {
                        const resultsContainer = document.getElementById('searchResults');
                        resultsContainer.innerHTML = '';

                        data.forEach(employee => {
                            const item = document.createElement('a');
                            item.href = `/employees/${employee.id}/`;
                            item.className = 'list-group-item list-group-item-action';
                            item.textContent = `${employee.full_name} (${employee.ministry_number})`;
                            resultsContainer.appendChild(item);
                        });

                        resultsContainer.style.display = data.length > 0 ? 'block' : 'none';
                    });
            }
        });
    }
});
