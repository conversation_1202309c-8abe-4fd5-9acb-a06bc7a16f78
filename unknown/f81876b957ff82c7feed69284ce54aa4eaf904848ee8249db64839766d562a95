{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-info-circle ml-2"></i>
                        <strong>تعليمات إدخال البيانات التعريفية للموظف</strong>
                    </div>
                    <ul>
                        <li><strong>الرقم الوزاري:</strong> يجب إدخال الرقم الوزاري للموظف بشكل صحيح.</li>
                        <li><strong>الرقم الوطني:</strong> يجب إدخال الرقم الوطني للموظف بشكل صحيح.</li>
                        <li><strong>رقم الهوية:</strong> يجب إدخال رقم الهوية للموظف بشكل صحيح.</li>
                        <li><strong>تاريخ الميلاد:</strong> يجب إدخال تاريخ الميلاد بشكل صحيح (اليوم/الشهر/السنة).</li>
                    </ul>
                </div>

                {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>يرجى تصحيح الأخطاء التالية:</strong>
                    {{ form.errors }}
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_ministry_number">{{ form.ministry_number.label }}</label>
                            {{ form.ministry_number }}
                            {% if form.ministry_number.errors %}
                            <div class="invalid-feedback d-block">{{ form.ministry_number.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_employee_name">{{ form.employee_name.label }}</label>
                            {{ form.employee_name }}
                            {% if form.employee_name.errors %}
                            <div class="invalid-feedback d-block">{{ form.employee_name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_national_id">{{ form.national_id.label }}</label>
                            {{ form.national_id }}
                            {% if form.national_id.errors %}
                            <div class="invalid-feedback d-block">{{ form.national_id.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_id_number">{{ form.id_number.label }}</label>
                            {{ form.id_number }}
                            {% if form.id_number.errors %}
                            <div class="invalid-feedback d-block">{{ form.id_number.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="id_birth_day">{{ form.birth_day.label }}</label>
                            {{ form.birth_day }}
                            {% if form.birth_day.errors %}
                            <div class="invalid-feedback d-block">{{ form.birth_day.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="id_birth_month">{{ form.birth_month.label }}</label>
                            {{ form.birth_month }}
                            {% if form.birth_month.errors %}
                            <div class="invalid-feedback d-block">{{ form.birth_month.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="id_birth_year">{{ form.birth_year.label }}</label>
                            {{ form.birth_year }}
                            {% if form.birth_year.errors %}
                            <div class="invalid-feedback d-block">{{ form.birth_year.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="id_address">{{ form.address.label }}</label>
                    {{ form.address }}
                    {% if form.address.errors %}
                    <div class="invalid-feedback d-block">{{ form.address.errors }}</div>
                    {% endif %}
                </div>

                {{ form.employee }}
                {{ form.employee_id }}

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Function to get employee data by ministry number
        function getEmployeeByMinistryNumber(ministryNumber) {
            if (!ministryNumber) return;
            
            $.ajax({
                url: "{% url 'employment:get_employee_by_ministry_number' %}",
                data: {
                    'ministry_number': ministryNumber
                },
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        $('#employee_name_display').val(data.employee.full_name);
                        $('#id_employee_id').val(data.employee.id);
                    } else {
                        $('#employee_name_display').val('');
                        $('#id_employee_id').val('');
                        alert('لم يتم العثور على موظف بهذا الرقم الوزاري');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء البحث عن الموظف');
                }
            });
        }

        // Bind event to ministry number input
        $('#ministry_number_input').on('blur', function() {
            getEmployeeByMinistryNumber($(this).val());
        });

        // If we have a ministry number on page load, get the employee data
        if ($('#ministry_number_input').val()) {
            getEmployeeByMinistryNumber($('#ministry_number_input').val());
        }
    });
</script>
{% endblock %}
