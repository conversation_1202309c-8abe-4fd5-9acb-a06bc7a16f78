/* Dark Mode CSS */

/*
 * هذا الملف يحتوي فقط على أنماط الوضع المظلم
 * جميع الأنماط هنا تطبق فقط عندما تكون فئة dark-mode موجودة على عنصر body
 * عند العودة إلى الوضع النهاري، لن يتم تطبيق أي من هذه الأنماط
 */

/* Base styles for dark mode */
body.dark-mode {
    background-color: #121212;
    color: #e0e0e0;
}

/* Navbar styles */
body.dark-mode .navbar {
    background-color: #1e1e1e !important;
    border-bottom: 1px solid #333;
}

body.dark-mode .navbar-brand,
body.dark-mode .nav-link,
body.dark-mode .navbar .dropdown-toggle,
body.dark-mode .navbar .username,
body.dark-mode .navbar .account-type {
    color: #fff !important;
}

body.dark-mode .navbar .account-type {
    color: #aaa !important;
}

/* Sidebar styles */
body.dark-mode .sidebar {
    background: linear-gradient(to bottom, #1e1e1e, #2d2d2d) !important;
    border-right: 1px solid #333;
}

body.dark-mode .sidebar-link {
    color: #e0e0e0 !important;
}

body.dark-mode .sidebar-link:hover,
body.dark-mode .sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .sidebar-link i {
    color: #aaa !important;
}

/* Card styles */
body.dark-mode .card {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
}

body.dark-mode .card-header {
    background-color: #2d2d2d !important;
    border-color: #333 !important;
}

body.dark-mode .card-footer {
    background-color: #2d2d2d !important;
    border-color: #333 !important;
}

/* Table styles */
body.dark-mode .table {
    color: #e0e0e0 !important;
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075) !important;
}

body.dark-mode .table th,
body.dark-mode .table td {
    border-color: #333 !important;
}

body.dark-mode .table-bordered {
    border-color: #333 !important;
}

body.dark-mode .table-light,
body.dark-mode .table-light > th,
body.dark-mode .table-light > td {
    background-color: #2d2d2d !important;
    color: #e0e0e0 !important;
}

/* Form styles */
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #2d2d2d !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background-color: #333 !important;
    border-color: #555 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .form-control::placeholder {
    color: #888 !important;
}

body.dark-mode .form-label {
    color: #e0e0e0 !important;
}

/* Button styles */
body.dark-mode .btn-light {
    background-color: #2d2d2d !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .btn-light:hover {
    background-color: #333 !important;
    border-color: #555 !important;
}

body.dark-mode .btn-outline-secondary {
    color: #aaa !important;
    border-color: #444 !important;
}

body.dark-mode .btn-outline-secondary:hover {
    background-color: #333 !important;
    color: #fff !important;
}

/* Dropdown styles */
body.dark-mode .dropdown-menu {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
}

body.dark-mode .dropdown-item {
    color: #e0e0e0 !important;
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background-color: #2d2d2d !important;
    color: #fff !important;
}

body.dark-mode .dropdown-divider {
    border-color: #333 !important;
}

/* Modal styles */
body.dark-mode .modal-content {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: #333 !important;
}

body.dark-mode .close {
    color: #e0e0e0 !important;
}

/* Alert styles */
body.dark-mode .alert-secondary {
    background-color: #2d2d2d !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

/* Text colors */
body.dark-mode .text-muted {
    color: #aaa !important;
}

body.dark-mode .text-dark {
    color: #e0e0e0 !important;
}

/* Search bar in dark mode */
body.dark-mode .navbar-search .form-control {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
}

body.dark-mode .navbar-search .form-control:focus {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

body.dark-mode .navbar-search .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

body.dark-mode .navbar-search .search-icon {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Dark mode toggle in dark mode */
body.dark-mode .dark-mode-toggle {
    color: #fff !important;
}

body.dark-mode .dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Footer in dark mode */
body.dark-mode .footer {
    background-color: #1e1e1e !important;
    border-top: 1px solid #333 !important;
}

/* Pagination in dark mode */
body.dark-mode .page-link {
    background-color: #2d2d2d !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .page-link:hover {
    background-color: #333 !important;
    color: #fff !important;
}

body.dark-mode .page-item.active .page-link {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

body.dark-mode .page-item.disabled .page-link {
    background-color: #1e1e1e !important;
    color: #666 !important;
}

/* Instructions page specific styles */
body.dark-mode .instruction-card {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .instruction-card .card-body {
    color: #e0e0e0 !important;
}

body.dark-mode .instruction-card h5,
body.dark-mode .instruction-card h6,
body.dark-mode .instruction-card .card-title {
    color: #fff !important;
}

body.dark-mode .instruction-card .card-text {
    color: #ccc !important;
}

body.dark-mode .dashboard-card {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .dashboard-card .card-body {
    color: #e0e0e0 !important;
}

body.dark-mode .dashboard-card h5,
body.dark-mode .dashboard-card h6,
body.dark-mode .dashboard-card .card-title {
    color: #fff !important;
}

body.dark-mode .dashboard-card .card-text {
    color: #ccc !important;
}

body.dark-mode .toc-card {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .toc-card .card-body {
    color: #e0e0e0 !important;
}

body.dark-mode .toc-card .font-weight-bold {
    color: #fff !important;
}

body.dark-mode .toc-card .text-muted {
    color: #aaa !important;
}

body.dark-mode .instruction-section {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .instruction-section h4,
body.dark-mode .instruction-section h5,
body.dark-mode .instruction-section h6,
body.dark-mode .instruction-section p,
body.dark-mode .instruction-section div {
    color: #e0e0e0 !important;
}

body.dark-mode .step-card {
    background-color: #1e1e1e !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
}

body.dark-mode .step-card h6 {
    color: #fff !important;
}

body.dark-mode .step-card p {
    color: #ccc !important;
}

body.dark-mode .section-title {
    color: #fff !important;
}

body.dark-mode .text-gray-800 {
    color: #e0e0e0 !important;
}

body.dark-mode .fw-bold {
    color: #fff !important;
}

/* Override gradient backgrounds in dark mode */
body.dark-mode .card-header[style*="linear-gradient"] {
    background: #2d2d2d !important;
    color: #fff !important;
}

body.dark-mode .card-header[style*="linear-gradient"] h5,
body.dark-mode .card-header[style*="linear-gradient"] h6 {
    color: #fff !important;
}

/* Override all black text colors in dark mode */
body.dark-mode [style*="color: #000"],
body.dark-mode [style*="color: #333"],
body.dark-mode [style*="color: black"],
body.dark-mode .text-black,
body.dark-mode .text-dark {
    color: #fff !important;
}

/* Specific overrides for inline styles */
body.dark-mode h1[style*="color: #333"],
body.dark-mode h2[style*="color: #333"],
body.dark-mode h3[style*="color: #333"],
body.dark-mode h4[style*="color: #333"],
body.dark-mode h5[style*="color: #333"],
body.dark-mode h6[style*="color: #333"] {
    color: #fff !important;
}

body.dark-mode p[style*="color: #333"],
body.dark-mode div[style*="color: #333"],
body.dark-mode span[style*="color: #333"] {
    color: #e0e0e0 !important;
}

/* Override card header text colors */
body.dark-mode .card-header h5[style*="color: #333"],
body.dark-mode .card-header h6[style*="color: #333"] {
    color: #fff !important;
}

/* Override all text elements with black colors */
body.dark-mode * {
    color: inherit;
}

body.dark-mode body,
body.dark-mode .container,
body.dark-mode .row,
body.dark-mode .col {
    color: #e0e0e0 !important;
}

/* Force white color for all headings in dark mode */
body.dark-mode h1,
body.dark-mode h2,
body.dark-mode h3,
body.dark-mode h4,
body.dark-mode h5,
body.dark-mode h6 {
    color: #fff !important;
}

/* Force light color for all paragraphs and text in dark mode */
body.dark-mode p,
body.dark-mode div,
body.dark-mode span,
body.dark-mode li,
body.dark-mode td,
body.dark-mode th {
    color: #e0e0e0 !important;
}

/* Override strong and bold text */
body.dark-mode strong,
body.dark-mode b,
body.dark-mode .font-weight-bold,
body.dark-mode .fw-bold {
    color: #fff !important;
}

/* Alert styles in dark mode */
body.dark-mode .alert-info {
    background-color: #1a4d66 !important;
    border-color: #2d5a87 !important;
    color: #b3d9ff !important;
}

/* Badge styles in dark mode */
body.dark-mode .badge {
    color: #fff !important;
}

body.dark-mode .badge.bg-warning {
    background-color: #664d03 !important;
}

body.dark-mode .badge.bg-info {
    background-color: #055160 !important;
}

body.dark-mode .badge.bg-danger {
    background-color: #842029 !important;
}

body.dark-mode .badge.bg-secondary {
    background-color: #41464b !important;
}

body.dark-mode .badge.bg-success {
    background-color: #0f5132 !important;
}
