from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee
from datetime import timedelta

class PenaltyType(models.Model):
    """Model for storing penalty types"""
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('نوع العقوبة')
        verbose_name_plural = _('أنواع العقوبات')
        ordering = ['name']

    def __str__(self):
        return self.name

class Penalty(models.Model):
    """Model for storing employee penalties"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='disciplinary_penalties', verbose_name=_('الموظف'))
    penalty_type = models.ForeignKey(PenaltyType, on_delete=models.CASCADE, related_name='penalties', verbose_name=_('نوع العقوبة'))
    date = models.DateField(_('التاريخ'))
    description = models.TextField(_('الوصف'))
    decision_number = models.CharField(_('رقم القرار'), max_length=50, blank=True, null=True)
    decision_date = models.DateField(_('تاريخ القرار'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('عقوبة')
        verbose_name_plural = _('العقوبات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.penalty_type.name} - {self.date}"

class UnpaidLeave(models.Model):
    """Model for storing employee unpaid leaves"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='unpaid_leaves', verbose_name=_('الموظف'))
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    reason = models.TextField(_('سبب الإجازة'), blank=True, null=True)
    decision_number = models.CharField(_('رقم القرار'), max_length=50, blank=True, null=True)
    decision_date = models.DateField(_('تاريخ القرار'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('إجازة بدون راتب')
        verbose_name_plural = _('إجازات بدون راتب')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.start_date} to {self.end_date}"

    @property
    def duration(self):
        """Calculate the duration of the unpaid leave in days"""
        return (self.end_date - self.start_date).days + 1
