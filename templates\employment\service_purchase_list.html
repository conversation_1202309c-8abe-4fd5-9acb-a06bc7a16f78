{% extends 'base.html' %}
{% load static %}

{% block title %}شراء الخدمات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/service_purchase.css' %}">
<link rel="stylesheet" href="{% static 'css/service_purchase_override.css' %}">
<style>
    .service-purchase-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .service-purchase-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .search-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .btn-action {
        margin: 2px;
        border-radius: 20px;
    }
    
    .badge-custom {
        font-size: 0.85em;
        padding: 0.5em 0.8em;
        border-radius: 20px;
    }
    
    /* Force dark text colors for table content */
    .service-purchase-card .table tbody td {
        color: #212529 !important;
    }
    
    .service-purchase-card .table tbody td span:not(.badge) {
        color: #212529 !important;
        font-weight: 500;
    }
    
    .service-purchase-card .table tbody td strong {
        color: #212529 !important;
        font-weight: 600;
    }
    
    .service-purchase-card .table tbody td .text-dark {
        color: #212529 !important;
    }
    
    /* Keep badge colors white */
    .service-purchase-card .table tbody td .badge {
        color: white !important;
    }
    
    /* Additional CSS to force dark text colors */
    .table tbody tr td {
        color: #212529 !important;
    }
    
    .table tbody tr td * {
        color: #212529 !important;
    }
    
    .table tbody tr td .badge {
        color: white !important;
    }
    
    .table tbody tr td .badge * {
        color: white !important;
    }
    
    /* Override any Bootstrap default colors */
    .table-hover tbody tr:hover td {
        color: #212529 !important;
    }
    
    /* Specific targeting for service purchase table */
    .service-purchase-table tbody td {
        color: #212529 !important;
        font-weight: 500;
    }
    
    .service-purchase-table tbody td span {
        color: #212529 !important;
    }
    
    .service-purchase-table tbody td strong {
        color: #212529 !important;
        font-weight: 600;
    }
    
    /* Exception for badges */
    .service-purchase-table tbody td .badge {
        color: white !important;
    }
    
    /* Dashboard Cards Styles */
    .dashboard-card {
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
    
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    
    .text-xs {
        font-size: 0.7rem;
    }
    
    .opacity-50 {
        opacity: 0.5;
    }
    
    .no-gutters {
        margin-right: 0;
        margin-left: 0;
    }
    
    .no-gutters > .col,
    .no-gutters > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart text-primary me-2"></i>
            شراء الخدمات
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employment:service_purchase_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة شراء خدمة
            </a>
            <a href="{% url 'employment:service_purchase_export_excel' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </a>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة لبيانات الموظفين
            </a>
        </div>
    </div>

    <!-- Statistics Cards Row -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي شراء الخدمات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                النتائج المعروضة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ service_purchases|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المدارس المستهدفة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unique_schools_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-school fa-2x text-info opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card search-card">
                <div class="card-body">
                    <form method="get" id="searchForm">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="{{ search_form.search.id_for_label }}" class="form-label text-white">
                                    <i class="fas fa-search me-1"></i>{{ search_form.search.label }}
                                </label>
                                {{ search_form.search }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.specialization.id_for_label }}" class="form-label text-white">
                                    <i class="fas fa-graduation-cap me-1"></i>{{ search_form.specialization.label }}
                                </label>
                                {{ search_form.specialization }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.school.id_for_label }}" class="form-label text-white">
                                    <i class="fas fa-school me-1"></i>{{ search_form.school.label }}
                                </label>
                                {{ search_form.school }}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-light me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-outline-light">
                                    <i class="fas fa-redo"></i> إعادة ضبط
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Purchases Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card service-purchase-card">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>قائمة شراء الخدمات
                    </h6>
                    <span class="badge bg-info">{{ service_purchases|length }} نتيجة</span>
                </div>
                <div class="card-body">
                    {% if service_purchases %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover service-purchase-table">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">الرقم</th>
                                    <th width="15%">الرقم الوزاري</th>
                                    <th width="25%">الاسم الكامل</th>
                                    <th width="15%">التخصص</th>
                                    <th width="15%">المدرسة الحالية</th>
                                    <th width="15%">المدرسة</th>
                                    <th width="10%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service_purchase in service_purchases %}
                                <tr>
                                    <td class="text-center">
                                        <span class="text-dark fw-bold" style="color: #212529 !important;">{{ forloop.counter }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary badge-custom">
                                            {{ service_purchase.employee.ministry_number|default:"-" }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-light text-primary rounded-circle">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <strong class="text-dark" style="color: #212529 !important;">{{ service_purchase.employee.full_name }}</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        {% if service_purchase.employee.specialization %}
                                            <span class="badge bg-info badge-custom">
                                                {{ service_purchase.employee.specialization }}
                                            </span>
                                        {% else %}
                                            <span class="text-dark fw-medium" style="color: #212529 !important;">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="text-dark fw-medium" style="color: #212529 !important;">{{ service_purchase.employee.school|default:"-" }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if service_purchase.target_school %}
                                            <span class="badge bg-primary badge-custom">
                                                {{ service_purchase.target_school }}
                                            </span>
                                        {% else %}
                                            <span class="text-dark fw-medium" style="color: #212529 !important;">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'employment:service_purchase_update' service_purchase.pk %}" 
                                               class="btn btn-warning btn-sm btn-action" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'employment:service_purchase_delete' service_purchase.pk %}" 
                                               class="btn btn-danger btn-sm btn-action" title="حذف"
                                               onclick="return confirm('هل أنت متأكد من حذف شراء الخدمة لهذا الموظف؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد شراء خدمات</h5>
                        <p class="text-muted">لم يتم العثور على أي شراء خدمات يطابق معايير البحث.</p>
                        <a href="{% url 'employment:service_purchase_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة شراء خدمة جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
{% if messages %}
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endfor %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/service_purchase.js' %}"></script>
<script>
$(document).ready(function() {
    // Auto-submit search form when inputs change
    $('#searchForm input, #searchForm select').on('input change', function() {
        // Add a small delay for text inputs
        if ($(this).is('input[type="text"]')) {
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(function() {
                $('#searchForm').submit();
            }, 500);
        } else {
            $('#searchForm').submit();
        }
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Force text colors after page load
    $(document).ready(function() {
        $('.service-purchase-table tbody td').css('color', '#212529');
        $('.service-purchase-table tbody td span:not(.badge)').css('color', '#212529');
        $('.service-purchase-table tbody td strong').css('color', '#212529');
        $('.service-purchase-table tbody td .text-dark').css('color', '#212529');
    });
});
</script>

<style>
/* Final override to ensure text visibility */
.service-purchase-table tbody td,
.service-purchase-table tbody td span:not(.badge),
.service-purchase-table tbody td strong {
    color: #212529 !important;
}
</style>
{% endblock %}