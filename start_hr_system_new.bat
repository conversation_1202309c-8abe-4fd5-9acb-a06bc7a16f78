@echo off
echo Starting HR System...
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH.
    echo.
    echo Please choose an option:
    echo 1. Install Python from Microsoft Store
    echo 2. Disable app execution aliases
    echo 3. Create virtual environment (if Python is installed but not in PATH)
    echo 4. Exit
    echo.
    set /p choice="Enter your choice (1-4): "
    
    if "%choice%"=="1" (
        call install_python.bat
        goto :end
    ) else if "%choice%"=="2" (
        call disable_app_execution_aliases.bat
        goto :end
    ) else if "%choice%"=="3" (
        call create_venv.bat
        goto :end
    ) else (
        goto :end
    )
)

REM Check if venv exists
if exist "venv\Scripts\activate.bat" (
    echo Using virtual environment...
    call venv\Scripts\activate.bat
    python manage.py runserver 0.0.0.0:8000
    goto :end
)

REM If no venv, use system Python
echo Using system Python...
python manage.py runserver 0.0.0.0:8000

:end
echo.
echo Press any key to exit...
pause > nul
