{% extends 'base.html' %}

{% block title %}حساب العمر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">حساب العمر للموظفين</h6>
            <div class="d-flex">
                <form method="get" class="me-2">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{% url 'employees:calculate_age' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
                <a href="{% url 'employees:employee_list' %}" class="btn btn-dark text-white">
                    <i class="fas fa-arrow-right me-1"></i> العودة لقائمة الموظفين
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>معلومات حساب العمر</strong>
                </div>
                <p>يتم حساب العمر بناءً على تاريخ الميلاد المسجل للموظف. يتم حساب العمر بالفرق بين تاريخ اليوم وتاريخ الميلاد.</p>
                <p>تاريخ اليوم: <strong>{{ today|date:"Y-m-d" }}</strong></p>
            </div>

            {% if search_query %}
            <div class="alert alert-success mb-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-search me-2"></i>
                    <span>نتائج البحث عن: <strong>{{ search_query }}</strong> - تم العثور على <strong>{{ employees|length }}</strong> موظف</span>
                </div>
            </div>
            {% endif %}

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr class="text-center">
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> الاسم الرباعي</th>
                            <th><i class="fas fa-briefcase me-1"></i> المسمى الوظيفي</th>
                            <th><i class="fas fa-building me-1"></i> القسم</th>
                            <th><i class="fas fa-birthday-cake me-1"></i> تاريخ الميلاد</th>
                            <th><i class="fas fa-hourglass-half me-1"></i> العمر (سنة/شهر/يوم)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr class="text-center">
                            <td>{{ employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' employee.id %}">
                                    {{ employee.full_name }}
                                </a>
                            </td>
                            <td>{{ employee.position_name }}</td>
                            <td>{{ employee.school }}</td>
                            <td>{{ employee.birth_date|date:"Y-m-d" }}</td>
                            <td>{{ employee.age_years }} سنة / {{ employee.age_months }} شهر / {{ employee.age_days }} يوم</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا يوجد موظفين لديهم تاريخ ميلاد مسجل</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[5, "desc"]], // Sort by age column (descending)
            "columnDefs": [
                { "width": "15%", "targets": 5 } // Make age column wider
            ],
            "pageLength": 25
        });
    });
</script>
{% endblock %}
