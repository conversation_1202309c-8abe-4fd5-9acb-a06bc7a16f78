{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .form-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }

    .form-body {
        padding: 40px;
        background: #f8f9fa;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .preview-card {
        background: white;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        display: none;
    }

    .preview-title {
        color: #667eea;
        font-weight: bold;
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .preview-description {
        color: #6c757d;
        font-style: italic;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="form-container">
        <div class="card form-card">
            <div class="form-header">
                <h2>
                    <i class="fas fa-briefcase me-2"></i>
                    {{ title }}
                </h2>
            </div>
            <div class="form-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- Job Name -->
                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}" class="form-label required-field">
                            <i class="fas fa-briefcase text-primary me-2"></i>
                            اسم الوظيفة
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Job Description -->
                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            <i class="fas fa-align-left text-info me-2"></i>
                            وصف الوظيفة
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="text-muted">وصف مختصر للوظيفة ومهامها (اختياري)</small>
                    </div>

                    <!-- Live Preview -->
                    <div id="jobPreview" class="preview-card">
                        <h5><i class="fas fa-eye text-primary me-2"></i>معاينة الوظيفة</h5>
                        <div class="preview-title" id="previewName">اسم الوظيفة</div>
                        <div class="preview-description" id="previewDescription">وصف الوظيفة</div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Buttons -->
                    <div class="d-flex justify-content-end gap-3 mt-4">
                        <a href="{% url 'employment:btec_job_list' %}" class="btn btn-cancel text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-save text-white">
                            <i class="fas fa-save me-2"></i>حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Live preview functionality
    function updatePreview() {
        const name = $('#id_name').val().trim();
        const description = $('#id_description').val().trim();
        
        if (name) {
            $('#previewName').text(name);
            $('#previewDescription').text(description || 'لا يوجد وصف');
            $('#jobPreview').show();
        } else {
            $('#jobPreview').hide();
        }
    }

    // Update preview on input
    $('#id_name, #id_description').on('input', updatePreview);

    // Initial preview update
    updatePreview();

    // Form validation
    $('form').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        if (!$('#id_name').val().trim()) {
            isValid = false;
            $('#id_name').addClass('is-invalid');
        } else {
            $('#id_name').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        }
    });

    // Remove validation classes on input
    $('.form-control').on('input', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>
{% endblock %}
