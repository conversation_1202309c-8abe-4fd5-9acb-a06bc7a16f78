from django.urls import path
from . import views

app_name = 'backup'

urlpatterns = [
    path('', views.backup_list, name='backup_list'),
    path('create/', views.create_backup, name='create_backup'),
    path('restore/', views.restore_backup, name='restore_backup'),
    path('upload/', views.upload_backup, name='upload_backup'),
    path('<int:pk>/download/', views.download_backup, name='download_backup'),
    path('<int:pk>/delete/', views.delete_backup, name='delete_backup'),
    path('select-folder/', views.select_folder_dialog, name='select_folder_dialog'),
    path('maintenance/status/', views.maintenance_status, name='maintenance_status'),
    path('maintenance/toggle/', views.toggle_maintenance, name='toggle_maintenance'),
]
