{% extends 'home/base.html' %}
{% load static %}

{% block title %}تعديل طلب النقل الداخلي - إداري{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid #007bff;
    }
    
    .section-title {
        color: #007bff;
        font-weight: bold;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-weight: bold;
    }
    
    .btn-save:hover {
        background: linear-gradient(45deg, #0056b3, #004085);
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-primary"></i>
            تعديل طلب النقل الداخلي - إداري
        </h1>
        <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-user-edit"></i>
                        تعديل بيانات طلب النقل الداخلي
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- معلومات الموظف -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-user"></i> معلومات الموظف
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="ministry_number" class="form-label">
                                        الرقم الوزاري <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="ministry_number" 
                                           name="ministry_number" value="{{ transfer.ministry_number }}" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="employee_id" class="form-label">
                                        الرقم الوطني <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="employee_id" 
                                           name="employee_id" value="{{ transfer.employee_id }}" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="employee_name" class="form-label">
                                        اسم الموظف <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="employee_name" 
                                           name="employee_name" value="{{ transfer.employee_name }}" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="specialization" class="form-label">التخصص</label>
                                    <input type="text" class="form-control" id="specialization" 
                                           name="specialization" value="{{ transfer.specialization|default:'' }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="actual_service" class="form-label">الخدمة الفعلية</label>
                                    <input type="text" class="form-control" id="actual_service" 
                                           name="actual_service" value="{{ transfer.actual_service|default:'' }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="current_department" class="form-label">
                                        القسم الحالي <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="current_department" 
                                           name="current_department" value="{{ transfer.current_department }}" required>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات النقل -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-map-marker-alt"></i> خيارات النقل
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="first_choice" class="form-label">
                                        الخيار الأول <span class="required-field">*</span>
                                    </label>
                                    <select class="form-control" id="first_choice" name="first_choice" required>
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.name }}" 
                                                {% if transfer.first_choice == department.name %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="second_choice" class="form-label">الخيار الثاني</label>
                                    <select class="form-control" id="second_choice" name="second_choice">
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.name }}" 
                                                {% if transfer.second_choice == department.name %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="third_choice" class="form-label">الخيار الثالث</label>
                                    <select class="form-control" id="third_choice" name="third_choice">
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.name }}" 
                                                {% if transfer.third_choice == department.name %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- حالة الطلب والملاحظات -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-clipboard-check"></i> حالة الطلب والملاحظات
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">حالة الطلب</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="pending" {% if transfer.status == 'pending' %}selected{% endif %}>
                                            قيد الانتظار
                                        </option>
                                        <option value="approved" {% if transfer.status == 'approved' %}selected{% endif %}>
                                            تمت الموافقة
                                        </option>
                                        <option value="rejected" {% if transfer.status == 'rejected' %}selected{% endif %}>
                                            مرفوض
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ transfer.notes|default:'' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary btn-save">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add form validation
    $('form').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // Remove validation class on input
    $('input, select').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>
{% endblock %}
