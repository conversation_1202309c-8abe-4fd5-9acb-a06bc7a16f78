{% extends 'base.html' %}
{% load static %}

{% block title %}بيانات تعريفية للموظف: {{ identification.employee.full_name }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">بيانات تعريفية للموظف: {{ identification.employee.full_name }}</h1>
        <div>
            <a href="{% url 'employment:edit_id_number' identification.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل رقم الهوية
            </a>
            <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">البيانات الشخصية</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light" width="40%">الرقم الوزاري</th>
                            <td>{{ identification.ministry_number }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">الاسم الكامل</th>
                            <td>{{ identification.employee.full_name }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">الرقم الوطني</th>
                            <td>{{ identification.national_id }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">رقم الهوية</th>
                            <td>{{ identification.id_number }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات إضافية</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light" width="40%">تاريخ الميلاد</th>
                            <td>{{ identification.birth_day }}/{{ identification.birth_month }}/{{ identification.birth_year }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">العنوان</th>
                            <td>{{ identification.address }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ الإضافة</th>
                            <td>{{ identification.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">آخر تحديث</th>
                            <td>{{ identification.updated_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات الموظف الوظيفية</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light" width="20%">المؤهل العلمي</th>
                            <td>{{ identification.employee.qualification }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">التخصص</th>
                            <td>{{ identification.employee.specialization }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ التعيين</th>
                            <td>{{ identification.employee.hire_date|date:"Y-m-d" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">المدرسة</th>
                            <td>{{ identification.employee.school }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">المسمى الوظيفي الحالي</th>
                            <td>{{ identification.employee.get_latest_position }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
