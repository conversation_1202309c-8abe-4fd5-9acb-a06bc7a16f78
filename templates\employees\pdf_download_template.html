<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        
        .header .info {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .status-active { color: #28a745; font-weight: bold; }
        .status-retired { color: #dc3545; font-weight: bold; }
        .status-transferred { color: #fd7e14; font-weight: bold; }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 11px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        
        .print-instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .print-instructions h4 {
            color: #0066cc;
            margin: 0 0 10px 0;
        }
        
        /* تحسين الطباعة */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        @media print {
            .print-instructions { display: none; }
            body { font-size: 10px; }
            table { font-size: 8px; }
            th, td { padding: 4px; }
        }
    </style>
</head>
<body>
    <div class="print-instructions no-print">
        <h4>📋 تعليمات الطباعة كـ PDF</h4>
        <p><strong>لحفظ هذا التقرير كملف PDF:</strong></p>
        <ol>
            <li>اضغط <kbd>Ctrl + P</kbd> أو انقر بزر الماوس الأيمن واختر "طباعة"</li>
            <li>في نافذة الطباعة، اختر "حفظ كـ PDF" أو "Microsoft Print to PDF"</li>
            <li>اختر مكان الحفظ واسم الملف</li>
            <li>اضغط "حفظ"</li>
        </ol>
        <p><em>💡 نصيحة: للحصول على أفضل نتيجة، اختر "Landscape" (أفقي) في إعدادات الطباعة</em></p>
    </div>
    
    <div class="header">
        <h1>{{ title }}</h1>
        <div class="info">
            <p>تاريخ إنشاء التقرير: {{ report_date }}</p>
            <p>إجمالي عدد الموظفين: {{ total_count }}</p>
        </div>
    </div>
    
    <div class="summary">
        <h3>📊 ملخص التقرير</h3>
        <p>يحتوي هذا التقرير على بيانات شاملة لجميع الموظفين تشمل المعلومات الشخصية، المؤهلات العلمية، بيانات التوظيف، والحالة الوظيفية.</p>
    </div>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>الرقم الوزاري</th>
                    <th>الاسم الكامل</th>
                    <th>الجنس</th>
                    <th>المؤهل العلمي</th>
                    <th>دبلوم عالي</th>
                    <th>ماجستير</th>
                    <th>دكتوراه</th>
                    <th>التخصص</th>
                    <th>القسم الحالي</th>
                    <th>المنصب الحالي</th>
                    <th>نوع التوظيف</th>
                    <th>حالة الموظف</th>
                    <th>تاريخ التعيين</th>
                    <th>رقم الهاتف</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees_data %}
                <tr>
                    <td>{{ employee.ministry_number|default:"-" }}</td>
                    <td>{{ employee.full_name|default:"-" }}</td>
                    <td>{{ employee.gender|default:"-" }}</td>
                    <td>{{ employee.qualification|default:"-" }}</td>
                    <td>{{ employee.post_graduate_diploma|default:"-" }}</td>
                    <td>{{ employee.masters_degree|default:"-" }}</td>
                    <td>{{ employee.phd_degree|default:"-" }}</td>
                    <td>{{ employee.specialization|default:"-" }}</td>
                    <td>{{ employee.school|default:"-" }}</td>
                    <td>{{ employee.current_position|default:"-" }}</td>
                    <td>{{ employee.employment_status|default:"-" }}</td>
                    <td class="{% if employee.employee_status == 'نشط' %}status-active{% elif employee.employee_status == 'متقاعد' %}status-retired{% elif employee.employee_status == 'منقول خارجياً' %}status-transferred{% endif %}">
                        {{ employee.employee_status|default:"-" }}
                    </td>
                    <td>{{ employee.hire_date|default:"-" }}</td>
                    <td>{{ employee.phone_number|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="14" style="text-align: center; color: #666; font-style: italic;">
                        لا توجد بيانات موظفين متاحة
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% if employees_data %}
    <div class="summary">
        <h3>📈 إحصائيات إضافية</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>إجمالي الموظفين:</strong> {{ total_count }}
            </div>
            <div>
                <strong>تاريخ التقرير:</strong> {{ report_date }}
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام شؤون الموظفين</p>
        <p>{{ report_date }} - جميع الحقوق محفوظة</p>
    </div>
    
    <script>
        // تلقائياً فتح نافذة الطباعة عند تحميل الصفحة
        window.onload = function() {
            // إعطاء وقت للصفحة لتحميل بالكامل
            setTimeout(function() {
                window.print();
            }, 1000);
        };
        
        // إضافة اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>