/* Navbar Improvements CSS */

/* Larger user name and notifications */
.username {
    font-weight: 700;
    font-size: 1.1rem !important;
}

.account-type {
    font-size: 0.85rem !important;
    color: #666;
}

body.dark-mode .account-type {
    color: #e0e0e0;
}

.nav-link .badge {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
}

.nav-link.position-relative i.fas.fa-bell {
    font-size: 1.4rem;
}

/* Search bar */
.navbar-search {
    position: relative;
    width: 300px;
    margin: 0 1rem;
}

.navbar-search .form-control {
    padding-right: 2.5rem;
    /* Colors are set in base.html to always be white-themed */
    border-radius: 50px;
    transition: all 0.3s ease;
}

.navbar-search .form-control:focus {
    /* Focus effect colors are set in base.html */
    width: 350px;
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
}

.navbar-search .search-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    /* Color is set in base.html */
    pointer-events: none;
}

/*
 * Dark mode styles for search bar are removed
 * as it is now always white-themed regardless of the mode
 */

/* Dark mode toggle */
.dark-mode-toggle {
    background-color: transparent;
    border: none;
    /* Color is set in base.html to always be white */
    font-size: 1.2rem;
    padding: 0.5rem;
    cursor: pointer;
    margin: 0 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
    position: relative;
}

.dark-mode-toggle:hover {
    /* Hover effect is set in base.html */
    transform: scale(1.1);
}

.dark-mode-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
}

.dark-mode-toggle:active {
    transform: scale(0.95);
}

/*
 * Dark mode styles for toggle button are removed
 * as it is now always white regardless of the mode
 */

/*
 * تم نقل أنماط الوضع المظلم إلى ملف dark-mode.css
 * هذا يساعد في تنظيم الكود وتجنب التعارض بين الأنماط
 */

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-search {
        width: 200px;
        margin: 0 0.5rem;
    }

    .navbar-search .form-control:focus {
        width: 250px;
    }
}

@media (max-width: 767.98px) {
    .navbar-search {
        display: none;
    }

    .dark-mode-toggle {
        margin: 0 0.25rem;
    }
}
