{% extends 'base.html' %}
{% load static %}

{% block title %}حول النظام - نظام إدارة الموارد البشرية{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Professional Design - Version 3.0 */
    /* Page Background */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-attachment: fixed;
        min-height: 100vh;
        position: relative;
    }
    
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="200" height="200" fill="url(%23grid)"/></svg>');
        opacity: 0.5;
        z-index: -1;
    }

    /* Hero Section */
    .about-hero {
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
        backdrop-filter: blur(10px);
        color: #2d3748;
        padding: 5rem 0;
        margin-bottom: 4rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 60px 60px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .about-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .about-logo {
        width: 140px;
        height: 140px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        position: relative;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .about-logo i {
        font-size: 3.5rem;
        color: white;
        animation: pulse 2s infinite;
        filter: drop-shadow(0 0 10px rgba(255,255,255,0.5));
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Enhanced Cards */
    .feature-card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        border-radius: 20px;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        border-radius: 20px 20px 0 0;
    }

    .feature-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 1);
    }

    .feature-card .card-body {
        padding: 2rem;
    }

    .feature-card .card-title {
        color: #2d3748;
        font-weight: 700;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .feature-card .card-title i {
        font-size: 1.5rem;
        margin-left: 0.5rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Section Titles */
    .section-title {
        position: relative;
        text-align: center;
        margin-bottom: 3rem;
        color: #2d3748;
        font-weight: 700;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    /* Enhanced List */
    .features-list {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: none;
    }

    .features-list .list-group-item {
        border: none;
        padding: 1rem 0;
        border-bottom: 1px solid #e2e8f0;
        background: transparent;
        transition: all 0.3s ease;
    }

    .features-list .list-group-item:hover {
        background: #f7fafc;
        padding-right: 1rem;
    }

    .features-list .list-group-item:last-child {
        border-bottom: none;
    }

    .features-list .list-group-item i {
        font-size: 1.2rem;
        margin-left: 1rem;
    }

    /* New Features Alert */
    .new-features {
        background: linear-gradient(135deg, #48bb78, #38a169);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
    }

    .new-features .alert-heading {
        color: white;
        font-weight: 700;
    }

    .new-features ul li {
        margin-bottom: 0.5rem;
        padding-right: 1rem;
        position: relative;
    }

    .new-features ul li::before {
        content: '✨';
        position: absolute;
        right: 0;
        top: 0;
    }

    /* Developer Section */
    .developer-section {
        background: #1f2937;
        color: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        border: 3px solid #fbbf24;
        margin: 2rem 0;
    }

    .developer-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(251, 191, 36, 0.05) 0%, transparent 70%);
        animation: rotate 30s linear infinite;
    }

    .developer-section::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 40%, rgba(251, 191, 36, 0.03) 50%, transparent 60%);
        animation: shimmer 4s ease-in-out infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes shimmer {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    .developer-section .content {
        position: relative;
        z-index: 2;
    }

    .developer-title {
        font-size: 1.3rem;
        color: #f9fafb;
        margin-bottom: 1rem;
        font-weight: 600;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .developer-name {
        font-size: 3.2rem;
        font-weight: 900;
        margin-bottom: 0.5rem;
        color: #fcd34d;
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
        line-height: 1.2;
        filter: drop-shadow(0 0 15px rgba(252, 211, 77, 0.8));
    }

    .developer-org {
        font-size: 1.4rem;
        color: #f9fafb;
        margin-bottom: 0.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .developer-dept {
        font-size: 1.2rem;
        color: #f3f4f6;
        font-weight: 500;
        margin-bottom: 1.5rem;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    }

    .developer-badge {
        display: inline-block;
        background: linear-gradient(45deg, #f59e0b, #d97706);
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 30px;
        font-size: 1.1rem;
        font-weight: 700;
        margin-top: 1.5rem;
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
        border: 2px solid #fcd34d;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .developer-icon {
        font-size: 4rem;
        color: #fcd34d;
        margin-bottom: 1rem;
        animation: pulse-gold 2s infinite;
        filter: drop-shadow(0 0 20px rgba(252, 211, 77, 1));
    }

    @keyframes pulse-gold {
        0%, 100% {
            transform: scale(1);
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
        }
        50% {
            transform: scale(1.1);
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .about-hero {
            padding: 2rem 0;
            border-radius: 0 0 25px 25px;
        }

        .about-logo {
            width: 80px;
            height: 80px;
        }

        .about-logo i {
            font-size: 2rem;
        }

        .developer-section {
            padding: 2rem 1rem;
            margin: 1rem 0;
            border: 2px solid #fcd34d;
        }

        .developer-name {
            font-size: 2.2rem;
        }

        .developer-title {
            font-size: 1.1rem;
        }

        .developer-org {
            font-size: 1.1rem;
        }

        .developer-dept {
            font-size: 1rem;
        }

        .developer-icon {
            font-size: 3rem;
        }

        .developer-badge {
            font-size: 1rem;
            padding: 0.6rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="about-hero">
    <div class="container text-center">
        <div class="about-logo">
            <i class="fas fa-users-cog"></i>
        </div>
        <h1 class="display-3 fw-bold mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            نظام إدارة الموارد البشرية
        </h1>
        <p class="lead mb-4 fs-4" style="color: #4a5568; font-weight: 500;">
            حلول متطورة وشاملة لإدارة الموارد البشرية في مديرية التربية والتعليم للواء قصبة المفرق
        </p>
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <div class="badge bg-gradient-primary fs-6 px-4 py-2" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                <i class="fas fa-code-branch me-2"></i>الإصدار 3.0.0
            </div>
            <div class="badge bg-success fs-6 px-4 py-2">
                <i class="fas fa-check-circle me-2"></i>نشط ومحدث
            </div>
            <div class="badge bg-info fs-6 px-4 py-2">
                <i class="fas fa-shield-alt me-2"></i>آمن ومعتمد
            </div>
        </div>
    </div>
</section>

<div class="container mb-5">
    <!-- Statistics Section -->
    <section class="mb-5">
        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #667eea;">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="fw-bold text-primary">12+</h3>
                        <p class="mb-0">وحدة نظام متكاملة</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #28a745;">
                            <i class="fas fa-shield-check"></i>
                        </div>
                        <h3 class="fw-bold text-success">100%</h3>
                        <p class="mb-0">أمان البيانات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #ffc107;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="fw-bold text-warning">24/7</h3>
                        <p class="mb-0">متاح دائماً</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #dc3545;">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3 class="fw-bold text-danger">3+</h3>
                        <p class="mb-0">سنوات تطوير</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="mb-5">
        <div class="row">
            <div class="col-lg-10 mx-auto text-center">
                <h2 class="section-title">نبذة عن النظام</h2>
                <p class="lead mb-4" style="line-height: 2; color: #4a5568; font-size: 1.2rem;">
                    نظام إدارة الموارد البشرية هو حلول تقنية متطورة ومتكاملة تم تصميمها خصيصاً لمديرية التربية والتعليم للواء قصبة المفرق.
                    يستخدم النظام أحدث التقنيات لتسهيل وأتمتة جميع العمليات المتعلقة بإدارة شؤون الموظفين، 
                    من تنظيم البيانات وإدارة الإجازات إلى إنشاء التقارير الشاملة وتوفير رؤى تحليلية دقيقة.
                </p>
                <div class="row g-3 justify-content-center">
                    <div class="col-auto">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-globe me-2"></i>واجهة ويب حديثة
                        </span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-mobile-alt me-2"></i>متجاوب مع الجوال
                        </span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-info fs-6 px-3 py-2">
                            <i class="fas fa-database me-2"></i>قاعدة بيانات آمنة
                        </span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-warning fs-6 px-3 py-2">
                            <i class="fas fa-sync me-2"></i>تحديثات مستمرة
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- System Modules Section -->
    <section class="mb-5">
        <h2 class="section-title">أجزاء النظام</h2>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-users"></i>الكادر</h6>
                        <p class="card-text">إدارة بيانات الموظفين وتفاصيلهم الوظيفية والشخصية، وإضافة موظفين جدد وتعديل بياناتهم.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-calendar-alt"></i>الإجازات</h6>
                        <p class="card-text">إدارة إجازات الموظفين بمختلف أنواعها، وتسجيل الإجازات الجديدة ومتابعة رصيد الإجازات.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-baby"></i>إجازات الأمومة <span class="badge bg-success ms-2">جديد</span></h6>
                        <p class="card-text">نظام متكامل لإدارة إجازات الأمومة مع حساب تلقائي للمدة (90 يوماً) وتصدير البيانات النشطة فقط.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-calendar-check"></i>إجازات الموظفين (المديرية)</h6>
                        <p class="card-text">إدارة إجازات موظفي المديرية، وإرسال رصيد الإجازات عبر واتس اب للموظفين أو لقسم كامل.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-tasks"></i>الإجراءات</h6>
                        <p class="card-text">إدارة الإجازات بدون راتب والعقوبات وأنواع العقوبات للموظفين.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-chart-line"></i>التقارير السنوية</h6>
                        <p class="card-text">إدارة التقارير السنوية للموظفين وتقييم الأداء، وإمكانية استيراد البيانات من ملفات Excel.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-file-alt"></i>التقارير</h6>
                        <p class="card-text">إنشاء تقارير متنوعة عن الموظفين والإجازات والتقييمات، مع إمكانية تصدير البيانات بصيغ مختلفة.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-folder"></i>الملفات</h6>
                        <p class="card-text">إدارة حركة الملفات وتسجيل خروج الملفات ومتابعة الملفات المنجزة.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-medal"></i>الرتب</h6>
                        <p class="card-text">إدارة أنواع الرتب ورتب الموظفين وإضافة رتب جديدة للموظفين.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-database"></i>النسخ الاحتياطي</h6>
                        <p class="card-text">إنشاء نسخ احتياطية من قاعدة البيانات واستعادتها عند الحاجة، مع إمكانية تحديد مواقع حفظ النسخ.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-user-cog"></i>إدارة المستخدمين</h6>
                        <p class="card-text">إدارة المستخدمين وتحديد الصلاحيات عبر نظام التبويبات، وإضافة مستخدمين جدد وتعديل بياناتهم.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-history"></i>سجل حركات النظام</h6>
                        <p class="card-text">عرض سجل حركات النظام وتصفية السجل ومسح سجل حركات النظام.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="mb-5">
        <h2 class="section-title">مميزات النظام</h2>
        <div class="features-list">
            <ul class="list-group list-group-flush">
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>واجهة مستخدم سهلة الاستخدام ومتجاوبة مع جميع أحجام الشاشات</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إمكانية إدارة بيانات الموظفين بشكل كامل ودقيق</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>نظام متكامل لإدارة الإجازات والتقارير السنوية</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إمكانية إرسال رصيد الإجازات عبر واتس اب للموظفين أو لقسم كامل</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إمكانية إنشاء تقارير متنوعة وتصديرها بصيغ مختلفة</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>نظام أمان متكامل لإدارة المستخدمين والصلاحيات عبر نظام التبويبات</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>نظام نسخ احتياطي متكامل لحماية البيانات</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إدارة الإجازات بدون راتب والعقوبات وأنواع العقوبات للموظفين</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إدارة حركة الملفات وتسجيل خروج الملفات ومتابعة الملفات المنجزة</li>
                <li class="list-group-item"><i class="fas fa-check-circle text-success"></i>إدارة أنواع الرتب ورتب الموظفين وإضافة رتب جديدة للموظفين</li>
            </ul>
        </div>
    </section>

    <!-- New Features Section -->
    <section class="mb-5">
        <h2 class="section-title">التحديثات الأخيرة</h2>
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="new-features alert">
                    <h6 class="alert-heading"><i class="fas fa-rocket me-2"></i>إصدار 3.0 - ميزات جديدة:</h6>
                    <ul class="mb-0 mt-3">
                        <li><strong>🚀 نظام الإعلانات المتطور:</strong> شريط إعلانات متحرك مع تأثيرات بصرية مميزة للإعلانات العاجلة</li>
                        <li><strong>📱 إرسال واتساب محسن:</strong> إرسال رصيد الإجازات للموظفين أو أقسام كاملة</li>
                        <li><strong>🔐 نظام صلاحيات متقدم:</strong> إدارة الصلاحيات عبر تبويبات منظمة وسهلة</li>
                        <li><strong>👶 نظام إجازات الأمومة:</strong> إدارة متكاملة لإجازات الأمومة مع حساب تلقائي للمدة (90 يوماً) وتصدير البيانات</li>
                        <li><strong>📊 واجهات محسنة:</strong> تصميم عصري ومتجاوب مع جميع الأجهزة</li>
                        <li><strong>⚡ أداء محسن:</strong> سرعة أكبر وتجربة مستخدم أفضل</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card feature-card h-100">
                    <div class="card-body">
                        <h6 class="card-title text-primary">
                            <i class="fas fa-cogs"></i> التحسينات التقنية
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تحديث قاعدة البيانات</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تحسين الأمان والحماية</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تطوير واجهة المستخدم</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>إضافة ميزات جديدة</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تحسين الاستقرار والأداء</li>
                        </ul>
                        <div class="mt-3">
                            <span class="badge bg-info">
                                <i class="fas fa-calendar me-1"></i>آخر تحديث: نوفمبر 2024
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Support & Contact Section -->
    <section class="mb-5">
        <h2 class="section-title">الدعم والتواصل</h2>
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #667eea;">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h6 class="card-title">الدعم التقني</h6>
                        <p class="card-text mb-3">متاح لحل المشاكل التقنية وتقديم المساعدة</p>
                        <span class="badge bg-success">متاح 24/7</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #28a745;">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h6 class="card-title">دليل المستخدم</h6>
                        <p class="card-text mb-3">شروحات مفصلة لجميع ميزات النظام</p>
                        <span class="badge bg-info">محدث دائماً</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card feature-card h-100 text-center">
                    <div class="card-body">
                        <div class="display-4 mb-3" style="color: #ffc107;">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h6 class="card-title">التحديثات</h6>
                        <p class="card-text mb-3">تحديثات دورية لتحسين الأداء والميزات</p>
                        <span class="badge bg-warning">شهرياً</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Developer Section -->
    <section class="mb-5">
        <div class="developer-section">
            <div class="content">
                <div class="developer-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="developer-title">تم تصميم وبرمجة هذا النظام من قبل</div>
                <div class="developer-name">أحمد العمري</div>
                <div class="developer-org">وزارة التربية والتعليم</div>
                <div class="developer-dept">مديرية قصبة المفرق</div>
                <div class="developer-badge">
                    <i class="fas fa-award me-2"></i>
                    مطور النظام
                </div>
            </div>
        </div>
    </section>

    <!-- System Information Footer -->
    <section class="mb-5">
        <div class="card feature-card">
            <div class="card-body text-center">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                        </div>
                        <h6 class="fw-bold">تاريخ الإطلاق</h6>
                        <p class="text-muted mb-0">يناير 2022</p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-code-branch fa-2x text-success"></i>
                        </div>
                        <h6 class="fw-bold">الإصدار الحالي</h6>
                        <p class="text-muted mb-0">v3.0.0</p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-users fa-2x text-warning"></i>
                        </div>
                        <h6 class="fw-bold">المستخدمون</h6>
                        <p class="text-muted mb-0">100+ موظف</p>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <i class="fas fa-server fa-2x text-info"></i>
                        </div>
                        <h6 class="fw-bold">مدة التشغيل</h6>
                        <p class="text-muted mb-0">99.9%</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}
