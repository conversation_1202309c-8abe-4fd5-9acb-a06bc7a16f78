{% extends 'base.html' %}

{% block title %}شهادة الخبرة{% endblock %}

{% block extra_css %}
<style>
    .table th {
        text-align: center;
        vertical-align: middle;
    }
    .table td {
        vertical-align: middle;
    }
    .btn-group {
        white-space: nowrap;
    }
    .filters-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold">شهادة الخبرة</h6>
        <div>
            <a href="{% url 'employment:employee_position_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> الحراك الوظيفي
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <form method="get">
                <div class="filters-container">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="بحث بالرقم الوزاري أو الاسم..." value="{{ search_query }}">
                                <button class="btn btn-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-8 mb-2 text-end">
                            {% if search_query %}
                            <a href="{% url 'employment:experience_certificate_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo"></i> إعادة الضبط
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th width="15%"><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th width="30%"><i class="fas fa-user me-1"></i> الاسم الكامل</th>
                        <th width="15%"><i class="fas fa-calendar-alt me-1"></i> تاريخ التعيين</th>
                        <th width="25%"><i class="fas fa-building me-1"></i> القسم</th>
                        <th width="15%"><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr class="text-center">
                        <td>{{ employee.ministry_number }}</td>
                        <td>{{ employee.full_name }}</td>
                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                        <td>{{ employee.school }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'employment:experience_certificate_view' employee.id %}" class="btn btn-sm btn-outline-primary experience-certificate-btn" data-employee-id="{{ employee.id }}" title="عرض شهادة الخبرة">
                                    <i class="fas fa-certificate"></i> شهادة الخبرة
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا يوجد موظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Manejar el botón de certificado de experiencia
        $('.experience-certificate-btn').on('click', function(e) {
            e.preventDefault();

            const employeeId = $(this).data('employee-id');

            // Crear un formulario para enviar
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = '{% url "employment:experience_certificate_view" 0 %}'.replace('0', employeeId);
            form.style.display = 'none';
            document.body.appendChild(form);
            form.submit();
        });
    });
</script>
{% endblock %}