from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Backup


class BackupForm(forms.ModelForm):
    """Form for creating a new backup"""
    backup_location = forms.CharField(
        label=_('مكان حفظ النسخة الاحتياطية'),
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
        help_text=_('مسار المجلد الذي سيتم حفظ النسخة الاحتياطية فيه (مثال: C:\\Backups)')
    )

    class Meta:
        model = Backup
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }


class RestoreForm(forms.Form):
    """Form for restoring a backup"""
    restore_type = forms.ChoiceField(
        label=_('نوع الاستعادة'),
        choices=[
            ('from_list', _('من قائمة النسخ الاحتياطية')),
            ('from_file', _('من ملف خارجي')),
        ],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial='from_list'
    )
    backup = forms.ModelChoiceField(
        queryset=Backup.objects.all(),
        label=_('اختر نسخة احتياطية'),
        empty_label=_('-- اختر نسخة احتياطية --'),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=False
    )
    backup_file = forms.FileField(
        label=_('ملف النسخة الاحتياطية'),
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.sql'}),
        help_text=_('اختر ملف النسخة الاحتياطية (.sql)')
    )
    confirm = forms.BooleanField(
        label=_('أتفهم أن هذه العملية ستؤدي إلى استبدال قاعدة البيانات الحالية'),
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    def clean(self):
        cleaned_data = super().clean()
        restore_type = cleaned_data.get('restore_type')
        backup = cleaned_data.get('backup')
        backup_file = cleaned_data.get('backup_file')

        if restore_type == 'from_list' and not backup:
            self.add_error('backup', _('يجب اختيار نسخة احتياطية'))

        if restore_type == 'from_file' and not backup_file:
            self.add_error('backup_file', _('يجب اختيار ملف النسخة الاحتياطية'))

        return cleaned_data


class UploadBackupForm(forms.ModelForm):
    """Form for uploading a backup file"""
    class Meta:
        model = Backup
        fields = ['name', 'description', 'file']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }
