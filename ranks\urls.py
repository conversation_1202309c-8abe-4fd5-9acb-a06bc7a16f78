from django.urls import path
from . import views

app_name = 'ranks'

urlpatterns = [
    # Rank Types URLs
    path('types/', views.rank_type_list, name='rank_type_list'),
    path('types/<int:pk>/update/', views.rank_type_update, name='rank_type_update'),
    path('types/<int:pk>/delete/', views.rank_type_delete, name='rank_type_delete'),

    # Employee Ranks URLs
    path('', views.employee_rank_list, name='employee_rank_list'),
    path('add/', views.employee_rank_create, name='employee_rank_create'),
    path('import/', views.employee_rank_import, name='employee_rank_import'),
    path('<int:pk>/', views.employee_rank_detail, name='employee_rank_detail'),
    path('<int:pk>/update/', views.employee_rank_update, name='employee_rank_update'),
    path('<int:pk>/delete/', views.employee_rank_delete, name='employee_rank_delete'),

    # Employee Allowances URLs
    path('allowances/', views.employee_allowance_list, name='employee_allowance_list'),
    path('allowances/add/', views.employee_allowance_create, name='employee_allowance_create'),
    path('allowances/<int:pk>/edit/', views.employee_allowance_update, name='employee_allowance_update'),
    path('allowances/<int:pk>/delete/', views.employee_allowance_delete, name='employee_allowance_delete'),
]
