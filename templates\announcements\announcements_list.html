{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الإعلانات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .announcement-card {
        border-left: 4px solid;
        border-radius: 8px;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        transition: all 0.3s ease;
        margin-bottom: 15px;
    }
    
    .announcement-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .announcement-card.info {
        border-left-color: #0d6efd;
    }
    
    .announcement-card.warning {
        border-left-color: #fd7e14;
    }
    
    .announcement-card.success {
        border-left-color: #198754;
    }
    
    .announcement-card.danger {
        border-left-color: #dc3545;
    }
    
    .announcement-header {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }
    
    .search-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid #dee2e6;
    }
    
    .stats-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    .priority-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .priority-low { background: #6c757d; color: white; }
    .priority-medium { background: #0d6efd; color: white; }
    .priority-high { background: #fd7e14; color: white; }
    .priority-urgent { background: #dc3545; color: white; }
    
    .type-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .type-info { background: #0d6efd; color: white; }
    .type-warning { background: #fd7e14; color: white; }
    .type-success { background: #198754; color: white; }
    .type-danger { background: #dc3545; color: white; }
    
    .btn-action {
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        margin: 2px;
    }
    
    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .status-toggle {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .status-toggle:hover {
        transform: scale(1.1);
    }
    
    /* Enhanced Stats Cards */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    
    .stats-content h2 {
        text-shadow: 0 1px 2px rgba(255,255,255,0.8);
        color: #000 !important;
    }
    
    .stats-card .text-dark {
        color: #000 !important;
    }
    
    .stats-card .stats-icon i {
        color: #000 !important;
    }
    
    .stats-card .stats-decoration i {
        color: #000 !important;
    }
    
    .stats-icon {
        position: relative;
        z-index: 2;
    }
    
    .stats-decoration {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
    }
    
    /* Table Styles */
    .announcements-table {
        border: 2px solid #dee2e6;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .announcements-table th {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
        color: white;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #495057;
        padding: 15px 10px;
        font-size: 0.9rem;
    }
    
    .announcements-table td {
        vertical-align: middle;
        border: 1px solid #dee2e6;
        padding: 12px 8px;
        text-align: center;
        font-size: 0.85rem;
    }
    
    .announcements-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .announcements-table tbody tr:hover {
        background-color: #e3f2fd;
        transform: scale(1.005);
        transition: all 0.2s ease;
    }
    
    .table-icon {
        font-size: 1.1rem;
        margin-left: 5px;
    }
    
    .content-preview {
        max-width: 200px;
        text-align: right;
    }
    
    .badge-type {
        font-size: 0.75rem;
        padding: 5px 10px;
    }
    
    .badge-priority {
        font-size: 0.75rem;
        padding: 5px 10px;
    }

    /* Animation for stats update */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .animate__animated.animate__pulse {
        animation: pulse 0.5s ease-in-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="announcement-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-bullhorn fa-4x"></i>
            </div>
            <div class="col-md-8">
                <h1 class="mb-2">إدارة الإعلانات</h1>
                <h4 class="mb-0">نظام إدارة وعرض الإعلانات</h4>
            </div>
            <div class="col-md-2">
                <a href="{% url 'announcements:announcement_create' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus"></i> إعلان جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-primary text-dark h-100">
                <div class="card-body text-center position-relative">
                    <div class="stats-icon">
                        <i class="fas fa-bullhorn fa-3x mb-3 opacity-75"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="display-6 fw-bold mb-1">{{ total_announcements }}</h2>
                        <p class="mb-0 fw-semibold">إجمالي الإعلانات</p>
                        <small class="opacity-75">جميع الإعلانات في النظام</small>
                    </div>
                    <div class="stats-decoration">
                        <i class="fas fa-chart-line position-absolute" style="top: 10px; right: 15px; opacity: 0.3; font-size: 1.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-success text-dark h-100">
                <div class="card-body text-center position-relative">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle fa-3x mb-3 opacity-75"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="display-6 fw-bold mb-1">{{ active_announcements }}</h2>
                        <p class="mb-0 fw-semibold">الإعلانات النشطة</p>
                        <small class="opacity-75">الإعلانات المفعلة حالياً</small>
                    </div>
                    <div class="stats-decoration">
                        <i class="fas fa-toggle-on position-absolute" style="top: 10px; right: 15px; opacity: 0.3; font-size: 1.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-warning text-dark h-100">
                <div class="card-body text-center position-relative">
                    <div class="stats-icon">
                        <i class="fas fa-home fa-3x mb-3 opacity-75"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="display-6 fw-bold mb-1">{{ homepage_announcements }}</h2>
                        <p class="mb-0 fw-semibold">إعلانات الصفحة الرئيسية</p>
                        <small class="opacity-75">تظهر في الشريط المتحرك</small>
                    </div>
                    <div class="stats-decoration">
                        <i class="fas fa-desktop position-absolute" style="top: 10px; right: 15px; opacity: 0.3; font-size: 1.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-info text-dark h-100">
                <div class="card-body text-center position-relative">
                    <div class="stats-icon">
                        <i class="fas fa-search fa-3x mb-3 opacity-75"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="display-6 fw-bold mb-1">{{ announcements.paginator.count }}</h2>
                        <p class="mb-0 fw-semibold">نتائج البحث</p>
                        <small class="opacity-75">الإعلانات المعروضة حالياً</small>
                    </div>
                    <div class="stats-decoration">
                        <i class="fas fa-filter position-absolute" style="top: 10px; right: 15px; opacity: 0.3; font-size: 1.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-container">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text bg-primary text-white">
                        <i class="fas fa-search"></i>
                    </span>
                    {{ form.search }}
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label small">نوع الإعلان</label>
                {{ form.announcement_type }}
            </div>
            <div class="col-md-2">
                <label class="form-label small">الأولوية</label>
                {{ form.priority }}
            </div>
            <div class="col-md-2">
                <label class="form-label small">الحالة</label>
                {{ form.status }}
            </div>
            <div class="col-md-2">
                <label class="form-label small">الصفحة الرئيسية</label>
                {{ form.show_on_homepage }}
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100" style="margin-top: 24px;">
                    بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Announcements Table -->
    <div class="card shadow">
        <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #495057 0%, #343a40 100%);">
            <h5 class="mb-0">
                <i class="fas fa-table table-icon"></i> جدول الإعلانات
                <span class="badge bg-light text-dark ms-2">{{ announcements.paginator.count }}</span>
            </h5>
        </div>
        <div class="card-body p-0">
            {% if announcements %}
                <div class="table-responsive">
                    <table class="table announcements-table mb-0">
                        <thead>
                            <tr>
                                <th style="width: 5%;">
                                    <i class="fas fa-hashtag table-icon"></i>ID
                                </th>
                                <th style="width: 5%;">
                                    <i class="fas fa-icons table-icon"></i>النوع
                                </th>
                                <th style="width: 30%;">
                                    <i class="fas fa-heading table-icon"></i>العنوان والمحتوى
                                </th>
                                <th style="width: 10%;">
                                    <i class="fas fa-tag table-icon"></i>التصنيف
                                </th>
                                <th style="width: 10%;">
                                    <i class="fas fa-exclamation-triangle table-icon"></i>الأولوية
                                </th>
                                <th style="width: 8%;">
                                    <i class="fas fa-toggle-on table-icon"></i>الحالة
                                </th>
                                <th style="width: 10%;">
                                    <i class="fas fa-chart-bar table-icon"></i>الإحصائيات
                                </th>
                                <th style="width: 12%;">
                                    <i class="fas fa-user table-icon"></i>المنشئ
                                </th>
                                <th style="width: 10%;">
                                    <i class="fas fa-cogs table-icon"></i>الإجراءات
                                </th>
                            </tr>
                        </thead>
                            {% for announcement in announcements %}
                                <tr>
                                    <!-- ID -->
                                    <td>
                                        <strong class="text-primary">#{{ announcement.pk }}</strong>
                                    </td>
                                    
                                    <!-- Type Icon -->
                                    <td>
                                        <i class="fas {{ announcement.type_icon }} fa-2x" style="color: {{ announcement.type_color }}" title="{{ announcement.get_announcement_type_display }}"></i>
                                    </td>
                                    
                                    <!-- Title and Content -->
                                    <td class="text-start">
                                        <div class="content-preview">
                                            <h6 class="mb-1">
                                                <a href="{% url 'announcements:announcement_detail' announcement.pk %}" class="text-decoration-none text-dark">
                                                    {{ announcement.title|truncatechars:40 }}
                                                </a>
                                            </h6>
                                            <p class="text-muted mb-1 small">{{ announcement.content|truncatewords:8 }}</p>
                                            {% if announcement.show_on_homepage %}
                                                <span class="badge bg-info badge-sm">
                                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <!-- Classification -->
                                    <td>
                                        <span class="badge badge-type bg-{{ announcement.type_color }} text-white">
                                            {{ announcement.get_announcement_type_display }}
                                        </span>
                                    </td>
                                    
                                    <!-- Priority -->
                                    <td>
                                        <span class="badge badge-priority bg-{% if announcement.priority == 'urgent' %}danger{% elif announcement.priority == 'high' %}warning{% elif announcement.priority == 'medium' %}primary{% else %}secondary{% endif %} text-white">
                                            {% if announcement.priority == 'urgent' %}🚨{% endif %}
                                            {{ announcement.get_priority_display }}
                                        </span>
                                    </td>
                                    
                                    <!-- Status -->
                                    <td>
                                        <div class="status-toggle" onclick="toggleStatus({{ announcement.pk }}, this)">
                                            {% if announcement.is_active %}
                                                <i class="fas fa-toggle-on fa-2x text-success" title="مفعل - انقر لإلغاء التفعيل"></i>
                                                <br><small class="text-success">نشط</small>
                                            {% else %}
                                                <i class="fas fa-toggle-off fa-2x text-danger" title="غير مفعل - انقر للتفعيل"></i>
                                                <br><small class="text-danger">غير نشط</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <!-- Statistics -->
                                    <td>
                                        <div class="text-center">
                                            <small class="d-block">
                                                <i class="fas fa-eye text-info"></i> {{ announcement.views_count }}
                                            </small>
                                            <small class="d-block">
                                                <i class="fas fa-mouse-pointer text-warning"></i> {{ announcement.clicks_count }}
                                            </small>
                                        </div>
                                    </td>
                                    
                                    <!-- Creator -->
                                    <td>
                                        <div class="text-center">
                                            <small class="d-block">
                                                <i class="fas fa-user text-secondary"></i><br>
                                                {{ announcement.created_by.get_full_name|default:announcement.created_by.username|truncatechars:15 }}
                                            </small>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-calendar"></i> {{ announcement.created_at|date:"m/d" }}
                                            </small>
                                        </div>
                                    </td>
                                    
                                    <!-- Actions -->
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <a href="{% url 'announcements:announcement_detail' announcement.pk %}" 
                                               class="btn btn-outline-info btn-action mb-1" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'announcements:announcement_update' announcement.pk %}" 
                                               class="btn btn-outline-warning btn-action mb-1" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'announcements:announcement_delete' announcement.pk %}" 
                                               class="btn btn-outline-danger btn-action" title="حذف"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا الإعلان؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if announcements.has_other_pages %}
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            {% if announcements.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.announcement_type %}&announcement_type={{ request.GET.announcement_type }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.show_on_homepage %}&show_on_homepage={{ request.GET.show_on_homepage }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ announcements.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.announcement_type %}&announcement_type={{ request.GET.announcement_type }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.show_on_homepage %}&show_on_homepage={{ request.GET.show_on_homepage }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {{ announcements.number }} من {{ announcements.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if announcements.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ announcements.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.announcement_type %}&announcement_type={{ request.GET.announcement_type }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.show_on_homepage %}&show_on_homepage={{ request.GET.show_on_homepage }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ announcements.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.announcement_type %}&announcement_type={{ request.GET.announcement_type }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.show_on_homepage %}&show_on_homepage={{ request.GET.show_on_homepage }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد إعلانات</h4>
                    <p class="text-muted">لم يتم العثور على إعلانات مطابقة للمعايير المحددة</p>
                    <a href="{% url 'announcements:announcement_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة إعلان جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleStatus(announcementId, element) {
    $.ajax({
        url: `/announcements/${announcementId}/toggle-status/`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(response) {
            if (response.success) {
                const icon = element.querySelector('i');
                const statusText = element.querySelector('small');

                if (response.is_active) {
                    icon.className = 'fas fa-toggle-on fa-2x text-success';
                    icon.title = 'مفعل - انقر لإلغاء التفعيل';
                    statusText.textContent = 'نشط';
                    statusText.className = 'text-success';
                } else {
                    icon.className = 'fas fa-toggle-off fa-2x text-danger';
                    icon.title = 'غير مفعل - انقر للتفعيل';
                    statusText.textContent = 'غير نشط';
                    statusText.className = 'text-danger';
                }

                // Update statistics cards if provided
                if (response.stats) {
                    updateStatsCards(response.stats);
                }

                // Show success message
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> ${response.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                $('.container-fluid').prepend(alertHtml);

                // Auto hide after 3 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 3000);
            }
        },
        error: function() {
            alert('حدث خطأ أثناء تغيير حالة الإعلان');
        }
    });
}

// Function to update statistics cards
function updateStatsCards(stats) {
    // Update total announcements
    $('.stats-card:nth-child(1) .stats-content h2').text(stats.total_announcements);

    // Update active announcements
    $('.stats-card:nth-child(2) .stats-content h2').text(stats.active_announcements);

    // Update homepage announcements
    $('.stats-card:nth-child(3) .stats-content h2').text(stats.homepage_announcements);

    // Add animation effect
    $('.stats-card .stats-content h2').each(function() {
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
            $(this).removeClass('animate__animated animate__pulse');
        }, 1000);
    });
}

// Function to refresh all statistics
function refreshStats() {
    $.ajax({
        url: '{% url "announcements:get_announcements_stats" %}',
        type: 'GET',
        success: function(stats) {
            updateStatsCards(stats);
        },
        error: function() {
            console.log('خطأ في تحديث الإحصائيات');
        }
    });
}

// Auto refresh stats every 30 seconds
setInterval(refreshStats, 30000);

// Check if page was loaded after an action (create, update, delete)
$(document).ready(function() {
    // If there are success messages, refresh stats
    if ($('.alert-success').length > 0) {
        setTimeout(refreshStats, 500);
    }
});
</script>
{% endblock %}