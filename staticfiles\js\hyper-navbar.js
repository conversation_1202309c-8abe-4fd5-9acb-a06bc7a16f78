/**
 * Hyper Navbar JavaScript
 * For handling navbar functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Hyper Navbar JS loaded');

    // Search functionality
    const searchToggle = document.getElementById('searchToggle');
    const searchBox = document.getElementById('searchBox');

    if (searchToggle && searchBox) {
        console.log('Search elements found');

        searchToggle.addEventListener('click', function(e) {
            console.log('Search toggle clicked');
            e.preventDefault();
            e.stopPropagation();

            if (searchBox.classList.contains('show')) {
                searchBox.classList.remove('show');
            } else {
                searchBox.classList.add('show');
                setTimeout(function() {
                    searchBox.querySelector('input').focus();
                }, 100);
            }
        });

        // Close search box when clicking outside
        document.addEventListener('click', function(event) {
            if (searchBox.classList.contains('show') &&
                !searchToggle.contains(event.target) &&
                !searchBox.contains(event.target)) {
                searchBox.classList.remove('show');
            }
        });
    } else {
        console.error('Search elements not found', { searchToggle, searchBox });
    }

    // Dark mode toggle functionality
    function initDarkMode() {
        console.log('Initializing dark mode functionality');

        const darkModeToggle = document.getElementById('darkModeToggle');
        const darkModeIcon = darkModeToggle ? darkModeToggle.querySelector('i') : null;

        if (!darkModeToggle) {
            console.error('Dark mode toggle button not found');
            return;
        }

        if (!darkModeIcon) {
            console.error('Dark mode icon not found');
            return;
        }

        console.log('Dark mode elements found:', { darkModeToggle, darkModeIcon });

        // Function to apply dark mode
        function applyDarkMode(isDark) {
            // Add animation class
            darkModeIcon.classList.add('fa-spin');

            // Apply changes after a small delay for animation effect
            setTimeout(function() {
                if (isDark) {
                    document.body.classList.add('dark-mode');
                    darkModeIcon.classList.remove('fa-moon');
                    darkModeIcon.classList.add('fa-sun');
                    console.log('Dark mode applied');
                } else {
                    document.body.classList.remove('dark-mode');
                    darkModeIcon.classList.remove('fa-sun');
                    darkModeIcon.classList.add('fa-moon');
                    console.log('Light mode applied');
                }

                // Remove animation class after changes
                setTimeout(function() {
                    darkModeIcon.classList.remove('fa-spin');
                }, 300);
            }, 150);
        }

        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        console.log('Saved theme:', savedTheme);

        // Apply initial theme
        applyDarkMode(savedTheme === 'dark');

        // Toggle dark mode on click
        darkModeToggle.addEventListener('click', function(e) {
            console.log('Dark mode toggle clicked');
            e.preventDefault();
            e.stopPropagation();

            const isDarkMode = !document.body.classList.contains('dark-mode');
            localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
            applyDarkMode(isDarkMode);
        });
    }

    // Initialize dark mode
    initDarkMode();

    // Notifications
    const markAllReadBtn = document.getElementById('markAllAsRead');

    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const badges = document.querySelectorAll('.notification-badge');
            badges.forEach(function(badge) {
                badge.style.display = 'none';
            });
        });
    }
});
