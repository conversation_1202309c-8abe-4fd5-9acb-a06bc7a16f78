{% extends 'base.html' %}
{% load static %}

{% block title %}معاينة تقرير التقييمات السنوية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background-color: white;
        }

        .container-fluid, .card {
            padding: 0 !important;
            margin: 0 !important;
            border: none !important;
            box-shadow: none !important;
        }

        .page-number:after {
            content: counter(page);
        }
    }

    .table th {
        background-color: #f2f2f2 !important;
        color: #000 !important;
        font-weight: bold;
    }

    .table-bordered th, .table-bordered td {
        border: 1px solid #000 !important;
    }

    .table th, .table td {
        text-align: center;
        vertical-align: middle;
        padding: 8px;
        font-size: 14px;
    }

    /* Enhanced styles for better PDF quality */
    .table {
        width: 100%;
        border-collapse: collapse;
        box-shadow: none;
    }

    .table-responsive {
        overflow: visible;
        border: none;
        box-shadow: none;
    }

    .even-row {
        background-color: #f9f9f9;
    }

    .report-title {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
    }

    .report-date, .report-count {
        font-size: 12px;
    }

    .report-count {
        font-weight: bold;
        color: #007bff;
    }

    .btn-toolbar {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="btn-toolbar d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">{{ report_title }} - معاينة قبل التصدير</h1>
        <div class="btn-group">
            <button id="captureAndSavePDF" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> تصدير PDF
            </button>
            <button onclick="printTable()" class="btn btn-primary">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="{% url 'performance:performance_list' %}{% if search_query %}?search={{ search_query }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body" id="tableContainer">
            <div class="report-title">{{ report_title }}</div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="report-date">تاريخ التقرير: {% now "Y-m-d" %}</div>
                <div class="report-count">عدد الموظفين: {{ employee_count }}</div>
            </div>

            {% if selected_year %}
            <div class="report-filters mb-3">
                <strong>الفلاتر المطبقة:</strong>
                <span class="badge bg-info me-2">السنة: {{ selected_year }}</span>
            </div>
            {% endif %}

            {% if evaluations %}
            <div class="table-responsive">
                <table class="table table-bordered" id="previewTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الرقم الوزاري</th>
                            <th>اسم الموظف</th>
                            <th>السنة</th>
                            <th>العلامة</th>
                            <th>الدرجة القصوى</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for evaluation in evaluations %}
                        <tr class="{% cycle '' 'even-row' %}">
                            <td>{{ evaluation.employee.ministry_number }}</td>
                            <td>{{ evaluation.employee.full_name }}</td>
                            <td>{{ evaluation.year }}</td>
                            <td>{{ evaluation.score }}</td>
                            <td>{{ evaluation.max_score }}</td>
                            <td>{{ evaluation.comments|default:"-" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد تقييمات سنوية متطابقة مع معايير البحث.
            </div>
            {% endif %}

            <div class="text-center mt-3 no-print">
                <p class="small text-muted">صفحة <span class="page-number">1</span></p>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4 no-print">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
        </div>
        <div class="card-body">
            <p>هذه معاينة للتقرير قبل تحميله كملف PDF. يمكنك الآن:</p>
            <ul>
                <li>مراجعة البيانات للتأكد من صحتها</li>
                <li>النقر على زر "تصدير PDF" لتحميل التقرير كملف PDF</li>
                <li>العودة إلى صفحة التقرير الرئيسية لإجراء تعديلات على معايير التصفية</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include required libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
    // Add page numbers when printing
    window.onbeforeprint = function() {
        document.querySelectorAll('.page-number').forEach(function(el) {
            el.textContent = '1';
        });
    };

    // Function to handle PDF download
    document.addEventListener('DOMContentLoaded', function() {
        const captureButton = document.getElementById('captureAndSavePDF');
        if (captureButton) {
            captureButton.addEventListener('click', function() {
                // Show loading message
                alert('جاري إنشاء ملف PDF، يرجى الانتظار...');

                // Get the table container
                var element = document.getElementById('tableContainer');

                // Use html2canvas to capture the table as an image
                html2canvas(element, {
                    scale: 2,  // Higher scale for better quality
                    useCORS: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    allowTaint: true,
                    foreignObjectRendering: false
                }).then(function(canvas) {
                    try {
                        // Get the jsPDF class from the loaded library
                        var { jsPDF } = window.jspdf;

                        // Create a new PDF document in landscape orientation
                        var pdf = new jsPDF({
                            orientation: 'landscape',
                            unit: 'mm',
                            format: 'a4'
                        });

                        // Get canvas as image
                        var imgData = canvas.toDataURL('image/jpeg', 1.0);

                        // Calculate dimensions to fit the page
                        var pdfWidth = pdf.internal.pageSize.getWidth();
                        var pdfHeight = pdf.internal.pageSize.getHeight();

                        var imgWidth = pdfWidth - 20;  // 10mm margin on each side
                        var imgHeight = (canvas.height * imgWidth) / canvas.width;

                        // If image is too tall, scale it to fit the page height
                        if (imgHeight > pdfHeight - 20) {
                            imgHeight = pdfHeight - 20;  // 10mm margin on top and bottom
                            imgWidth = (canvas.width * imgHeight) / canvas.height;
                        }

                        // Calculate position to center the image
                        var x = (pdfWidth - imgWidth) / 2;
                        var y = (pdfHeight - imgHeight) / 2;

                        // Add the image to the PDF
                        pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

                        // Add metadata
                        pdf.setProperties({
                            title: 'تقرير التقييمات السنوية',
                            subject: 'تقرير التقييمات السنوية',
                            creator: 'نظام شؤون الموظفين',
                            author: 'نظام شؤون الموظفين'
                        });

                        // Save the PDF
                        pdf.save('تقرير_التقييمات_السنوية.pdf');

                        // Show success message
                        alert('تم إنشاء ملف PDF بنجاح!');
                    } catch (error) {
                        // Show error message
                        console.error('Error creating PDF:', error);
                        alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
                    }
                }).catch(function(error) {
                    // Show error message
                    console.error('Error capturing image:', error);
                    alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
                });
            });
        }
    });

    // Function to handle printing
    function printTable() {
        // Show loading message
        alert('جاري تجهيز الصفحة للطباعة، يرجى الانتظار...');

        // Get the table container
        var element = document.getElementById('tableContainer');

        // Use html2canvas to capture the table as an image
        html2canvas(element, {
            scale: 2,  // Higher scale for better quality
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false,
            allowTaint: true,
            foreignObjectRendering: false
        }).then(function(canvas) {
            try {
                // Create a new window
                var printWindow = window.open('', '_blank');

                // Write HTML content to the new window
                printWindow.document.write('<html><head><title>تقرير التقييمات السنوية</title>');
                printWindow.document.write('<style>body { margin: 0; padding: 20px; direction: rtl; } img { max-width: 100%; height: auto; }</style>');
                printWindow.document.write('</head><body>');

                // Add the canvas as an image
                printWindow.document.write('<img src="' + canvas.toDataURL('image/jpeg', 1.0) + '" />');

                printWindow.document.write('</body></html>');

                // Close the document
                printWindow.document.close();

                // Wait for the image to load before printing
                printWindow.onload = function() {
                    printWindow.print();
                };
            } catch (error) {
                // Show error message
                console.error('Error preparing for print:', error);
                alert('حدث خطأ أثناء تجهيز الصفحة للطباعة: ' + error.message);
            }
        }).catch(function(error) {
            // Show error message
            console.error('Error capturing image:', error);
            alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
        });
    }
</script>
{% endblock %}
