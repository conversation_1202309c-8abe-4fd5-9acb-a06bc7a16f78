# Generated by Django 5.2 on 2025-05-15 09:08

import home.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0004_systemsettings'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApprovedForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='عنوان النموذج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف النموذج')),
                ('file', models.FileField(upload_to=home.models.get_file_upload_path, verbose_name='الملف')),
                ('file_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الملف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
            ],
            options={
                'verbose_name': 'نموذج معتمد',
                'verbose_name_plural': 'النماذج المعتمدة',
                'ordering': ['order', '-created_at'],
            },
        ),
    ]
