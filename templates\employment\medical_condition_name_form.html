{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .form-section h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }
    
    .required-field label:after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{{ title }}</h2>
    <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى القائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <!-- Medical Condition Name Information Section -->
            <div class="form-section">
                <h4>معلومات اسم الحالة المرضية</h4>
                <div class="row">
                    <div class="col-md-12 required-field">
                        <div class="mb-3">
                            <label for="id_name">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="id_description">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.description.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Errors -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Submit Button -->
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
