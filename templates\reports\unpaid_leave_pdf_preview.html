{% extends 'base.html' %}
{% load static %}

{% block title %}معاينة تقرير الإجازات بدون راتب - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            margin: 0;
            padding: 0;
            background-color: white;
        }
        .container-fluid {
            width: 100%;
            margin: 0;
            padding: 0;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-header, .card-body {
            padding: 0 !important;
        }
        table {
            width: 100% !important;
        }
    }
    
    /* Make table more compact for PDF */
    #previewTable th, #previewTable td {
        padding: 0.5rem;
        text-align: center;
    }
    
    /* Add some styling for the PDF view */
    #tableContainer {
        direction: rtl;
        text-align: right;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الإجازات بدون راتب - معاينة قبل التصدير</h1>
        <div>
            <a href="{% url 'reports:unpaid_leave_report' %}?start_date={{ start_date|default:'' }}&end_date={{ end_date|default:'' }}&department={{ department_id|default:'' }}" class="btn btn-secondary me-2 no-print">
                <i class="fas fa-arrow-left"></i> العودة للتقرير
            </a>
            <button onclick="printDocument()" class="btn btn-danger no-print">
                <i class="fas fa-file-pdf"></i> تحميل PDF
            </button>
            <button onclick="printTable()" class="btn btn-info ms-2 no-print">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الإجازات بدون راتب</h6>
            <span class="badge bg-primary fs-6 p-2">عدد الإجازات: {{ leaves|length }}</span>
        </div>
        <div class="card-body">
            {% if leaves %}
            <div id="tableContainer" class="table-responsive">
                <h3 class="text-center mb-3">تقرير الإجازات بدون راتب</h3>
                <p class="text-center mb-4">تاريخ التقرير: {% now "Y-m-d" %}</p>
                <table class="table table-bordered" id="previewTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th>الرقم الوزاري</th>
                            <th>اسم الموظف</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leaves %}
                        <tr>
                            <td>{{ leave.employee.ministry_number }}</td>
                            <td>{{ leave.employee.full_name }}</td>
                            <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.days_count }}</td>
                            <td>
                                {% if leave.status == 'approved' %}
                                <span class="badge bg-success">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'pending' %}
                                <span class="badge bg-warning text-dark">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">{{ leave.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ leave.get_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد إجازات بدون راتب تطابق معايير البحث.
            </div>
            {% endif %}
        </div>
    </div>

    <div class="card shadow mb-4 no-print">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
        </div>
        <div class="card-body">
            <p>هذه معاينة للتقرير قبل تحميله كملف PDF. يمكنك الآن:</p>
            <ul>
                <li>مراجعة البيانات للتأكد من صحتها</li>
                <li>النقر على زر "تحميل PDF" لتحميل التقرير كملف PDF</li>
                <li>العودة إلى صفحة التقرير الرئيسية لإجراء تعديلات على معايير التصفية</li>
            </ul>
            <p><strong>ملاحظة:</strong> عند تصدير التقرير إلى PDF، يتم ترتيب الجدول من اليمين إلى اليسار بدءًا من الرقم الوزاري.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include required libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
    // Initialize DataTable when document is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable with minimal features for the preview
        var table = $('#previewTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[0, "asc"]],
            "pageLength": -1,  // Show all rows
            "dom": 'rt',       // Only show the table, no search or pagination
            "buttons": []
        });
    });

    // Function to handle PDF download
    function printDocument() {
        // Show loading message
        alert('جاري إنشاء ملف PDF، يرجى الانتظار...');

        // Get the table container
        var element = document.getElementById('tableContainer');

        // Use html2canvas to capture the table as an image
        html2canvas(element, {
            scale: 2,  // Higher scale for better quality
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false,
            allowTaint: true,
            foreignObjectRendering: false
        }).then(function(canvas) {
            try {
                // Get the jsPDF class from the loaded library
                var { jsPDF } = window.jspdf;

                // Create a new PDF document in landscape orientation
                var pdf = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });

                // Calculate the width and height of the canvas
                var imgWidth = 280; // A4 landscape width (297mm) with margins
                var imgHeight = canvas.height * imgWidth / canvas.width;

                // Add the image to the PDF
                var imgData = canvas.toDataURL('image/png');
                pdf.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);

                // Save the PDF
                pdf.save('تقرير_الإجازات_بدون_راتب.pdf');
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.');
            }
        });
    }

    // Function to print the table
    function printTable() {
        window.print();
    }
</script>
{% endblock %}
