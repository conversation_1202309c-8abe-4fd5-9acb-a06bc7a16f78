# Generated by Django 5.2 on 2025-04-07 10:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ministry_number', models.CharField(max_length=20, unique=True, verbose_name='Ministry Number')),
                ('national_id', models.Char<PERSON><PERSON>(max_length=20, unique=True, verbose_name='National ID')),
                ('full_name', models.Char<PERSON>ield(max_length=255, verbose_name='Full Name')),
                ('qualification', models.Char<PERSON>ield(max_length=255, verbose_name='Qualification')),
                ('specialization', models.Char<PERSON>ield(max_length=255, verbose_name='Specialization')),
                ('hire_date', models.DateField(verbose_name='Hire Date')),
                ('school', models.Char<PERSON><PERSON>(max_length=255, verbose_name='School')),
                ('birth_date', models.DateField(verbose_name='Birth Date')),
                ('address', models.TextField(verbose_name='Address')),
                ('phone_number', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
                'ordering': ['full_name'],
            },
        ),
        migrations.CreateModel(
            name='Penalty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('description', models.TextField(verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='penalties', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Penalty',
                'verbose_name_plural': 'Penalties',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AnnualReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField(verbose_name='Year')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Score')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='annual_reports', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Annual Report',
                'verbose_name_plural': 'Annual Reports',
                'ordering': ['-year'],
                'unique_together': {('employee', 'year')},
            },
        ),
    ]
