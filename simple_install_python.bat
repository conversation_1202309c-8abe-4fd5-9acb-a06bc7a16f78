@echo off
echo ===================================
echo تثبيت بايثون وتشغيل نظام الموارد البشرية
echo ===================================
echo.
echo سيقوم هذا الملف بمساعدتك في تثبيت بايثون وتشغيل النظام.
echo.
echo الخيارات:
echo 1. فتح متجر مايكروسوفت لتثبيت بايثون
echo 2. تعطيل اختصارات تنفيذ التطبيقات
echo 3. تشغيل النظام (بعد تثبيت بايثون)
echo 4. الخروج
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo فتح متجر مايكروسوفت...
    start ms-windows-store://pdp/?ProductId=9PJPW5LDXLZ5
    echo بعد التثبيت، قم بتشغيل الخيار 3 لتشغيل النظام.
    pause
    %0
) else if "%choice%"=="2" (
    echo فتح إعدادات اختصارات تنفيذ التطبيقات...
    start ms-settings:appsfeatures-app
    echo انتقل إلى "اختصارات تنفيذ التطبيقات" وقم بإيقاف تشغيل python.exe و python3.exe
    pause
    %0
) else if "%choice%"=="3" (
    echo محاولة تشغيل النظام...
    
    if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" (
        echo استخدام بايثون من متجر مايكروسوفت...
        "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" manage.py runserver
        goto :end
    )
    
    if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" (
        echo استخدام بايثون 3 من متجر مايكروسوفت...
        "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" manage.py runserver
        goto :end
    )
    
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
        echo استخدام بايثون 3.11...
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" manage.py runserver
        goto :end
    )
    
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" (
        echo استخدام بايثون 3.10...
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" manage.py runserver
        goto :end
    )
    
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
        echo استخدام بايثون 3.9...
        "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" manage.py runserver
        goto :end
    )
    
    echo لم يتم العثور على بايثون. يرجى تثبيت بايثون أولاً باستخدام الخيار 1.
    pause
    %0
) else (
    echo الخروج...
    goto :end
)

:end
echo.
echo انتهى التنفيذ. اضغط أي مفتاح للخروج...
pause > nul
