{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الخطأ - {{ error.get_error_type_display }}{% endblock %}

{% block extra_css %}
<style>
    .error-detail-card {
        border: none;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .error-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }
    
    .error-severity-critical {
        background-color: #dc3545;
        color: white;
    }
    
    .error-severity-high {
        background-color: #fd7e14;
        color: white;
    }
    
    .error-severity-medium {
        background-color: #ffc107;
        color: black;
    }
    
    .error-severity-low {
        background-color: #28a745;
        color: white;
    }
    
    .error-status-new {
        background-color: #dc3545;
        color: white;
    }
    
    .error-status-in_progress {
        background-color: #ffc107;
        color: black;
    }
    
    .error-status-resolved {
        background-color: #28a745;
        color: white;
    }
    
    .error-status-ignored {
        background-color: #6c757d;
        color: white;
    }
    
    .info-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .info-value {
        color: #212529;
        margin-bottom: 15px;
    }
    
    .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
    }
    
    .occurrence-badge {
        background-color: #17a2b8;
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 1rem;
        font-weight: bold;
    }
    
    .timeline-item {
        border-left: 3px solid #dee2e6;
        padding-left: 20px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
    }
    
    .resolution-section {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{% url 'system_logs:system_error_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة لقائمة الأخطاء
            </a>
        </div>
    </div>

    <!-- Error Header -->
    <div class="error-detail-card">
        <div class="error-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3 class="mb-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error.get_error_type_display }}
                    </h3>
                    <p class="mb-0 opacity-75">{{ error.error_description }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="mb-2">
                        <span class="badge error-severity-{{ error.severity }} fs-6">
                            {{ error.get_severity_display }}
                        </span>
                    </div>
                    <div class="mb-2">
                        <span class="badge error-status-{{ error.status }} fs-6" id="currentStatus">
                            {{ error.get_status_display }}
                        </span>
                    </div>
                    <div>
                        <span class="occurrence-badge">
                            تكرر {{ error.occurrence_count }} مرة
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Basic Information -->
            <div class="row">
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle text-primary"></i>
                            معلومات أساسية
                        </h5>
                        
                        <div class="info-label">تاريخ الحدوث الأول:</div>
                        <div class="info-value">
                            {{ error.timestamp|date:"Y-m-d H:i:s" }}
                        </div>
                        
                        <div class="info-label">آخر حدوث:</div>
                        <div class="info-value">
                            {{ error.last_occurrence|date:"Y-m-d H:i:s" }}
                        </div>
                        
                        <div class="info-label">عدد مرات التكرار:</div>
                        <div class="info-value">
                            <span class="occurrence-badge">{{ error.occurrence_count }}</span>
                        </div>
                        
                        {% if error.module %}
                        <div class="info-label">الوحدة:</div>
                        <div class="info-value">
                            <span class="badge bg-info">{{ error.module }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-map-marker-alt text-warning"></i>
                            موقع الخطأ
                        </h5>
                        
                        <div class="info-label">اسم الصفحة:</div>
                        <div class="info-value">
                            <strong>{{ error.page_name }}</strong>
                        </div>
                        
                        <div class="info-label">رابط الصفحة:</div>
                        <div class="info-value">
                            <code>{{ error.page_url }}</code>
                        </div>
                        
                        {% if error.file_path %}
                        <div class="info-label">مسار الملف:</div>
                        <div class="info-value">
                            <code>{{ error.file_path }}</code>
                        </div>
                        {% endif %}
                        
                        {% if error.line_number %}
                        <div class="info-label">رقم السطر:</div>
                        <div class="info-value">
                            <span class="badge bg-secondary">{{ error.line_number }}</span>
                        </div>
                        {% endif %}
                        
                        {% if error.function_name %}
                        <div class="info-label">اسم الدالة:</div>
                        <div class="info-value">
                            <code>{{ error.function_name }}()</code>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- User Information -->
            <div class="row">
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-user text-success"></i>
                            معلومات المستخدم
                        </h5>
                        
                        <div class="info-label">المستخدم:</div>
                        <div class="info-value">
                            {% if error.user %}
                                <strong>{{ error.user.username }}</strong>
                                {% if error.user.first_name or error.user.last_name %}
                                    ({{ error.user.first_name }} {{ error.user.last_name }})
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                        
                        {% if error.ip_address %}
                        <div class="info-label">عنوان IP:</div>
                        <div class="info-value">
                            <code>{{ error.ip_address }}</code>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="mb-3">
                            <i class="fas fa-cogs text-info"></i>
                            معلومات الطلب
                        </h5>
                        
                        {% if error.request_method %}
                        <div class="info-label">طريقة الطلب:</div>
                        <div class="info-value">
                            <span class="badge bg-primary">{{ error.request_method }}</span>
                        </div>
                        {% endif %}
                        
                        {% if error.user_agent %}
                        <div class="info-label">معلومات المتصفح:</div>
                        <div class="info-value">
                            <small class="text-muted">{{ error.user_agent|truncatechars:100 }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="row">
                <div class="col-12">
                    <h5 class="mb-3">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        رسالة الخطأ
                    </h5>
                    <div class="code-block">{{ error.error_message }}</div>
                </div>
            </div>

            <!-- Stack Trace -->
            {% if error.stack_trace %}
            <div class="row mt-4">
                <div class="col-12">
                    <h5 class="mb-3">
                        <i class="fas fa-code text-secondary"></i>
                        تتبع المكدس (Stack Trace)
                    </h5>
                    <div class="code-block">{{ error.stack_trace }}</div>
                </div>
            </div>
            {% endif %}

            <!-- Request Data -->
            {% if error.request_data %}
            <div class="row mt-4">
                <div class="col-12">
                    <h5 class="mb-3">
                        <i class="fas fa-database text-info"></i>
                        بيانات الطلب
                    </h5>
                    <div class="code-block">{{ error.request_data }}</div>
                </div>
            </div>
            {% endif %}

            <!-- Resolution Section -->
            {% if error.status == 'resolved' and error.resolution_notes %}
            <div class="resolution-section">
                <h5 class="mb-3 text-success">
                    <i class="fas fa-check-circle"></i>
                    تم حل الخطأ
                </h5>
                
                <div class="info-label">تم الحل بواسطة:</div>
                <div class="info-value">
                    {% if error.resolved_by %}
                        <strong>{{ error.resolved_by.username }}</strong>
                    {% else %}
                        <span class="text-muted">غير محدد</span>
                    {% endif %}
                </div>
                
                {% if error.resolved_at %}
                <div class="info-label">تاريخ الحل:</div>
                <div class="info-value">
                    {{ error.resolved_at|date:"Y-m-d H:i:s" }}
                </div>
                {% endif %}
                
                <div class="info-label">ملاحظات الحل:</div>
                <div class="info-value">
                    {{ error.resolution_notes }}
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="btn-group">
                        {% if error.status != 'resolved' %}
                        <button class="btn btn-success" onclick="updateStatus('resolved')">
                            <i class="fas fa-check"></i>
                            تم الحل
                        </button>
                        {% endif %}
                        
                        {% if error.status != 'in_progress' %}
                        <button class="btn btn-warning" onclick="updateStatus('in_progress')">
                            <i class="fas fa-clock"></i>
                            قيد المعالجة
                        </button>
                        {% endif %}
                        
                        {% if error.status != 'ignored' %}
                        <button class="btn btn-secondary" onclick="updateStatus('ignored')">
                            <i class="fas fa-times"></i>
                            تجاهل
                        </button>
                        {% endif %}
                        
                        {% if error.status != 'new' %}
                        <button class="btn btn-danger" onclick="updateStatus('new')">
                            <i class="fas fa-exclamation"></i>
                            جديد
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الخطأ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <div class="mb-3">
                        <label for="newStatus" class="form-label">الحالة الجديدة</label>
                        <select id="newStatus" class="form-select" disabled>
                            <option value="new">جديد</option>
                            <option value="in_progress">قيد المعالجة</option>
                            <option value="resolved">تم الحل</option>
                            <option value="ignored">تم التجاهل</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="resolutionNotes" class="form-label">ملاحظات</label>
                        <textarea id="resolutionNotes" class="form-control" rows="3" 
                                  placeholder="اكتب ملاحظات حول تحديث الحالة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveStatusUpdate()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedStatus = null;

function updateStatus(status) {
    selectedStatus = status;
    document.getElementById('newStatus').value = status;
    
    // Set appropriate placeholder based on status
    const textarea = document.getElementById('resolutionNotes');
    switch(status) {
        case 'resolved':
            textarea.placeholder = 'اكتب كيف تم حل هذا الخطأ...';
            break;
        case 'in_progress':
            textarea.placeholder = 'اكتب ما يتم عمله لحل هذا الخطأ...';
            break;
        case 'ignored':
            textarea.placeholder = 'اكتب سبب تجاهل هذا الخطأ...';
            break;
        default:
            textarea.placeholder = 'اكتب ملاحظات حول تحديث الحالة...';
    }
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function saveStatusUpdate() {
    if (!selectedStatus) return;
    
    const notes = document.getElementById('resolutionNotes').value;
    
    fetch(`/system-logs/errors/{{ error.id }}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            status: selectedStatus,
            resolution_notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status badge
            const statusBadge = document.getElementById('currentStatus');
            statusBadge.textContent = data.new_status;
            statusBadge.className = `badge error-status-${selectedStatus} fs-6`;
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusModal'));
            modal.hide();
            
            // Show success message and reload page
            showAlert('success', data.message);
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في تحديث الحالة');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}