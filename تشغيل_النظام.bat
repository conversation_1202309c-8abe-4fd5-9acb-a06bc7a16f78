@echo off
echo ===================================
echo نظام الموارد البشرية - قائمة التشغيل
echo ===================================
echo.
echo الخيارات:
echo 1. تثبيت بايثون من متجر مايكروسوفت
echo 2. تثبيت بايثون من الموقع الرسمي
echo 3. تعطيل اختصارات تنفيذ التطبيقات
echo 4. تشغيل النظام باستخدام المسار الكامل لبايثون
echo 5. تشغيل النظام باستخدام py
echo 6. تشغيل النظام باستخدام CMD
echo 7. عرض دليل المستخدم
echo 8. الخروج
echo.
set /p choice="اختر رقم (1-8): "

if "%choice%"=="1" (
    echo تشغيل ملف تثبيت بايثون من متجر مايكروسوفت...
    call simple_install_python.bat
    goto :menu
) else if "%choice%"=="2" (
    echo تشغيل ملف تثبيت بايثون من الموقع الرسمي...
    call install_python_from_web.bat
    goto :menu
) else if "%choice%"=="3" (
    echo تشغيل ملف تعطيل اختصارات تنفيذ التطبيقات...
    start ms-settings:appsfeatures-app
    echo انتقل إلى "اختصارات تنفيذ التطبيقات" وقم بإيقاف تشغيل python.exe و python3.exe
    pause
    goto :menu
) else if "%choice%"=="4" (
    echo تشغيل النظام باستخدام المسار الكامل لبايثون...
    call run_with_full_path.bat
    goto :menu
) else if "%choice%"=="5" (
    echo تشغيل النظام باستخدام py...
    call run_with_py.bat
    goto :menu
) else if "%choice%"=="6" (
    echo تشغيل النظام باستخدام CMD...
    call run_with_cmd.bat
    goto :menu
) else if "%choice%"=="7" (
    echo عرض دليل المستخدم...
    notepad دليل_المستخدم.txt
    goto :menu
) else if "%choice%"=="8" (
    echo الخروج...
    goto :end
) else (
    echo خيار غير صالح. يرجى المحاولة مرة أخرى.
    pause
    goto :menu
)

:menu
cls
%0

:end
echo.
echo شكرًا لاستخدام نظام الموارد البشرية. اضغط أي مفتاح للخروج...
pause > nul
