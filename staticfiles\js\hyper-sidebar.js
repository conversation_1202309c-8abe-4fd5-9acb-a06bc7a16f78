/**
 * Hyper Sidebar JavaScript
 * Dark Elegant Theme
 */

document.addEventListener('DOMContentLoaded', function() {
    // Toggle sidebar on mobile
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const sidebar = document.getElementById('sidebarMenu');
    const body = document.body;

    if (sidebarToggleBtn && sidebar) {
        sidebarToggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            body.classList.toggle('sidebar-open');
        });

        // Close sidebar when clicking outside
        document.addEventListener('click', function(event) {
            if (window.innerWidth < 992 &&
                !sidebar.contains(event.target) &&
                !sidebarToggleBtn.contains(event.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                body.classList.remove('sidebar-open');
            }
        });
    }

    // Sidebar collapse/expand button
    const sidebarCollapseBtn = document.getElementById('sidebarCollapseBtn');

    if (sidebarCollapseBtn) {
        sidebarCollapseBtn.addEventListener('click', function() {
            body.classList.toggle('sidebar-collapsed');

            // Save state to localStorage
            if (body.classList.contains('sidebar-collapsed')) {
                localStorage.setItem('sidebar-collapsed', 'true');
                sidebarCollapseBtn.querySelector('i').classList.remove('fa-angle-double-right');
                sidebarCollapseBtn.querySelector('i').classList.add('fa-angle-double-left');
            } else {
                localStorage.setItem('sidebar-collapsed', 'false');
                sidebarCollapseBtn.querySelector('i').classList.remove('fa-angle-double-left');
                sidebarCollapseBtn.querySelector('i').classList.add('fa-angle-double-right');
            }
        });

        // Check localStorage for saved state
        if (localStorage.getItem('sidebar-collapsed') === 'true') {
            body.classList.add('sidebar-collapsed');
            sidebarCollapseBtn.querySelector('i').classList.remove('fa-angle-double-right');
            sidebarCollapseBtn.querySelector('i').classList.add('fa-angle-double-left');
        }
    }

    // Submenu toggle
    const submenuToggles = document.querySelectorAll('.sidebar-link[data-bs-toggle="collapse"]');

    submenuToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            if (body.classList.contains('sidebar-collapsed') && window.innerWidth >= 992) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        });
    });

    // Add active class to sidebar links based on current URL
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-link');

    sidebarLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href)) {
            link.classList.add('active');

            // If link is in a submenu, expand the parent
            const parentSubmenu = link.closest('.sidebar-submenu');
            if (parentSubmenu) {
                const parentToggle = document.querySelector(`[data-bs-target="#${parentSubmenu.id}"]`);
                if (parentToggle) {
                    parentToggle.classList.add('active');
                    parentToggle.setAttribute('aria-expanded', 'true');
                    parentSubmenu.classList.add('show');
                }
            }
        }
    });

    // Dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');

    if (darkModeToggle) {
        console.log("Dark mode toggle found");
        // Check for saved theme preference or respect OS preference
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.body.classList.add('dark-mode');
            if (darkModeToggle.querySelector('i')) {
                darkModeToggle.querySelector('i').classList.remove('fa-moon');
                darkModeToggle.querySelector('i').classList.add('fa-sun');
            }
        }

        // Toggle dark mode on click
        darkModeToggle.addEventListener('click', function() {
            console.log("Dark mode toggle clicked");
            document.body.classList.toggle('dark-mode');

            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('theme', 'dark');
                if (darkModeToggle.querySelector('i')) {
                    darkModeToggle.querySelector('i').classList.remove('fa-moon');
                    darkModeToggle.querySelector('i').classList.add('fa-sun');
                }
            } else {
                localStorage.setItem('theme', 'light');
                if (darkModeToggle.querySelector('i')) {
                    darkModeToggle.querySelector('i').classList.remove('fa-sun');
                    darkModeToggle.querySelector('i').classList.add('fa-moon');
                }
            }
        });
    } else {
        console.log("Dark mode toggle not found");
    }

    // Search toggle
    const searchToggle = document.getElementById('searchToggle');
    const searchBox = document.getElementById('searchBox');

    if (searchToggle && searchBox) {
        console.log("Search toggle and box found");
        searchToggle.addEventListener('click', function(e) {
            console.log("Search toggle clicked");
            e.preventDefault();
            searchBox.classList.toggle('show');
            if (searchBox.classList.contains('show')) {
                searchBox.querySelector('input').focus();
            }
        });

        // Close search box when clicking outside
        document.addEventListener('click', function(event) {
            if (!searchToggle.contains(event.target) &&
                !searchBox.contains(event.target) &&
                searchBox.classList.contains('show')) {
                searchBox.classList.remove('show');
            }
        });
    } else {
        console.log("Search toggle or box not found", {searchToggle, searchBox});
    }

    // Notifications dropdown
    const notificationsToggle = document.getElementById('notificationsToggle');

    if (notificationsToggle) {
        // Mark all as read
        const markAllReadBtn = document.getElementById('markAllAsRead');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const badges = document.querySelectorAll('.notification-badge');
                badges.forEach(function(badge) {
                    badge.style.display = 'none';
                });
            });
        }
    }
});
