{% extends 'base.html' %}
{% load static %}

{% block title %}أنواع الرتب - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>أنواع الرتب</h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRankTypeModal">
        <i class="fas fa-plus"></i> إضافة نوع رتبة جديد
    </button>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع أنواع الرتب</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-tag me-1"></i> الاسم</th>
                        <th><i class="fas fa-info-circle me-1"></i> الوصف</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rank_type in rank_types %}
                    <tr class="text-center">
                        <td>{{ rank_type.name }}</td>
                        <td>{{ rank_type.description|default:"-" }}</td>
                        <td>
                            <button class="btn btn-info btn-sm view-rank-type" data-id="{{ rank_type.pk }}" data-name="{{ rank_type.name }}" data-description="{{ rank_type.description|default:'' }}">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-warning btn-sm edit-rank-type" data-id="{{ rank_type.pk }}" data-name="{{ rank_type.name }}" data-description="{{ rank_type.description|default:'' }}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger btn-sm delete-rank-type" data-id="{{ rank_type.pk }}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr class="text-center">
                        <td colspan="3">لا يوجد أنواع رتب</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Rank Type Modal -->
<div class="modal fade" id="addRankTypeModal" tabindex="-1" aria-labelledby="addRankTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRankTypeModalLabel">إضافة نوع رتبة جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_name" class="form-label">الاسم</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_description" class="form-label">الوصف</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Rank Type Modal -->
<div class="modal fade" id="viewRankTypeModal" tabindex="-1" aria-labelledby="viewRankTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewRankTypeModalLabel">عرض نوع الرتبة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">الاسم:</label>
                    <p id="view_name" class="form-control-static"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الوصف:</label>
                    <p id="view_description" class="form-control-static"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Rank Type Modal -->
<div class="modal fade" id="editRankTypeModal" tabindex="-1" aria-labelledby="editRankTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRankTypeModalLabel">تعديل نوع الرتبة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editRankTypeForm" method="post">
                <div class="modal-body">
                    {% csrf_token %}
                    <input type="hidden" id="edit_rank_type_id" name="rank_type_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // View Rank Type
        const viewButtons = document.querySelectorAll('.view-rank-type');
        const viewModal = new bootstrap.Modal(document.getElementById('viewRankTypeModal'));
        const viewNameElement = document.getElementById('view_name');
        const viewDescriptionElement = document.getElementById('view_description');

        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');

                viewNameElement.textContent = name;
                viewDescriptionElement.textContent = description || '-';

                viewModal.show();
            });
        });

        // Edit Rank Type
        const editButtons = document.querySelectorAll('.edit-rank-type');
        const editModal = new bootstrap.Modal(document.getElementById('editRankTypeModal'));
        const editForm = document.getElementById('editRankTypeForm');
        const editIdInput = document.getElementById('edit_rank_type_id');
        const editNameInput = document.getElementById('edit_name');
        const editDescriptionInput = document.getElementById('edit_description');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');

                editIdInput.value = id;
                editNameInput.value = name;
                editDescriptionInput.value = description;

                editForm.action = `/ranks/types/${id}/update/`;
                editModal.show();
            });
        });

        // Delete Rank Type
        const deleteButtons = document.querySelectorAll('.delete-rank-type');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف نوع الرتبة هذا؟')) {
                    window.location.href = `/ranks/types/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}
