from django.urls import resolve
from django.utils.deprecation import MiddlewareMixin
from .views import log_user_action
from .models import SystemLog

class SystemLogMiddleware(MiddlewareMixin):
    """Middleware to log user actions automatically"""

    def process_request(self, request):
        """Process request to log user actions"""
        # Skip logging for static files, admin, and system logs pages
        if any([
            request.path.startswith('/static/'),
            request.path.startswith('/media/'),
            request.path.startswith('/admin/'),
            request.path.startswith('/system-logs/'),
        ]):
            return None

        # Only log for authenticated users
        if not request.user.is_authenticated:
            return None

        # Determine module based on URL
        url_name = resolve(request.path).url_name
        namespace = resolve(request.path).namespace

        # Map namespace to module
        module_mapping = {
            'employees': SystemLog.EMPLOYEES,
            'employment': SystemLog.EMPLOYMENT,
            'leaves': SystemLog.LEAVES,
            'performance': SystemLog.PERFORMANCE,
            'reports': SystemLog.REPORTS,
            'accounts': SystemLog.ACCOUNTS,
            'backup': SystemLog.BACKUP,
            'disciplinary': SystemLog.DISCIPLINARY,
            'file_management': SystemLog.FILES,
            'ranks': SystemLog.RANKS,
        }

        module = module_mapping.get(namespace, SystemLog.SYSTEM)

        # Determine action based on request method
        if request.method == 'GET':
            action = SystemLog.VIEW
        elif request.method == 'POST':
            if 'delete' in url_name:
                action = SystemLog.DELETE
            elif 'create' in url_name or 'add' in url_name:
                action = SystemLog.CREATE
            elif 'update' in url_name or 'edit' in url_name:
                action = SystemLog.UPDATE
            elif 'import' in url_name:
                action = SystemLog.IMPORT
            elif 'export' in url_name:
                action = SystemLog.EXPORT
            elif 'backup' in url_name:
                action = SystemLog.BACKUP
            elif 'restore' in url_name:
                action = SystemLog.RESTORE
            else:
                action = SystemLog.OTHER
        else:
            action = SystemLog.OTHER

        # Log the action with Arabic description
        log_user_action(
            request=request,
            user=request.user,
            module=module,
            action=action,
            page=request.path
            # Don't pass description - let the function generate Arabic description
        )

        return None
