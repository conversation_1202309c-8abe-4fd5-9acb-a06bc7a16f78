{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل الموقف الفني - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تعديل الموقف الفني</h2>
    <a href="{% url 'employment:technical_position_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">تعديل بيانات الموقف الفني</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="id_specialization" class="form-label">التخصص</label>
                        {{ form.specialization }}
                    </div>

                    <div class="mb-3">
                        <label for="id_gender" class="form-label">الجنس</label>
                        {{ form.gender }}
                    </div>

                    <div class="mb-3">
                        <label for="id_vacancies" class="form-label">عدد الشواغر</label>
                        {{ form.vacancies }}
                    </div>

                    <div id="school_departments_container">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle"></i>
                            يرجى اختيار الأقسام المدرسية التي توجد فيها الشواغر
                        </div>
                        <div class="mb-3">
                            <label for="id_school_departments" class="form-label">الأقسام المدرسية <span class="text-danger">*</span></label>
                            {{ form.school_departments }}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="id_notes" class="form-label">ملاحظات (المبرر) <span class="text-danger">*</span></label>
                        {{ form.notes }}
                        <small class="form-text text-muted">يرجى إدخال المبرر من الشاغر (حقل إجباري)</small>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'employment:technical_position_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const vacanciesInput = document.getElementById('id_vacancies');
        const schoolDepartmentsContainer = document.getElementById('school_departments_container');
        const schoolDepartmentsSelect = document.getElementById('id_school_departments');

        // Function to handle vacancies input change
        function handleVacanciesChange() {
            const vacanciesCount = parseInt(vacanciesInput.value) || 0;

            // Enable the required number of options based on vacancies count
            const options = schoolDepartmentsSelect.options;

            // Reset selection if vacancies count changes
            if (schoolDepartmentsSelect.selectedOptions.length > vacanciesCount) {
                for (let i = 0; i < options.length; i++) {
                    options[i].selected = false;
                }
            }

            // Update the message
            const infoMessage = schoolDepartmentsContainer.querySelector('.alert');
            infoMessage.innerHTML = `<i class="fas fa-info-circle"></i> يرجى اختيار الأقسام المدرسية التي توجد فيها الشواغر (يمكن تكرار نفس القسم)`;

            // Make the field required
            schoolDepartmentsSelect.setAttribute('required', 'required');
            schoolDepartmentsSelect.setAttribute('size', Math.min(10, options.length));

            // تم إزالة التحقق من عدد الأقسام المختارة للسماح بتكرار القسم
        }

        // Add event listener for vacancies input
        if (vacanciesInput) {
            vacanciesInput.addEventListener('input', handleVacanciesChange);
            // Initial check
            handleVacanciesChange();
        }
    });
</script>
{% endblock %}