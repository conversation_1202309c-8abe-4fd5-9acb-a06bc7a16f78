/* Service Purchase Styles */
.service-purchase-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Employee Search Styles */
.employee-search-card {
    border: 2px solid #17a2b8;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.2);
}

.employee-search-card .card-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-radius: 13px 13px 0 0;
}

.employee-details-display {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 1.5rem;
}

.employee-details-display h6 {
    color: #155724;
    margin-bottom: 1rem;
}

.employee-details-display p {
    margin-bottom: 0.5rem;
    color: #155724;
}

.employee-details-display strong {
    color: #0c5460;
}

.search-button {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.select-employee-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.select-employee-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.service-purchase-card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.3s ease;
}

.service-purchase-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.service-purchase-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.service-purchase-stats {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.service-purchase-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.service-purchase-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.service-purchase-table tbody tr {
    transition: all 0.3s ease;
}

.service-purchase-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.service-purchase-table tbody td {
    color: #212529 !important;
}

.service-purchase-table tbody td .text-dark {
    color: #212529 !important;
    font-weight: 500;
}

.service-purchase-table tbody td .badge {
    color: white !important;
}

/* Ensure all text in table is visible and dark - Higher specificity */
.service-purchase-card .table tbody td,
.service-purchase-card .table tbody td span:not(.badge),
.service-purchase-card .table tbody td strong,
.service-purchase-card .table tbody td .text-dark {
    color: #212529 !important;
    font-weight: 500 !important;
}

.service-purchase-card .table tbody td .text-muted {
    color: #6c757d !important;
}

.service-purchase-card .table tbody td .fw-bold {
    font-weight: 600 !important;
}

/* Badge colors remain unchanged */
.service-purchase-card .table tbody td .badge {
    color: white !important;
    font-weight: 500 !important;
}

/* Force dark text for all non-badge elements */
.table tbody td span:not(.badge) {
    color: #212529 !important;
}

.table tbody td strong {
    color: #212529 !important;
}

/* Additional text visibility improvements */
.table-dark th {
    color: white !important;
}

.service-purchase-card .card-body {
    color: #212529;
}

.service-purchase-card .table td {
    vertical-align: middle;
    padding: 0.75rem;
}

/* Improve button visibility */
.btn-action {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* Stats card text visibility */
.stats-card h3,
.stats-card p {
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.service-purchase-badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.85rem;
}

.service-purchase-btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.service-purchase-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.service-purchase-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.service-purchase-form .form-control,
.service-purchase-form .form-select {
    border-radius: 10px;
    border: 2px solid #e3e6f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.service-purchase-form .form-control:focus,
.service-purchase-form .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.service-purchase-alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
}

.service-purchase-alert.alert-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

.service-purchase-alert.alert-success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.service-purchase-alert.alert-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.service-purchase-alert.alert-danger {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

/* Animation for new entries */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-purchase-new-entry {
    animation: slideInUp 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-purchase-container {
        padding: 1rem 0;
    }
    
    .service-purchase-stats {
        padding: 1.5rem;
    }
    
    .service-purchase-form {
        padding: 1.5rem;
    }
    
    .service-purchase-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Print styles */
@media print {
    .service-purchase-container {
        background: white;
    }
    
    .service-purchase-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .service-purchase-btn,
    .service-purchase-header .btn {
        display: none;
    }
}