# Data migration to populate initial important links

from django.db import migrations


def create_initial_links(apps, schema_editor):
    ImportantLink = apps.get_model('home', 'ImportantLink')
    
    initial_links = [
        {
            'name': 'وزارة التربية والتعليم',
            'url': 'https://moe.gov.jo/',
            'description': 'الموقع الرسمي لوزارة التربية والتعليم الأردنية',
            'favicon_url': 'https://moe.gov.jo/favicon.ico',
            'order': 1,
            'is_active': True
        },
        {
            'name': 'منصة الموظفين',
            'url': 'https://emp.moe.gov.jo/public/P01_Home/',
            'description': 'منصة إدارة شؤون الموظفين في وزارة التربية والتعليم',
            'favicon_url': 'https://emp.moe.gov.jo/favicon.ico',
            'order': 2,
            'is_active': True
        },
        {
            'name': 'منصة تدريب المعلمين',
            'url': 'https://teachers.gov.jo/',
            'description': 'منصة التدريب والتطوير المهني للمعلمين',
            'favicon_url': 'https://teachers.gov.jo/favicon.ico',
            'order': 3,
            'is_active': True
        },
        {
            'name': 'OpenEmis',
            'url': 'https://emis.moe.gov.jo/openemis-core/',
            'description': 'نظام ادارة المعلومات التربوية الاردن',
            'favicon_url': 'https://emis.moe.gov.jo/favicon.ico',
            'order': 4,
            'is_active': True
        },
        {
            'name': 'BtecEmis JO',
            'url': 'https://apps.moe.gov.jo/btec/btecemis/public/Home/',
            'description': 'نظام إدارة معلومات التعليم المهني والتقني',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico',
            'order': 5,
            'is_active': True
        },
        {
            'name': 'التعليم الإضافي',
            'url': 'https://apps.moe.gov.jo/apps/subteachers/',
            'description': 'منصة إدارة التعليم الإضافي للمعلمين',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico',
            'order': 6,
            'is_active': True
        },
        {
            'name': 'الوصف الوظيفي',
            'url': 'https://apps.moe.gov.jo/files/actcards/',
            'description': 'بطاقات الوصف الوظيفي للوظائف التعليمية والإدارية',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico',
            'order': 7,
            'is_active': True
        },
        {
            'name': 'نماذج تقييم الأداء السنوي',
            'url': 'https://apps.moe.gov.jo/files/actcards/%D9%86%D9%85%D8%A7%D8%B0%D8%AC%20%D8%AA%D9%82%D9%8A%D9%8A%D9%85%20%20%D8%A7%D9%84%D8%A7%D8%AF%D8%A7%D8%A1%20%D8%A7%D9%84%D9%85%D8%B9%D8%AA%D9%85%D8%AF%D8%A9%20%20%D9%84%D9%84%D8%B9%D8%A7%D9%85%202025/',
            'description': 'نماذج التقييم السنوي المعتمدة',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico',
            'order': 8,
            'is_active': True
        },
    ]
    
    for link_data in initial_links:
        ImportantLink.objects.create(**link_data)


def reverse_create_initial_links(apps, schema_editor):
    ImportantLink = apps.get_model('home', 'ImportantLink')
    ImportantLink.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0007_importantlink'),
    ]

    operations = [
        migrations.RunPython(create_initial_links, reverse_create_initial_links),
    ]
