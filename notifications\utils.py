from .models import Notification
from django.contrib.auth import get_user_model

User = get_user_model()

def create_notification(title, message, notification_type='info', icon='fa-bell', user=None, is_global=False):
    """
    Utility function to create notifications easily
    """
    return Notification.create_notification(
        title=title,
        message=message,
        notification_type=notification_type,
        icon=icon,
        user=user,
        is_global=is_global
    )

def notify_user(user, title, message, notification_type='info', icon='fa-bell'):
    """
    Send notification to a specific user
    """
    return create_notification(
        title=title,
        message=message,
        notification_type=notification_type,
        icon=icon,
        user=user
    )

def notify_all_users(title, message, notification_type='info', icon='fa-bell'):
    """
    Send notification to all users
    """
    return create_notification(
        title=title,
        message=message,
        notification_type=notification_type,
        icon=icon,
        is_global=True
    )

def notify_admins(title, message, notification_type='info', icon='fa-bell'):
    """
    Send notification to all admin users
    """
    admin_users = User.objects.filter(is_staff=True)
    notifications = []
    for admin in admin_users:
        notification = create_notification(
            title=title,
            message=message,
            notification_type=notification_type,
            icon=icon,
            user=admin
        )
        notifications.append(notification)
    return notifications

# Predefined notification templates
def notify_employee_added(employee, created_by=None):
    """Notify when a new employee is added"""
    title = "موظف جديد"
    message = f"تم إضافة الموظف {employee.full_name} إلى النظام"
    
    if created_by:
        notify_user(created_by, title, message, 'success', 'fa-user-plus')
    
    # Notify all admins
    notify_admins(title, message, 'info', 'fa-user-plus')

def notify_leave_request(leave, user=None):
    """Notify when a leave request is submitted"""
    title = "طلب إجازة جديد"
    message = f"تم تقديم طلب إجازة من {leave.employee.full_name} من {leave.start_date} إلى {leave.end_date}"
    
    # Notify admins
    notify_admins(title, message, 'warning', 'fa-calendar')

def notify_leave_approved(leave):
    """Notify when a leave request is approved"""
    title = "تمت الموافقة على الإجازة"
    message = f"تمت الموافقة على طلب الإجازة من {leave.start_date} إلى {leave.end_date}"
    
    # You would need to get the employee's user account to send this
    # This is just an example structure

def notify_system_update(version=None):
    """Notify about system updates"""
    title = "تحديث النظام"
    message = f"تم تحديث النظام بنجاح"
    if version:
        message += f" إلى الإصدار {version}"
    
    notify_all_users(title, message, 'success', 'fa-cog')

def notify_backup_completed():
    """Notify when backup is completed"""
    title = "اكتملت النسخة الاحتياطية"
    message = "تم إنشاء النسخة الاحتياطية بنجاح"
    
    notify_admins(title, message, 'success', 'fa-database')

def notify_error(error_message, user=None):
    """Notify about system errors"""
    title = "خطأ في النظام"
    message = f"حدث خطأ: {error_message}"
    
    if user:
        notify_user(user, title, message, 'error', 'fa-exclamation-triangle')
    else:
        notify_admins(title, message, 'error', 'fa-exclamation-triangle')
