{% extends 'base.html' %}

{% block title %}إضافة إجازة أمومة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">إضافة إجازة أمومة جديدة</h6>
                    <a href="{% url 'employees:maternity_leaves_list' %}" class="btn btn-dark text-white">
                        <i class="fas fa-arrow-right me-1"></i> العودة لقائمة الإجازات
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>معلومات مهمة</strong>
                        </div>
                        <ul class="mb-0">
                            <li>مدة إجازة الأمومة 90 يوماً</li>
                            <li>يتم احتساب الإجازة من اليوم التالي لتاريخ البداية</li>
                            <li>يمكن إضافة إجازة أمومة للموظفات الإناث فقط</li>
                            <li>لا يمكن إضافة إجازة أمومة لموظفة لديها إجازة نشطة</li>
                        </ul>
                    </div>

                    <form method="post" id="maternityLeaveForm">
                        {% csrf_token %}
                        
                        <!-- Employee Search Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-search me-2"></i>البحث عن الموظفة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="employee_search_input_maternity" class="form-label">البحث عن الموظفة</label>
                                            <input type="text" class="form-control" id="employee_search_input_maternity" 
                                                   placeholder="اكتب الرقم الوزاري أو اسم الموظفة للبحث...">
                                            <div class="form-text">ابحث بالرقم الوزاري أو الاسم</div>
                                        </div>
                                        
                                        <!-- Search Results -->
                                        <div id="search_results_maternity" class="d-none">
                                            <h6 class="text-primary">نتائج البحث:</h6>
                                            <div id="results_container_maternity" class="list-group mb-3">
                                                <!-- Results will be populated here -->
                                            </div>
                                        </div>
                                        
                                        <!-- Selected Employee Display -->
                                        <div class="mb-3">
                                            <label for="selected_employee_display_maternity" class="form-label">الموظفة المحددة</label>
                                            <input type="text" class="form-control" id="selected_employee_display_maternity" 
                                                   readonly placeholder="لم يتم اختيار موظفة بعد">
                                        </div>
                                        
                                        <!-- Hidden field for employee ID -->
                                        <input type="hidden" id="employee_id_maternity" name="employee_id" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Employee Details Display -->
                        <div id="employee_details_maternity" class="row mb-4 d-none">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>تفاصيل الموظفة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>الرقم الوزاري:</strong> <span id="display_ministry_number"></span></p>
                                                <p><strong>الاسم الكامل:</strong> <span id="display_full_name"></span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>التخصص:</strong> <span id="display_specialization"></span></p>
                                                <p><strong>القسم:</strong> <span id="display_school"></span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maternity Leave Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ بداية الإجازة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                    <div class="form-text">سيتم احتساب الإجازة من اليوم التالي</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date_display" class="form-label">تاريخ انتهاء الإجازة (تلقائي)</label>
                                    <input type="text" class="form-control" id="end_date_display" readonly 
                                           placeholder="سيتم حسابه تلقائياً">
                                    <div class="form-text">يتم حساب تاريخ الانتهاء تلقائياً (90 يوم)</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save me-1"></i> حفظ إجازة الأمومة
                                </button>
                                <a href="{% url 'employees:maternity_leaves_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let searchTimeout;
    
    // Employee search functionality
    $('#employee_search_input_maternity').on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                searchEmployees(query);
            }, 300);
        } else {
            $('#search_results_maternity').addClass('d-none');
        }
    });
    
    function searchEmployees(query) {
        $.ajax({
            url: '{% url "employees:search_employee_for_maternity" %}',
            data: { 'q': query },
            dataType: 'json',
            success: function(data) {
                displaySearchResults(data.results);
            },
            error: function() {
                console.error('Error searching for employees');
            }
        });
    }
    
    function displaySearchResults(results) {
        const container = $('#results_container_maternity');
        container.empty();
        
        if (results.length > 0) {
            results.forEach(function(employee) {
                const resultItem = $(`
                    <div class="list-group-item list-group-item-action employee-result" 
                         data-employee-id="${employee.id}"
                         data-ministry-number="${employee.ministry_number}"
                         data-full-name="${employee.full_name}"
                         data-specialization="${employee.specialization}"
                         data-school="${employee.school}">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${employee.full_name}</h6>
                            <small class="text-muted">${employee.ministry_number}</small>
                        </div>
                        <p class="mb-1"><strong>التخصص:</strong> ${employee.specialization}</p>
                        <small><strong>القسم:</strong> ${employee.school}</small>
                    </div>
                `);
                container.append(resultItem);
            });
            $('#search_results_maternity').removeClass('d-none');
        } else {
            container.append('<div class="list-group-item">لم يتم العثور على موظفات</div>');
            $('#search_results_maternity').removeClass('d-none');
        }
    }
    
    // Handle employee selection
    $(document).on('click', '.employee-result', function() {
        const employeeId = $(this).data('employee-id');
        const ministryNumber = $(this).data('ministry-number');
        const fullName = $(this).data('full-name');
        const specialization = $(this).data('specialization');
        const school = $(this).data('school');
        
        // Set form values
        $('#employee_id_maternity').val(employeeId);
        $('#selected_employee_display_maternity').val(`${fullName} (${ministryNumber})`);
        
        // Display employee details
        $('#display_ministry_number').text(ministryNumber);
        $('#display_full_name').text(fullName);
        $('#display_specialization').text(specialization);
        $('#display_school').text(school);
        
        // Show employee details and hide search results
        $('#employee_details_maternity').removeClass('d-none');
        $('#search_results_maternity').addClass('d-none');
        $('#employee_search_input_maternity').val('');
    });
    
    // Calculate end date when start date changes
    $('#start_date').on('change', function() {
        const startDate = new Date($(this).val());
        if (startDate) {
            // Add 90 days to start date
            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 90);
            
            // Format date as YYYY-MM-DD
            const formattedEndDate = endDate.toISOString().split('T')[0];
            $('#end_date_display').val(formattedEndDate);
        }
    });
    
    // Form validation
    $('#maternityLeaveForm').on('submit', function(e) {
        const employeeId = $('#employee_id_maternity').val();
        const startDate = $('#start_date').val();
        
        if (!employeeId) {
            e.preventDefault();
            alert('يرجى اختيار موظفة لإجازة الأمومة');
            return false;
        }
        
        if (!startDate) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ بداية الإجازة');
            return false;
        }
    });
});
</script>
{% endblock %}