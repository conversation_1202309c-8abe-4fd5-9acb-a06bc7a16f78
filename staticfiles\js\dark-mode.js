/**
 * Dark Mode Toggle Functionality
 * This script handles the dark mode toggle functionality
 */

// تنفيذ الكود عند اكتمال تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    initDarkModeToggle();
});

/**
 * تهيئة زر التبديل بين الوضع المظلم والنهاري
 */
function initDarkModeToggle() {
    console.log('Initializing dark mode toggle...');

    // الحصول على زر التبديل
    const darkModeToggle = document.getElementById('darkModeToggle');

    // التحقق من وجود الزر
    if (!darkModeToggle) {
        console.error('Dark mode toggle button not found!');
        // محاولة مرة أخرى بعد فترة قصيرة
        setTimeout(initDarkModeToggle, 500);
        return;
    }

    console.log('Dark mode toggle button found');

    // الحصول على عناصر DOM
    const body = document.body;
    const icon = darkModeToggle.querySelector('i');

    // التحقق من وجود الأيقونة
    if (!icon) {
        console.error('Icon element not found inside dark mode toggle button!');
        return;
    }

    // تحديد الوضع الافتراضي بناءً على تفضيلات المستخدم في نظام التشغيل
    const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

    // التحقق من وجود تفضيل محفوظ
    const savedTheme = localStorage.getItem('theme');
    console.log('Saved theme:', savedTheme);

    // تطبيق الوضع المناسب
    if (savedTheme === 'dark' || (savedTheme === null && prefersDarkMode)) {
        applyDarkMode();
    } else {
        applyLightMode();
    }

    // متغير لحفظ الأنماط الأصلية
    let originalStyles = {};

    // وظيفة حفظ الأنماط الأصلية
    function saveOriginalStyles() {
        console.log('Saving original styles before applying dark mode');

        // حفظ أنماط الشريط العلوي
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            originalStyles.navbar = {
                backgroundColor: getComputedStyle(navbar).backgroundColor,
                borderBottom: getComputedStyle(navbar).borderBottom
            };
        }

        // حفظ أنماط القائمة الجانبية
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            originalStyles.sidebar = {
                background: getComputedStyle(sidebar).background,
                borderRight: getComputedStyle(sidebar).borderRight
            };
        }

        // حفظ متغيرات CSS الأصلية
        originalStyles.cssVars = {
            primaryColor: getComputedStyle(document.documentElement).getPropertyValue('--primary-color'),
            secondaryColor: getComputedStyle(document.documentElement).getPropertyValue('--secondary-color'),
            textColor: getComputedStyle(document.documentElement).getPropertyValue('--text-color')
        };

        console.log('Original styles saved:', originalStyles);
    }

    // وظيفة تطبيق الوضع المظلم
    function applyDarkMode() {
        console.log('Applying dark mode');

        // حفظ الأنماط الأصلية قبل تطبيق الوضع المظلم
        saveOriginalStyles();

        // إضافة فئة dark-mode
        body.classList.add('dark-mode');

        // تحديث الأيقونة
        icon.className = '';
        icon.classList.add('fas', 'fa-sun');

        // حفظ التفضيل
        localStorage.setItem('theme', 'dark');

        // تحديث متغيرات CSS
        document.documentElement.style.setProperty('--primary-color', '#1e1e1e');
        document.documentElement.style.setProperty('--secondary-color', '#2d2d2d');
        document.documentElement.style.setProperty('--text-color', '#e0e0e0');
    }

    // وظيفة تطبيق الوضع النهاري
    function applyLightMode() {
        console.log('Applying light mode - returning to original state');

        // إزالة فئة dark-mode فقط
        body.classList.remove('dark-mode');

        // تحديث الأيقونة
        icon.className = '';
        icon.classList.add('fas', 'fa-moon');

        // حفظ التفضيل
        localStorage.setItem('theme', 'light');

        // إزالة أي متغيرات CSS تم تعيينها بواسطة JavaScript
        document.documentElement.removeAttribute('style');

        // إعادة تعيين ألوان الشريط العلوي والقائمة الجانبية إلى الألوان الأصلية
        resetOriginalStyles();
    }

    // وظيفة إعادة تعيين الأنماط الأصلية
    function resetOriginalStyles() {
        console.log('Resetting to original styles');

        // إزالة جميع الأنماط المضافة بواسطة JavaScript
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.hasAttribute('style')) {
                element.removeAttribute('style');
            }
        });

        // إعادة تعيين متغيرات CSS إلى قيمها الأصلية
        if (originalStyles.cssVars) {
            if (originalStyles.cssVars.primaryColor) {
                document.documentElement.style.setProperty('--primary-color', originalStyles.cssVars.primaryColor);
            }
            if (originalStyles.cssVars.secondaryColor) {
                document.documentElement.style.setProperty('--secondary-color', originalStyles.cssVars.secondaryColor);
            }
            if (originalStyles.cssVars.textColor) {
                document.documentElement.style.setProperty('--text-color', originalStyles.cssVars.textColor);
            }
        }

        // إعادة تعيين الشريط العلوي إلى الأنماط الأصلية
        const navbar = document.querySelector('.navbar');
        if (navbar && originalStyles.navbar) {
            // استخدام !important لضمان تطبيق الأنماط الأصلية
            navbar.setAttribute('style', `
                background-color: ${originalStyles.navbar.backgroundColor} !important;
                border-bottom: ${originalStyles.navbar.borderBottom} !important;
            `);
        }

        // إعادة تعيين القائمة الجانبية إلى الأنماط الأصلية
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && originalStyles.sidebar) {
            // استخدام !important لضمان تطبيق الأنماط الأصلية
            sidebar.setAttribute('style', `
                background: ${originalStyles.sidebar.background} !important;
                border-right: ${originalStyles.sidebar.borderRight} !important;
            `);
        }

        // إعادة تطبيق الأنماط الأصلية من ملفات CSS
        const originalStylesheet = document.createElement('link');
        originalStylesheet.rel = 'stylesheet';
        originalStylesheet.href = '/static/css/black-white-theme.css?v=' + new Date().getTime();
        document.head.appendChild(originalStylesheet);

        // إزالة الملف بعد تحميله لتجنب التعارض
        originalStylesheet.onload = function() {
            setTimeout(() => {
                document.head.removeChild(originalStylesheet);
            }, 100);
        };

        console.log('Original styles have been reset');
    }

    // إضافة مستمع حدث للنقر على زر التبديل
    darkModeToggle.addEventListener('click', function(event) {
        console.log('Dark mode toggle clicked');

        // منع السلوك الافتراضي للزر
        event.preventDefault();

        // التبديل بين الوضعين
        if (body.classList.contains('dark-mode')) {
            applyLightMode();
        } else {
            applyDarkMode();
        }
    });

    // الاستماع لتغييرات في تفضيلات نظام التشغيل
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            // تطبيق الوضع الجديد فقط إذا لم يكن المستخدم قد اختار تفضيلًا
            if (!localStorage.getItem('theme')) {
                if (event.matches) {
                    applyDarkMode();
                } else {
                    applyLightMode();
                }
            }
        });
    }

    console.log('Dark mode toggle initialized successfully');
}
