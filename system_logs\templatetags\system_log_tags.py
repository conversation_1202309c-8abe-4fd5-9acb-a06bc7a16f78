from django import template
from django.utils import timezone
import re

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary by key"""
    if isinstance(key, dict) and 'value' in key:
        key = key['value']
    return dictionary.get(key, [])

@register.filter
def localize_datetime(dt):
    """Convert datetime to Jordan timezone (Asia/Amman)"""
    if not dt:
        return ""

    # Use Jordan timezone (Asia/Amman) from Django settings
    jordan_timezone = timezone.get_current_timezone()  # This will use TIME_ZONE from settings.py

    # Make sure the datetime is timezone-aware
    if timezone.is_naive(dt):
        dt = timezone.make_aware(dt, timezone.utc)

    # Convert to Jordan timezone
    local_dt = dt.astimezone(jordan_timezone)

    # We're now using numeric month format instead of Arabic month names

    # Format date components
    year = local_dt.year
    month = local_dt.month  # Use month number instead of name
    day = local_dt.day
    hour = local_dt.hour
    minute = local_dt.minute
    second = local_dt.second

    # Format AM/PM in Arabic
    am_pm = 'صباحاً' if hour < 12 else 'مساءً'

    # Convert to 12-hour format
    hour_12 = hour % 12
    if hour_12 == 0:
        hour_12 = 12

    # Format the date and time in Arabic style with month as a number
    formatted_date = f"{day}/{month:02d}/{year}"
    formatted_time = f"{hour_12}:{minute:02d}:{second:02d} {am_pm}"

    # Combine date and time with HTML formatting for better display
    formatted_datetime = f'<span class="text-primary">{formatted_date}</span> <span class="text-muted">{formatted_time}</span>'

    return formatted_datetime

@register.filter
def split(value, delimiter):
    """Split a string by delimiter and return the list of parts"""
    if not value:
        return []
    return value.split(delimiter)
