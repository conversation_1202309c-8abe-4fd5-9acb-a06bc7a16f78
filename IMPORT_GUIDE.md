# دليل استيراد بيانات الموظفين - محسن

## التحسينات المطبقة

### 🔧 إصلاح المشاكل الرئيسية
- **إصلاح خطأ التواريخ المطلوبة**: تم حل مشكلة `NOT NULL constraint failed: employees_employee.hire_date`
- **معالجة محسنة للتواريخ**: دعم لتنسيقات متعددة للتواريخ
- **التحقق من الحقول المطلوبة**: فحص شامل للبيانات قبل الإدراج
- **معالجة أسماء الأعمدة**: دعم لأسماء الأعمدة بتشفيرات مختلفة

### ✨ المميزات الجديدة
- **رسائل خطأ مفصلة**: تجميع الأخطاء حسب النوع مع إرشادات الحل
- **تتبع التقدم**: عرض تقدم المعالجة في وحدة التحكم
- **تنظيف البيانات**: إزالة الصفوف الفارغة تلقائياً
- **تحليل الملف**: فحص الملف وعرض الإحصائيات قبل المعالجة

## الحقول المطلوبة (لا يمكن أن تكون فارغة)

1. **الرقم الوزاري** - مطلوب ووحيد
2. **الاسم الكامل** - مطلوب
3. **تاريخ التعيين** - مطلوب (تنسيق: YYYY-MM-DD أو DD/MM/YYYY)
4. **تاريخ الميلاد** - مطلوب (تنسيق: YYYY-MM-DD أو DD/MM/YYYY)
5. **القسم** - مطلوب

## الحقول الاختيارية (ستأخذ قيم افتراضية إذا كانت فارغة)

- **الرقم الوطني** - اختياري لكن يجب أن يكون وحيد إذا أُدخل
- **المؤهل العلمي** - افتراضي: "غير محدد"
- **التخصص** - افتراضي: "غير محدد"
- **العنوان** - افتراضي: "غير محدد"
- **رقم الهاتف** - اختياري
- **الجنس** - افتراضي: "ذكر" (القيم المقبولة: "ذكر" أو "انثى")

## تنسيقات التواريخ المدعومة

- `YYYY-MM-DD` (مثل: 2020-01-15)
- `DD/MM/YYYY` (مثل: 15/01/2020)
- `MM/DD/YYYY` (مثل: 01/15/2020)
- `YYYY/MM/DD` (مثل: 2020/01/15)
- `DD-MM-YYYY` (مثل: 15-01-2020)

## كيفية الاستخدام

### 1. تحضير الملف
- حمّل قالب الاستيراد من صفحة الاستيراد
- تأكد من وجود جميع الأعمدة المطلوبة
- تأكد من صحة تنسيق التواريخ
- تأكد من عدم تكرار الأرقام الوزارية والوطنية

### 2. رفع الملف
- اذهب إلى صفحة "استيراد وتصدير بيانات الموظفين"
- اختر ملف Excel
- اضغط "استيراد البيانات"

### 3. مراجعة النتائج
- ستظهر رسالة نجاح للسجلات المستوردة
- ستظهر رسالة تحذير مع تفاصيل السجلات المتخطاة
- راجع الأخطاء وأصلحها في الملف إذا لزم الأمر

## أمثلة على البيانات الصحيحة

| الرقم الوزاري | الرقم الوطني | الاسم الكامل | المؤهل العلمي | التخصص | تاريخ التعيين | القسم | تاريخ الميلاد | العنوان | رقم الهاتف | الجنس |
|-------------|-------------|------------|-------------|---------|-------------|--------|-------------|---------|------------|-------|
| EMP001 | 1234567890 | محمد أحمد علي | بكالوريوس | علوم حاسوب | 2020-01-15 | قسم تكنولوجيا المعلومات | 1990-05-10 | عمان - الأردن | 0777123456 | ذكر |
| EMP002 | 0987654321 | فاطمة سعد محمد | ماجستير | إدارة أعمال | 2019-05-20 | قسم الإدارة | 1985-12-20 | إربد - الأردن | 0799876543 | انثى |

## رسائل الخطأ الشائعة وحلولها

### `تاريخ التعيين مطلوب`
- **السبب**: الخلية فارغة أو تحتوي على قيمة غير صالحة
- **الحل**: تأكد من إدخال تاريخ صحيح بالتنسيق المطلوب

### `الرقم الوزاري موجود بالفعل`
- **السبب**: الرقم الوزاري مُستخدم من قبل موظف آخر
- **الحل**: استخدم رقم وزاري فريد

### `الرقم الوطني موجود بالفعل`
- **السبب**: الرقم الوطني مُستخدم من قبل موظف آخر
- **الحل**: تأكد من صحة الرقم الوطني أو اتركه فارغاً

### `بيانات ناقصة`
- **السبب**: واحد أو أكثر من الحقول المطلوبة فارغ
- **الحل**: تأكد من تعبئة جميع الحقول المطلوبة

## ملاحظات إضافية

- الملف يجب أن يكون بصيغة Excel (.xlsx أو .xls)
- أسماء الأعمدة يجب أن تطابق القالب المحدد
- سيتم تخطي الصفوف الفارغة تلقائياً
- النظام يدعم معالجة آلاف السجلات في ملف واحد
- يمكن مراجعة تقدم العملية في وحدة التحكم

## الدعم الفني

إذا واجهت مشاكل في الاستيراد:
1. تأكد من اتباع التنسيق المحدد
2. راجع رسائل الخطأ المفصلة
3. جرب ملف اختبار صغير أولاً
4. تواصل مع الدعم الفني مع إرفاق تفاصيل الخطأ