{% extends 'base.html' %}
{% load static %}

{% block title %}رتب الموظفين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق بسيط للفلاتر */
    .filters-container {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تحسين مظهر الأزرار */
    .btn-filter {
        padding: 8px 12px;
        height: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>رتب الموظفين</h2>
    <div>
        <a href="{% url 'ranks:employee_rank_import' %}" class="btn btn-success">
            <i class="fas fa-file-import"></i> استيراد من Excel
        </a>
        <a href="{% url 'ranks:employee_rank_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة رتبة جديدة للموظف
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}?export=excel{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}{% if selected_rank_type %}&rank_type={{ selected_rank_type }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-export"></i> تصدير إلى Excel
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}?export=pdf_preview{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}{% if selected_rank_type %}&rank_type={{ selected_rank_type }}{% endif %}" class="btn btn-danger">
            <i class="fas fa-file-pdf"></i> معاينة PDF
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="d-flex align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">جميع رتب الموظفين</h6>
                <span class="badge bg-primary ms-2 fs-6 p-2">عدد الموظفين: {{ employee_count }}</span>
            </div>
        </div>

        <div class="filters-container" style="width: 50%; margin-right: auto; margin-left: 0;">
            <div class="row g-2">
                <!-- شريط البحث -->
                <div class="col-md-3 mb-2">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="بحث..." value="{{ search_query|default:'' }}">
                        <button class="btn btn-secondary" type="button" onclick="applyFilters()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- فلتر السنة -->
                <div class="col-md-3 mb-2">
                    <select class="form-control form-select" id="yearFilter" onchange="applyFilters()">
                        <option value="">جميع السنوات</option>
                        {% for year in years %}
                        <option value="{{ year }}" {% if selected_year == year|stringformat:"i" %}selected{% endif %}>{{ year }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- فلتر نوع الرتبة -->
                <div class="col-md-3 mb-2">
                    <select class="form-control form-select" id="rankTypeFilter" onchange="applyFilters()">
                        <option value="">جميع الرتب</option>
                        {% for rank_type in rank_types %}
                        <option value="{{ rank_type.id }}" {% if selected_rank_type == rank_type.id|stringformat:"i" %}selected{% endif %}>{{ rank_type.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- زر إعادة ضبط الفلاتر -->
                <div class="col-md-3 mb-2">
                    <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary w-100">
                        <i class="fas fa-redo"></i> إعادة الضبط
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                        <th><i class="fas fa-tag me-1"></i> نوع الرتبة</th>
                        <th><i class="fas fa-calendar-alt me-1"></i> تاريخ الحصول عليها</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee_rank in employee_ranks %}
                    <tr class="text-center">
                        <td>{{ employee_rank.employee.ministry_number }}</td>
                        <td>
                            <a href="{% url 'employees:employee_detail' employee_rank.employee.pk %}">
                                {{ employee_rank.employee.full_name }}
                            </a>
                        </td>
                        <td>{{ employee_rank.rank_type.name }}</td>
                        <td>{{ employee_rank.date_obtained|date:"Y-m-d" }}</td>
                        <td>
                            <a href="{% url 'ranks:employee_rank_detail' employee_rank.pk %}" class="btn btn-info btn-sm" style="width: 80px;">
                                <i class="fas fa-eye"></i> معاينة
                            </a>
                            <a href="{% url 'ranks:employee_rank_update' employee_rank.pk %}" class="btn btn-warning btn-sm" style="width: 80px;">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <button class="btn btn-danger btn-sm delete-rank" data-id="{{ employee_rank.pk }}" style="width: 80px;">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr class="text-center">
                        <td colspan="5">لا يوجد رتب للموظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete Rank
        const deleteButtons = document.querySelectorAll('.delete-rank');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذه الرتبة؟')) {
                    window.location.href = `/ranks/${id}/delete/`;
                }
            });
        });

        // Apply filters function
        window.applyFilters = function() {
            const year = $('#yearFilter').val();
            const rankType = $('#rankTypeFilter').val();
            const search = $('#searchInput').val();

            let url = '?';
            if (year) url += `year=${year}&`;
            if (rankType) url += `rank_type=${rankType}&`;
            if (search) url += `search=${search}&`;

            // Remove trailing &
            if (url.endsWith('&')) {
                url = url.slice(0, -1);
            }

            // If only ? is left, remove it
            if (url === '?') {
                url = '';
            }

            window.location.href = url;
        };

        // Add event listener for search input
        $('#searchInput').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                applyFilters();
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
