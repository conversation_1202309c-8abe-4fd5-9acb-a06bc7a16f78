{% extends 'base.html' %}
{% load static %}
{% load system_log_tags %}

{% block title %}سجل حركات النظام - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .description-cell {
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #f8f9fa;
        padding: 10px 15px !important;
        border-radius: 4px;
    }

    .table td {
        vertical-align: middle;
        white-space: normal;
        word-wrap: break-word;
    }

    .table th {
        text-align: center;
        background-color: #f1f5f9;
    }

    /* تحسين عرض الجدول */
    .table-responsive {
        overflow-x: auto;
        min-height: 300px;
    }

    /* تأكيد أن الجدول يأخذ العرض الكامل */
    .table {
        width: 100% !important;
        table-layout: fixed;
    }

    /* تنسيق الأسماء في الوصف */
    .description-cell strong {
        color: #0d6efd;
        font-weight: bold;
    }

    /* تنسيق الأقواس في الوصف */
    .description-cell span.object-name {
        color: #198754;
        font-weight: 500;
    }

    /* تحسين مظهر الخلايا */
    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الصفوف الفردية والزوجية */
    .table-hover tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>سجل حركات النظام</h2>
        <p class="text-muted">
            <i class="fas fa-clock me-1"></i> جميع التواريخ والأوقات تعرض حسب التوقيت المحلي (Asia/Amman - توقيت الأردن)
        </p>
    </div>
    <div>
        <a href="?export=1{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-export"></i> تصدير إلى Excel
        </a>
        <form method="post" action="{% url 'system_logs:update_log_descriptions' %}" class="d-inline" id="updateDescriptionsForm">
            {% csrf_token %}
            <button type="submit" class="btn btn-primary mx-2" id="updateDescriptionsBtn" title="تحديث جميع أوصاف السجلات إلى اللغة العربية">
                <i class="fas fa-language"></i> تحديث الأوصاف بالعربية
            </button>
        </form>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
            <i class="fas fa-trash"></i> مسح السجلات
        </button>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تصفية السجلات</h6>
    </div>
    <div class="card-body">
        <form method="get" class="mb-4" id="filter-form">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="module" class="form-label">القسم</label>
                    <select name="module" id="module" class="form-control">
                        <option value="">الكل</option>
                        {% for module in modules_data %}
                        <option value="{{ module.value }}" {% if selected_module == module.value %}selected{% endif %}>
                            {{ module.display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="action" class="form-label">الإجراء</label>
                    <select name="action" id="action" class="form-control">
                        <option value="">الكل</option>
                        {% for action in actions_data %}
                        <option value="{{ action.value }}" {% if selected_action == action.value %}selected{% endif %}>
                            {{ action.display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="user" class="form-label">المستخدم</label>
                    <select name="user" id="user" class="form-control">
                        <option value="">الكل</option>
                        {% for user in users_data %}
                        <option value="{{ user.id }}" {% if selected_user == user.id|stringformat:"i" %}selected{% endif %}>
                            {{ user.username }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_range" class="form-label">الفترة الزمنية</label>
                    <select name="date_range" id="date_range" class="form-control">
                        <option value="1" {% if selected_date_range == '1' %}selected{% endif %}>آخر 24 ساعة</option>
                        <option value="7" {% if selected_date_range == '7' %}selected{% endif %}>آخر 7 أيام</option>
                        <option value="30" {% if selected_date_range == '30' %}selected{% endif %}>آخر 30 يوم</option>
                        <option value="90" {% if selected_date_range == '90' %}selected{% endif %}>آخر 3 أشهر</option>
                        <option value="0" {% if selected_date_range == '0' %}selected{% endif %}>كل السجلات</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-9 mb-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" name="search" id="search" class="form-control" value="{{ search_query }}" placeholder="ابحث في الصفحة، الوصف، المستخدم...">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-dark w-100">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{% if sidebar_pages and selected_module %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">صفحات قسم {% for module in modules_data %}{% if module.value == selected_module %}{{ module.display }}{% endif %}{% endfor %}</h6>
    </div>
    <div class="card-body">
        <div class="row">
            {% for page in sidebar_pages|get_item:selected_module %}
            <div class="col-md-4 mb-2">
                <div class="card">
                    <div class="card-body py-2 px-3 text-center">
                        <small class="d-block text-truncate">{{ page }}</small>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <p class="text-center">لا توجد صفحات لهذا القسم</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- رسالة توضيحية عند عدم وجود فلاتر -->
{% if not selected_module and not selected_action and not selected_user and not selected_date_range and not search_query %}
<div class="card shadow mb-4">
    <div class="card-body">
        <div class="alert alert-info text-center py-5" style="border: 2px dashed #0dcaf0; background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);">
            <div class="mb-4">
                <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                <h4 class="text-info fw-bold">يرجى اختيار الفلاتر لعرض النتائج</h4>
                <p class="lead text-muted mb-4">لعرض سجلات حركات النظام، يرجى اختيار واحد أو أكثر من الفلاتر التالية ثم الضغط على زر "تصفية"</p>
            </div>
            
            <div class="row text-start">
                <div class="col-md-6 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-folder text-primary me-2"></i>
                        <strong>القسم:</strong>
                    </div>
                    <small class="text-muted ms-3">اختر قسم معين مثل الموظفين، المستخدمين، الكادر، النظام، أو الإجازات</small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-cogs text-success me-2"></i>
                        <strong>الإجراء:</strong>
                    </div>
                    <small class="text-muted ms-3">اختر نوع الإجراء مثل إضافة، تعديل، حذف، أو أخرى</small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-user text-warning me-2"></i>
                        <strong>المستخدم:</strong>
                    </div>
                    <small class="text-muted ms-3">اختر مستخدم معين لعرض أنشطته فقط</small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-calendar-alt text-danger me-2"></i>
                        <strong>الفترة الزمنية:</strong>
                    </div>
                    <small class="text-muted ms-3">اختر فترة زمنية محددة أو "كل السجلات" لعرض جميع السجلات</small>
                </div>
                
                <div class="col-12 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-search text-info me-2"></i>
                        <strong>البحث النصي:</strong>
                    </div>
                    <small class="text-muted ms-3">ابحث في محتوى السجلات، أسماء الصفحات، الأوصاف، أو أسماء المستخدمين</small>
                </div>
            </div>
            
            <div class="mt-4 p-3 bg-light rounded">
                <i class="fas fa-lightbulb text-warning me-2"></i>
                <strong>نصيحة:</strong> يمكنك استخدام عدة فلاتر معاً للحصول على نتائج أكثر دقة
            </div>
        </div>
    </div>
</div>
{% else %}

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="m-0 font-weight-bold text-primary">سجلات حركات النظام</h6>
        </div>

        <div class="d-flex justify-content-center align-items-center flex-wrap">
            <div class="d-flex align-items-center mx-3 mb-2">
                <span class="badge bg-primary rounded-pill fs-5 me-2" style="font-size: 1.2rem; padding: 0.5rem 0.8rem;">
                    <i class="fas fa-list-ul me-1"></i>
                    <span id="total-logs-count">
                        {% if total_logs %}
                            {{ total_logs }}
                        {% elif page_obj %}
                            {{ page_obj.paginator.count }}
                        {% else %}
                            {{ logs.count }}
                        {% endif %}
                    </span>
                </span>
                <span class="fw-bold" style="font-size: 1.1rem;">إجمالي السجلات</span>
            </div>

            <div class="mx-3 mb-2">
                <div class="input-group">
                    <span class="input-group-text bg-light fw-bold">
                        <i class="fas fa-eye me-1"></i> عرض
                    </span>
                    <select class="form-select form-select-lg" id="per-page-selector" style="min-width: 150px; font-size: 1rem;">
                        <option value="25" {% if per_page == '25' %}selected{% endif %}>25 سجل</option>
                        <option value="50" {% if per_page == '50' %}selected{% endif %}>50 سجل</option>
                        <option value="100" {% if per_page == '100' %}selected{% endif %}>100 سجل</option>
                        <option value="200" {% if per_page == '200' %}selected{% endif %}>200 سجل</option>
                        <option value="500" {% if per_page == '500' %}selected{% endif %}>500 سجل</option>
                        <option value="all" {% if per_page == 'all' %}selected{% endif %}>عرض الكل</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover w-100">
                <thead class="table-light">
                    <tr>
                        <th style="min-width: 150px;"><i class="fas fa-calendar-alt me-1"></i> التاريخ والوقت</th>
                        <th style="min-width: 100px;"><i class="fas fa-user me-1"></i> المستخدم</th>
                        <th style="min-width: 120px;"><i class="fas fa-folder me-1"></i> القسم</th>
                        <th style="min-width: 120px;"><i class="fas fa-cogs me-1"></i> الإجراء</th>
                        <th style="min-width: 150px;"><i class="fas fa-file-alt me-1"></i> الصفحة</th>
                        <th style="min-width: 350px; width: 30%;"><i class="fas fa-info-circle me-1"></i> الوصف</th>
                        <th style="min-width: 150px;"><i class="fas fa-desktop me-1"></i> نظام التشغيل والعنوان</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    {% include 'system_logs/logs_table_rows.html' %}
                </tbody>
            </table>
            <div id="loading-indicator" class="text-center d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري تحميل المزيد من السجلات...</p>
            </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer">
            {% if page_obj and page_obj.has_other_pages %}
            <nav aria-label="System logs pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link pagination-link" href="?page=1{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link pagination-link" href="?page={{ page_obj.previous_page_number }}{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link pagination-link" href="?page={{ num }}{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link pagination-link" href="?page={{ page_obj.next_page_number }}{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link pagination-link" href="?page={{ page_obj.paginator.num_pages }}{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            <div class="text-center mt-2">
                <small class="text-muted">
                    {% if page_obj %}
                        عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} سجل
                        {% if page_obj.has_other_pages %}
                        | صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        {% endif %}
                    {% else %}
                        عرض جميع السجلات ({{ total_logs }} سجل)
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Clear Logs Modal -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clearLogsModalLabel">تأكيد مسح السجلات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> سيؤدي هذا الإجراء إلى حذف جميع سجلات حركات النظام بشكل نهائي ولا يمكن التراجع عنه.
                </div>
                <p>لتأكيد الحذف، يرجى كتابة <strong>DELETE_ALL</strong> في الحقل أدناه:</p>
                <form method="post" action="{% url 'system_logs:clear_system_logs' %}" id="clearLogsForm">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <input type="text" class="form-control" id="confirmationInput" name="confirmation" placeholder="اكتب DELETE_ALL للتأكيد" required>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> مسح السجلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
    $(document).ready(function() {
        // Handle filter form submission
        $('#filter-form').on('submit', function(e) {
            e.preventDefault(); // منع الإرسال العادي
            
            // تحديث URL بالفلاتر الجديدة
            const formData = new FormData(this);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }
            
            // تحديث URL في المتصفح
            const newUrl = window.location.pathname + '?' + params.toString();
            window.history.pushState({}, '', newUrl);
            
            // إعادة تحميل الصفحة لتطبيق الفلاتر
            window.location.reload();
        });

        // Handle clear logs confirmation
        $('#clearLogsForm').on('submit', function(e) {
            const confirmation = $('#confirmationInput').val();
            if (confirmation !== 'DELETE_ALL') {
                e.preventDefault();
                alert('يرجى كتابة "DELETE_ALL" بالضبط للتأكيد على حذف جميع السجلات.');
                return false;
            }
            return true;
        });

        // Reset confirmation input when modal is closed
        $('#clearLogsModal').on('hidden.bs.modal', function() {
            $('#confirmationInput').val('');
        });

        // Current limit for logs
        let currentLimit = 100;

        // Function to load logs via AJAX
        function loadLogs() {
            // Show loading indicator
            $('#loading-indicator').removeClass('d-none');

            // Get current filter values
            const module = $('#module').val();
            const action = $('#action').val();
            const user = $('#user').val();
            const dateRange = $('#date_range').val();
            const search = $('#search').val();

            // Make AJAX request
            $.ajax({
                url: '{% url "system_logs:get_system_logs_ajax" %}',
                data: {
                    module: module,
                    action: action,
                    user: user,
                    date_range: dateRange,
                    search: search,
                    limit: currentLimit
                },
                success: function(response) {
                    // Update table body
                    $('#logs-table-body').html(response.html);

                    // Update total count
                    $('#total-logs-count').text(response.total_count);

                    // Hide loading indicator
                    $('#loading-indicator').addClass('d-none');
                },
                error: function() {
                    // Hide loading indicator
                    $('#loading-indicator').addClass('d-none');

                    // Show error message
                    alert('حدث خطأ أثناء تحميل السجلات. يرجى المحاولة مرة أخرى.');
                }
            });
        }

        // Handle per page selector change with AJAX
        $('#per-page-selector').on('change', function() {
            const perPage = $(this).val();
            const currentUrl = new URL(window.location);

            if (perPage === 'all') {
                currentUrl.searchParams.set('per_page', 'all');
                currentUrl.searchParams.delete('page');
            } else {
                currentUrl.searchParams.set('per_page', perPage);
                currentUrl.searchParams.set('page', '1'); // Reset to first page
            }

            // Use AJAX to update table instead of reloading page
            loadLogsWithAjax(currentUrl.toString());
        });

        // Function to load logs with AJAX
        function loadLogsWithAjax(url) {
            console.log('Loading logs with AJAX:', url);

            const $tableBody = $('#logs-table-body');
            const $cardFooter = $('.card-footer');
            const $totalCount = $('#total-logs-count');

            // Show loading indicator in table
            $tableBody.html('<tr><td colspan="8" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-2">جاري تحديث عدد البيانات...</p></td></tr>');

            $.ajax({
                url: url,
                type: 'GET',
                success: function(data) {
                    try {
                        console.log('AJAX response received for per page change');

                        // Extract content from response
                        const $newContent = $(data);
                        const $newTableBody = $newContent.find('#logs-table-body');
                        const $newCardFooter = $newContent.find('.card-footer');
                        const $newTotalCount = $newContent.find('#total-logs-count');
                        const $newPerPageSelect = $newContent.find('#per-page-selector');

                        console.log('New table body found:', $newTableBody.length > 0);
                        console.log('New card footer found:', $newCardFooter.length > 0);
                        console.log('New total count found:', $newTotalCount.length > 0);

                        // Update table body content
                        if ($newTableBody.length > 0) {
                            $tableBody.html($newTableBody.html());
                            console.log('Table body updated successfully');
                        } else {
                            console.warn('No table body found in response');
                            $tableBody.html('<tr><td colspan="8" class="text-center text-muted">لا توجد سجلات</td></tr>');
                        }

                        // Update pagination footer
                        if ($newCardFooter.length > 0) {
                            $cardFooter.html($newCardFooter.html());
                            console.log('Card footer updated successfully');
                        } else {
                            $cardFooter.empty();
                            console.log('Card footer cleared');
                        }

                        // Update total count
                        if ($newTotalCount.length > 0) {
                            $totalCount.text($newTotalCount.text());
                            console.log('Total count updated successfully');
                        }

                        // Update per page selector to maintain selection
                        if ($newPerPageSelect.length > 0) {
                            const selectedValue = $newPerPageSelect.val();
                            $('#per-page-selector').val(selectedValue);
                            console.log('Per page selector updated to:', selectedValue);
                        }

                        // Update browser URL without reloading
                        window.history.pushState({}, '', url);

                        console.log('AJAX per page change completed successfully');
                    } catch (error) {
                        console.error('Error processing AJAX response:', error);
                        $tableBody.html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response text:', xhr.responseText);
                    $tableBody.html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات: ' + error + '</td></tr>');
                }
            });
        }

        // Handle limit selector change (legacy support)
        $('#limit-selector').on('change', function() {
            // Get new limit
            const newLimit = parseInt($(this).val());

            // Update current limit
            currentLimit = newLimit;

            // Load logs with new limit
            loadLogs();
        });

        // Handle update descriptions form
        $('#updateDescriptionsForm').on('submit', function(e) {
            e.preventDefault();

            const $btn = $('#updateDescriptionsBtn');
            const originalText = $btn.html();

            // Show loading state
            $btn.prop('disabled', true);
            $btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحديث...');

            // Submit form via AJAX
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message with details
                        const successMessage = `✅ تم تحديث الأوصاف بنجاح!\n\n` +
                                              `📊 الإحصائيات:\n` +
                                              `• تم تحديث: ${response.updated_count} سجل\n` +
                                              `• إجمالي السجلات: ${response.total_logs}\n\n` +
                                              `🔄 سيتم إعادة تحميل السجلات لعرض التحديثات...`;

                        alert(successMessage);

                        // Reload the logs to show updated descriptions
                        loadLogs();
                    } else {
                        alert(response.message || 'حدث خطأ أثناء تحديث الأوصاف.');
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'حدث خطأ أثناء تحديث الأوصاف. يرجى المحاولة مرة أخرى.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    alert(errorMessage);
                    console.error('Error:', error);
                },
                complete: function() {
                    // Restore button state
                    $btn.prop('disabled', false);
                    $btn.html(originalText);
                }
            });
        });

        // Handle filter form submission (but not the update descriptions form)
        $('form:not([action*="update_log_descriptions"]):not([action*="clear_system_logs"])').on('submit', function(e) {
            // Don't reload the page
            e.preventDefault();

            // Load logs with new filters
            loadLogs();
        });

        // AJAX Pagination
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();

            const url = $(this).attr('href');
            console.log('AJAX Pagination URL:', url);

            const $tableBody = $('#logs-table-body');
            const $cardFooter = $('.card-footer');
            const $totalCount = $('#total-logs-count');

            // Show loading indicator
            $tableBody.html('<tr><td colspan="8" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-2">جاري تحميل السجلات...</p></td></tr>');

            $.ajax({
                url: url,
                type: 'GET',
                success: function(data) {
                    try {
                        console.log('AJAX response received');

                        // Extract content from response
                        const $newContent = $(data);
                        const $newTableBody = $newContent.find('#logs-table-body');
                        const $newCardFooter = $newContent.find('.card-footer');
                        const $newTotalCount = $newContent.find('#total-logs-count');

                        console.log('New table body found:', $newTableBody.length > 0);
                        console.log('New card footer found:', $newCardFooter.length > 0);
                        console.log('New total count found:', $newTotalCount.length > 0);

                        // Update table body content
                        if ($newTableBody.length > 0) {
                            $tableBody.html($newTableBody.html());
                            console.log('Table body updated successfully');
                        } else {
                            console.warn('No table body found in response');
                            $tableBody.html('<tr><td colspan="8" class="text-center text-muted">لا توجد سجلات</td></tr>');
                        }

                        // Update pagination footer
                        if ($newCardFooter.length > 0) {
                            $cardFooter.html($newCardFooter.html());
                            console.log('Card footer updated successfully');
                        } else {
                            $cardFooter.empty();
                            console.log('Card footer cleared');
                        }

                        // Update total count
                        if ($newTotalCount.length > 0) {
                            $totalCount.text($newTotalCount.text());
                            console.log('Total count updated successfully');
                        }

                        // Scroll to top of table
                        $('html, body').animate({
                            scrollTop: $('.card').offset().top - 100
                        }, 500);

                        console.log('AJAX pagination completed successfully');
                    } catch (error) {
                        console.error('Error processing AJAX response:', error);
                        $tableBody.html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response text:', xhr.responseText);
                    $tableBody.html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات: ' + error + '</td></tr>');
                }
            });
        });
    });
</script>

<style>
.ajax-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}
{% endblock %}