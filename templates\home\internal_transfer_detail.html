{% extends 'base.html' %}

{% block title %}تفاصيل طلب النقل الداخلي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل طلب النقل الداخلي</h1>
        <div>
            <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>

        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الطلب</h6>
                    <span class="badge {% if transfer.status == 'pending' %}bg-warning text-dark{% elif transfer.status == 'approved' %}bg-success{% elif transfer.status == 'rejected' %}bg-danger{% endif %} p-2">
                        {% if transfer.status == 'pending' %}قيد الانتظار
                        {% elif transfer.status == 'approved' %}تمت الموافقة
                        {% elif transfer.status == 'rejected' %}مرفوض
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="border-bottom pb-2 mb-3">معلومات الموظف</h5>
                        </div>
                        <div class="col-md-6">
                            <p><strong>اسم الموظف:</strong> {{ transfer.employee_name }}</p>
                            <p><strong>الرقم الوزاري:</strong> {{ transfer.ministry_number }}</p>
                            <p><strong>الرقم الوطني:</strong> {{ transfer.employee_id }}</p>
                            <p><strong>الجنس:</strong> {{ transfer.get_gender_display }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الطلب:</strong> <span class="text-primary">{{ transfer.created_at|date:"Y-m-d" }}</span> <span class="text-muted">{{ transfer.created_at|date:"H:i:s" }}</span></p>
                            <p><strong>رقم الهاتف:</strong> {{ transfer.phone_number }}</p>
                            <p><strong>البريد الإلكتروني:</strong> {{ transfer.email|default:"غير محدد" }}</p>
                            <p><strong>آخر تحديث:</strong> <span class="text-primary">{{ transfer.updated_at|date:"Y-m-d" }}</span> <span class="text-muted">{{ transfer.updated_at|date:"H:i:s" }}</span></p>
                            <p><strong>المنطقة الزمنية:</strong> <span class="badge bg-info">{{ transfer.created_at.tzinfo }}</span></p>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="border-bottom pb-2 mb-3">معلومات الوظيفة الحالية</h5>
                        </div>
                        <div class="col-md-6">
                            <p><strong>القسم الحالي:</strong> {{ transfer.current_department }}</p>
                            <p><strong>تاريخ التعيين:</strong> {{ transfer.hire_date|date:"Y-m-d"|default:"غير محدد" }}</p>
                            <p><strong>الخدمة الفعلية:</strong>
                                {% if transfer.actual_service_detailed %}
                                <span class="badge bg-success">{{ transfer.actual_service_detailed }}</span>
                                {% else %}
                                {{ transfer.actual_service|default:"غير محدد" }}
                                {% endif %}
                            </p>
                            <p><strong>آخر مسمى وظيفي:</strong> {{ transfer.last_position|default:"غير محدد" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>المؤهل العلمي:</strong> {{ transfer.qualification|default:"غير محدد" }}</p>
                            <p><strong>التخصص:</strong> {{ transfer.specialization|default:"غير محدد" }}</p>
                            <p><strong>آخر رتبة:</strong>
                                {% if transfer.latest_rank_details %}
                                <span class="badge bg-primary">{{ transfer.latest_rank_details.name }}</span>
                                <small class="text-muted ms-2">تاريخ الحصول: {{ transfer.latest_rank_details.date }}</small>
                                {% if transfer.latest_rank_details.degree %}
                                <small class="text-muted ms-2">الدرجة: {{ transfer.latest_rank_details.degree }}</small>
                                {% endif %}
                                {% else %}
                                {{ transfer.last_rank|default:"غير محدد" }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-12">
                            <p><strong>العنوان:</strong> {{ transfer.address|default:"غير محدد" }}</p>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="border-bottom pb-2 mb-3">خيارات النقل</h5>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-white text-primary border-primary">
                                    <h6 class="mb-0">الخيار الأول</h6>
                                </div>
                                <div class="card-body">
                                    {{ transfer.first_choice }}
                                </div>
                            </div>
                        </div>
                        {% if transfer.second_choice %}
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-white text-primary border-primary">
                                    <h6 class="mb-0">الخيار الثاني</h6>
                                </div>
                                <div class="card-body">
                                    {{ transfer.second_choice }}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if transfer.third_choice %}
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-white text-primary border-primary">
                                    <h6 class="mb-0">الخيار الثالث</h6>
                                </div>
                                <div class="card-body">
                                    {{ transfer.third_choice }}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="border-bottom pb-2 mb-3">سبب النقل</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ transfer.reason|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if transfer.notes %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="border-bottom pb-2 mb-3">ملاحظات</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ transfer.notes|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="#">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="status" class="form-label">تغيير حالة الطلب</label>
                            <select name="status" id="status" class="form-control">
                                <option value="pending" {% if transfer.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="approved" {% if transfer.status == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                                <option value="rejected" {% if transfer.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea name="notes" id="notes" rows="4" class="form-control">{{ transfer.notes }}</textarea>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>

                    <div class="d-grid">
                        <a href="{% url 'home:internal_transfer_delete' transfer.id %}" class="btn btn-danger">
                            <i class="fas fa-trash"></i> حذف الطلب
                        </a>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">رابط التعديل</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">يمكن للموظف تعديل طلبه من خلال الرابط التالي:</p>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" value="{{ request.scheme }}://{{ request.get_host }}{% url 'home:internal_transfer_edit' token=transfer.edit_token %}" id="edit_link" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyEditLink()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="form-text">يمكنك نسخ هذا الرابط وإرساله للموظف إذا أراد تعديل طلبه.</div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
    function copyEditLink() {
        var copyText = document.getElementById("edit_link");
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");

        // Show copied message
        var button = document.querySelector("button[onclick='copyEditLink()']");
        var originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }
</script>
{% endblock %}
