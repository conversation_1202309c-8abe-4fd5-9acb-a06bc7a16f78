# Generated manually

from django.db import migrations, models
import django.db.models.deletion


def create_default_job(apps, schema_editor):
    """Create a default job for existing BTEC teachers"""
    BtecJob = apps.get_model('employment', 'BtecJob')
    BtecJob.objects.create(id=1, name='معلم', description='وظيفة معلم افتراضية')


def reverse_create_default_job(apps, schema_editor):
    """Remove the default job"""
    BtecJob = apps.get_model('employment', 'BtecJob')
    BtecJob.objects.filter(id=1).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0020_employeeallowance'),
    ]

    operations = [
        # First create the BtecJob model
        migrations.CreateModel(
            name='BtecJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='اسم الوظيفة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الوظيفة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'وظيفة BTEC',
                'verbose_name_plural': 'وظائف BTEC',
                'ordering': ['name'],
            },
        ),
        # Create the default job
        migrations.RunPython(create_default_job, reverse_create_default_job),
    ]
