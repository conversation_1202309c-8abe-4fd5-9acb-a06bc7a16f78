from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee

class PerformanceEvaluation(models.Model):
    """Model for storing employee performance evaluations"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='performance_evaluations')
    year = models.PositiveIntegerField(_('Year'))
    score = models.DecimalField(_('Score'), max_digits=5, decimal_places=2)
    max_score = models.DecimalField(_('Maximum Score'), max_digits=5, decimal_places=2, default=100.00)
    evaluator = models.CharField(_('Evaluator'), max_length=255, blank=True, null=True)
    comments = models.TextField(_('Comments'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Performance Evaluation')
        verbose_name_plural = _('Performance Evaluations')
        ordering = ['-year']
        unique_together = ['employee', 'year']

    def __str__(self):
        return f"{self.employee.full_name} - {self.year}"

    @property
    def percentage(self):
        if self.max_score > 0:
            return (self.score / self.max_score) * 100
        return 0
