{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء إشعار جديد - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-control {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .recipient-options {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check {
        margin-bottom: 0.5rem;
    }

    .alert {
        border-radius: 8px;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <h2><i class="fas fa-bell me-2"></i>إنشاء إشعار جديد</h2>
                    <p class="mb-0">إرسال إشعار للمستخدمين في النظام</p>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}" class="form-label fw-bold">
                            <i class="fas fa-heading me-2"></i>{{ form.title.label }}
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger mt-1">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.message.id_for_label }}" class="form-label fw-bold">
                            <i class="fas fa-comment me-2"></i>{{ form.message.label }}
                        </label>
                        {{ form.message }}
                        {% if form.message.errors %}
                            <div class="text-danger mt-1">{{ form.message.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.notification_type.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-tag me-2"></i>{{ form.notification_type.label }}
                                </label>
                                {{ form.notification_type }}
                                {% if form.notification_type.errors %}
                                    <div class="text-danger mt-1">{{ form.notification_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.icon.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-icons me-2"></i>{{ form.icon.label }}
                                </label>
                                {{ form.icon }}
                                {% if form.icon.errors %}
                                    <div class="text-danger mt-1">{{ form.icon.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="recipient-options">
                        <h5 class="mb-3"><i class="fas fa-users me-2"></i>المستقبلين</h5>
                        
                        <div class="form-check">
                            {{ form.send_to_all }}
                            <label class="form-check-label" for="{{ form.send_to_all.id_for_label }}">
                                <i class="fas fa-globe me-2"></i>{{ form.send_to_all.label }}
                            </label>
                        </div>

                        <div class="form-check">
                            {{ form.send_to_admins }}
                            <label class="form-check-label" for="{{ form.send_to_admins.id_for_label }}">
                                <i class="fas fa-user-shield me-2"></i>{{ form.send_to_admins.label }}
                            </label>
                        </div>

                        <div class="form-group mt-3">
                            <label for="{{ form.specific_users.id_for_label }}" class="form-label fw-bold">
                                <i class="fas fa-user-friends me-2"></i>{{ form.specific_users.label }}
                            </label>
                            {{ form.specific_users }}
                            <small class="form-text text-muted">اضغط Ctrl لاختيار عدة مستخدمين</small>
                            {% if form.specific_users.errors %}
                                <div class="text-danger mt-1">{{ form.specific_users.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>إرسال الإشعار
                        </button>
                        <a href="{% url 'notifications:notification_list' %}" class="btn btn-secondary btn-lg ms-2">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle recipient type selection
    const sendToAll = document.getElementById('{{ form.send_to_all.id_for_label }}');
    const sendToAdmins = document.getElementById('{{ form.send_to_admins.id_for_label }}');
    const specificUsers = document.getElementById('{{ form.specific_users.id_for_label }}');

    function toggleSpecificUsers() {
        if (sendToAll.checked || sendToAdmins.checked) {
            specificUsers.disabled = true;
            specificUsers.style.opacity = '0.5';
        } else {
            specificUsers.disabled = false;
            specificUsers.style.opacity = '1';
        }
    }

    sendToAll.addEventListener('change', function() {
        if (this.checked) {
            sendToAdmins.checked = false;
        }
        toggleSpecificUsers();
    });

    sendToAdmins.addEventListener('change', function() {
        if (this.checked) {
            sendToAll.checked = false;
        }
        toggleSpecificUsers();
    });

    // Initial state
    toggleSpecificUsers();
});
</script>
{% endblock %}
