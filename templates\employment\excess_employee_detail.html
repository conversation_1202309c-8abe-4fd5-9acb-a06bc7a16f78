{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الموظف الزائد{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .detail-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 15px 20px;
        font-weight: bold;
        color: #495057;
    }

    .detail-card .card-body {
        padding: 20px;
    }

    .detail-row {
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: bold;
        color: #495057;
    }

    .detail-value {
        color: #212529;
    }

    .status-badge {
        font-size: 0.85rem;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .status-pending {
        background-color: #ffc107;
        color: #000;
    }

    .status-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .resolution-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title mb-4">تفاصيل الموظف الزائد</h1>

            <!-- Display Django messages -->
            {% if messages %}
            <div class="messages mb-4">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between mb-4">
                <div>
                    <a href="{% url 'employment:excess_employee_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
                <div>
                    <a href="{% url 'employment:excess_employee_update' excess_employee.id %}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    {% if excess_employee.status == 'pending' %}
                    <a href="{% url 'employment:excess_employee_resolve' excess_employee.id %}" class="btn btn-success">
                        <i class="fas fa-check"></i> معالجة
                    </a>
                    {% endif %}
                    <a href="{% url 'employment:excess_employee_delete' excess_employee.id %}" class="btn btn-danger">
                        <i class="fas fa-trash"></i> حذف
                    </a>
                </div>
            </div>

            <!-- Employee Information -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-user me-2"></i> معلومات الموظف
                </div>
                <div class="card-body">
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">الرقم الوزاري</div>
                            <div class="detail-value">{{ excess_employee.employee.ministry_number }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">الاسم الكامل</div>
                            <div class="detail-value">{{ excess_employee.employee.full_name }}</div>
                        </div>
                    </div>
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">الرقم الوطني</div>
                            <div class="detail-value">{{ excess_employee.employee.national_id }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">تاريخ التعيين</div>
                            <div class="detail-value">{{ excess_employee.employee.hire_date|date:"Y-m-d" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Excess Information -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> معلومات الزيادة
                </div>
                <div class="card-body">
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">القسم الحالي</div>
                            <div class="detail-value">{{ excess_employee.current_department.name }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">المسمى الوظيفي</div>
                            <div class="detail-value">{{ excess_employee.position.name }}</div>
                        </div>
                    </div>
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">الحالة</div>
                            <div class="detail-value">
                                {% if excess_employee.status == 'pending' %}
                                <span class="badge status-badge status-pending">قيد المعالجة</span>
                                {% else %}
                                <span class="badge status-badge status-resolved">تمت المعالجة</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">تاريخ الإضافة</div>
                            <div class="detail-value">{{ excess_employee.created_at|date:"Y-m-d H:i" }}</div>
                        </div>
                    </div>
                    <div class="row detail-row">
                        <div class="col-md-12">
                            <div class="detail-label">سبب اعتبار الموظف زائد</div>
                            <div class="detail-value">{{ excess_employee.reason }}</div>
                        </div>
                    </div>

                    {% if excess_employee.status == 'resolved' %}
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">تاريخ المعالجة</div>
                            <div class="detail-value">{{ excess_employee.resolution_date|date:"Y-m-d" }}</div>
                        </div>
                        <div class="col-md-12">
                            <div class="detail-label">ملاحظات المعالجة</div>
                            <div class="detail-value">{{ excess_employee.resolution_notes }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Resolution Form (if pending) -->
            {% if excess_employee.status == 'pending' and resolve_form %}
            <div class="resolution-section">
                <h4 class="mb-3">معالجة الموظف الزائد</h4>
                <form method="post" action="{% url 'employment:excess_employee_resolve' excess_employee.id %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ resolve_form.resolution_notes.id_for_label }}" class="form-label">ملاحظات المعالجة</label>
                        {{ resolve_form.resolution_notes }}
                        {% if resolve_form.resolution_notes.errors %}
                        <div class="invalid-feedback d-block">
                            {{ resolve_form.resolution_notes.errors }}
                        </div>
                        {% endif %}
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> تأكيد المعالجة
                    </button>
                </form>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
