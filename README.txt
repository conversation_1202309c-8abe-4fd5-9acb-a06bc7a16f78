تشغيل نظام الموارد البشرية
====================

إذا كنت تواجه مشكلة "Python was not found"، فهناك عدة حلول ممكنة:

الحل 1: استخدام ملف run_with_venv.bat
--------------------------------
هذا الملف سيحاول تشغيل السيرفر باستخدام Python من البيئة الافتراضية أو من المواقع الشائعة لتثبيت Python.
1. انقر نقرًا مزدوجًا على ملف run_with_venv.bat

الحل 2: تثبيت Python من Microsoft Store
--------------------------------
إذا لم يكن Python مثبتًا على جهازك:
1. انقر نقرًا مزدوجًا على ملف install_python.bat
2. اتبع التعليمات لتثبيت Python من Microsoft Store
3. بعد التثبيت، قم بتشغيل run_with_venv.bat

الحل 3: تعطيل اختصارات تنفيذ التطبيقات
--------------------------------
إذا كان Python مثبتًا ولكن لا يزال هناك خطأ:
1. انقر نقرًا مزدوجًا على ملف disable_app_execution_aliases.bat
2. اتبع التعليمات لتعطيل اختصارات تنفيذ التطبيقات لـ Python
3. بعد التعطيل، قم بتشغيل run_with_venv.bat

الحل 4: تثبيت Python يدويًا
--------------------------------
إذا كنت تفضل تثبيت Python يدويًا:
1. قم بزيارة https://www.python.org/downloads/
2. قم بتنزيل وتثبيت أحدث إصدار من Python (تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت)
3. بعد التثبيت، قم بتشغيل run_with_venv.bat

ملاحظات إضافية
--------------
- تأكد من أن لديك جميع المكتبات المطلوبة مثبتة. يمكنك تثبيتها باستخدام الأمر:
  pip install -r requirements.txt
- إذا كنت لا تزال تواجه مشاكل، يمكنك محاولة إنشاء بيئة افتراضية جديدة:
  python -m venv venv
  venv\Scripts\activate
  pip install -r requirements.txt
  python manage.py runserver
