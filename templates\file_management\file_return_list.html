{% extends 'base.html' %}
{% load static %}

{% block title %}الملفات المنجزة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">الملفات المنجزة</h1>
        <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة لحركة الملفات
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">الملفات التي تمت إعادتها</h6>
            <form class="d-flex" method="get">
                <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">بحث</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr class="text-center">
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                            <th><i class="fas fa-calendar-check me-1"></i> تاريخ عودة الملف</th>
                            <th><i class="fas fa-tasks me-1"></i> الإجراء المتخذ</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in file_movements %}
                        <tr class="text-center">
                            <td>{{ movement.employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' movement.employee.pk %}">
                                    {{ movement.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ movement.return_date|date:"Y-m-d" }}</td>
                            <td>{{ movement.action_taken|truncatechars:50 }}</td>
                            <td>
                                <a href="{% url 'file_management:file_movement_detail' movement.pk %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{% url 'file_management:file_movement_update' movement.pk %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'file_management:file_movement_delete' movement.pk %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="text-center">
                            <td colspan="5">لا توجد ملفات منجزة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
