# Generated manually for ImportantLink model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0006_technicaltransfer'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportantLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الرابط')),
                ('url', models.URLField(verbose_name='الرابط')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('favicon_url', models.URLField(blank=True, null=True, verbose_name='رابط الأيقونة')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'رابط مهم',
                'verbose_name_plural': 'الروابط المهمة',
                'ordering': ['order', 'name'],
            },
        ),
    ]
