# Generated by Django 5.2 on 2025-05-24 19:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0015_department_directorate_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='department',
            name='highest_grade',
            field=models.CharField(blank=True, choices=[('kg1', 'روضة أولى'), ('kg2', 'روضة ثانية'), ('grade1', 'الصف الأول'), ('grade2', 'الصف الثاني'), ('grade3', 'الصف الثالث'), ('grade4', 'الصف الرابع'), ('grade5', 'الصف الخامس'), ('grade6', 'الصف السادس'), ('grade7', 'الصف السابع'), ('grade8', 'الصف الثامن'), ('grade9', 'الصف التاسع'), ('grade10', 'الصف العاشر'), ('grade11', 'الصف الحادي عشر'), ('grade12', 'الصف الثاني عشر')], max_length=20, null=True, verbose_name='Highest Grade'),
        ),
        migrations.AddField(
            model_name='department',
            name='lowest_grade',
            field=models.CharField(blank=True, choices=[('kg1', 'روضة أولى'), ('kg2', 'روضة ثانية'), ('grade1', 'الصف الأول'), ('grade2', 'الصف الثاني'), ('grade3', 'الصف الثالث'), ('grade4', 'الصف الرابع'), ('grade5', 'الصف الخامس'), ('grade6', 'الصف السادس'), ('grade7', 'الصف السابع'), ('grade8', 'الصف الثامن'), ('grade9', 'الصف التاسع'), ('grade10', 'الصف العاشر'), ('grade11', 'الصف الحادي عشر'), ('grade12', 'الصف الثاني عشر')], max_length=20, null=True, verbose_name='Lowest Grade'),
        ),
    ]
