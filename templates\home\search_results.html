{% extends 'base.html' %}
{% load static %}

{% block title %}نتائج البحث - {{ query }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>نتائج البحث: {{ query }}</h2>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">نتائج البحث</h6>
        </div>
        <div class="card-body">
            {% if not query %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle fa-lg me-2"></i>
                    الرجاء إدخال كلمة البحث في شريط البحث أعلى الصفحة.
                </div>
            {% elif not results.employees and not results.departments and not results.positions and not results.transfers and not results.forms %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle fa-lg me-2"></i>
                    لم يتم العثور على نتائج مطابقة لـ "{{ query }}".
                </div>
            {% else %}
                <!-- Employees Results -->
                {% if results.employees %}
                    <div class="mb-5">
                        <h4 class="mb-3">الموظفين ({{ results.employees|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم الوزاري</th>
                                        <th>الاسم</th>
                                        <th>المدرسة/القسم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for employee in results.employees %}
                                        <tr>
                                            <td>{{ employee.ministry_number }}</td>
                                            <td>{{ employee.full_name }}</td>
                                            <td>{{ employee.school }}</td>
                                            <td>
                                                <a href="{% url 'employees:employee_detail' employee.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}

                <!-- Departments Results -->
                {% if results.departments %}
                    <div class="mb-5">
                        <h4 class="mb-3">المدارس/الأقسام ({{ results.departments|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>مكان العمل</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for department in results.departments %}
                                        <tr>
                                            <td>{{ department.name }}</td>
                                            <td>{{ department.get_workplace_display }}</td>
                                            <td>{{ department.description|default:"-"|truncatechars:50 }}</td>
                                            <td>
                                                <a href="{% url 'employment:department_detail' department.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}

                <!-- Positions Results -->
                {% if results.positions %}
                    <div class="mb-5">
                        <h4 class="mb-3">المسميات الوظيفية ({{ results.positions|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المسمى الوظيفي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for position in results.positions %}
                                        <tr>
                                            <td>{{ position.name }}</td>
                                            <td>
                                                <a href="{% url 'employment:position_detail' position.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}

                <!-- Transfers Results -->
                {% if results.transfers %}
                    <div class="mb-5">
                        <h4 class="mb-3">طلبات النقل الداخلي ({{ results.transfers|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم الوزاري</th>
                                        <th>الاسم</th>
                                        <th>القسم الحالي</th>
                                        <th>الخيار الأول</th>
                                        <th>تاريخ الطلب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transfer in results.transfers %}
                                        <tr>
                                            <td>{{ transfer.ministry_number }}</td>
                                            <td>{{ transfer.employee_name }}</td>
                                            <td>{{ transfer.current_department }}</td>
                                            <td>{{ transfer.first_choice }}</td>
                                            <td>{{ transfer.created_at|date:"Y-m-d" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}

                <!-- Forms Results -->
                {% if results.forms %}
                    <div class="mb-5">
                        <h4 class="mb-3">النماذج المعتمدة ({{ results.forms|length }})</h4>
                        <div class="row">
                            {% for form in results.forms %}
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">{{ form.title }}</h5>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">{{ form.description|truncatechars:100 }}</p>
                                        </div>
                                        <div class="card-footer">
                                            <a href="{% url 'home:approved_form_download' form.id %}" class="btn btn-primary">
                                                <i class="fas fa-download"></i> تنزيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
