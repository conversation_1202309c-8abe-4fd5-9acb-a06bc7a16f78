from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, SetPasswordForm
from django.utils.translation import gettext_lazy as _
from .models import User, UserPermission

class AdminPasswordChangeForm(SetPasswordForm):
    """Form for an admin to change a user's password"""

    class Meta:
        model = User
        fields = ['new_password1', 'new_password2']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['new_password1'].label = 'كلمة المرور الجديدة'
        self.fields['new_password2'].label = 'تأكيد كلمة المرور'

class CustomUserCreationForm(UserCreationForm):
    is_admin_user = forms.BooleanField(label='مدير (جميع الصلاحيات)', required=False)

    PERMISSION_CHOICES = [
        ('', '-- اختر مستوى الصلاحية --'),
        ('admin', 'مدير (عرض - اضافة - تعديل - حذف)'),
        ('supervisor', 'مشرف (عرض - اضافة - تعديل)'),
        ('user', 'مستخدم عادي (عرض - اضافة)'),
        ('readonly', 'مستخدم استعلام فقط (عرض فقط)'),
    ]

    user_permission_level = forms.ChoiceField(
        label='صلاحيات المستخدم',
        choices=PERMISSION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'email', 'phone_number', 'address', 'is_full_admin', 'user_permission_level']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['is_full_admin'].label = 'مدير النظام (جميع الصلاحيات)'
        self.fields['is_full_admin'].help_text = 'منح المستخدم جميع الصلاحيات وإمكانية الوصول إلى جميع الصفحات'

        # Hide is_admin_user field as we now use is_full_admin
        self.fields['is_admin_user'].widget = forms.HiddenInput()

        # Hide is_admin and is_staff fields as we now use user_permission_level
        self.fields['is_admin'] = forms.BooleanField(required=False, widget=forms.HiddenInput())
        self.fields['is_staff'] = forms.BooleanField(required=False, widget=forms.HiddenInput())

        # Set initial value for is_full_admin based on instance
        if self.instance.pk:
            self.fields['is_full_admin'].initial = self.instance.is_full_admin

            # Set initial permission level based on user permissions
            if not self.instance.is_full_admin:
                if self.instance.is_admin:
                    self.fields['user_permission_level'].initial = 'admin'
                elif self.instance.is_staff:
                    self.fields['user_permission_level'].initial = 'supervisor'
                else:
                    self.fields['user_permission_level'].initial = 'user'

    def clean(self):
        cleaned_data = super().clean()
        # Sync is_admin_user with is_full_admin for backward compatibility
        if cleaned_data.get('is_full_admin'):
            cleaned_data['is_admin_user'] = True
            cleaned_data['is_admin'] = True
            cleaned_data['is_staff'] = True
            print(f"User will be set as full admin: is_admin=True, is_full_admin=True")
        elif cleaned_data.get('is_admin_user'):
            cleaned_data['is_full_admin'] = True
            cleaned_data['is_admin'] = True
            cleaned_data['is_staff'] = True
            print(f"User will be set as full admin via is_admin_user: is_admin=True, is_full_admin=True")
        else:
            # Set permissions based on user_permission_level
            permission_level = cleaned_data.get('user_permission_level')
            if permission_level == 'admin':
                cleaned_data['is_admin'] = True
                cleaned_data['is_staff'] = True
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'supervisor':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = True
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'user':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = False
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'readonly':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = False
                cleaned_data['is_full_admin'] = False

        return cleaned_data

class CustomUserChangeForm(UserChangeForm):
    password = None  # Remove password field from the form
    is_admin_user = forms.BooleanField(label='مدير (جميع الصلاحيات)', required=False)

    PERMISSION_CHOICES = [
        ('', '-- اختر مستوى الصلاحية --'),
        ('admin', 'مدير (عرض - اضافة - تعديل - حذف)'),
        ('supervisor', 'مشرف (عرض - اضافة - تعديل)'),
        ('user', 'مستخدم عادي (عرض - اضافة)'),
        ('readonly', 'مستخدم استعلام فقط (عرض فقط)'),
    ]

    user_permission_level = forms.ChoiceField(
        label='صلاحيات المستخدم',
        choices=PERMISSION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'email', 'phone_number', 'address', 'is_full_admin', 'is_active', 'user_permission_level']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['is_full_admin'].label = 'مدير النظام (جميع الصلاحيات)'
        self.fields['is_full_admin'].help_text = 'منح المستخدم جميع الصلاحيات وإمكانية الوصول إلى جميع الصفحات'

        # Hide is_admin_user field as we now use is_full_admin
        self.fields['is_admin_user'].widget = forms.HiddenInput()

        # Hide is_admin and is_staff fields as we now use user_permission_level
        self.fields['is_admin'] = forms.BooleanField(required=False, widget=forms.HiddenInput())
        self.fields['is_staff'] = forms.BooleanField(required=False, widget=forms.HiddenInput())

        # Set is_admin_user checkbox based on existing permissions
        if self.instance.pk:
            # Check if user has full admin permissions
            self.fields['is_admin_user'].initial = self.instance.is_full_admin
            self.fields['is_full_admin'].initial = self.instance.is_full_admin

            # Set initial permission level based on user permissions
            if not self.instance.is_full_admin:
                if self.instance.is_admin:
                    self.fields['user_permission_level'].initial = 'admin'
                elif self.instance.is_staff:
                    self.fields['user_permission_level'].initial = 'supervisor'
                else:
                    self.fields['user_permission_level'].initial = 'user'

            # Debug output
            print(f"Initializing form for user {self.instance.username}: is_admin={self.instance.is_admin}, is_full_admin={self.instance.is_full_admin}")

    def clean(self):
        cleaned_data = super().clean()
        # Sync is_admin_user with is_full_admin for backward compatibility
        if cleaned_data.get('is_full_admin'):
            cleaned_data['is_admin_user'] = True
            cleaned_data['is_admin'] = True
            cleaned_data['is_staff'] = True
            print(f"User will be set as full admin: is_admin=True, is_full_admin=True")
        elif cleaned_data.get('is_admin_user'):
            cleaned_data['is_full_admin'] = True
            cleaned_data['is_admin'] = True
            cleaned_data['is_staff'] = True
            print(f"User will be set as full admin via is_admin_user: is_admin=True, is_full_admin=True")
        else:
            # Set permissions based on user_permission_level
            permission_level = cleaned_data.get('user_permission_level')
            if permission_level == 'admin':
                cleaned_data['is_admin'] = True
                cleaned_data['is_staff'] = True
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'supervisor':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = True
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'user':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = False
                cleaned_data['is_full_admin'] = False
            elif permission_level == 'readonly':
                cleaned_data['is_admin'] = False
                cleaned_data['is_staff'] = False
                cleaned_data['is_full_admin'] = False

        return cleaned_data

class UserPermissionForm(forms.ModelForm):
    PERMISSION_CHOICES = [
        ('admin', 'مدير (عرض - اضافة - تعديل - حذف)'),
        ('supervisor', 'مشرف (عرض - اضافة - تعديل)'),
        ('user', 'مستخدم عادي (عرض - اضافة)'),
        ('readonly', 'مستخدم استعلام فقط (عرض فقط)'),
    ]

    permission_level = forms.ChoiceField(
        label='مستوى الصلاحية',
        choices=PERMISSION_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class Meta:
        model = UserPermission
        fields = ['module_name', 'can_view', 'can_add', 'can_edit', 'can_delete', 'visible_pages']
        widgets = {
            'can_view': forms.HiddenInput(),
            'can_add': forms.HiddenInput(),
            'can_edit': forms.HiddenInput(),
            'can_delete': forms.HiddenInput(),
            'visible_pages': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['module_name'].widget = forms.Select(choices=[
            # الوحدات الأساسية
            ('home', 'الصفحة الرئيسية والنقل الداخلي'),
            ('employees', 'الموظفين'),
            ('employment', 'الكادر'),
            ('leaves', 'الإجراءات'),
            ('disciplinary', 'العقوبات'),
            ('directorate_leaves', 'إجازات الموظفين (المديرية)'),
            ('files', 'الملفات'),
            ('ranks', 'الرتب'),

            # وحدات التقارير والتقييم
            ('performance', 'التقارير السنوية'),
            ('reports', 'تقارير النظام'),

            # وحدات النظام
            ('accounts', 'المستخدمين'),
            ('backup', 'النسخ الاحتياطية'),
            ('system_logs', 'سجل حركات النظام'),

            # وحدات التواصل
            ('announcements', 'الإعلانات'),
            ('notifications', 'الإشعارات'),
        ])

        # Add labels for fields
        self.fields['module_name'].label = 'القسم'

        # Set initial permission level based on instance
        if self.instance.pk:
            if self.instance.can_delete:
                self.fields['permission_level'].initial = 'admin'
            elif self.instance.can_edit:
                self.fields['permission_level'].initial = 'supervisor'
            else:
                self.fields['permission_level'].initial = 'user'

    def get_pages_choices(self):
        # Return a list of choices based on the module_name
        # This will be dynamically updated via JavaScript
        return [
            # الكادر - الموظفين
            ('employees:employee_list', 'بيانات الموظفين'),
            ('employees:employee_create', 'إضافة موظف'),
            ('employees:employee_detail', 'تفاصيل الموظف'),
            ('employees:employee_update', 'تعديل الموظف'),
            ('employees:employee_delete', 'حذف الموظف'),
            ('employees:employee_import_export', 'استيراد وتصدير الموظفين'),
            ('employees:get_employee_by_ministry_number', 'البحث برقم الوزارة'),
            ('employees:annual_report_create', 'إضافة تقرير سنوي'),
            ('employees:calculate_age', 'حساب العمر'),
            ('employees:add_qualification', 'إضافة مؤهل'),
            ('employees:search_employee_for_qualification', 'البحث عن موظف للمؤهل'),
            
            # إجازات الأمومة
            ('employees:maternity_leaves_list', 'قائمة إجازات الأمومة'),
            ('employees:add_maternity_leave', 'إضافة إجازة أمومة'),
            ('employees:maternity_leave_detail', 'تفاصيل إجازة الأمومة'),
            ('employees:maternity_leave_update', 'تعديل إجازة الأمومة'),
            ('employees:maternity_leave_delete', 'حذف إجازة الأمومة'),
            ('employees:search_employee_for_maternity', 'البحث عن موظف للأمومة'),
            ('employees:export_maternity_leaves_excel', 'تصدير إجازات الأمومة Excel'),
            
            # المتقاعدين
            ('employees:retired_employees_list', 'قائمة المتقاعدين'),
            ('employees:retired_employee_detail', 'تفاصيل المتقاعد'),
            ('employees:retired_employee_update', 'تعديل بيانات المتقاعد'),
            ('employees:retired_employee_delete', 'حذف المتقاعد'),
            ('employees:search_employees_for_retirement', 'البحث عن موظف للتقاعد'),
            
            # النقل الخارجي
            ('employees:external_transfers_list', 'قائمة النقل الخارجي'),
            ('employees:external_transfer_detail', 'تفاصيل النقل الخارجي'),
            ('employees:external_transfer_update', 'تعديل النقل الخارجي'),
            ('employees:external_transfer_delete', 'حذف النقل الخارجي'),
            ('employees:search_employees_for_transfer', 'البحث عن موظف للنقل'),
            
            # النقل الداخلي
            ('employees:internal_transfers_list', 'قائمة النقل الداخلي'),
            ('employees:internal_transfer_detail', 'تفاصيل النقل الداخلي'),
            ('employees:internal_transfer_update', 'تعديل النقل الداخلي'),
            ('employees:internal_transfer_delete', 'حذف النقل الداخلي'),
            ('employees:internal_transfers_statistics', 'إحصائيات النقل الداخلي'),
            ('employees:internal_transfers_statistics_api', 'API إحصائيات النقل الداخلي'),
            ('employees:export_internal_transfers_excel', 'تصدير النقل الداخلي Excel'),

            # الكادر - الوظائف
            ('employment:department_list', 'الأقسام'),
            ('employment:position_list', 'المسميات الوظيفية'),
            ('employment:employee_position_list', 'الحراك الوظيفي'),
            ('employment:experience_certificate_list', 'شهادة الخبرة'),
            ('employment:employee_identification_list', 'البيانات التعريفية'),
            ('employment:technical_position_list', 'الموقف الفني'),
            ('employment:actual_service_list', 'الخدمة الفعلية'),

            # الإجراءات
            ('leaves:unpaid_leave_list', 'إجازات بدون راتب'),
            ('leaves:leave_list', 'قائمة الإجازات'),
            ('leaves:leave_create', 'إضافة إجازة'),
            ('leaves:leave_balance_list', 'رصيد الإجازات'),
            ('leaves:leave_reports', 'تقارير الإجازات'),
            ('disciplinary:penalty_list', 'العقوبات'),
            ('disciplinary:penalty_type_list', 'أنواع العقوبات'),

            # إجازات الموظفين (المديرية)
            ('directorate_leaves:whatsapp_send', 'إرسال (واتس اب)'),

            # الملفات
            ('file_management:file_movement_list', 'حركة الملف'),
            ('file_management:file_checkout', 'تسجيل خروج ملف'),
            ('file_management:file_return_list', 'الملفات المنجزة'),

            # الرتب
            ('ranks:rank_type_list', 'أنواع الرتب'),
            ('ranks:employee_rank_create', 'إضافة رتبة للموظف'),
            ('ranks:employee_rank_list', 'رتب الموظفين'),

            # التقارير السنوية
            ('performance:performance_list', 'التقارير السنوية'),

            # تقارير النظام
            ('reports:report_dashboard', 'تقارير النظام'),

            # المستخدمين
            ('accounts:user_list', 'المستخدمين'),

            # النسخ الاحتياطية
            ('backup:backup_list', 'النسخ الاحتياطية'),

            # سجل حركات النظام
            ('system_logs:system_log_list', 'سجل حركات النظام'),
            ('system_logs:system_error_list', 'سجل أخطاء النظام'),
            ('system_logs:system_error_detail', 'تفاصيل الخطأ'),
            
            # الإعلانات
            ('announcements:announcements_list', 'قائمة الإعلانات'),
            ('announcements:announcement_create', 'إضافة إعلان'),
            ('announcements:announcement_detail', 'تفاصيل الإعلان'),
            ('announcements:announcement_update', 'تعديل الإعلان'),
            ('announcements:announcement_delete', 'حذف الإعلان'),
            ('announcements:announcement_public_view', 'عرض الإعلان العام'),
            ('announcements:announcement_toggle_status', 'تغيير حالة الإعلان'),
            ('announcements:announcement_click_tracking', 'تتبع النقرات'),
            ('announcements:get_homepage_announcements', 'إعلانات الصفحة الرئيسية'),
            
            # الإشعارات
            ('notifications:notification_list', 'قائمة الإشعارات'),

            # الصفحة الرئيسية - النقل الداخلي
            ('home:home', 'الصفحة الرئيسية'),
            ('home:analytics_dashboard', 'لوحة التحليلات'),
            ('home:important_links', 'الروابط المهمة'),
            ('home:internal_transfer', 'طلب النقل الداخلي'),
            ('home:search_employee', 'البحث عن موظف'),
            ('home:search_transfer_request', 'البحث عن طلب النقل'),
            ('home:internal_transfer_success', 'نجاح طلب النقل'),
            ('home:internal_transfer_edit', 'تعديل طلب النقل'),
            ('home:about', 'حول النظام'),
            ('home:search', 'البحث'),

            # إدارة طلبات النقل الداخلي
            ('home:internal_transfer_list', 'قائمة طلبات النقل الداخلي'),
            ('home:internal_transfer_detail', 'تفاصيل طلب النقل الداخلي'),
            ('home:internal_transfer_delete', 'حذف طلب النقل الداخلي'),
            ('home:toggle_internal_transfer', 'تفعيل/إلغاء النقل الداخلي'),
            ('home:enable_internal_transfer', 'تفعيل النقل الداخلي'),
            ('home:disable_internal_transfer', 'إلغاء النقل الداخلي'),
            ('home:delete_all_internal_transfers', 'حذف جميع طلبات النقل'),
            ('home:export_internal_transfers', 'تصدير طلبات النقل'),
            ('home:print_transfer_letters', 'طباعة كتب النقل الداخلي'),
            ('home:print_single_transfer_letter', 'طباعة كتاب نقل فردي'),
            ('home:print_transfer_summary', 'طباعة ملخص النقل'),
            ('home:admin_internal_transfer_create', 'إنشاء طلب نقل (مدير)'),

            # النقل الفني
            ('home:technical_transfer', 'النقل الفني'),
            ('home:technical_transfer_search', 'البحث في النقل الفني'),
            ('home:technical_transfer_list', 'قائمة النقل الفني'),
            ('home:technical_transfer_print', 'طباعة النقل الفني'),
            ('home:technical_transfer_delete', 'حذف النقل الفني'),

            # النماذج المعتمدة
            ('home:approved_forms_list', 'قائمة النماذج المعتمدة'),
            ('home:approved_forms_admin', 'إدارة النماذج المعتمدة'),
            ('home:approved_form_create', 'إنشاء نموذج معتمد'),
            ('home:approved_form_update', 'تحديث نموذج معتمد'),
            ('home:approved_form_delete', 'حذف نموذج معتمد'),
            ('home:approved_form_download', 'تحميل نموذج معتمد'),

            # استعلام رصيد الإجازات
            ('home:leave_balance_inquiry', 'استعلام رصيد الإجازات'),
            ('home:get_employee_leave_balance', 'الحصول على رصيد إجازات الموظف'),

            # استعلام بيانات الموظف
            ('home:employee_inquiry', 'استعلام بيانات الموظف'),
            ('home:get_employee_details', 'الحصول على تفاصيل الموظف'),

            # إدارة الروابط المهمة
            ('home:important_links_admin', 'إدارة الروابط المهمة'),
            ('home:important_link_add', 'إضافة رابط مهم'),
            ('home:important_link_update', 'تحديث رابط مهم'),
            ('home:important_link_delete', 'حذف رابط مهم'),

            # إدارة نقل الموظفين الجديدة
            ('home:employee_transfer_management', 'إدارة نقل الموظفين'),
            ('home:search_employee_for_transfer', 'البحث عن موظف للنقل'),
            ('home:add_employee_transfer', 'إضافة نقل موظف'),
            ('home:update_employee_transfer', 'تحديث نقل موظف'),
            ('home:delete_employee_transfer', 'حذف نقل موظف'),
            ('home:print_transfer_letter', 'طباعة كتاب النقل'),
        ]

    def clean(self):
        cleaned_data = super().clean()

        # Get the permission level
        permission_level = cleaned_data.get('permission_level')

        # Set permissions based on the selected level
        if permission_level == 'admin':
            cleaned_data['can_view'] = True
            cleaned_data['can_add'] = True
            cleaned_data['can_edit'] = True
            cleaned_data['can_delete'] = True
        elif permission_level == 'supervisor':
            cleaned_data['can_view'] = True
            cleaned_data['can_add'] = True
            cleaned_data['can_edit'] = True
            cleaned_data['can_delete'] = False
        elif permission_level == 'user':
            cleaned_data['can_view'] = True
            cleaned_data['can_add'] = True
            cleaned_data['can_edit'] = False
            cleaned_data['can_delete'] = False
        elif permission_level == 'readonly':
            cleaned_data['can_view'] = True
            cleaned_data['can_add'] = False
            cleaned_data['can_edit'] = False
            cleaned_data['can_delete'] = False

        # Get all pages for the selected module
        module_name = cleaned_data.get('module_name')
        if module_name:
            all_pages = [page[0] for page in self.get_pages_choices() if page[0].startswith(f'{module_name}:')]
            cleaned_data['visible_pages'] = ','.join(all_pages)
            print(f"Setting all {len(all_pages)} pages as visible for module {module_name}")

        # Make sure module_name is set
        if 'module_name' not in cleaned_data and 'module_name' in self.data:
            cleaned_data['module_name'] = self.data['module_name']

        # Debug output
        print(f"Cleaned data: {cleaned_data}")

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Set permissions based on the permission level
        permission_level = self.cleaned_data.get('permission_level')
        if permission_level == 'admin':
            instance.can_view = True
            instance.can_add = True
            instance.can_edit = True
            instance.can_delete = True
        elif permission_level == 'supervisor':
            instance.can_view = True
            instance.can_add = True
            instance.can_edit = True
            instance.can_delete = False
        elif permission_level == 'user':
            instance.can_view = True
            instance.can_add = True
            instance.can_edit = False
            instance.can_delete = False
        elif permission_level == 'readonly':
            instance.can_view = True
            instance.can_add = False
            instance.can_edit = False
            instance.can_delete = False

        # Get all pages for the selected module
        module_name = self.cleaned_data.get('module_name')
        if module_name:
            all_pages = [page[0] for page in self.get_pages_choices() if page[0].startswith(f'{module_name}:')]
            instance.visible_pages = ','.join(all_pages)

        if commit:
            instance.save()
        return instance
