{% extends 'base.html' %}
{% load static %}

{% block title %}العلاوات{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .filter-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table th {
        background-color: #495057;
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
    }

    .table td {
        text-align: center;
        vertical-align: middle;
        border-color: #dee2e6;
    }

    .badge-yes {
        background-color: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .badge-no {
        background-color: #dc3545;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .btn-group-actions {
        display: flex;
        gap: 5px;
        justify-content: center;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 15px 15px;
    }

    .export-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-block;
        margin-left: 10px;
    }

    .export-btn:hover {
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0">
                    <i class="fas fa-money-bill-wave me-3"></i>
                    العلاوات
                </h1>
                <p class="mb-0 mt-2">إدارة علاوات الموظفين</p>
            </div>
            <div class="col-md-6 text-end">
                <a href="{% url 'employment:employee_allowance_create' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    إضافة علاوات موظف
                </a>
                <a href="?export=excel{% if search_term %}&search={{ search_term }}{% endif %}{% if education_allowance %}&education_allowance={{ education_allowance }}{% endif %}{% if adjustment_allowance %}&adjustment_allowance={{ adjustment_allowance }}{% endif %}{% if transportation_allowance %}&transportation_allowance={{ transportation_allowance }}{% endif %}{% if supervisory_allowance %}&supervisory_allowance={{ supervisory_allowance }}{% endif %}{% if technical_allowance %}&technical_allowance={{ technical_allowance }}{% endif %}" 
                   class="export-btn">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير إلى Excel
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ total_allowances }}</div>
                <div class="stats-label">إجمالي السجلات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ education_count }}</div>
                <div class="stats-label">علاوة التعليم</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ adjustment_count }}</div>
                <div class="stats-label">التجيير</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ transportation_count }}</div>
                <div class="stats-label">التنقلات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ supervisory_count }}</div>
                <div class="stats-label">العلاوة الإشرافية</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number">{{ technical_count }}</div>
                <div class="stats-label">علاوة فنية</div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                {{ search_form.search_term.label_tag }}
                {{ search_form.search_term }}
            </div>
            <div class="col-md-2">
                {{ search_form.education_allowance.label_tag }}
                {{ search_form.education_allowance }}
            </div>
            <div class="col-md-2">
                {{ search_form.adjustment_allowance.label_tag }}
                {{ search_form.adjustment_allowance }}
            </div>
            <div class="col-md-2">
                {{ search_form.transportation_allowance.label_tag }}
                {{ search_form.transportation_allowance }}
            </div>
            <div class="col-md-2">
                {{ search_form.supervisory_allowance.label_tag }}
                {{ search_form.supervisory_allowance }}
            </div>
            <div class="col-md-1">
                <label>&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
        <div class="row mt-3">
            <div class="col-md-2">
                {{ search_form.technical_allowance.label_tag }}
                {{ search_form.technical_allowance }}
            </div>
            <div class="col-md-2">
                <label>&nbsp;</label>
                <div>
                    <a href="{% url 'employment:employee_allowance_list' %}" class="btn btn-secondary w-100">
                        <i class="fas fa-times"></i> إلغاء الفلتر
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="table-container">
        {% if allowances %}
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الرقم الوزاري</th>
                    <th>الاسم الكامل</th>
                    <th>علاوة التعليم</th>
                    <th>التجيير</th>
                    <th>التنقلات</th>
                    <th>العلاوة الإشرافية</th>
                    <th>علاوة فنية</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for allowance in allowances %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ allowance.ministry_number|default:'-' }}</td>
                    <td>{{ allowance.employee.full_name }}</td>
                    <td>
                        {% if allowance.education_allowance == 'yes' %}
                            <span class="badge-yes">نعم</span>
                        {% else %}
                            <span class="badge-no">لا</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if allowance.adjustment_allowance == 'yes' %}
                            <span class="badge-yes">نعم</span>
                        {% else %}
                            <span class="badge-no">لا</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if allowance.transportation_allowance == 'yes' %}
                            <span class="badge-yes">نعم</span>
                        {% else %}
                            <span class="badge-no">لا</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if allowance.supervisory_allowance == 'yes' %}
                            <span class="badge-yes">نعم</span>
                        {% else %}
                            <span class="badge-no">لا</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if allowance.technical_allowance == 'yes' %}
                            <span class="badge-yes">نعم</span>
                        {% else %}
                            <span class="badge-no">لا</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group-actions">
                            <a href="{% url 'employment:employee_allowance_update' allowance.pk %}" 
                               class="btn btn-warning btn-sm" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'employment:employee_allowance_delete' allowance.pk %}" 
                               class="btn btn-danger btn-sm" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد سجلات علاوات</h5>
            <p class="text-muted">لم يتم العثور على أي سجلات علاوات تطابق معايير البحث</p>
            <a href="{% url 'employment:employee_allowance_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أول سجل علاوات
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
