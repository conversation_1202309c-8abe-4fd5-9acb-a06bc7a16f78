{% extends 'base.html' %}

{% block title %}تفاصيل إجازة الأمومة - {{ maternity_leave.employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل إجازة الأمومة</h6>
                    <div>
                        <a href="{% url 'employees:maternity_leave_update' maternity_leave.pk %}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i> تعديل
                        </a>
                        <a href="{% url 'employees:maternity_leaves_list' %}" class="btn btn-dark text-white">
                            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Employee Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-user me-2"></i>معلومات الموظفة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الرقم الوزاري:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.employee.ministry_number }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الاسم الكامل:</label>
                                        <p class="form-control-plaintext">
                                            <a href="{% url 'employees:employee_detail' maternity_leave.employee.id %}">
                                                {{ maternity_leave.employee.full_name }}
                                            </a>
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">التخصص:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.employee.specialization }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">القسم:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.employee.school }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">المسمى الوظيفي:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.employee.position_name|default:"-" }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الجنس:</label>
                                        <p class="form-control-plaintext">
                                            {% if maternity_leave.employee.gender == 'female' %}
                                                أنثى
                                            {% else %}
                                                ذكر
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Maternity Leave Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-baby me-2"></i>معلومات إجازة الأمومة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">تاريخ بداية الإجازة:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.start_date|date:"Y-m-d" }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">تاريخ انتهاء الإجازة:</label>
                                        <p class="form-control-plaintext">{{ maternity_leave.end_date|date:"Y-m-d" }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">حالة الإجازة:</label>
                                        <p class="form-control-plaintext">
                                            {% if maternity_leave.is_active %}
                                                <span class="badge bg-success fs-6">نشطة</span>
                                            {% else %}
                                                <span class="badge bg-secondary fs-6">منتهية</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">مدة الإجازة:</label>
                                        <p class="form-control-plaintext">90 يوماً</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if maternity_leave.notes %}
                    <hr>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-sticky-note me-2"></i>الملاحظات</h5>
                            <div class="alert alert-light">
                                {{ maternity_leave.notes|linebreaks }}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <hr>

                    <!-- System Information -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-muted mb-3"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">تاريخ الإنشاء:</label>
                                        <p class="form-control-plaintext text-muted">{{ maternity_leave.created_at|date:"Y-m-d H:i" }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-muted">آخر تحديث:</label>
                                        <p class="form-control-plaintext text-muted">{{ maternity_leave.updated_at|date:"Y-m-d H:i" }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">حالة الإجازة</h6>
                </div>
                <div class="card-body text-center">
                    {% if maternity_leave.is_active %}
                        <div class="text-success mb-3">
                            <i class="fas fa-check-circle fa-3x"></i>
                        </div>
                        <h5 class="text-success">إجازة نشطة</h5>
                        <p class="text-muted">الإجازة قيد التنفيذ حالياً</p>
                    {% else %}
                        <div class="text-secondary mb-3">
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                        <h5 class="text-secondary">إجازة منتهية</h5>
                        <p class="text-muted">انتهت الإجازة في {{ maternity_leave.end_date|date:"Y-m-d" }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'employees:maternity_leave_update' maternity_leave.pk %}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>تعديل الإجازة
                        </a>
                        <a href="{% url 'employees:employee_detail' maternity_leave.employee.id %}" class="btn btn-info">
                            <i class="fas fa-user me-2"></i>عرض الموظفة
                        </a>
                        <a href="{% url 'employees:maternity_leave_delete' maternity_leave.pk %}" class="btn btn-danger" 
                           onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟')">
                            <i class="fas fa-trash me-2"></i>حذف الإجازة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Duration Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المدة</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border-bottom pb-2">
                                <h4 class="text-primary">90</h4>
                                <small class="text-muted">يوماً</small>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <small class="text-muted">
                            من {{ maternity_leave.start_date|date:"Y-m-d" }}<br>
                            إلى {{ maternity_leave.end_date|date:"Y-m-d" }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}