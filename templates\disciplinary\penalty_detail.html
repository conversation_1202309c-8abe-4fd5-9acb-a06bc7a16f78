{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل العقوبة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل العقوبة</h1>
        <div>
            <a href="{% url 'disciplinary:penalty_update' penalty.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{% url 'disciplinary:penalty_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات العقوبة</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">الموظف</th>
                            <td>
                                <a href="{% url 'employees:employee_detail' penalty.employee.pk %}">
                                    {{ penalty.employee.full_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th class="bg-light">الرقم الوزاري</th>
                            <td>{{ penalty.employee.ministry_number }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">نوع العقوبة</th>
                            <td>{{ penalty.penalty_type.name }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">التاريخ</th>
                            <td>{{ penalty.date|date:"Y-m-d" }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th class="bg-light">رقم القرار</th>
                            <td>{{ penalty.decision_number|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ القرار</th>
                            <td>{% if penalty.decision_date %}{{ penalty.decision_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">تاريخ الإنشاء</th>
                            <td>{{ penalty.created_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                        <tr>
                            <th class="bg-light">آخر تحديث</th>
                            <td>{{ penalty.updated_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">الوصف</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ penalty.description|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
