{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة مؤهل علمي جديد - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .search-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    .employee-info {
        background-color: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #2196f3;
        margin-bottom: 20px;
        display: none;
    }
    
    .qualification-form {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        display: none;
    }
    
    .form-control:disabled {
        background-color: #f8f9fa;
        opacity: 0.6;
    }
    
    .qualification-field {
        margin-bottom: 20px;
    }
    
    .qualification-field label {
        font-weight: bold;
        color: #495057;
    }
    
    .alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-graduation-cap me-2"></i>إضافة مؤهل علمي جديد</h2>
        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لبيانات الموظفين
        </a>
    </div>

    <!-- Search Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-search"></i> البحث عن الموظف
            </h6>
        </div>
        <div class="card-body">
            <div class="search-section">
                <div class="row">
                    <div class="col-md-6">
                        <label for="ministry_number" class="form-label">
                            <i class="fas fa-id-card text-primary"></i> الرقم الوزاري
                        </label>
                        <input type="text" class="form-control" id="ministry_number" placeholder="أدخل الرقم الوزاري للموظف">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-primary" id="searchBtn">
                            <i class="fas fa-search"></i> البحث
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Info Section -->
    <div class="card shadow mb-4" id="employeeInfoCard">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-user"></i> بيانات الموظف
            </h6>
        </div>
        <div class="card-body">
            <div class="employee-info" id="employeeInfo">
                <div class="row">
                    <div class="col-md-4">
                        <strong>الاسم الرباعي:</strong>
                        <span id="employeeName"></span>
                    </div>
                    <div class="col-md-4">
                        <strong>الرقم الوزاري:</strong>
                        <span id="employeeMinistryNumber"></span>
                    </div>
                    <div class="col-md-4">
                        <strong>المؤهل الحالي:</strong>
                        <span id="currentQualification"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Qualification Form Section -->
    <div class="card shadow mb-4" id="qualificationFormCard">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-certificate"></i> إضافة المؤهلات العلمية
            </h6>
        </div>
        <div class="card-body">
            <div class="qualification-form" id="qualificationForm">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> يجب إدخال المؤهل العلمي (ماجستير) قبل إدخال المؤهل العلمي (دكتوراه)
                </div>
                
                <form method="post" id="addQualificationForm">
                    {% csrf_token %}
                    <input type="hidden" id="employee_id" name="employee_id">
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="qualification-field">
                                <label for="post_graduate_diploma" class="form-label">
                                    <i class="fas fa-certificate text-success"></i>
                                    المؤهل العلمي (دبلوم بعد البكالوريس)
                                </label>
                                <input type="text" class="form-control" id="post_graduate_diploma" 
                                       name="post_graduate_diploma" placeholder="أدخل المؤهل العلمي">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="qualification-field">
                                <label for="masters_degree" class="form-label">
                                    <i class="fas fa-user-graduate text-info"></i>
                                    المؤهل العلمي (ماجستير)
                                </label>
                                <input type="text" class="form-control" id="masters_degree" 
                                       name="masters_degree" placeholder="أدخل المؤهل العلمي">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="qualification-field">
                                <label for="phd_degree" class="form-label">
                                    <i class="fas fa-award text-warning"></i>
                                    المؤهل العلمي (دكتوراه)
                                </label>
                                <input type="text" class="form-control" id="phd_degree" 
                                       name="phd_degree" placeholder="أدخل المؤهل العلمي" disabled>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save"></i> حفظ المؤهلات العلمية
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة ضبط
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Search for employee
    $('#searchBtn').click(function() {
        var ministryNumber = $('#ministry_number').val().trim();
        
        if (!ministryNumber) {
            alert('يرجى إدخال الرقم الوزاري');
            return;
        }
        
        $.ajax({
            url: "{% url 'employees:search_employee_for_qualification' %}",
            data: {
                'ministry_number': ministryNumber
            },
            success: function(response) {
                if (response.success) {
                    // Show employee info
                    $('#employeeName').text(response.employee.full_name);
                    $('#employeeMinistryNumber').text(response.employee.ministry_number);
                    $('#currentQualification').text(response.employee.qualification || 'غير محدد');
                    $('#employee_id').val(response.employee.id);
                    
                    // Fill existing qualifications
                    $('#post_graduate_diploma').val(response.employee.post_graduate_diploma);
                    $('#masters_degree').val(response.employee.masters_degree);
                    $('#phd_degree').val(response.employee.phd_degree);
                    
                    // Show sections
                    $('#employeeInfo').show();
                    $('#qualificationForm').show();
                    
                    // Check PhD field availability
                    checkPhdAvailability();
                } else {
                    alert(response.error);
                    $('#employeeInfo').hide();
                    $('#qualificationForm').hide();
                }
            },
            error: function() {
                alert('حدث خطأ أثناء البحث');
            }
        });
    });
    
    // Enable search on Enter key
    $('#ministry_number').keypress(function(e) {
        if (e.which == 13) {
            $('#searchBtn').click();
        }
    });
    
    // Check PhD field availability when masters field changes
    $('#masters_degree').on('input', function() {
        checkPhdAvailability();
    });
    
    function checkPhdAvailability() {
        var mastersValue = $('#masters_degree').val().trim();
        var phdField = $('#phd_degree');
        
        if (mastersValue) {
            phdField.prop('disabled', false);
            phdField.removeClass('disabled');
        } else {
            phdField.prop('disabled', true);
            phdField.addClass('disabled');
            phdField.val(''); // Clear PhD field if masters is empty
        }
    }
    
    // Form validation
    $('#addQualificationForm').submit(function(e) {
        var postGrad = $('#post_graduate_diploma').val().trim();
        var masters = $('#masters_degree').val().trim();
        var phd = $('#phd_degree').val().trim();
        
        if (!postGrad && !masters && !phd) {
            e.preventDefault();
            alert('يرجى إدخال مؤهل علمي واحد على الأقل');
            return false;
        }
        
        if (phd && !masters) {
            e.preventDefault();
            alert('يجب إدخال المؤهل العلمي (ماجستير) قبل إدخال المؤهل العلمي (دكتوراه)');
            return false;
        }
        
        return true;
    });
});

function resetForm() {
    $('#ministry_number').val('');
    $('#post_graduate_diploma').val('');
    $('#masters_degree').val('');
    $('#phd_degree').val('');
    $('#employee_id').val('');
    $('#employeeInfo').hide();
    $('#qualificationForm').hide();
    $('#phd_degree').prop('disabled', true);
}
</script>
{% endblock %}