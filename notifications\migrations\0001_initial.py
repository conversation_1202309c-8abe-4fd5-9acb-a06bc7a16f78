# Generated by Django 5.2 on 2025-05-24 19:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('success', 'نجاح'), ('warning', 'تحذير'), ('error', 'خطأ')], default='info', max_length=20, verbose_name='نوع الإشعار')),
                ('icon', models.CharField(choices=[('fa-user-plus', 'إضافة مستخدم'), ('fa-check-circle', 'تم بنجاح'), ('fa-exclamation-triangle', 'تحذير'), ('fa-times-circle', 'خطأ'), ('fa-info-circle', 'معلومات'), ('fa-bell', 'إشعار عام'), ('fa-file-alt', 'ملف'), ('fa-calendar', 'تاريخ'), ('fa-user', 'مستخدم'), ('fa-cog', 'إعدادات')], default='fa-bell', max_length=50, verbose_name='الأيقونة')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_global', models.BooleanField(default=False, verbose_name='إشعار عام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('user', models.ForeignKey(blank=True, help_text='إذا تُرك فارغاً، سيظهر الإشعار لجميع المستخدمين', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
    ]
