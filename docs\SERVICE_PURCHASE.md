# ميزة شراء الخدمات - Service Purchase Feature

## نظرة عامة
ميزة شراء الخدمات تسمح بإدارة الموظفين الذين قاموا بشراء سنوات خدمة إضافية. عند إضافة موظف إلى قائمة شراء الخدمات، يتم نقله تلقائياً من قائمة الموظفين العادية إلى قائمة شراء الخدمات.

## الميزات الرئيسية

### 1. إدارة شراء الخدمات
- إضافة موظف جديد لشراء الخدمات
- تعديل بيانات شراء الخدمة
- حذف شراء الخدمة (إرجاع الموظف للقائمة العادية)
- عرض قائمة جميع الموظفين الذين اشتروا خدمات

### 2. البيانات المطلوبة
- **الموظف**: اختيار من قائمة الموظفين المتاحين
- **تاريخ الشراء**: تاريخ شراء الخدمة
- **فترة الخدمة المشتراة**:
  - السنوات (بالأرقام العشرية)
  - الأشهر (0-11)
  - الأيام (0-30)
- **مبلغ الشراء**: المبلغ المدفوع بالدينار العراقي
- **ملاحظات**: ملاحظات اختيارية

### 3. التصفية والبحث
- البحث بالاسم أو الرقم الوزاري
- تصفية حسب التخصص
- تصفية حسب المدرسة

### 4. التصدير
- تصدير البيانات إلى ملف Excel
- تنسيق احترافي مع الألوان والحدود
- عرض جميع البيانات المهمة

## كيفية الوصول للميزة

### من صفحة بيانات الموظفين
1. اذهب إلى صفحة "بيانات الموظفين"
2. انقر على زر "شراء الخدمات" (أخضر اللون)
3. ستنتقل إلى صفحة قائمة شراء الخدمات

### من القائمة الجانبية
- Employment → Service Purchase

## استخدام الميزة

### إضافة شراء خدمة جديد
1. في صفحة قائمة شراء الخدمات، انقر على "إضافة شراء خدمة"
2. اختر الموظف من القائمة المنسدلة
3. أدخل تاريخ الشراء
4. أدخل فترة الخدمة (سنوات، أشهر، أيام)
5. أدخل مبلغ الشراء
6. أضف ملاحظات إن وجدت
7. انقر على "إضافة شراء الخدمة"

### تعديل شراء خدمة
1. في قائمة شراء الخدمات، انقر على زر "تعديل" (أصفر)
2. عدّل البيانات المطلوبة
3. انقر على "تحديث شراء الخدمة"

### حذف شراء خدمة
1. انقر على زر "حذف" (أحمر)
2. أكد الحذف في الصفحة التالية
3. سيتم إرجاع الموظف إلى قائمة الموظفين العادية

## التقارير والإحصائيات

### الإحصائيات المعروضة
- إجمالي عدد شراء الخدمات
- عدد النتائج المعروضة
- إجمالي المبالغ المدفوعة
- متوسط مبلغ الشراء

### تصدير Excel
- انقر على زر "تصدير إلى Excel"
- سيتم تحميل ملف Excel يحتوي على:
  - الرقم التسلسلي
  - الرقم الوزاري
  - الاسم الكامل
  - التخصص
  - المدرسة
  - تاريخ الشراء
  - سنوات الخدمة
  - أشهر الخدمة
  - أيام الخدمة
  - إجمالي فترة الخدمة
  - مبلغ الشراء
  - الملاحظات

## التكامل مع النظام

### تأثير على قوائم الموظفين
- الموظفون الذين لديهم شراء خدمة نشط لا يظهرون في قائمة الموظفين العادية
- عند حذف شراء الخدمة، يعود الموظف إلى القائمة العادية

### التكامل مع الإشعارات
- إشعار عند إضافة شراء خدمة جديد
- إشعار عند تعديل شراء خدمة
- إشعار عند حذف شراء خدمة

### التكامل مع سجل النظام
- تسجيل جميع العمليات في سجل النظام
- تتبع المستخدم والوقت والإجراء المتخذ

## الأمان والصلاحيات
- يتطلب تسجيل الدخول للوصول للميزة
- جميع العمليات مسجلة في سجل النظام
- التحقق من صحة البيانات قبل الحفظ

## الملفات المتعلقة بالميزة

### Models
- `employment/models.py` - نموذج ServicePurchase

### Views
- `employment/views.py` - views شراء الخدمات

### Forms
- `employment/forms.py` - نماذج شراء الخدمات

### Templates
- `templates/employment/service_purchase_list.html`
- `templates/employment/service_purchase_form.html`
- `templates/employment/service_purchase_confirm_delete.html`

### Static Files
- `static/css/service_purchase.css`
- `static/js/service_purchase.js`

### URLs
- `employment/urls.py` - مسارات شراء الخدمات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الموظف لا يظهر في قائمة الاختيار**
   - تأكد أن الموظف ليس متقاعداً
   - تأكد أن الموظف ليس منقولاً خارجياً
   - تأكد أن الموظف ليس لديه شراء خدمة نشط مسبقاً

2. **خطأ في حفظ البيانات**
   - تأكد من ملء جميع الحقول المطلوبة
   - تأكد من صحة تنسيق التاريخ
   - تأكد من أن مبلغ الشراء أكبر من صفر

3. **مشاكل في التصدير**
   - تأكد من تثبيت مكتبة openpyxl
   - تحقق من صلاحيات الكتابة في مجلد التحميل

## التحديثات المستقبلية
- إضافة تقارير مفصلة أكثر
- إضافة إمكانية تصدير PDF
- إضافة إحصائيات متقدمة
- إضافة إمكانية استيراد البيانات من Excel