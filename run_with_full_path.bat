@echo off
echo ===================================
echo تشغيل نظام الموارد البشرية باستخدام المسار الكامل لبايثون
echo ===================================
echo.

REM محاولة تشغيل النظام باستخدام المسارات المحتملة لبايثون
echo محاولة تشغيل النظام...

if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" (
    echo استخدام بايثون من متجر مايكروسوفت...
    "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" (
    echo استخدام بايثون 3 من متجر مايكروسوفت...
    "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo استخدام بايثون 3.11...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" (
    echo استخدام بايثون 3.10...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
    echo استخدام بايثون 3.9...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Python311\python.exe" (
    echo استخدام بايثون 3.11...
    C:\Python311\python.exe manage.py runserver
    goto :end
)

if exist "C:\Python310\python.exe" (
    echo استخدام بايثون 3.10...
    C:\Python310\python.exe manage.py runserver
    goto :end
)

if exist "C:\Python39\python.exe" (
    echo استخدام بايثون 3.9...
    C:\Python39\python.exe manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python311\python.exe" (
    echo استخدام بايثون 3.11...
    "C:\Program Files\Python311\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python310\python.exe" (
    echo استخدام بايثون 3.10...
    "C:\Program Files\Python310\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python39\python.exe" (
    echo استخدام بايثون 3.9...
    "C:\Program Files\Python39\python.exe" manage.py runserver
    goto :end
)

echo لم يتم العثور على بايثون. يرجى تثبيت بايثون أولاً.
echo يمكنك استخدام ملف simple_install_python.bat أو install_python_from_web.bat لتثبيت بايثون.

:end
echo.
echo انتهى التنفيذ. اضغط أي مفتاح للخروج...
pause > nul
