{% extends 'base.html' %}

{% block title %}طلبات النقل الداخلي{% endblock %}

{% block extra_css %}
<style>
    /* تنسيق بسيط للفلاتر */
    .filters-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
    }

    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: 38px;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تحسين مظهر الأزرار */
    .btn-filter {
        padding: 8px 12px;
        height: 38px;
    }

    /* تنسيق للفلاتر في صف واحد */
    .filters-row {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    /* تنسيق الأزرار في سطر واحد */
    .buttons-row {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 5px;
    }

    .buttons-row .btn {
        font-size: 0.9rem;
        padding: 10px 15px;
        margin-left: 5px;
        flex-shrink: 0;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .buttons-row {
            justify-content: flex-start !important;
            overflow-x: auto;
        }

        .buttons-row .btn {
            font-size: 0.8rem;
            padding: 6px 10px;
            margin-left: 3px;
        }

        .col-md-9 .d-flex {
            flex-wrap: wrap;
            gap: 5px;
        }

        .filter-control {
            width: 100% !important;
            margin-right: 0 !important;
            margin-bottom: 5px;
        }

        #applyFiltersBtn, .btn-secondary {
            margin-top: 5px;
        }
    }

    /* تنسيق حقل البحث */
    #searchInput {
        height: 38px;
        font-size: 1rem;
    }

    /* تنسيق الإحصائيات */
    .stats-container {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    .stat-item i {
        margin-left: 0.5rem;
        font-size: 1rem;
    }

    .stat-item .stat-value {
        font-weight: bold;
        margin-right: 0.5rem;
    }

    /* تحسين عرض أزرار الإجراءات */
    .btn-group .btn {
        margin-right: 2px;
        white-space: nowrap;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    /* ضمان ظهور زر الحذف */
    .btn-danger {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        color: white !important;
    }

    .btn-danger:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
    }

    /* تحسين عرض الجدول */
    .table td {
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">طلبات النقل الداخلي</h1>
            <div class="stats-container">
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>إجمالي المتقدمين:</span>
                    <span class="stat-value">{{ transfers|length }}</span>
                </div>

                <div class="stat-item" style="border-color: #ffc107; background-color: #fff8e1;">
                    <i class="fas fa-clock text-warning"></i>
                    <span>قيد الانتظار:</span>
                    <span class="stat-value">{{ pending_count }}</span>
                </div>

                <div class="stat-item" style="border-color: #28a745; background-color: #e8f5e9;">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>تمت الموافقة:</span>
                    <span class="stat-value">{{ approved_count }}</span>
                </div>

                <div class="stat-item" style="border-color: #dc3545; background-color: #ffebee;">
                    <i class="fas fa-times-circle text-danger"></i>
                    <span>مرفوض:</span>
                    <span class="stat-value">{{ rejected_count }}</span>
                </div>
            </div>
        </div>
    </div>
<h3 >قائمة طلبات النقل الداخلي</h3>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <!-- العنوان الرئيسي -->
           
            
            <!-- الأزرار -->
            <div class="d-flex justify-content-end align-items-center mb-3 flex-nowrap buttons-row">
                <a href="{% url 'home:admin_internal_transfer_create' %}" class="btn btn-info me-1" style="white-space: nowrap;">
                    <i class="fas fa-plus"></i> تقديم طلب نقل داخلي
                </a>
                
                <a href="{% url 'home:export_internal_transfers' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success me-1" style="white-space: nowrap;">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>

                <button class="btn btn-danger me-1" type="button" data-bs-toggle="modal" data-bs-target="#deleteAllTransfersModal" style="white-space: nowrap;">
                    <i class="fas fa-trash-alt"></i> حذف جميع الطلبات
                </button>

                <a href="{% url 'home:print_transfer_letters' %}" class="btn btn-primary me-1" style="white-space: nowrap;">
                    <i class="fas fa-print"></i> طباعة كتب النقل الداخلي
                </a>

                {% if settings.internal_transfer_enabled %}
                <button class="btn btn-danger" type="button" data-bs-toggle="modal" data-bs-target="#disableTransferModal" style="white-space: nowrap;">
                    <i class="fas fa-ban"></i> تعطيل صفحة النقل الداخلي
                </button>
                {% else %}
                <form method="post" action="{% url 'home:enable_internal_transfer' %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success" style="white-space: nowrap;">
                        <i class="fas fa-check-circle"></i> تفعيل صفحة النقل الداخلي
                    </button>
                </form>
                {% endif %}
            </div>

            <form id="filterForm" method="GET" action="{% url 'home:internal_transfer_list' %}">
                <div class="filters-container">
                    <div class="row">
                        <!-- Search Form -->
                        <div class="col-md-3 mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control filter-control" id="searchInput" name="search" placeholder="بحث..." value="{{ search_query|default:'' }}" style="height: 38px;">
                                <button class="btn btn-outline-primary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="col-md-9 mb-2">
                            <div class="d-flex">
                                <!-- Filters -->
                                <select class="form-control filter-control me-1" id="statusFilter" name="status" style="width: 150px;">
                                    <option value="">حالة الطلب</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>مرفوض</option>
                                </select>

                                <select class="form-control filter-control me-1" id="yearFilter" name="year" style="width: 100px;">
                                    <option value="">السنة</option>
                                    {% for year in years %}
                                    <option value="{{ year }}" {% if year_filter == year|stringformat:'i' %}selected{% endif %}>{{ year }}</option>
                                    {% endfor %}
                                </select>

                                <select class="form-control filter-control me-1" id="genderFilter" name="gender" style="width: 120px;">
                                    <option value="">الجنس</option>
                                    {% for gender_item in genders %}
                                    <option value="{{ gender_item.gender }}" {% if gender_filter == gender_item.gender %}selected{% endif %}>
                                        {% if gender_item.gender == 'male' %}ذكر{% else %}أنثى{% endif %} ({{ gender_item.count }})
                                    </option>
                                    {% endfor %}
                                </select>

                                <select class="form-control filter-control me-1" id="specializationFilter" name="specialization" style="width: 180px;">
                                    <option value="">التخصص</option>
                                    {% for spec in specializations %}
                                    <option value="{{ spec.specialization }}" {% if specialization_filter == spec.specialization %}selected{% endif %}>
                                        {{ spec.specialization }} ({{ spec.count }})
                                    </option>
                                    {% endfor %}
                                </select>

                                <button type="submit" class="btn btn-outline-primary me-1" id="applyFiltersBtn" title="تطبيق الفلاتر">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                                <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-outline-secondary" title="إعادة تعيين الفلاتر">
                                    <i class="fas fa-redo"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Debug info -->
            <span class="d-none">Status: {{ settings.internal_transfer_enabled }}</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-id-badge me-1"></i> الرقم الوطني</th>
                            <th><i class="fas fa-user me-1"></i> الاسم</th>
                            <th><i class="fas fa-graduation-cap me-1"></i> التخصص</th>
                            <th><i class="fas fa-clock me-1"></i> الخدمة الفعلية</th>
                            <th><i class="fas fa-building me-1"></i> القسم الحالي</th>
                            <th><i class="fas fa-check-circle me-1"></i> الخيار الأول</th>
                            <th><i class="fas fa-check-circle me-1"></i> الخيار الثاني</th>
                            <th><i class="fas fa-check-circle me-1"></i> الخيار الثالث</th>
                            <th><i class="fas fa-calendar-alt me-1"></i> تاريخ الطلب</th>
                            <th><i class="fas fa-flag me-1"></i> الحالة</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transfer in transfers %}
                        <tr>
                            <td>{{ transfer.ministry_number }}</td>
                            <td>{{ transfer.employee_id }}</td>
                            <td>{{ transfer.employee_name }}</td>
                            <td>{{ transfer.specialization|default:"-" }}</td>
                            <td>{{ transfer.actual_service|default:"-" }}</td>
                            <td>{{ transfer.current_department }}</td>
                            <td>{{ transfer.first_choice }}</td>
                            <td>{{ transfer.second_choice|default:"-" }}</td>
                            <td>{{ transfer.third_choice|default:"-" }}</td>
                            <td>{{ transfer.created_at|date:"Y-m-d" }}</td>
                            <td>
                                {% if transfer.status == 'pending' %}
                                <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                {% elif transfer.status == 'approved' %}
                                <span class="badge bg-success">تمت الموافقة</span>
                                {% elif transfer.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوض</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group" aria-label="إجراءات">
                                    <a href="{% url 'home:internal_transfer_detail' transfer.id %}"
                                       class="btn btn-info btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{% url 'home:admin_internal_transfer_edit' pk=transfer.id %}"
                                       class="btn btn-warning btn-sm" title="تعديل الطلب">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'home:internal_transfer_delete' transfer.id %}"
                                       class="btn btn-danger btn-sm" title="حذف الطلب"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="12" class="text-center">لا توجد طلبات نقل داخلي</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<!-- Delete All Transfers Modal -->
<div class="modal fade" id="deleteAllTransfersModal" tabindex="-1" role="dialog" aria-labelledby="deleteAllTransfersModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAllTransfersModalLabel">
                    <i class="fas fa-trash-alt me-2"></i> حذف جميع طلبات النقل الداخلي
                </h5>
                <button class="btn-close btn-close-white" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'home:delete_all_internal_transfers' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير!</strong> سيؤدي هذا الإجراء إلى حذف جميع طلبات النقل الداخلي بشكل نهائي ولا يمكن التراجع عنه.
                    </div>
                    <p>لتأكيد الحذف، يرجى كتابة <strong>DELETE_ALL</strong> في الحقل أدناه:</p>
                    <div class="form-group">
                        <input type="text" class="form-control" id="confirmationInput" name="confirmation" placeholder="اكتب DELETE_ALL للتأكيد" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-1"></i> حذف جميع الطلبات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Disable Internal Transfer Modal -->
<div class="modal fade" id="disableTransferModal" tabindex="-1" role="dialog" aria-labelledby="disableTransferModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="disableTransferModalLabel">
                    <i class="fas fa-ban me-2"></i> تعطيل صفحة النقل الداخلي
                </h5>
                <button class="btn-close btn-close-white" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'home:disable_internal_transfer' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <p>هل أنت متأكد من رغبتك في تعطيل صفحة النقل الداخلي؟ لن يتمكن الموظفون من تقديم طلبات جديدة أو تعديل طلباتهم الحالية.</p>
                    <div class="form-group">
                        <label for="disableMessage">الرسالة التي ستظهر للموظفين:</label>
                        <textarea class="form-control" id="disableMessage" name="message" rows="3">{{ settings.internal_transfer_message }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        تعطيل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable with minimal configuration
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json"
            },
            "order": [[9, "desc"]], // Sort by date (column 9) in descending order
            "searching": false,     // Disable DataTables search as we use our own
            "paging": false,        // Disable pagination as we handle filtering server-side
            "info": false           // Disable showing "Showing X of Y entries"
        });

        // Handle search button click
        $('#searchBtn').on('click', function() {
            $('#filterForm').submit();
        });

        // Handle reset button click
        $('#resetBtn').on('click', function() {
            window.location.href = "{% url 'home:internal_transfer_list' %}";
        });

        // Handle search input enter key
        $('#searchInput').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                $('#filterForm').submit();
            }
        });

        // Ensure disable form submits correctly
        $('#disableTransferModal form').on('submit', function() {
            console.log('Disable form submitted');
            return true;
        });

        // Handle delete all confirmation
        $('#deleteAllTransfersModal form').on('submit', function(e) {
            const confirmation = $('#confirmationInput').val();
            if (confirmation !== 'DELETE_ALL') {
                e.preventDefault();
                alert('يرجى كتابة "DELETE_ALL" بالضبط للتأكيد على حذف جميع الطلبات.');
                return false;
            }
            return true;
        });

        // Reset confirmation input when modal is closed
        $('#deleteAllTransfersModal').on('hidden.bs.modal', function() {
            $('#confirmationInput').val('');
        });
    });
</script>
{% endblock %}
