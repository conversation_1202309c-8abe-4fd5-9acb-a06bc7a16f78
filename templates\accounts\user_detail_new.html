{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}تفاصيل المستخدم{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل المستخدم</h1>
        <a href="{% url 'accounts:user_update' user_obj.pk %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المستخدم</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم المستخدم:</strong> {{ user_obj.username }}</p>
                            <p><strong>الاسم الكامل:</strong> {{ user_obj.get_full_name }}</p>
                            <p><strong>البريد الإلكتروني:</strong> {{ user_obj.email|default:"غير محدد" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الانضمام:</strong> {{ user_obj.date_joined|date:"Y-m-d" }}</p>
                            <p><strong>آخر تسجيل دخول:</strong> {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل الدخول بعد" }}</p>
                            <p><strong>الحالة:</strong> 
                                {% if user_obj.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </p>
                            <p><strong>نوع المستخدم:</strong> 
                                {% if user_obj.is_superuser %}
                                <span class="badge bg-danger">مدير النظام</span>
                                {% elif user_obj.is_admin %}
                                <span class="badge bg-warning">مدير</span>
                                {% else %}
                                <span class="badge bg-info">مستخدم عادي</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الصلاحيات</h6>
                </div>
                <div class="card-body">
                    {% if user_permissions %}
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>القسم</th>
                                <th>عرض</th>
                                <th>إضافة</th>
                                <th>تعديل</th>
                                <th>حذف</th>
                                <th>الصفحات المرئية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for perm in user_permissions %}
                            <tr>
                                <td>
                                    {% if perm.module_name == 'employees' %}بيانات الموظفين
                                    {% elif perm.module_name == 'employment' %}الكادر
                                    {% elif perm.module_name == 'leaves' %}الإجازات
                                    {% elif perm.module_name == 'performance' %}التقارير السنوية
                                    {% elif perm.module_name == 'reports' %}التقارير
                                    {% elif perm.module_name == 'file_management' %}الملفات
                                    {% elif perm.module_name == 'disciplinary' %}العقوبات
                                    {% elif perm.module_name == 'ranks' %}الرتب
                                    {% elif perm.module_name == 'accounts' %}المستخدمين
                                    {% elif perm.module_name == 'backup' %}النسخ الاحتياطي
                                    {% elif perm.module_name == 'directorate_leaves' %}إجازات المديرية
                                    {% else %}{{ perm.module_name }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.can_view %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.can_add %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.can_edit %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.can_delete %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perm.visible_pages %}
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#pagesModal{{ perm.id }}">
                                        عرض الصفحات ({{ perm.visible_pages.split|length }})
                                    </button>

                                    <!-- Modal for pages -->
                                    <div class="modal fade" id="pagesModal{{ perm.id }}" tabindex="-1" aria-labelledby="pagesModalLabel{{ perm.id }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="pagesModalLabel{{ perm.id }}">الصفحات المرئية</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="accordion" id="pagesAccordion{{ perm.id }}">
                                                        {% if perm.visible_pages %}
                                                        {% with pages=perm.visible_pages.split %}
                                                        
                                                        <!-- تقسيم الصفحات حسب الأقسام -->
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header" id="employees-heading{{ perm.id }}">
                                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#employees-collapse{{ perm.id }}" aria-expanded="false" aria-controls="employees-collapse{{ perm.id }}">
                                                                    بيانات الموظفين
                                                                </button>
                                                            </h2>
                                                            <div id="employees-collapse{{ perm.id }}" class="accordion-collapse collapse" aria-labelledby="employees-heading{{ perm.id }}" data-bs-parent="#pagesAccordion{{ perm.id }}">
                                                                <div class="accordion-body">
                                                                    <ul class="list-group">
                                                                        {% for page in pages %}
                                                                            {% if 'employees:' in page %}
                                                                            <li class="list-group-item">
                                                                                {% if page == 'employees:employee_list' %}قائمة الموظفين
                                                                                {% elif page == 'employees:employee_create' %}إضافة موظف
                                                                                {% elif page == 'employees:employee_detail' %}تفاصيل الموظف
                                                                                {% elif page == 'employees:employee_update' %}تعديل بيانات الموظف
                                                                                {% elif page == 'employees:employee_delete' %}حذف موظف
                                                                                {% elif page == 'employees:import_employees' %}استيراد بيانات الموظفين
                                                                                {% elif page == 'employees:export_employees' %}تصدير بيانات الموظفين
                                                                                {% elif page == 'employees:calculate_age' %}حساب العمر
                                                                                {% else %}{{ page }}
                                                                                {% endif %}
                                                                            </li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- الكادر -->
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header" id="employment-heading{{ perm.id }}">
                                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#employment-collapse{{ perm.id }}" aria-expanded="false" aria-controls="employment-collapse{{ perm.id }}">
                                                                    الكادر
                                                                </button>
                                                            </h2>
                                                            <div id="employment-collapse{{ perm.id }}" class="accordion-collapse collapse" aria-labelledby="employment-heading{{ perm.id }}" data-bs-parent="#pagesAccordion{{ perm.id }}">
                                                                <div class="accordion-body">
                                                                    <ul class="list-group">
                                                                        {% for page in pages %}
                                                                            {% if 'employment:' in page %}
                                                                            <li class="list-group-item">
                                                                                {% if page == 'employment:department_list' %}الأقسام
                                                                                {% elif page == 'employment:position_list' %}المسميات الوظيفية
                                                                                {% elif page == 'employment:employee_position_list' %}الحراك الوظيفي
                                                                                {% elif page == 'employment:experience_certificate_list' %}شهادة الخبرة
                                                                                {% elif page == 'employment:technical_position_list' %}الموقف الفني
                                                                                {% elif page == 'employment:actual_service_list' %}الخدمة الفعلية
                                                                                {% else %}{{ page }}
                                                                                {% endif %}
                                                                            </li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- الإجراءات -->
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header" id="leaves-heading{{ perm.id }}">
                                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#leaves-collapse{{ perm.id }}" aria-expanded="false" aria-controls="leaves-collapse{{ perm.id }}">
                                                                    الإجراءات
                                                                </button>
                                                            </h2>
                                                            <div id="leaves-collapse{{ perm.id }}" class="accordion-collapse collapse" aria-labelledby="leaves-heading{{ perm.id }}" data-bs-parent="#pagesAccordion{{ perm.id }}">
                                                                <div class="accordion-body">
                                                                    <ul class="list-group">
                                                                        {% for page in pages %}
                                                                            {% if 'leaves:' in page %}
                                                                            <li class="list-group-item">
                                                                                {% if page == 'leaves:leave_list' %}قائمة الإجازات
                                                                                {% elif page == 'leaves:leave_create' %}إضافة إجازة
                                                                                {% elif page == 'leaves:leave_balance_list' %}رصيد الإجازات
                                                                                {% elif page == 'leaves:leave_reports' %}تقارير الإجازات
                                                                                {% elif page == 'leaves:unpaid_leave_list' %}إجازات بدون راتب
                                                                                {% else %}{{ page }}
                                                                                {% endif %}
                                                                            </li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- إجازات الموظفين (المديرية) -->
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header" id="directorate-leaves-heading{{ perm.id }}">
                                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#directorate-leaves-collapse{{ perm.id }}" aria-expanded="false" aria-controls="directorate-leaves-collapse{{ perm.id }}">
                                                                    إجازات الموظفين (المديرية)
                                                                </button>
                                                            </h2>
                                                            <div id="directorate-leaves-collapse{{ perm.id }}" class="accordion-collapse collapse" aria-labelledby="directorate-leaves-heading{{ perm.id }}" data-bs-parent="#pagesAccordion{{ perm.id }}">
                                                                <div class="accordion-body">
                                                                    <ul class="list-group">
                                                                        {% for page in pages %}
                                                                            {% if 'directorate_leaves:' in page %}
                                                                            <li class="list-group-item">
                                                                                {% if page == 'directorate_leaves:whatsapp_send' %}إرسال (واتس اب)
                                                                                {% else %}{{ page }}
                                                                                {% endif %}
                                                                            </li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- صفحات أخرى -->
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header" id="other-heading{{ perm.id }}">
                                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#other-collapse{{ perm.id }}" aria-expanded="false" aria-controls="other-collapse{{ perm.id }}">
                                                                    صفحات أخرى
                                                                </button>
                                                            </h2>
                                                            <div id="other-collapse{{ perm.id }}" class="accordion-collapse collapse" aria-labelledby="other-heading{{ perm.id }}" data-bs-parent="#pagesAccordion{{ perm.id }}">
                                                                <div class="accordion-body">
                                                                    <ul class="list-group">
                                                                        {% for page in pages %}
                                                                            {% if 'employees:' not in page and 'employment:' not in page and 'leaves:' not in page and 'directorate_leaves:' not in page %}
                                                                            <li class="list-group-item">{{ page }}</li>
                                                                            {% endif %}
                                                                        {% endfor %}
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {% endwith %}
                                                        {% else %}
                                                        <div class="alert alert-info">لا توجد صفحات محددة</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="badge bg-secondary">لا توجد صفحات محددة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% endif %}
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">خيارات متقدمة</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="{% url 'accounts:admin_change_password' user_obj.pk %}" class="btn btn-warning">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </a>
                        <a href="{% url 'accounts:user_delete' user_obj.pk %}" class="btn btn-danger">
                            <i class="fas fa-trash"></i> حذف المستخدم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
