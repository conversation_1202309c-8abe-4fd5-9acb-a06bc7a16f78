"""
URL configuration for hr_system project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from employees import views

# Define error handlers
from hr_system import views as error_views
handler400 = error_views.handler400
handler403 = error_views.handler403
handler404 = error_views.handler404
handler500 = error_views.handler500

urlpatterns = [
    path('admin/', admin.site.urls),

    # Home app URLs (new homepage)
    path('', include('home.urls')),

    # Dashboard (moved to /dashboard/)
    path('dashboard/', views.dashboard, name='dashboard'),

    # App URLs
    path('employees/', include('employees.urls')),
    path('employment/', include('employment.urls')),
    path('leaves/', include('leaves.urls')),
    # path('directorate_leaves/', include('directorate_leaves.urls')),  # Commented out as app doesn't exist
    path('performance/', include('performance.urls')),
    path('reports/', include('reports.urls')),
    path('accounts/', include('accounts.urls')),
    path('backup/', include('backup.urls')),
    path('disciplinary/', include('disciplinary.urls')),
    path('files/', include('file_management.urls')),
    path('ranks/', include('ranks.urls')),
    path('system-logs/', include('system_logs.urls')),
    path('notifications/', include('notifications.urls')),
    path('announcements/', include('announcements.urls')),
]

# Serve static and media files in development
if settings.DEBUG:
    # Serve media files
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Serve static files directly from STATICFILES_DIRS first (original files)
    for static_dir in settings.STATICFILES_DIRS:
        urlpatterns += static(settings.STATIC_URL, document_root=static_dir)

    # Then serve from STATIC_ROOT (collected files)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
