import tkinter as tk
from tkinter import filedialog
import os
import tempfile
import json

def select_folder():
    """
    Open a folder selection dialog and return the selected folder path.
    This function runs in the server and opens a native folder dialog.
    """
    # Hide the main tkinter window
    root = tk.Tk()
    root.withdraw()
    
    # Open the folder dialog
    folder_path = filedialog.askdirectory(
        title="اختر مجلد حفظ النسخة الاحتياطية",
        initialdir=os.path.expanduser("~")
    )
    
    # Close the tkinter window
    root.destroy()
    
    return folder_path

def get_folder_path():
    """
    Get the folder path from the folder dialog and save it to a temporary file.
    Returns the path to the temporary file containing the folder path.
    """
    folder_path = select_folder()
    
    # Create a temporary file to store the folder path
    with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w') as temp_file:
        json.dump({'folder_path': folder_path}, temp_file)
        temp_file_path = temp_file.name
    
    return temp_file_path
