{% extends 'base.html' %}
{% load static %}

{% block title %}{% if employment %}تعديل موظف{% else %}إضافة موظف جديد{% endif %} - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if employment %}تعديل موظف{% else %}إضافة موظف جديد{% endif %}</h2>
    <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للكادر
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات التوظيف</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.employee.id_for_label }}" class="form-label">الموظف</label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.department.id_for_label }}" class="form-label">القسم</label>
                    {{ form.department }}
                    {% if form.department.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.department.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.position.id_for_label }}" class="form-label">المسمى الوظيفي</label>
                    {{ form.position }}
                    {% if form.position.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.position.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.status.id_for_label }}" class="form-label">الحالة</label>
                    {{ form.status }}
                    {% if form.status.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.status.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ التعيين</label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.start_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Hidden fields -->
            <div style="display: none;">
                {{ form.end_date }}
                {{ form.is_current }}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(function(element) {
            element.classList.add('form-check-input');
        });

        // Handle end date based on is_current checkbox
        const isCurrentCheckbox = document.getElementById('{{ form.is_current.id_for_label }}');
        const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');

        if (isCurrentCheckbox && endDateInput) {
            // Set default values for hidden fields
            isCurrentCheckbox.checked = true;
            endDateInput.value = '';
        }
    });
</script>
{% endblock %}
