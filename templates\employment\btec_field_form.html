{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }
    
    .field-preview {
        background: #f8f9ff;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .preview-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 15px;
    }
    
    .preview-content {
        background: white;
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-tags text-primary me-2"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:btec_field_list' %}">حقول BTEC</a></li>
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tag me-2"></i>
                        {{ title }}
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" id="fieldForm">
                        {% csrf_token %}
                        
                        <!-- Field Name -->
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-tag me-2"></i>
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                أدخل اسم حقل BTEC (مثل: تكنولوجيا المعلومات، الهندسة، إدارة الأعمال)
                            </small>
                        </div>

                        <!-- Field Description -->
                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                وصف مختصر للحقل وما يشمله من مواد أو تخصصات (اختياري)
                            </small>
                        </div>

                        <!-- Preview -->
                        <div class="field-preview" id="fieldPreview" style="display: none;">
                            <div class="preview-title">
                                <i class="fas fa-eye me-2"></i>
                                معاينة الحقل
                            </div>
                            <div class="preview-content">
                                <h6 id="previewName" class="text-primary mb-2"></h6>
                                <p id="previewDescription" class="text-muted mb-0"></p>
                            </div>
                        </div>

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-4">
                            <a href="{% url 'employment:btec_field_list' %}" class="btn btn-cancel text-white">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-save text-white">
                                <i class="fas fa-save me-2"></i>حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Live preview functionality
    function updatePreview() {
        const name = $('#id_name').val().trim();
        const description = $('#id_description').val().trim();
        
        if (name) {
            $('#previewName').text(name);
            $('#previewDescription').text(description || 'لا يوجد وصف');
            $('#fieldPreview').show();
        } else {
            $('#fieldPreview').hide();
        }
    }
    
    // Update preview on input
    $('#id_name, #id_description').on('input', updatePreview);
    
    // Initial preview update
    updatePreview();
    
    // Form validation
    $('#fieldForm').on('submit', function(e) {
        const name = $('#id_name').val().trim();
        
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم الحقل');
            $('#id_name').focus();
            return false;
        }
        
        if (name.length < 2) {
            e.preventDefault();
            alert('اسم الحقل يجب أن يكون أكثر من حرفين');
            $('#id_name').focus();
            return false;
        }
    });
    
    // Auto-resize textarea
    $('#id_description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
{% endblock %}
