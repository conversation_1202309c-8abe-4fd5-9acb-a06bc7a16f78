{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير قسم - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تقرير قسم</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.department.id_for_label }}" class="form-label">القسم</label>
                {{ form.department }}
                {% if form.department.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.department.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-export"></i> إنشاء التقرير
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
    </div>
    <div class="card-body">
        <p>يقوم هذا التقرير بإنشاء ملف Excel يحتوي على معلومات عن القسم المحدد والموظفين العاملين فيه.</p>
        <p>يتضمن التقرير المعلومات التالية:</p>
        <ul>
            <li>الرقم الوزاري للموظف</li>
            <li>اسم الموظف</li>
            <li>المنصب</li>
            <li>تاريخ التعيين</li>
            <li>المؤهل العلمي (بكالوريس / دبلوم)</li>
            <li>المؤهل العلمي (دبلوم بعد البكالوريس)</li>
            <li>المؤهل العلمي (ماجستير)</li>
            <li>المؤهل العلمي (دكتوراه)</li>
            <li>التخصص</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
