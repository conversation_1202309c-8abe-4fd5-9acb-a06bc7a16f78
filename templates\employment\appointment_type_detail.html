{% extends 'base.html' %}
{% load static %}

{% block title %}{{ appointment_type.name }} - صفة التعيين - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>صفة التعيين: {{ appointment_type.name }}</h2>
    <div>
        <a href="{% url 'employment:appointment_type_update' appointment_type.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employment:appointment_type_delete' appointment_type.pk %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
        <a href="{% url 'employment:appointment_type_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لصفات التعيين
        </a>
    </div>
</div>

{% if employees %}
<div class="alert alert-warning mb-4">
    <div class="d-flex align-items-center mb-2">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه: لا يمكن حذف صفة التعيين</strong>
    </div>
    <p>لا يمكن حذف صفة التعيين "{{ appointment_type.name }}" لأنها مرتبطة بـ {{ employees|length }} موظف.</p>
    <p class="mb-0">يجب عليك أولاً تغيير صفة التعيين للموظفين المرتبطين بها قبل محاولة الحذف.</p>
</div>
{% endif %}

<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات صفة التعيين</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">اسم صفة التعيين</th>
                        <td>{{ appointment_type.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الوصف</th>
                        <td>{{ appointment_type.description|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الإنشاء</th>
                        <td>{{ appointment_type.created_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">آخر تحديث</th>
                        <td>{{ appointment_type.updated_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">الموظفين بصفة التعيين</h6>
                <a href="#" class="btn btn-sm btn-success">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الرقم الوزاري</th>
                                <th>الاسم الكامل</th>
                                <th>المنصب</th>
                                <th>القسم</th>
                                <th>تاريخ التعيين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employment in employees %}
                            <tr>
                                <td>{{ employment.employee.ministry_number }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employment.employee.pk %}">
                                        {{ employment.employee.full_name }}
                                    </a>
                                </td>
                                <td>{{ employment.position.name }}</td>
                                <td>{{ employment.department.name }}</td>
                                <td>{{ employment.start_date|date:"Y-m-d" }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employment.employee.pk %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">لا يوجد موظفين بهذه صفة التعيين</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
