{% extends 'base.html' %}
{% load static %}

{% block title %}استيراد رتب الموظفين - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>استيراد رتب الموظفين من ملف Excel</h2>
    <div>
        <a href="{% url 'ranks:employee_rank_import' %}?download_template=1" class="btn btn-success">
            <i class="fas fa-download"></i> تنزيل قالب Excel
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لقائمة رتب الموظفين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">استيراد رتب الموظفين</h6>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.excel_file.id_for_label }}" class="form-label">{{ form.excel_file.label }}</label>
                        {{ form.excel_file }}
                        <div class="form-text">{{ form.excel_file.help_text }}</div>
                        {% if form.excel_file.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.excel_file.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.rank_type_mapping.id_for_label }}" class="form-label">{{ form.rank_type_mapping.label }}</label>
                        {{ form.rank_type_mapping }}
                        <div class="form-text">{{ form.rank_type_mapping.help_text }}</div>
                        {% if form.rank_type_mapping.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.rank_type_mapping.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-import"></i> استيراد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">تعليمات الاستيراد</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info alert-permanent">
                    <h5 class="alert-heading">تعليمات الاستيراد</h5>
                    <p>يجب أن يحتوي ملف Excel على الأعمدة التالية:</p>
                    <ul>
                        <li><strong>الرقم الوزاري</strong>: الرقم الوزاري للموظف (مطلوب)</li>
                        <li><strong>نوع الرتبة</strong>: اسم نوع الرتبة (اختياري إذا تم تحديد نوع افتراضي)</li>
                        <li><strong>تاريخ الحصول عليها</strong>: تاريخ الحصول على الرتبة بتنسيق YYYY-MM-DD (اختياري)</li>
                        <li><strong>ملاحظات</strong>: ملاحظات إضافية (اختياري)</li>
                    </ul>
                    <p>ملاحظات:</p>
                    <ul>
                        <li>يمكنك تنزيل قالب Excel من الزر أعلاه</li>
                        <li>إذا كان الموظف موجود بالفعل في النظام وله رتبة من نفس النوع، سيتم تحديث الرتبة الموجودة</li>
                        <li>إذا لم يتم العثور على نوع الرتبة في النظام، سيتم استخدام النوع الافتراضي المحدد</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
