{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .btn-group .btn {
        margin-right: 5px;
    }
    
    .search-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
        padding-left: 12px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
                    <div>
                        <a href="{% url 'home:print_transfer_letters' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة لطباعة كتب النقل
                        </a>
                    </div>
                </div>
                
                <!-- Search and Add Section -->
                <div class="card-body">
                    <div class="search-section">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-plus-circle"></i> إضافة نقل موظف جديد
                        </h6>
                        
                        <form id="addTransferForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="searchTerm" class="form-label">البحث عن الموظف</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchTerm" 
                                               placeholder="أدخل الرقم الوزاري أو اسم الموظف">
                                        <button class="btn btn-primary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="employeeDetails" style="display: none;">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الرقم الوزاري</label>
                                        <input type="text" class="form-control" id="ministryNumber" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="employeeName" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="specialization" readonly>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الخدمة الفعلية</label>
                                        <input type="text" class="form-control" id="actualService" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">القسم الحالي</label>
                                        <input type="text" class="form-control" id="currentDepartment" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">القسم الجديد <span class="text-danger">*</span></label>
                                        <select class="form-control select2" id="newDepartment" required>
                                            <option value="">اختر القسم الجديد...</option>
                                            {% for department in departments %}
                                            <option value="{{ department.id }}">{{ department.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">صفة النقل <span class="text-danger">*</span></label>
                                        <select class="form-control" id="transferType" required>
                                            <option value="">اختر صفة النقل...</option>
                                            <option value="internal_transfer">النقل الداخلي</option>
                                            <option value="temporary_assignment">التكليف المؤقت</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">التنسيب <span class="text-danger">*</span></label>
                                        <select class="form-control" id="endorsement" required>
                                            <option value="">اختر التنسيب...</option>
                                            <option value="admin_financial_manager">تنسيب مدير الشؤون الادارية والمالية</option>
                                            <option value="hr_committee">تنسيب لجنة الموارد البشرية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" rows="2" placeholder="أدخل الملاحظات (اختياري)"></textarea>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary" id="cancelBtn">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ النقل
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Transfers Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الوزاري</th>
                                    <th>الاسم الكامل</th>
                                    <th>التخصص</th>
                                    <th>الخدمة الفعلية</th>
                                    <th>القسم الحالي</th>
                                    <th>القسم الجديد</th>
                                    <th>صفة النقل</th>
                                    <th>التنسيب</th>
                                    <th>ملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transfersTableBody">
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ transfer.ministry_number }}</td>
                                    <td>{{ transfer.employee_name }}</td>
                                    <td>{{ transfer.specialization|default:"-" }}</td>
                                    <td>{{ transfer.actual_service|default:"-" }}</td>
                                    <td>{{ transfer.current_department }}</td>
                                    <td>{{ transfer.new_department.name }}</td>
                                    <td>
                                        {% if transfer.transfer_type == 'internal_transfer' %}
                                            النقل الداخلي
                                        {% else %}
                                            التكليف المؤقت
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transfer.endorsement == 'admin_financial_manager' %}
                                            تنسيب مدير الشؤون الادارية والمالية
                                        {% else %}
                                            تنسيب لجنة الموارد البشرية
                                        {% endif %}
                                    </td>
                                    <td>{{ transfer.notes|default:"-" }}</td>
                                    <td>
                                        <a href="{% url 'home:print_transfer_letter' transfer.id %}" 
                                           class="btn btn-primary btn-sm" target="_blank">
                                            <i class="fas fa-print"></i> طباعة كتاب النقل
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center">لا توجد عمليات نقل مسجلة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'ابحث عن القسم...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
    
    // Search employee
    $('#searchBtn').click(function() {
        const searchTerm = $('#searchTerm').val().trim();
        if (!searchTerm) {
            alert('يرجى إدخال الرقم الوزاري أو اسم الموظف');
            return;
        }
        
        $.post('{% url "home:search_employee_for_transfer" %}', {
            'search_term': searchTerm,
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        }, function(response) {
            if (response.success) {
                const emp = response.employee;
                $('#ministryNumber').val(emp.ministry_number);
                $('#employeeName').val(emp.full_name);
                $('#specialization').val(emp.specialization);
                $('#actualService').val(emp.actual_service);
                $('#currentDepartment').val(emp.current_department);
                $('#employeeDetails').show();
            } else {
                alert(response.error);
                $('#employeeDetails').hide();
            }
        }).fail(function() {
            alert('حدث خطأ أثناء البحث');
        });
    });
    
    // Cancel button
    $('#cancelBtn').click(function() {
        $('#addTransferForm')[0].reset();
        $('#employeeDetails').hide();
        $('.select2').val(null).trigger('change');
    });
    
    // Submit form
    $('#addTransferForm').submit(function(e) {
        e.preventDefault();
        
        const formData = {
            'ministry_number': $('#ministryNumber').val(),
            'employee_name': $('#employeeName').val(),
            'specialization': $('#specialization').val(),
            'actual_service': $('#actualService').val(),
            'current_department': $('#currentDepartment').val(),
            'new_department_id': $('#newDepartment').val(),
            'transfer_type': $('#transferType').val(),
            'endorsement': $('#endorsement').val(),
            'notes': $('#notes').val(),
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        };
        
        $.post('{% url "home:add_employee_transfer" %}', formData, function(response) {
            if (response.success) {
                alert(response.message);
                location.reload();
            } else {
                alert(response.error);
            }
        }).fail(function() {
            alert('حدث خطأ أثناء حفظ البيانات');
        });
    });
});
</script>
{% endblock %}
