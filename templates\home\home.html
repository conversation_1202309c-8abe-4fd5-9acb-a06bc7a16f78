{% extends 'home/base.html' %}
{% load static %}

{% block title %}الصفحة الرئيسية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* استيراد خط عربي جميل */
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
    
    * {
        font-family: 'Cairo', 'Tajawal', sans-serif;
    }
    
    /* Hero Section - تصميم أنيق ومتدرج */
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 35vh;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 0;
    }
    
    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
        animation: heroPattern 20s linear infinite;
    }
    
    @keyframes heroPattern {
        0% { transform: translateX(0) translateY(0); }
        100% { transform: translateX(60px) translateY(60px); }
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: white;
    }
    
    /* Hero Main Layout */
    .hero-main-layout {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 4rem;
        margin-bottom: 3rem;
        min-height: 500px;
    }
    
    .hero-right-side {
        flex: 0 0 45%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        text-align: center;
    }
    
    .hero-left-side {
        flex: 0 0 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: right;
        padding-right: 2rem;
    }
    
    /* Floating Logo */
    .floating-logo {
        position: relative;
        animation: logoFloat 6s ease-in-out infinite;
    }
    
    .main-logo {
        width: 250px;
        height: 250px;
        object-fit: contain;
        background: transparent;
        border-radius: 50%;
        padding: 0;
        box-shadow: 
            0 20px 40px rgba(0,0,0,0.3),
            0 0 0 10px rgba(255,255,255,0.1),
            0 0 0 20px rgba(255,255,255,0.05);
        transition: all 0.5s ease;
        border: none;
        filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
    }
    
    .main-logo:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 
            0 30px 60px rgba(0,0,0,0.4),
            0 0 0 15px rgba(255,255,255,0.2),
            0 0 0 30px rgba(255,255,255,0.1);
        filter: drop-shadow(0 15px 30px rgba(0,0,0,0.3));
    }
    
    /* Ministry Info */
    .ministry-info {
        text-align: center;
        max-width: 500px;
    }
    
    .kingdom-badge, .ministry-name, .directorate-name {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8rem;
        margin-bottom: 1rem;
        padding: 0.8rem 1.5rem;
        background: rgba(255,255,255,0.15);
        border-radius: 25px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255,255,255,0.3);
        transition: all 0.3s ease;
        animation: fadeInUp 1s ease-out;
    }
    
    .kingdom-badge {
        background: linear-gradient(135deg, rgba(255,215,0,0.3), rgba(255,255,255,0.2));
        border: 2px solid rgba(255,215,0,0.5);
        font-size: 1.3rem;
        font-weight: 700;
        animation-delay: 0.2s;
    }
    
    .ministry-name {
        background: linear-gradient(135deg, rgba(0,123,255,0.3), rgba(255,255,255,0.2));
        border: 2px solid rgba(0,123,255,0.5);
        font-size: 1.2rem;
        font-weight: 600;
        animation-delay: 0.4s;
    }
    
    .directorate-name {
        background: linear-gradient(135deg, rgba(40,167,69,0.3), rgba(255,255,255,0.2));
        border: 2px solid rgba(40,167,69,0.5);
        font-size: 1rem;
        font-weight: 500;
        animation-delay: 0.6s;
    }
    
    .kingdom-badge:hover, .ministry-name:hover, .directorate-name:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    
    /* Welcome Section */
    .welcome-section {
        text-align: right;
        margin-bottom: 2rem;
        animation: fadeInLeft 1s ease-out 0.8s both;
    }
    
    .welcome-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1.5rem;
        background: linear-gradient(135deg, rgba(255,193,7,0.3), rgba(255,255,255,0.2));
        border: 2px solid rgba(255,193,7,0.6);
        border-radius: 20px;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        animation: pulse 2s infinite;
    }
    
    .main-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }
    
    .title-highlight {
        display: block;
        background: linear-gradient(135deg, #FFD700, #FFA500);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .title-subtitle {
        display: block;
        font-size: 2rem;
        font-weight: 600;
        color: rgba(255,255,255,0.9);
        margin-top: 0.5rem;
    }
    
    .system-description {
        font-size: 1.3rem;
        line-height: 1.6;
        color: rgba(255,255,255,0.9);
        max-width: 700px;
        margin: 0 auto;
    }
    
    .features-text {
        display: block;
        margin-top: 1rem;
        font-size: 1.1rem;
        color: rgba(255,255,255,0.8);
    }
    
    .features-text i {
        color: #28a745;
        margin: 0 0.5rem;
    }
    
    /* Action Section */
    .action-section {
        text-align: right;
        margin-bottom: 2rem;
        animation: fadeInLeft 1s ease-out 1s both;
    }
    
    .primary-btn {
        position: relative;
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1.2rem 3rem;
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-size: 1.3rem;
        font-weight: 600;
        box-shadow: 0 10px 30px rgba(0,123,255,0.4);
        transition: all 0.4s ease;
        overflow: hidden;
        border: 2px solid rgba(255,255,255,0.3);
    }
    
    .primary-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,123,255,0.6);
        color: white;
    }
    
    .btn-glow {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }
    
    .primary-btn:hover .btn-glow {
        left: 100%;
    }
    
    .help-text, .welcome-back {
        margin-top: 1rem;
        font-size: 1.1rem;
        color: rgba(255,255,255,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    
    /* Quick Stats */
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
        animation: fadeInUp 1s ease-out 1.2s both;
    }
    
    .stat-item {
        text-align: center;
        padding: 2rem 1rem;
        background: rgba(255,255,255,0.1);
        border-radius: 20px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
        transform: translateX(-100%);
        transition: transform 0.5s ease;
    }
    
    .stat-item:hover::before {
        transform: translateX(0);
    }
    
    .stat-item:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        background: rgba(255,255,255,0.15);
    }
    
    .stat-item i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #FFD700;
        animation: bounce 2s infinite;
    }
    
    .stat-number {
        display: block;
        font-size: 3rem;
        font-weight: 800;
        color: white;
        margin-bottom: 0.5rem;
        position: relative;
    }
    
    .stat-sign {
        font-size: 3rem;
        font-weight: 800;
        margin-right: 0.2rem;
        color: white;
        opacity: 1;
    }

    .stat-sign.positive {
        color: white;
    }

    .stat-sign.negative {
        color: white;
    }
    
    .stat-label {
        font-size: 1.2rem;
        color: rgba(255,255,255,0.8);
        font-weight: 500;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .ministry-content {
        gap: 2rem;
        flex-wrap: wrap;
        width: 100%;
    }
    
    .ministry-text {
        flex-grow: 1;
        min-width: 300px;
        text-align: center;
    }
    
    .ministry-logo {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .logo-image {
        width: 130px;
        height: 130px;
        object-fit: contain;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 50%;
        padding: 15px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        transition: all 0.3s ease;
        animation: logoFloat 3s ease-in-out infinite;
        border: 4px solid rgba(255, 255, 255, 0.4);
    }
    
    .logo-image:hover {
        transform: scale(1.1);
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
        border: 3px solid rgba(255, 255, 255, 0.8);
    }
    
    @keyframes logoFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }
    
    /* Responsive Design */
    @media (max-width: 992px) {
        .hero-main-layout {
            flex-direction: column;
            gap: 3rem;
            min-height: auto;
        }
        
        .hero-right-side, .hero-left-side {
            flex: 1;
            text-align: center;
            padding-right: 0;
        }
        
        .hero-left-side {
            order: 2;
        }
        
        .hero-right-side {
            order: 1;
        }
        
        .main-logo {
            width: 220px;
            height: 220px;
            padding: 0;
        }
        
        .main-title {
            font-size: 3rem;
        }
        
        .welcome-section, .action-section {
            text-align: center;
        }
        
        .quick-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .main-logo {
            width: 180px;
            height: 180px;
            padding: 0;
        }
        
        .main-title {
            font-size: 2.5rem;
        }
        
        .title-subtitle {
            font-size: 1.5rem;
        }
        
        .system-description {
            font-size: 1.1rem;
        }
        
        .kingdom-badge, .ministry-name, .directorate-name {
            font-size: 1rem;
            padding: 0.6rem 1.2rem;
        }
        
        .primary-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }
        
        .quick-stats {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .stat-item {
            padding: 1.5rem 1rem;
        }
        
        .stat-item i {
            font-size: 2.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
        }
    }
    
    @media (max-width: 480px) {
        .main-logo {
            width: 150px;
            height: 150px;
            padding: 0;
        }
        
        .main-title {
            font-size: 2rem;
        }
        
        .title-subtitle {
            font-size: 1.2rem;
        }
        
        .system-description {
            font-size: 1rem;
        }
        
        .kingdom-badge, .ministry-name, .directorate-name {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        
        .primary-btn {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
        }
        
        .stat-item {
            padding: 1rem;
        }
        
        .stat-item i {
            font-size: 2rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .stat-label {
            font-size: 1rem;
        }
    }
    
    .ministry-title {
        font-size: 2.2rem;
        font-weight: 700;
        color: #ffffff;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
        letter-spacing: 1px;
    }
    
    .ministry-subtitle {
        font-size: 1.8rem;
        font-weight: 600;
        color: #f8f9fa;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        margin-bottom: 0;
        letter-spacing: 0.5px;
        margin-top: 1rem;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .hero-title {
        font-size: 2.8rem;
        font-weight: 800;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
        font-weight: 400;
        margin-bottom: 1.5rem;
        opacity: 0.95;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }
    
    .hero-btn {
        background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        border: none;
        padding: 15px 40px;
        font-size: 1.2rem;
        font-weight: 600;
        border-radius: 50px;
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        transition: all 0.3s ease;
        text-decoration: none;
        color: white !important;
        display: inline-block;
    }
    
    .hero-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
        background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        color: white !important;
    }
    
    /* شريط الإعلانات المحسن */
    .announcements-ticker {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 20px 0;
        overflow: hidden;
        position: relative;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        margin-bottom: 0;
    }
    
    .announcements-ticker::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: shimmer 3s infinite;
    }
    
    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
    
    .ticker-content {
        display: flex;
        animation: scroll-right 35s linear infinite;
        white-space: nowrap;
    }
    
    .ticker-item {
        display: inline-flex;
        align-items: center;
        margin-right: 60px;
        font-size: 1.1rem;
        font-weight: 500;
        cursor: pointer;
        padding: 12px 20px;
        border-radius: 25px;
        transition: all 0.4s ease;
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .ticker-item i {
        margin-left: 12px;
        font-size: 1.3rem;
    }
    
    .ticker-item:hover {
        text-shadow: 0 0 15px rgba(255,255,255,0.9);
        background: rgba(255,255,255,0.2);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    
    .ticker-item.announcement-urgent:hover {
        text-shadow: 0 0 20px rgba(255,255,255,1);
        transform: translateY(-3px) scale(1.1);
        transition: all 0.4s ease;
    }
    
    @keyframes scroll-right {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
    
    .ticker-pause:hover .ticker-content {
        animation-play-state: paused;
    }
    
    .announcement-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
        margin-right: 10px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.4);
    }
    
    .announcement-urgent {
        position: relative;
    }
    
    .announcement-urgent .announcement-badge {
        background: linear-gradient(45deg, #ff1744, #ff5722);
        color: white;
        animation: urgentPulse 2s infinite;
        box-shadow: 0 0 15px rgba(255, 23, 68, 0.6);
    }
    
    .announcement-urgent::before {
        content: "";
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg, rgba(255, 23, 68, 0.4), rgba(255, 87, 34, 0.4));
        border-radius: 28px;
        animation: urgentGlow 2.5s infinite;
        z-index: -1;
    }
    
    @keyframes urgentPulse {
        0%, 100% { 
            transform: scale(1);
            background: linear-gradient(45deg, #ff1744, #ff5722);
        }
        50% { 
            transform: scale(1.1);
            background: linear-gradient(45deg, #ff5722, #ff1744);
        }
    }
    
    @keyframes urgentGlow {
        0%, 100% { 
            opacity: 0.4;
            box-shadow: 0 0 8px rgba(255, 23, 68, 0.4);
        }
        50% { 
            opacity: 0.8;
            box-shadow: 0 0 25px rgba(255, 23, 68, 0.8);
        }
    }
    
    .no-announcements {
        text-align: center;
        font-style: italic;
        opacity: 0.9;
        font-weight: 500;
    }
    
    /* تحسين البطاقات */
    .service-card {
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        border: none;
        border-radius: 20px;
        box-shadow: 
            0 10px 30px rgba(0,0,0,0.1),
            0 1px 8px rgba(0,0,0,0.05);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        position: relative;
    }
    
    .service-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
        background-size: 200% 200%;
        animation: cardTopGradient 4s ease infinite;
    }
    
    @keyframes cardTopGradient {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }
    
    .service-card:hover {
        transform: translateY(-12px) rotate(1deg);
        box-shadow: 
            0 20px 50px rgba(0,0,0,0.15),
            0 5px 20px rgba(0,0,0,0.1);
    }
    
    .service-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .service-card:hover .service-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    }
    
    .service-icon i {
        font-size: 2rem;
        color: white;
    }
    
    .service-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .service-description {
        color: #6c757d;
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }
    
    .service-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .service-btn:hover {
        background: linear-gradient(45deg, #764ba2, #667eea);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    /* قسم المميزات */
    .features-section {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 4rem 0;
        margin-top: 4rem;
        border-radius: 30px 30px 0 0;
    }
    
    .feature-item {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-right: 4px solid transparent;
    }
    
    .feature-item:hover {
        transform: translateX(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        border-right-color: #667eea;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        flex-shrink: 0;
    }
    
    .feature-icon i {
        color: white;
        font-size: 1.2rem;
    }
    
    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .hero-section {
            min-height: 30vh;
        }
        
        .hero-title {
            font-size: 2rem;
        }
        
        .hero-subtitle {
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .hero-btn {
            font-size: 1rem;
            padding: 12px 30px;
        }
        
        .ticker-item {
            font-size: 1rem;
            margin-right: 40px;
            padding: 10px 15px;
        }
        
        .service-icon {
            width: 70px;
            height: 70px;
        }
        
        .service-icon i {
            font-size: 1.8rem;
        }
    }
    
    /* تأثيرات إضافية */
    .fade-in {
        animation: fadeInUp 0.8s ease-out;
    }
    
    .fade-in-delay {
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="hero-content fade-in">
            <!-- Main Hero Layout -->
            <div class="hero-main-layout">
                <!-- Right Side: Logo and Ministry Info -->
                <div class="hero-right-side">
                    <div class="floating-logo">
                        <img src="{% static 'img/logos/moe-emblem.png' %}" alt="شعار وزارة التربية والتعليم" class="main-logo">
                    </div>
                    <br>
                    <div class="ministry-info">
                        <div class="kingdom-badge">
                            <i class="fas fa-crown"></i>
                            <span>المملكة الأردنية الهاشمية</span>
                        </div>
                        <div class="ministry-name">
                            <i class="fas fa-graduation-cap"></i>
                            <span>وزارة التربية والتعليم</span>
                        </div>
                        <div class="directorate-name">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>مديرية التربية والتعليم للواء قصبة المفرق</span>
                        </div>
                    </div>
                </div>

                <!-- Left Side: Welcome and Actions -->
                <div class="hero-left-side">
                    <!-- Welcome Section -->
                    <div class="welcome-section">
                        <div class="welcome-badge">
                            <i class="fas fa-star"></i>
                            <span>أهلاً وسهلاً</span>
                        </div>
                        <h1 class="main-title">
                            <span class="title-highlight">نظام شؤون الموظفين</span>
                            <span class="title-subtitle">الرقمي المتطور</span>
                        </h1>
                        <p class="system-description">
                            <i class="fas fa-rocket"></i>
                            منصة رقمية شاملة ومتطورة لإدارة شؤون الموظفين والكادر البشري
                            <br>
                            <span class="features-text">
                                <i class="fas fa-check-circle"></i> سهولة الاستخدام
                                <i class="fas fa-check-circle"></i> أمان عالي
                                <i class="fas fa-check-circle"></i> تقارير شاملة
                            </span>
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-section">
                        {% if not user.is_authenticated %}
                            <a href="{% url 'accounts:login' %}" class="primary-btn">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>دخول النظام</span>
                                <div class="btn-glow"></div>
                            </a>
                            <div class="help-text">
                                <i class="fas fa-info-circle"></i>
                                يرجى تسجيل الدخول للوصول إلى جميع الخدمات
                            </div>
                        {% else %}
                            <a href="{% url 'dashboard' %}" class="primary-btn">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة التحكم</span>
                                <div class="btn-glow"></div>
                            </a>
                            <div class="welcome-back">
                                <i class="fas fa-user-check"></i>
                                مرحباً بعودتك، {{ user.get_full_name|default:user.username }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span class="stat-number" data-count="4000" data-sign="+">
                        <span class="stat-sign positive">+</span>0
                    </span>
                    <span class="stat-label">موظف</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-building"></i>
                    <span class="stat-number" data-count="172" data-sign="+">
                        <span class="stat-sign positive">+</span>0
                    </span>
                    <span class="stat-label">مدرسة</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-chart-line"></i>
                    <span class="stat-number" data-count="99">0</span>
                    <span class="stat-label">% كفاءة</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span class="stat-number" data-count="24">0</span>
                    <span class="stat-label">ساعة خدمة</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Announcements Ticker -->
<div class="announcements-ticker ticker-pause" id="announcementsTicker">
    <div class="container">
        <div class="ticker-content" id="tickerContent">
            <div class="ticker-item no-announcements">
                <i class="fas fa-spinner fa-spin"></i>
                جاري تحميل الإعلانات...
            </div>
        </div>
    </div>
</div>

<div class="container my-5">
    <div class="text-center mb-5 fade-in">
        <h2 class="display-6 fw-bold text-primary mb-3">⚡ خدماتنا المتميزة</h2>
        <p class="lead text-muted">استكشف مجموعة واسعة من الخدمات الإلكترونية المتطورة</p>
    </div>
    
    <div class="row g-4">
        <div class="col-lg-4 col-md-6">
            <div class="service-card h-100 fade-in">
                <div class="card-body text-center p-4">
                    <div class="service-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h3 class="service-title">النقل الداخلي</h3>
                    <p class="service-description">تقديم طلبات النقل بين الأقسام والإدارات بكل سهولة ويسر مع متابعة حالة الطلب.</p>
                    <a href="{% url 'home:internal_transfer' %}" class="service-btn">
                        <i class="fas fa-paper-plane me-2"></i> تقديم طلب
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="service-card h-100 fade-in-delay">
                <div class="card-body text-center p-4">
                    <div class="service-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <h3 class="service-title">روابط مهمة</h3>
                    <p class="service-description">مجموعة من الروابط المهمة للخدمات الحكومية والأنظمة ذات العلاقة بالموظفين.</p>
                    <a href="{% url 'home:important_links' %}" class="service-btn">
                        <i class="fas fa-external-link-alt me-2"></i> عرض الروابط
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="service-card h-100 fade-in">
                <div class="card-body text-center p-4">
                    <div class="service-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="service-title">النماذج المعتمدة</h3>
                    <p class="service-description">نماذج رسمية معتمدة يمكن تحميلها واستخدامها في المعاملات الإدارية.</p>
                    <a href="{% url 'home:approved_forms_list' %}" class="service-btn">
                        <i class="fas fa-download me-2"></i> عرض النماذج
                    </a>
                </div>
            </div>
        </div>

    </div>
    
    <!-- الصف الثاني -->
    <div class="row g-4 mt-4">
        <div class="col-lg-6 col-md-6">
            <div class="service-card h-100 fade-in-delay">
                <div class="card-body text-center p-4">
                    <div class="service-icon">
                        <i class="fas fa-search-plus"></i>
                    </div>
                    <h3 class="service-title">استعلام الموظفين</h3>
                    <p class="service-description">البحث السريع عن بيانات الموظفين في المديرية مع عرض المعلومات الأساسية والتفاصيل.</p>
                    <a href="{% url 'home:employee_inquiry' %}" class="service-btn">
                        <i class="fas fa-search me-2"></i> استعلام
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="service-card h-100 fade-in">
                <div class="card-body text-center p-4">
                    <div class="service-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3 class="service-title">حول النظام</h3>
                    <p class="service-description">معلومات شاملة عن النظام ومطوره والخدمات التي يقدمها للمستخدمين.</p>
                    <a href="{% url 'home:about' %}" class="service-btn">
                        <i class="fas fa-info me-2"></i> المزيد
                    </a>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Features Section -->
<div class="features-section">
    <div class="container">
        <div class="text-center mb-5 fade-in">
            <h2 class="display-6 fw-bold mb-3">✨ مميزات النظام الرائعة</h2>
            <p class="lead text-muted">نظام متكامل يجمع بين القوة والسهولة في الاستخدام</p>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">إدارة بيانات الموظفين</h5>
                        <p class="text-muted mb-0">إدارة شاملة ومتطورة لجميع بيانات الموظفين مع أمان عالي</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in-delay">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">إدارة الإجازات والغياب</h5>
                        <p class="text-muted mb-0">نظام ذكي لإدارة الإجازات مع إشعارات تلقائية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">إدارة الرتب والترقيات</h5>
                        <p class="text-muted mb-0">تتبع دقيق للرتب والترقيات مع تقارير تفصيلية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in-delay">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">إدارة تقييم الأداء</h5>
                        <p class="text-muted mb-0">نظام تقييم شامل وعادل لقياس أداء الموظفين</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">إدارة النقل الداخلي</h5>
                        <p class="text-muted mb-0">تسهيل عملية النقل بين الأقسام بطريقة إلكترونية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-item d-flex align-items-center fade-in-delay">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">تقارير وإحصائيات متنوعة</h5>
                        <p class="text-muted mb-0">تقارير ذكية وإحصائيات دقيقة لاتخاذ القرارات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Features -->
        <div class="row mt-5 g-4">
            <div class="col-lg-4 col-md-6">
                <div class="text-center feature-item fade-in">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="fw-bold mb-2">أمان عالي</h5>
                    <p class="text-muted">حماية متقدمة للبيانات مع تشفير قوي</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="text-center feature-item fade-in-delay">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h5 class="fw-bold mb-2">متوافق مع الجوال</h5>
                    <p class="text-muted">تصميم متجاوب يعمل على جميع الأجهزة</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="text-center feature-item fade-in">
                    <div class="feature-icon mx-auto mb-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5 class="fw-bold mb-2">عمل على مدار الساعة</h5>
                    <p class="text-muted">خدمة متاحة 24/7 بدون انقطاع</p>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load announcements
    loadAnnouncements();
    
    // Refresh announcements every 5 minutes
    setInterval(loadAnnouncements, 300000);
    
    // Initialize animations on scroll
    initializeAnimations();
    
    // Add smooth hover effects
    addHoverEffects();
    
    // Animate statistics numbers
    animateStats();
});

function loadAnnouncements() {
    $.ajax({
        url: '/announcements/api/homepage/',
        method: 'GET',
        success: function(response) {
            displayAnnouncements(response.announcements);
        },
        error: function() {
            displayNoAnnouncements();
        }
    });
}

function displayAnnouncements(announcements) {
    const tickerContent = $('#tickerContent');
    
    if (announcements && announcements.length > 0) {
        let tickerHtml = '';
        
        announcements.forEach(function(announcement) {
            const urgentClass = announcement.priority === 'urgent' ? 'announcement-urgent' : '';
            const icon = getAnnouncementIcon(announcement.type);
            
            // عرض badge الأولوية للعاجل فقط
            let badgeHtml = '';
            if (announcement.priority === 'urgent') {
                badgeHtml = `<span class="announcement-badge">🚨 عاجل</span>`;
            }
            
            tickerHtml += `
                <div class="ticker-item ${urgentClass}" onclick="handleAnnouncementClick(${announcement.id})" title="انقر لقراءة التفاصيل">
                    <i class="fas ${icon}"></i>
                    ${announcement.title}
                    ${badgeHtml}
                    <i class="fas fa-chevron-left ms-2" style="font-size: 0.8rem; opacity: 0.7;"></i>
                </div>
            `;
        });
        
        tickerContent.html(tickerHtml);
        $('#announcementsTicker').show();
    } else {
        displayNoAnnouncements();
    }
}

function displayNoAnnouncements() {
    const tickerContent = $('#tickerContent');
    tickerContent.html(`
        <div class="ticker-item no-announcements">
            <i class="fas fa-info-circle"></i>
            لا توجد إعلانات متاحة حالياً
        </div>
    `);
}

function getAnnouncementIcon(type) {
    const icons = {
        'info': 'fa-info-circle',
        'warning': 'fa-exclamation-triangle',
        'success': 'fa-check-circle',
        'danger': 'fa-exclamation-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function getAnnouncementBadge(priority) {
    const badges = {
        'low': 'منخفض',
        'medium': 'متوسط',
        'high': 'عالي',
        'urgent': 'عاجل'
    };
    return badges[priority] || 'متوسط';
}

function handleAnnouncementClick(announcementId) {
    // Track the click
    $.ajax({
        url: `/announcements/${announcementId}/track-click/`,
        method: 'POST',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val() || getCookie('csrftoken')
        },
        success: function(response) {
            // Navigate to public announcement view page
            window.location.href = `/announcements/view/${announcementId}/`;
        },
        error: function() {
            // Navigate even if tracking fails
            window.location.href = `/announcements/view/${announcementId}/`;
        }
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Animation functions
function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe all animated elements
    document.querySelectorAll('.fade-in, .fade-in-delay').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.8s ease-out';
        observer.observe(el);
    });
    
    // Add delay for fade-in-delay elements
    document.querySelectorAll('.fade-in-delay').forEach((el, index) => {
        el.style.transitionDelay = `${0.2 + (index * 0.1)}s`;
    });
}

function addHoverEffects() {
    // Enhanced service card hover effects
    $('.service-card').each(function() {
        const $card = $(this);
        
        $card.hover(
            function() {
                // Mouse enter
                $(this).css('transform', 'translateY(-15px) scale(1.02)');
                $(this).find('.service-icon').css('transform', 'scale(1.15) rotate(10deg)');
            },
            function() {
                // Mouse leave
                $(this).css('transform', 'translateY(0) scale(1)');
                $(this).find('.service-icon').css('transform', 'scale(1) rotate(0deg)');
            }
        );
    });
    
    // Feature items hover effect
    $('.feature-item').hover(
        function() {
            $(this).css('transform', 'translateX(-8px) scale(1.02)');
        },
        function() {
            $(this).css('transform', 'translateX(0) scale(1)');
        }
    );
    
    // Hero button pulse effect
    $('.hero-btn').hover(function() {
        $(this).addClass('animate__pulse');
        setTimeout(() => {
            $(this).removeClass('animate__pulse');
        }, 600);
    });
}

// Add loading animation
function showLoadingAnimation() {
    const loader = `
        <div class="d-flex justify-content-center align-items-center py-5">
            <div class="spinner-border text-primary me-3" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <span class="text-primary fw-bold">جاري تحميل البيانات...</span>
        </div>
    `;
    return loader;
}

// Enhanced smooth scrolling
$(document).on('click', 'a[href^="#"]', function(e) {
    e.preventDefault();
    
    const target = $(this.getAttribute('href'));
    if (target.length) {
        $('html, body').animate({
            scrollTop: target.offset().top - 80
        }, 1000, 'easeInOutCubic');
    }
});

// Animate statistics numbers
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const animateNumber = (element) => {
        const target = parseInt(element.getAttribute('data-count'));
        const sign = element.getAttribute('data-sign') || '';
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const signElement = element.querySelector('.stat-sign');
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Update the text content while preserving the sign
            if (signElement) {
                const numberText = Math.floor(current).toLocaleString();
                element.innerHTML = `<span class="stat-sign ${sign === '+' ? 'positive' : sign === '-' ? 'negative' : ''}">${sign}</span>${numberText}`;
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 16);
    };
    
    // Use Intersection Observer to trigger animation when stats come into view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                if (statNumber && !statNumber.classList.contains('animated')) {
                    statNumber.classList.add('animated');
                    animateNumber(statNumber);
                }
            }
        });
    }, { threshold: 0.5 });
    
    // Observe all stat items
    document.querySelectorAll('.stat-item').forEach(item => {
        observer.observe(item);
    });
}

// Add welcome message animation
setTimeout(() => {
    if ($('.hero-content').length) {
        $('.hero-content').addClass('animate__animated animate__fadeInUp');
    }
}, 500);
</script>
{% endblock %}
