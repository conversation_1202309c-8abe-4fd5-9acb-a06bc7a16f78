{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if service_purchase %}تعديل شراء الخدمة{% else %}إضافة شراء خدمة جديد{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .form-card {
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-custom {
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }
    
    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .service-info-card {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 2px solid #2196f3;
        border-radius: 15px;
        margin-bottom: 2rem;
    }
    
    .service-info-card h4,
    .service-info-card h6,
    .service-info-card p {
        color: #212529 !important;
        text-shadow: none;
    }
    
    .service-info-card .text-dark {
        color: #212529 !important;
    }

    .service-info-card h4 {
        color: #212529 !important;
        font-weight: 600 !important;
    }

    .service-info-card h6 {
        color: #212529 !important;
        font-weight: 600 !important;
    }

    .service-info-card p {
        color: #212529 !important;
        font-weight: 500 !important;
    }
    
    .service-info-card .fw-bold {
        font-weight: 600 !important;
        color: #212529 !important;
    }
    
    .service-info-card .fw-medium {
        font-weight: 500 !important;
        color: #212529 !important;
    }
    
    .service-info-card i {
        color: #2196f3 !important;
    }
    
    /* Employee details display styling */
    .employee-details-display {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 2px solid #28a745;
        border-radius: 10px;
        padding: 1.5rem;
    }
    
    .employee-details-display h6 {
        color: #155724 !important;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .employee-details-display p {
        margin-bottom: 0.5rem;
        color: #212529 !important;
        font-weight: 500;
    }
    
    .employee-details-display strong {
        color: #0c5460 !important;
        font-weight: 600;
    }
    
    .employee-details-display span {
        color: #212529 !important;
        font-weight: 500;
    }
    
    /* Additional text visibility improvements */
    .text-dark {
        color: #212529 !important;
    }
    
    .fw-medium {
        font-weight: 500 !important;
    }
    
    .fw-bold {
        font-weight: 600 !important;
    }
    
    /* Ensure service info card text is visible */
    .service-info-card .text-white {
        color: white !important;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }
    
    .required-field::after {
        content: " *";
        color: #e74c3c;
        font-weight: bold;
    }
</style>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart text-primary me-2"></i>
            {% if service_purchase %}تعديل شراء الخدمة{% else %}إضافة شراء خدمة جديد{% endif %}
        </h1>
        <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <!-- Service Purchase Info Card (for editing) -->
    {% if service_purchase %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card service-info-card">
                <div class="card-body text-center">
                    <h4 class="mb-3 text-dark fw-bold">
                        <i class="fas fa-user me-2 text-primary"></i>
                        {{ service_purchase.employee.full_name }}
                    </h4>
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-dark fw-bold">الرقم الوزاري</h6>
                            <p class="mb-0 text-dark fw-medium">{{ service_purchase.employee.ministry_number|default:"-" }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-dark fw-bold">التخصص</h6>
                            <p class="mb-0 text-dark fw-medium">{{ service_purchase.employee.specialization|default:"-" }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-dark fw-bold">المدرسة</h6>
                            <p class="mb-0 text-dark fw-medium">{{ service_purchase.employee.school|default:"-" }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-dark fw-bold">تاريخ الإنشاء</h6>
                            <p class="mb-0 text-dark fw-medium">{{ service_purchase.created_at|date:"Y-m-d H:i" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card form-card">
                <div class="form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-{% if service_purchase %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if service_purchase %}تعديل بيانات شراء الخدمة{% else %}بيانات شراء الخدمة الجديد{% endif %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" id="servicePurchaseForm">
                        {% csrf_token %}
                        
                        <!-- Employee Search (only for new records) -->
                        {% if not service_purchase %}
                        <!-- Ministry Number Search -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card border-info employee-search-card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-search me-2"></i>البحث عن الموظف
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <label for="{{ form.ministry_number_search.id_for_label }}" class="form-label">
                                                    <i class="fas fa-id-card me-2"></i>{{ form.ministry_number_search.label }}
                                                </label>
                                                {{ form.ministry_number_search }}
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="button" id="searchEmployeeBtn" class="btn search-button">
                                                    <i class="fas fa-search"></i> بحث
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- Employee Details Display -->
                                        <div id="employeeDetails" class="mt-3" style="display: none;">
                                            <div class="employee-details-display">
                                                <h6 class="alert-heading">
                                                    <i class="fas fa-user-check me-2"></i>بيانات الموظف
                                                </h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="text-dark"><strong class="text-primary">الاسم الكامل:</strong> <span id="employeeFullName" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">الرقم الوزاري:</strong> <span id="employeeMinistryNumber" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">الرقم الوطني:</strong> <span id="employeeNationalId" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">الجنس:</strong> <span id="employeeGender" class="text-dark fw-medium">-</span></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="text-dark"><strong class="text-primary">التخصص:</strong> <span id="employeeSpecialization" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">المدرسة الحالية:</strong> <span id="employeeSchool" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">الهاتف:</strong> <span id="employeePhone" class="text-dark fw-medium">-</span></p>
                                                        <p class="text-dark"><strong class="text-primary">تاريخ الميلاد:</strong> <span id="employeeBirthDate" class="text-dark fw-medium">-</span></p>
                                                    </div>
                                                </div>
                                                <button type="button" id="selectEmployeeBtn" class="btn select-employee-button btn-sm">
                                                    <i class="fas fa-check"></i> اختيار هذا الموظف
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- Search Message -->
                                        <div id="searchMessage" class="mt-3" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden Employee Field -->
                        {{ form.employee }}
                        {% if form.employee.errors %}
                            <div class="text-danger mb-3">
                                {% for error in form.employee.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% endif %}

                        <!-- School Selection -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="{{ form.target_school.id_for_label }}" class="form-label required-field">
                                    <i class="fas fa-school me-2"></i>{{ form.target_school.label }}
                                </label>
                                {{ form.target_school }}
                                {% if form.target_school.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.target_school.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    اختر المدرسة التي تحتاج إلى شراء الخدمات لهذا الموظف
                                </small>
                            </div>
                        </div>





                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    <i class="fas fa-sticky-note me-2"></i>{{ form.notes.label }}
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-center">
                                    <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-secondary btn-custom me-md-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-custom">
                                        <i class="fas fa-save"></i>
                                        {% if service_purchase %}تحديث شراء الخدمة{% else %}إضافة شراء الخدمة{% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
{% if messages %}
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endfor %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    let selectedEmployee = null;
    
    // Initialize Select2 for school selection
    $('#school_select').select2({
        placeholder: 'اختر المدرسة',
        allowClear: true,
        width: '100%',
        theme: 'bootstrap-5',
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
    
    // Search employee by ministry number
    $('#searchEmployeeBtn').on('click', function() {
        const ministryNumber = $('#ministry_number_search').val().trim();
        
        if (!ministryNumber) {
            showMessage('الرجاء إدخال الرقم الوزاري', 'warning');
            return;
        }
        
        // Show loading
        $(this).html('<i class="fas fa-spinner fa-spin"></i> جاري البحث...');
        $(this).prop('disabled', true);
        
        // AJAX request
        $.ajax({
            url: '{% url "employment:search_employee_by_ministry_number" %}',
            method: 'GET',
            data: {
                'ministry_number': ministryNumber
            },
            success: function(response) {
                if (response.success) {
                    selectedEmployee = response.employee;
                    displayEmployeeDetails(response.employee);
                    showMessage('تم العثور على الموظف بنجاح', 'success');
                } else {
                    hideEmployeeDetails();
                    // Show specific error message for duplicate employee
                    if (response.message.includes('لديه شراء خدمة نشط مسبقاً')) {
                        showMessage(response.message, 'warning');
                    } else {
                        showMessage(response.message, 'danger');
                    }
                }
            },
            error: function() {
                hideEmployeeDetails();
                showMessage('حدث خطأ في البحث', 'danger');
            },
            complete: function() {
                $('#searchEmployeeBtn').html('<i class="fas fa-search"></i> بحث');
                $('#searchEmployeeBtn').prop('disabled', false);
            }
        });
    });
    
    // Select employee button
    $('#selectEmployeeBtn').on('click', function() {
        if (selectedEmployee) {
            $('#id_employee').val(selectedEmployee.id);
            showMessage('تم اختيار الموظف بنجاح', 'success');
            
            // Auto-fill target school with current school if available
            if (selectedEmployee.school) {
                $('#school_select').val(selectedEmployee.school);
            }
        }
    });
    
    // Enter key search
    $('#ministry_number_search').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#searchEmployeeBtn').click();
        }
    });
    
    function displayEmployeeDetails(employee) {
        $('#employeeFullName').text(employee.full_name || '-').addClass('text-dark fw-medium');
        $('#employeeMinistryNumber').text(employee.ministry_number || '-').addClass('text-dark fw-medium');
        $('#employeeNationalId').text(employee.national_id || '-').addClass('text-dark fw-medium');
        $('#employeeSpecialization').text(employee.specialization || '-').addClass('text-dark fw-medium');
        $('#employeeSchool').text(employee.school || '-').addClass('text-dark fw-medium');
        $('#employeePhone').text(employee.phone || '-').addClass('text-dark fw-medium');
        $('#employeeBirthDate').text(employee.birth_date || '-').addClass('text-dark fw-medium');
        $('#employeeGender').text(employee.gender || '-').addClass('text-dark fw-medium');
        
        // Ensure all text elements have proper styling
        $('.employee-details-display p').addClass('text-dark');
        $('.employee-details-display strong').addClass('text-primary');
        $('.employee-details-display span').addClass('text-dark fw-medium');
        
        $('#employeeDetails').show();
    }
    
    function hideEmployeeDetails() {
        $('#employeeDetails').hide();
        selectedEmployee = null;
    }
    
    function showMessage(message, type) {
        const alertClass = `alert-${type}`;
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'warning' ? 'fa-exclamation-triangle' : 
                         'fa-times-circle';
        
        const messageHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas ${iconClass} me-2"></i>
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('#searchMessage').html(messageHtml).show();
        
        // Auto-hide after 8 seconds for warning messages, 5 seconds for others
        const hideDelay = type === 'warning' ? 8000 : 5000;
        setTimeout(function() {
            $('#searchMessage .alert').fadeOut();
        }, hideDelay);
    }
    
    // Form validation
    $('#servicePurchaseForm').on('submit', function(e) {
        let isValid = true;
        let errorMessages = [];
        
        // Validate employee selection (for new records)
        if ($('#id_employee').length && !$('#id_employee').val()) {
            isValid = false;
            errorMessages.push('الرجاء اختيار الموظف');
        }
        
        // Validate target school
        if (!$('#school_select').val()) {
            isValid = false;
            errorMessages.push('الرجاء اختيار المدرسة');
        }
        
        if (!isValid) {
            e.preventDefault();
            
            // Show error messages
            const errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                '<strong>يرجى تصحيح الأخطاء التالية:</strong><br>' +
                errorMessages.join('<br>') +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
            
            $('.card-body').prepend(errorHtml);
            
            // Scroll to top to show errors
            $('html, body').animate({
                scrollTop: $('.card').offset().top - 100
            }, 500);
            
            return false;
        }
    });

    
    // Form validation (simplified)
    $('#servicePurchaseForm').on('submit', function(e) {
        let isValid = true;
        let errorMessage = '';
        
        // Check if employee is selected (for new records)
        {% if not service_purchase %}
        if (!$('#id_employee').val()) {
            isValid = false;
            errorMessage += 'الرجاء اختيار الموظف.\n';
        }
        {% endif %}
        
        // Check target school
        if (!$('#school_select').val()) {
            isValid = false;
            errorMessage += 'الرجاء اختيار المدرسة.\n';
        }
        
        if (!isValid) {
            e.preventDefault();
            showMessage(errorMessage.trim(), 'danger');
            return false;
        }
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Force text colors on page load
    $(document).ready(function() {
        // Apply dark colors to service info card
        $('.service-info-card h4, .service-info-card h6, .service-info-card p').css({
            'color': '#212529',
            'text-shadow': 'none'
        });
        
        // Apply colors to employee details
        $('.employee-details-display p').css('color', '#212529');
        $('.employee-details-display strong').css('color', '#0c5460');
        $('.employee-details-display span').css('color', '#212529');
    });
});
</script>

<style>
/* Final CSS overrides for text visibility */
.service-info-card * {
    color: #212529 !important;
    text-shadow: none !important;
}

.employee-details-display * {
    color: #212529 !important;
}

.employee-details-display strong {
    color: #0c5460 !important;
}

.employee-details-display h6 {
    color: #155724 !important;
}
</style>
{% endblock %}