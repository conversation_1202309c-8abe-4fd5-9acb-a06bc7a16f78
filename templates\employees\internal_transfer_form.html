{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل حركة النقل - {{ transfer.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .form-card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    .form-card .card-header {
        background: linear-gradient(45deg, #f6c23e, #e69500);
        color: white;
        border-bottom: none;
    }
    .form-group label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
    }
    .form-control {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
        padding: 0.75rem;
    }
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .required {
        color: #e74a3b;
    }
    .employee-info {
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-warning"></i>
            تعديل حركة النقل
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> عرض التفاصيل
            </a>
            <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information Display -->
        <div class="col-lg-4 mb-4">
            <div class="card form-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="employee-info">
                        <div class="mb-3">
                            <strong>الرقم الوزاري:</strong>
                            <span class="badge bg-primary ms-2">{{ transfer.employee.ministry_number }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>الاسم الكامل:</strong><br>
                            {{ transfer.employee.full_name }}
                        </div>
                        <div class="mb-3">
                            <strong>التخصص:</strong><br>
                            {{ transfer.employee.specialization }}
                        </div>
                        <div class="mb-0">
                            <strong>القسم الحالي:</strong><br>
                            <span class="badge bg-success">{{ transfer.employee.school }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="col-lg-8 mb-4">
            <div class="card form-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات النقل
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label for="previous_department">
                                        القسم السابق <span class="required">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="previous_department" 
                                           name="previous_department" 
                                           value="{{ transfer.previous_department }}" 
                                           required>
                                    <small class="form-text text-muted">القسم الذي انتقل منه الموظف</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label for="new_department">
                                        القسم الجديد <span class="required">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="new_department" 
                                           name="new_department" 
                                           value="{{ transfer.new_department }}" 
                                           required>
                                    <small class="form-text text-muted">القسم الذي انتقل إليه الموظف</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label for="transfer_date">
                                        تاريخ النقل <span class="required">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="transfer_date" 
                                           name="transfer_date" 
                                           value="{{ transfer.transfer_date|date:'Y-m-d' }}" 
                                           required>
                                    <small class="form-text text-muted">التاريخ الذي تم فيه النقل</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label for="start_date">
                                        تاريخ المباشرة <span class="required">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="start_date" 
                                           name="start_date" 
                                           value="{{ transfer.start_date|date:'Y-m-d' }}" 
                                           required>
                                    <small class="form-text text-muted">تاريخ بدء العمل في القسم الجديد</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-group">
                                <label for="notes">الملاحظات</label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="4" 
                                          placeholder="أدخل أي ملاحظات إضافية حول النقل...">{{ transfer.notes }}</textarea>
                                <small class="form-text text-muted">معلومات إضافية عن النقل (اختياري)</small>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-group mb-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save"></i> حفظ التعديلات
                                    </button>
                                    <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" 
                                       class="btn btn-secondary ms-2">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        آخر تحديث: {{ transfer.updated_at|date:"Y-m-d H:i" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer History -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card form-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-history"></i>
                        سجل النقل الحالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>من:</strong><br>
                                <span class="badge bg-warning text-dark">{{ transfer.previous_department }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>إلى:</strong><br>
                                <span class="badge bg-success">{{ transfer.new_department }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>تاريخ النقل:</strong><br>
                                {{ transfer.transfer_date }}
                            </div>
                            <div class="col-md-3">
                                <strong>تاريخ المباشرة:</strong><br>
                                {{ transfer.start_date }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Date validation
        $('#transfer_date, #start_date').on('change', function() {
            var transferDate = new Date($('#transfer_date').val());
            var startDate = new Date($('#start_date').val());
            
            if (transferDate && startDate && startDate < transferDate) {
                alert('تاريخ المباشرة يجب أن يكون بعد أو يساوي تاريخ النقل');
                $('#start_date').focus();
            }
        });

        // Form validation
        $('form').on('submit', function(e) {
            var previousDept = $('#previous_department').val().trim();
            var newDept = $('#new_department').val().trim();
            
            if (previousDept === newDept) {
                e.preventDefault();
                alert('القسم السابق والقسم الجديد لا يمكن أن يكونا متشابهين');
                $('#new_department').focus();
                return false;
            }
        });
    });
</script>
{% endblock %}