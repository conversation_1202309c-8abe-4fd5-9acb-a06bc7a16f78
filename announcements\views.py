from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from django.utils import timezone
from django.core.paginator import <PERSON>gin<PERSON>
from .models import Announcement
from .forms import Announce<PERSON>Form, AnnouncementSearchForm
from system_logs.models import SystemLog


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@login_required
def announcements_list(request):
    """View to display list of announcements with search and filters"""
    form = AnnouncementSearchForm(request.GET or None)
    announcements = Announcement.objects.select_related('created_by').all()
    
    # Apply search filters
    if form.is_valid():
        # Search in title and content
        search_query = form.cleaned_data.get('search')
        if search_query:
            announcements = announcements.filter(
                Q(title__icontains=search_query) |
                Q(content__icontains=search_query)
            )
        
        # Filter by type
        announcement_type = form.cleaned_data.get('announcement_type')
        if announcement_type:
            announcements = announcements.filter(announcement_type=announcement_type)
        
        # Filter by priority
        priority = form.cleaned_data.get('priority')
        if priority:
            announcements = announcements.filter(priority=priority)
        
        # Filter by status
        status = form.cleaned_data.get('status')
        if status == 'active':
            announcements = announcements.filter(is_active=True)
        elif status == 'inactive':
            announcements = announcements.filter(is_active=False)
        elif status == 'expired':
            now = timezone.now()
            announcements = announcements.filter(
                end_date__isnull=False,
                end_date__lt=now
            )
        
        # Filter by homepage display
        show_on_homepage = form.cleaned_data.get('show_on_homepage')
        if show_on_homepage == 'yes':
            announcements = announcements.filter(show_on_homepage=True)
        elif show_on_homepage == 'no':
            announcements = announcements.filter(show_on_homepage=False)
    
    # Pagination
    paginator = Paginator(announcements, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    total_announcements = Announcement.objects.count()
    active_announcements = Announcement.get_active_announcements().count()
    homepage_announcements = Announcement.get_homepage_announcements().count()
    
    context = {
        'form': form,
        'page_obj': page_obj,
        'announcements': page_obj,
        'total_announcements': total_announcements,
        'active_announcements': active_announcements,
        'homepage_announcements': homepage_announcements,
    }
    
    return render(request, 'announcements/announcements_list.html', context)


@login_required
def announcement_create(request):
    """View to create new announcement"""
    if request.method == 'POST':
        form = AnnouncementForm(request.POST)
        if form.is_valid():
            announcement = form.save(commit=False)
            announcement.created_by = request.user
            announcement.save()
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.SYSTEM,
                action=SystemLog.CREATE,
                page='announcement_create',
                object_id=str(announcement.id),
                object_repr=announcement.title,
                description=f'تم إضافة إعلان جديد: {announcement.title}'
            )
            
            messages.success(request, f'تم إضافة الإعلان "{announcement.title}" بنجاح.')
            return redirect('announcements:announcements_list')
    else:
        form = AnnouncementForm()
    
    context = {
        'form': form,
        'title': 'إضافة إعلان جديد'
    }
    
    return render(request, 'announcements/announcement_form.html', context)


@login_required
def announcement_detail(request, pk):
    """View to display announcement details"""
    announcement = get_object_or_404(Announcement, pk=pk)
    
    # Increment views count
    announcement.increment_views()
    
    context = {
        'announcement': announcement,
    }
    
    return render(request, 'announcements/announcement_detail.html', context)


@login_required
def announcement_update(request, pk):
    """View to update announcement"""
    announcement = get_object_or_404(Announcement, pk=pk)
    
    if request.method == 'POST':
        form = AnnouncementForm(request.POST, instance=announcement)
        if form.is_valid():
            form.save()
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.SYSTEM,
                action=SystemLog.UPDATE,
                page='announcement_update',
                object_id=str(announcement.id),
                object_repr=announcement.title,
                description=f'تم تحديث الإعلان: {announcement.title}'
            )
            
            messages.success(request, f'تم تحديث الإعلان "{announcement.title}" بنجاح.')
            return redirect('announcements:announcements_list')
    else:
        form = AnnouncementForm(instance=announcement)
    
    context = {
        'form': form,
        'announcement': announcement,
        'title': f'تعديل الإعلان: {announcement.title}'
    }
    
    return render(request, 'announcements/announcement_form.html', context)


@login_required
def announcement_delete(request, pk):
    """View to delete announcement"""
    announcement = get_object_or_404(Announcement, pk=pk)
    
    if request.method == 'POST':
        title = announcement.title
        
        # Log the action
        SystemLog.objects.create(
            user=request.user,
            ip_address=get_client_ip(request),
            module=SystemLog.SYSTEM,
            action=SystemLog.DELETE,
            page='announcement_delete',
            object_id=str(announcement.id),
            object_repr=title,
            description=f'تم حذف الإعلان: {title}'
        )
        
        announcement.delete()
        messages.success(request, f'تم حذف الإعلان "{title}" بنجاح.')
        return redirect('announcements:announcements_list')
    
    context = {
        'announcement': announcement,
    }
    
    return render(request, 'announcements/announcement_confirm_delete.html', context)


@login_required
def announcement_toggle_status(request, pk):
    """AJAX view to toggle announcement active status"""
    if request.method == 'POST':
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.is_active = not announcement.is_active
        announcement.save()
        
        # Log the action
        status = 'تفعيل' if announcement.is_active else 'إلغاء تفعيل'
        SystemLog.objects.create(
            user=request.user,
            ip_address=get_client_ip(request),
            module=SystemLog.SYSTEM,
            action=SystemLog.UPDATE,
            page='announcement_toggle_status',
            object_id=str(announcement.id),
            object_repr=announcement.title,
            description=f'تم {status} الإعلان: {announcement.title}'
        )
        
        # Get updated statistics
        total_announcements = Announcement.objects.count()
        active_announcements = Announcement.get_active_announcements().count()
        homepage_announcements = Announcement.get_homepage_announcements().count()

        return JsonResponse({
            'success': True,
            'is_active': announcement.is_active,
            'message': f'تم {status} الإعلان بنجاح.',
            'stats': {
                'total_announcements': total_announcements,
                'active_announcements': active_announcements,
                'homepage_announcements': homepage_announcements
            }
        })
    
    return JsonResponse({'success': False})


@login_required
def get_announcements_stats(request):
    """AJAX view to get updated announcement statistics"""
    total_announcements = Announcement.objects.count()
    active_announcements = Announcement.get_active_announcements().count()
    homepage_announcements = Announcement.get_homepage_announcements().count()

    return JsonResponse({
        'total_announcements': total_announcements,
        'active_announcements': active_announcements,
        'homepage_announcements': homepage_announcements
    })


@login_required
def announcement_click_tracking(request, pk):
    """AJAX view to track announcement clicks"""
    if request.method == 'POST':
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.increment_clicks()
        
        return JsonResponse({
            'success': True,
            'clicks_count': announcement.clicks_count
        })
    
    return JsonResponse({'success': False})


def get_homepage_announcements(request):
    """API view to get announcements for homepage display"""
    announcements = Announcement.get_homepage_announcements()[:5]  # Limit to 5
    
    data = []
    for announcement in announcements:
        data.append({
            'id': announcement.id,
            'title': announcement.title,
            'content': announcement.content,
            'type': announcement.announcement_type,
            'type_color': announcement.type_color,
            'type_icon': announcement.type_icon,
            'priority': announcement.priority,
            'priority_color': announcement.priority_color,
            'link_url': announcement.link_url,
            'link_text': announcement.link_text,
            'created_at': announcement.created_at.isoformat(),
        })
    
    return JsonResponse({'announcements': data})


def announcement_public_view(request, pk):
    """
    Public view for announcement details - no login required
    This is for viewing announcements from the homepage ticker
    """
    announcement = get_object_or_404(Announcement, pk=pk, is_active=True)
    
    # Track the view
    announcement.views_count += 1
    announcement.save(update_fields=['views_count'])
    
    # Get client IP for logging
    client_ip = get_client_ip(request)
    
    # Log the view (optional)
    try:
        SystemLog.objects.create(
            user=request.user if request.user.is_authenticated else None,
            action='VIEW_ANNOUNCEMENT_PUBLIC',
            model_name='Announcement',
            object_id=announcement.id,
            object_repr=str(announcement),
            ip_address=client_ip,
            details=f'عرض عام للإعلان: {announcement.title}'
        )
    except:
        pass  # Don't fail if logging fails
    
    context = {
        'announcement': announcement,
        'page_title': f'إعلان: {announcement.title}',
    }
    
    return render(request, 'announcements/announcement_public_view.html', context)
