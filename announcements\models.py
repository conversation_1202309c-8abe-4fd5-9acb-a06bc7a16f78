from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.conf import settings
from django.urls import reverse

User = get_user_model()


class Announcement(models.Model):
    """Model for system announcements"""
    
    PRIORITY_CHOICES = [
        ('low', _('منخفضة')),
        ('medium', _('متوسطة')),
        ('high', _('عالية')),
        ('urgent', _('عاجل')),
    ]
    
    TYPE_CHOICES = [
        ('info', _('إعلام')),
        ('warning', _('تحذير')),
        ('success', _('نجاح')),
        ('danger', _('خطر')),
    ]
    
    title = models.CharField(
        _('عنوان الإعلان'),
        max_length=200,
        help_text=_('عنوان مختصر للإعلان')
    )
    
    content = models.TextField(
        _('محتوى الإعلان'),
        help_text=_('نص الإعلان المراد عرضه')
    )
    
    announcement_type = models.CharField(
        _('نوع الإعلان'),
        max_length=20,
        choices=TYPE_CHOICES,
        default='info',
        help_text=_('نوع الإعلان يحدد اللون والأيقونة')
    )
    
    priority = models.CharField(
        _('الأولوية'),
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium',
        help_text=_('أولوية الإعلان')
    )
    
    is_active = models.BooleanField(
        _('مفعل'),
        default=True,
        help_text=_('الإعلانات المفعلة فقط تظهر في الموقع')
    )
    
    show_on_homepage = models.BooleanField(
        _('عرض في الصفحة الرئيسية'),
        default=True,
        help_text=_('عرض الإعلان في الشريط المتحرك بالصفحة الرئيسية')
    )
    
    start_date = models.DateTimeField(
        _('تاريخ البداية'),
        help_text=_('متى يبدأ عرض الإعلان')
    )
    
    end_date = models.DateTimeField(
        _('تاريخ النهاية'),
        blank=True,
        null=True,
        help_text=_('متى ينتهي عرض الإعلان (اتركه فارغاً للعرض دائماً)')
    )
    
    link_url = models.URLField(
        _('رابط الإعلان'),
        blank=True,
        null=True,
        help_text=_('رابط اختياري يتم فتحه عند النقر على الإعلان')
    )
    
    link_text = models.CharField(
        _('نص الرابط'),
        max_length=100,
        blank=True,
        null=True,
        help_text=_('نص يظهر للرابط (مثل: اقرأ المزيد)')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='announcements',
        verbose_name=_('أضيف بواسطة')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإضافة')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )
    
    views_count = models.PositiveIntegerField(
        _('عدد المشاهدات'),
        default=0,
        help_text=_('عدد مرات عرض الإعلان')
    )
    
    clicks_count = models.PositiveIntegerField(
        _('عدد النقرات'),
        default=0,
        help_text=_('عدد مرات النقر على الإعلان')
    )

    class Meta:
        verbose_name = _('إعلان')
        verbose_name_plural = _('الإعلانات')
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['is_active', 'show_on_homepage']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['priority', 'announcement_type']),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_priority_display()})"
    
    def get_absolute_url(self):
        return reverse('announcements:announcement_detail', kwargs={'pk': self.pk})
    
    @property
    def is_currently_active(self):
        """Check if announcement is currently active"""
        from django.utils import timezone
        now = timezone.now()
        
        if not self.is_active:
            return False
            
        if self.start_date > now:
            return False
            
        if self.end_date and self.end_date < now:
            return False
            
        return True
    
    @property
    def type_color(self):
        """Get bootstrap color class for announcement type"""
        color_map = {
            'info': 'primary',
            'warning': 'warning',
            'success': 'success',
            'danger': 'danger',
        }
        return color_map.get(self.announcement_type, 'primary')
    
    @property
    def type_icon(self):
        """Get FontAwesome icon for announcement type"""
        icon_map = {
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-triangle',
            'success': 'fa-check-circle',
            'danger': 'fa-exclamation-circle',
        }
        return icon_map.get(self.announcement_type, 'fa-info-circle')
    
    @property
    def priority_color(self):
        """Get color class for priority"""
        priority_map = {
            'low': 'secondary',
            'medium': 'primary',
            'high': 'warning',
            'urgent': 'danger',
        }
        return priority_map.get(self.priority, 'primary')
    
    def increment_views(self):
        """Increment views count"""
        self.views_count += 1
        self.save(update_fields=['views_count'])
    
    def increment_clicks(self):
        """Increment clicks count"""
        self.clicks_count += 1
        self.save(update_fields=['clicks_count'])

    @classmethod
    def get_active_announcements(cls):
        """Get currently active announcements"""
        from django.utils import timezone
        now = timezone.now()
        
        return cls.objects.filter(
            is_active=True,
            start_date__lte=now
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
        )
    
    @classmethod
    def get_homepage_announcements(cls):
        """Get announcements for homepage display"""
        return cls.get_active_announcements().filter(
            show_on_homepage=True
        ).order_by('-priority', '-created_at')
