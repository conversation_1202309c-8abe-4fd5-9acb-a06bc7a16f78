{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if is_create %}
        إضافة مدرسة صفرية - نظام شؤون الموظفين
    {% else %}
        تعديل مدرسة صفرية - نظام شؤون الموظفين
    {% endif %}
{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .form-group label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }
    
    .required-field::after {
        content: " *";
        color: #e74a3b;
    }
    
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    /* Select2 Custom Styles */
    .select2-container--bootstrap-5 .select2-selection {
        min-height: calc(1.5em + 0.75rem + 2px);
    }
    
    .select2-container--bootstrap-5 .select2-selection--single {
        padding: 0.375rem 0.75rem;
    }
    
    .select2-container {
        width: 100% !important;
    }
    
    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
    }
    
    .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-school text-warning me-2"></i>
            {% if is_create %}
                إضافة مدرسة صفرية
            {% else %}
                تعديل مدرسة صفرية
            {% endif %}
        </h2>
        <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">
                {% if is_create %}
                    <i class="fas fa-plus-circle me-2"></i>بيانات المدرسة الصفرية الجديدة
                {% else %}
                    <i class="fas fa-edit me-2"></i>تعديل بيانات المدرسة الصفرية
                {% endif %}
            </h6>
        </div>
        
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.school_choice.id_for_label }}" class="required-field">اسم المدرسة</label>
                            {{ form.school_choice }}
                            {% if form.school_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.school_choice.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">ابحث واختر المدرسة من القائمة</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.specialization_choice.id_for_label }}" class="required-field">التخصص</label>
                            {{ form.specialization_choice }}
                            {% if form.specialization_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.specialization_choice.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">ابحث واختر التخصص المطلوب</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="{{ form.vacancies_count.id_for_label }}" class="required-field">عدد الشواغر</label>
                            {{ form.vacancies_count }}
                            {% if form.vacancies_count.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.vacancies_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">أدخل عدد الشواغر المطلوبة</div>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label for="{{ form.justification.id_for_label }}" class="required-field">المبرر</label>
                    {{ form.justification }}
                    {% if form.justification.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.justification.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="help-text">اشرح سبب اعتبار هذه المدرسة صفرية في هذا التخصص</div>
                </div>

                <div class="form-group mb-4">
                    <label for="{{ form.actions.id_for_label }}">الإجراءات</label>
                    {{ form.actions }}
                    {% if form.actions.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.actions.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="help-text">اذكر الإجراءات المطلوبة أو المتخذة (اختياري)</div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-primary">
                        {% if is_create %}
                            <i class="fas fa-save"></i> إضافة المدرسة الصفرية
                        {% else %}
                            <i class="fas fa-save"></i> حفظ التغييرات
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    {% if not is_create %}
    <div class="card shadow mt-4">
        <div class="card-header bg-info text-white py-2">
            <h6 class="m-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>تاريخ الإضافة:</strong> {{ zero_school.created_at|date:"Y-m-d H:i" }}
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>آخر تحديث:</strong> {{ zero_school.updated_at|date:"Y-m-d H:i" }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 for school selection
    $('#id_school_choice').select2({
        theme: 'bootstrap-5',
        placeholder: 'ابحث عن المدرسة...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            },
            loadingMore: function() {
                return "جاري تحميل المزيد...";
            }
        }
    });
    
    // Initialize Select2 for specialization selection
    $('#id_specialization_choice').select2({
        theme: 'bootstrap-5',
        placeholder: 'ابحث عن التخصص...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });
    
    // Add form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var requiredFields = ['school_choice', 'specialization_choice', 'vacancies_count', 'justification'];
        
        requiredFields.forEach(function(fieldName) {
            var field = $('[name="' + fieldName + '"]');
            var value = field.val();
            
            if (!value || value.trim() === '') {
                field.addClass('is-invalid');
                // For Select2, also add error class to container
                if (field.hasClass('select2')) {
                    field.next('.select2-container').addClass('is-invalid');
                }
                isValid = false;
            } else {
                field.removeClass('is-invalid');
                if (field.hasClass('select2')) {
                    field.next('.select2-container').removeClass('is-invalid');
                }
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // Remove validation errors on input/change
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        if ($(this).hasClass('select2')) {
            $(this).next('.select2-container').removeClass('is-invalid');
        }
    });
    
    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Custom validation styles for Select2
    $('.select2-container').on('focus', function() {
        $(this).removeClass('is-invalid');
    });
});
</script>

<style>
/* Additional styles for Select2 validation */
.select2-container.is-invalid .select2-selection {
    border-color: #dc3545;
}

.select2-container.is-invalid .select2-selection:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
</style>
{% endblock %}