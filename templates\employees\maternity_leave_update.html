{% extends 'base.html' %}

{% block title %}تعديل إجازة الأمومة - {{ maternity_leave.employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">تعديل إجازة الأمومة</h6>
                    <div>
                        <a href="{% url 'employees:maternity_leave_detail' maternity_leave.pk %}" class="btn btn-info me-2">
                            <i class="fas fa-eye me-1"></i> عرض التفاصيل
                        </a>
                        <a href="{% url 'employees:maternity_leaves_list' %}" class="btn btn-dark text-white">
                            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Employee Information (Read-only) -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات الموظفة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>الرقم الوزاري:</strong> {{ maternity_leave.employee.ministry_number }}</p>
                                            <p><strong>الاسم الكامل:</strong> {{ maternity_leave.employee.full_name }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>التخصص:</strong> {{ maternity_leave.employee.specialization }}</p>
                                            <p><strong>القسم:</strong> {{ maternity_leave.employee.school }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="updateMaternityLeaveForm">
                        {% csrf_token %}
                        
                        <!-- Maternity Leave Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">تاريخ بداية الإجازة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ maternity_leave.start_date|date:'Y-m-d' }}" required>
                                    <div class="form-text">سيتم احتساب الإجازة من اليوم التالي</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date_display" class="form-label">تاريخ انتهاء الإجازة (تلقائي)</label>
                                    <input type="text" class="form-control" id="end_date_display" readonly 
                                           value="{{ maternity_leave.end_date|date:'Y-m-d' }}">
                                    <div class="form-text">يتم حساب تاريخ الانتهاء تلقائياً (90 يوم)</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">حالة الإجازة</label>
                                    <select class="form-control" id="is_active" name="is_active">
                                        <option value="True" {% if maternity_leave.is_active %}selected{% endif %}>نشطة</option>
                                        <option value="False" {% if not maternity_leave.is_active %}selected{% endif %}>منتهية</option>
                                    </select>
                                    <div class="form-text">يمكن تغيير حالة الإجازة يدوياً</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدة الإجازة</label>
                                    <input type="text" class="form-control" value="90 يوماً" readonly>
                                    <div class="form-text">مدة إجازة الأمومة ثابتة</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4" 
                                              placeholder="أدخل أي ملاحظات إضافية...">{{ maternity_leave.notes }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Current Status Alert -->
                        <div class="row mb-4">
                            <div class="col-12">
                                {% if maternity_leave.is_active %}
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>الحالة الحالية:</strong> إجازة نشطة
                                    </div>
                                {% else %}
                                    <div class="alert alert-secondary">
                                        <i class="fas fa-clock me-2"></i>
                                        <strong>الحالة الحالية:</strong> إجازة منتهية
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save me-1"></i> حفظ التعديلات
                                </button>
                                <a href="{% url 'employees:maternity_leave_detail' maternity_leave.pk %}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i> إلغاء
                                </a>
                                <a href="{% url 'employees:maternity_leave_delete' maternity_leave.pk %}" class="btn btn-danger" 
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟')">
                                    <i class="fas fa-trash me-1"></i> حذف الإجازة
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Calculate end date when start date changes
    $('#start_date').on('change', function() {
        const startDate = new Date($(this).val());
        if (startDate) {
            // Add 90 days to start date
            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 90);
            
            // Format date as YYYY-MM-DD
            const formattedEndDate = endDate.toISOString().split('T')[0];
            $('#end_date_display').val(formattedEndDate);
        }
    });
    
    // Form validation
    $('#updateMaternityLeaveForm').on('submit', function(e) {
        const startDate = $('#start_date').val();
        
        if (!startDate) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ بداية الإجازة');
            return false;
        }
        
        // Confirm update
        if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}