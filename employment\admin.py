from django.contrib import admin
from .models import Department, Position, EmploymentStatus, Employment, ZeroSchool, ServicePurchase

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name',)

@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name',)

@admin.register(EmploymentStatus)
class EmploymentStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')

@admin.register(Employment)
class EmploymentAdmin(admin.ModelAdmin):
    list_display = ('employee', 'department', 'position', 'status', 'start_date', 'end_date', 'is_current')
    list_filter = ('department', 'position', 'status', 'is_current')
    search_fields = ('employee__full_name', 'employee__ministry_number')
    date_hierarchy = 'start_date'


@admin.register(ZeroSchool)
class ZeroSchoolAdmin(admin.ModelAdmin):
    list_display = ('school_name', 'specialization', 'vacancies_count', 'created_at')
    list_filter = ('specialization', 'created_at')
    search_fields = ('school_name', 'justification')
    date_hierarchy = 'created_at'
    ordering = ('school_name', 'specialization')
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('school_name', 'specialization', 'vacancies_count')
        }),
        ('التفاصيل', {
            'fields': ('justification', 'actions')
        }),
    )


@admin.register(ServicePurchase)
class ServicePurchaseAdmin(admin.ModelAdmin):
    list_display = ('employee', 'target_school', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'target_school', 'notes')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('معلومات الشراء', {
            'fields': ('target_school',)
        }),
        ('تفاصيل إضافية', {
            'fields': ('notes', 'is_active')
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ('created_at', 'updated_at')
        return self.readonly_fields
