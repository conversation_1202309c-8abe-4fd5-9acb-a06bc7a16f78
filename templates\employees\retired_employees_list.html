{% extends 'base.html' %}
{% load static %}

{% block title %}المتقاعدين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .retirement-card {
        border-left: 4px solid #dc3545;
        border-radius: 8px;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        transition: all 0.3s ease;
    }
    
    .retirement-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .retirement-badge {
        background: linear-gradient(45deg, #dc3545, #e9ecef);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .employee-info {
        background: rgba(220, 53, 69, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .btn-action {
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .search-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid #dee2e6;
    }
    
    .retire-form {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid #bbdefb;
    }
    
    /* Search Results Styling */
    #searchResults {
        max-height: 300px;
        overflow-y: auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1050;
    }
    
    .employee-result-item {
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        text-align: right;
    }
    
    .employee-result-item:hover {
        background-color: #e3f2fd;
        transform: translateX(-5px);
    }
    
    .employee-result-item:active {
        background-color: #bbdefb;
    }
    
    .form-control.is-valid {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .employee-search-container {
        position: relative;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-clock text-danger"></i> المتقاعدين
        </h1>
        <div>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> العودة لبيانات الموظفين
            </a>
        </div>
    </div>

    <!-- Search and Add Section -->
    <div class="row mb-4">
        <!-- Search -->
        <div class="col-md-8">
            <div class="search-container">
                <form method="get" class="row g-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text bg-primary text-white">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" 
                                   name="search" 
                                   class="form-control" 
                                   placeholder="البحث بالرقم الوزاري، الرقم الوطني، الاسم، أو سبب التقاعد..."
                                   value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'employees:retired_employees_list' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times"></i> مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Add Retired Employee -->
        <div class="col-md-4">
            <div class="retire-form">
                <h6 class="font-weight-bold text-primary mb-3">
                    <i class="fas fa-user-plus"></i> إضافة موظف متقاعد
                </h6>
                <button type="button" class="btn btn-info w-100" data-bs-toggle="modal" data-bs-target="#retireEmployeeModal">
                    <i class="fas fa-user-clock"></i> تقاعد موظف
                </button>
            </div>
        </div>
    </div>

    <!-- Retired Employees Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list-ul"></i> قائمة المتقاعدين
                <span class="badge bg-danger text-white ms-2">
                    <i class="fas fa-user-clock"></i> {{ retired_employees|length }}
                </span>
            </h6>
        </div>
        <div class="card-body">
            {% if retired_employees %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-id-card text-info"></i> الرقم الوزاري</th>
                                <th><i class="fas fa-id-badge text-warning"></i> الرقم الوطني</th>
                                <th><i class="fas fa-user text-primary"></i> الاسم الكامل</th>
                                <th><i class="fas fa-calendar-alt text-danger"></i> تاريخ التقاعد</th>
                                <th><i class="fas fa-clipboard-list text-success"></i> سبب التقاعد</th>
                                <th><i class="fas fa-cogs text-secondary"></i> الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for retired_employee in retired_employees %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ retired_employee.employee.ministry_number }}
                                    </span>
                                </td>
                                <td>{{ retired_employee.employee.national_id }}</td>
                                <td>
                                    <strong class="text-primary">{{ retired_employee.employee.full_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-danger text-white fs-6" style="padding: 8px 12px; border-radius: 8px;">
                                        {{ retired_employee.retirement_date|date:"Y/m/d" }}
                                    </span>
                                </td>
                                <td>{{ retired_employee.retirement_reason }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'employees:retired_employee_detail' retired_employee.pk %}" 
                                           class="btn btn-sm btn-outline-info btn-action"
                                           title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'employees:retired_employee_update' retired_employee.pk %}" 
                                           class="btn btn-sm btn-outline-warning btn-action"
                                           title="تعديل البيانات">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'employees:retired_employee_delete' retired_employee.pk %}" 
                                           class="btn btn-sm btn-outline-danger btn-action"
                                           title="إلغاء التقاعد وإعادة للخدمة"
                                           onclick="return confirm('هل أنت متأكد من إلغاء تقاعد هذا الموظف؟\nسيتم إعادته لقائمة الموظفين النشطين.')">
                                            <i class="fas fa-undo-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد بيانات متقاعدين</h4>
                    <p class="text-muted">
                        {% if search_query %}
                            لم يتم العثور على نتائج للبحث "{{ search_query }}"
                        {% else %}
                            لا يوجد موظفين متقاعدين حالياً
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Retire Employee Modal -->
<div class="modal fade" id="retireEmployeeModal" tabindex="-1" aria-labelledby="retireEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="retireEmployeeModalLabel">
                    <i class="fas fa-user-clock"></i> تقاعد موظف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <!-- Employee Search Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="employee-search-container">
                                <div class="mb-3">
                                    <label for="{{ form.employee_search.id_for_label }}" class="form-label">
                                        <i class="fas fa-search text-primary"></i> {{ form.employee_search.label }}
                                    </label>
                                    {{ form.employee_search }}
                                    {% if form.employee_search.errors %}
                                        <div class="text-danger">{{ form.employee_search.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">ابدأ الكتابة للبحث عن الموظف... (يمكنك البحث بالرقم الوزاري أو الاسم)</small>
                                </div>
                                
                                <!-- Search Results -->
                                <div id="searchResults" class="list-group mb-3" style="display: none;"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.selected_employee_display.id_for_label }}" class="form-label">
                                    <i class="fas fa-user text-success"></i> {{ form.selected_employee_display.label }}
                                </label>
                                {{ form.selected_employee_display }}
                                {{ form.employee_id }}
                                {% if form.employee_id.errors %}
                                    <div class="text-danger">{{ form.employee_id.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">انقر في الحقل لإزالة الاختيار الحالي</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Retirement Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.retirement_date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar text-danger"></i> {{ form.retirement_date.label }}
                                </label>
                                {{ form.retirement_date }}
                                {% if form.retirement_date.errors %}
                                    <div class="text-danger">{{ form.retirement_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.retirement_reason.id_for_label }}" class="form-label">
                                    <i class="fas fa-clipboard-list text-warning"></i> {{ form.retirement_reason.label }}
                                </label>
                                {{ form.retirement_reason }}
                                {% if form.retirement_reason.errors %}
                                    <div class="text-danger">{{ form.retirement_reason.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            <i class="fas fa-sticky-note text-info"></i> {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-user-clock"></i> تقاعد الموظف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let searchTimeout;
    const searchResults = $('#searchResults');
    const searchInput = $('#employee_search_input');
    const selectedDisplay = $('#selected_employee_display');
    const employeeIdField = $('#id_employee_id');
    
    // Set today's date as default retirement date
    const today = new Date().toISOString().split('T')[0];
    $('#id_retirement_date').val(today);
    
    // Employee search functionality
    searchInput.on('input', function() {
        const query = $(this).val().trim();
        
        // Clear previous timeout
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.hide().empty();
            return;
        }
        
        // Debounce search
        searchTimeout = setTimeout(function() {
            searchEmployees(query);
        }, 300);
    });
    
    function searchEmployees(query) {
        $.ajax({
            url: '{% url "employees:search_employees_for_retirement" %}',
            method: 'GET',
            data: {
                'term': query
            },
            success: function(data) {
                displaySearchResults(data.results);
            },
            error: function() {
                searchResults.html('<div class="alert alert-danger">حدث خطأ أثناء البحث</div>').show();
            }
        });
    }
    
    function displaySearchResults(results) {
        searchResults.empty();
        
        if (results.length === 0) {
            searchResults.html('<div class="list-group-item">لا توجد نتائج</div>').show();
            return;
        }
        
        results.forEach(function(employee) {
            const resultItem = $(`
                <button type="button" class="list-group-item list-group-item-action employee-result-item" 
                        data-employee-id="${employee.id}"
                        data-employee-name="${employee.full_name}"
                        data-ministry-number="${employee.ministry_number}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${employee.full_name}</h6>
                            <small class="text-muted">الرقم الوزاري: ${employee.ministry_number}</small>
                        </div>
                        <div>
                            <span class="badge bg-primary">${employee.school}</span>
                        </div>
                    </div>
                </button>
            `);
            
            resultItem.on('click', function() {
                selectEmployee(employee);
            });
            
            searchResults.append(resultItem);
        });
        
        searchResults.show();
    }
    
    function selectEmployee(employee) {
        // Set selected employee
        employeeIdField.val(employee.id);
        selectedDisplay.val(`${employee.full_name} (${employee.ministry_number})`);
        
        // Clear search
        searchInput.val('');
        searchResults.hide().empty();
        
        // Show success feedback
        selectedDisplay.addClass('is-valid');
        setTimeout(function() {
            selectedDisplay.removeClass('is-valid');
        }, 2000);
    }
    
    // Clear selection
    selectedDisplay.on('focus', function() {
        if (confirm('هل تريد إزالة الاختيار الحالي؟')) {
            $(this).val('');
            employeeIdField.val('');
        }
    });
    
    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#employee_search_input, #searchResults').length) {
            searchResults.hide();
        }
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        const employeeId = employeeIdField.val();
        const retirementDate = $('#id_retirement_date').val();
        const retirementReason = $('#id_retirement_reason').val();
        
        if (!employeeId) {
            e.preventDefault();
            alert('الرجاء اختيار موظف للتقاعد');
            searchInput.focus();
            return false;
        }
        
        if (!retirementDate) {
            e.preventDefault();
            alert('الرجاء إدخال تاريخ التقاعد');
            $('#id_retirement_date').focus();
            return false;
        }
        
        if (!retirementReason.trim()) {
            e.preventDefault();
            alert('الرجاء إدخال سبب التقاعد');
            $('#id_retirement_reason').focus();
            return false;
        }
        
        // Final confirmation
        const selectedEmployeeName = selectedDisplay.val();
        const confirmed = confirm(`هل أنت متأكد من تقاعد الموظف:\n${selectedEmployeeName}؟`);
        
        if (!confirmed) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}