# Generated by Django 5.2 on 2025-06-22 16:58

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0005_alter_employee_options_employee_masters_degree_and_more'),
        ('employment', '0026_department_school_national_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServicePurchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_date', models.DateField(default=django.utils.timezone.now, verbose_name='Purchase Date')),
                ('service_years', models.DecimalField(decimal_places=2, help_text='عدد سنوات الخدمة المشتراة', max_digits=5, verbose_name='Service Years')),
                ('service_months', models.IntegerField(default=0, help_text='عدد أشهر الخدمة المشتراة', verbose_name='Service Months')),
                ('service_days', models.IntegerField(default=0, help_text='عدد أيام الخدمة المشتراة', verbose_name='Service Days')),
                ('purchase_amount', models.DecimalField(decimal_places=2, help_text='مبلغ شراء الخدمة', max_digits=10, verbose_name='Purchase Amount')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employees.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Service Purchase',
                'verbose_name_plural': 'Service Purchases',
                'ordering': ['-created_at'],
            },
        ),
    ]
