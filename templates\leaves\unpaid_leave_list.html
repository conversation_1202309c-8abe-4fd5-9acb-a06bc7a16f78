{% extends 'base.html' %}
{% load static %}

{% block title %}الإجازات بدون راتب - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">الإجازات بدون راتب</h1>
        <a href="{% url 'leaves:unpaid_leave_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة إجازة بدون راتب
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">جميع الإجازات بدون راتب</h6>
            <form class="d-flex" method="get">
                <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">بحث</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> الموظف</th>
                            <th><i class="fas fa-calendar-plus me-1"></i> تاريخ البداية</th>
                            <th><i class="fas fa-calendar-minus me-1"></i> تاريخ النهاية</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leaves %}
                        <tr>
                            <td>
                                <strong>{{ leave.employee.ministry_number }}</strong>
                            </td>
                            <td>
                                <a href="{% url 'employees:employee_detail' leave.employee.pk %}">
                                    {{ leave.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                            <td>
                                <a href="{% url 'leaves:unpaid_leave_detail' leave.pk %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{% url 'leaves:unpaid_leave_update' leave.pk %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'leaves:leave_delete' leave.pk %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">لا توجد إجازات بدون راتب</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
