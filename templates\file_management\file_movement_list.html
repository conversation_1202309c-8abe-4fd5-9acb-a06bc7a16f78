{% extends 'base.html' %}
{% load static %}

{% block title %}حركة الملفات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">حركة الملفات</h1>
        <div>
            <a href="{% url 'file_management:file_checkout' %}" class="btn btn-primary">
                <i class="fas fa-file-export"></i> تسجيل خروج ملف
            </a>
            <a href="{% url 'file_management:file_movement_create' %}" class="btn btn-success">
                <i class="fas fa-plus"></i> إضافة حركة ملف
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">جميع حركات الملفات</h6>
            <form class="d-flex" method="get">
                <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">بحث</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> اسم الموظف</th>
                            <th><i class="fas fa-file-export me-1"></i> تاريخ خروج الملف</th>
                            <th><i class="fas fa-file-import me-1"></i> تاريخ عودة الملف</th>
                            <th><i class="fas fa-info-circle me-1"></i> الحالة</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in file_movements %}
                        <tr>
                            <td>{{ movement.employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' movement.employee.pk %}">
                                    {{ movement.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ movement.checkout_date|date:"Y-m-d" }}</td>
                            <td>{% if movement.return_date %}{{ movement.return_date|date:"Y-m-d" }}{% else %}-{% endif %}</td>
                            <td>
                                {% if movement.status == 'out' %}
                                <span class="badge bg-warning text-dark">{{ movement.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-success">{{ movement.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'file_management:file_movement_detail' movement.pk %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                {% if movement.status == 'out' %}
                                <a href="{% url 'file_management:file_return' movement.pk %}" class="btn btn-success btn-sm">
                                    <i class="fas fa-file-import"></i> إرجاع
                                </a>
                                {% endif %}
                                <a href="{% url 'file_management:file_movement_update' movement.pk %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'file_management:file_movement_delete' movement.pk %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد حركات ملفات</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
