{% extends 'base.html' %}
{% load static %}

{% block title %}حقول BTEC{% endblock %}

{% block extra_css %}
<style>
    .field-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
    }

    .field-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .field-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
    }

    .field-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .field-description {
        opacity: 0.9;
        margin-bottom: 0;
    }

    .teacher-count {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 0.9rem;
        display: inline-block;
        margin-top: 10px;
    }

    .field-actions {
        padding: 15px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }

    .btn-action {
        padding: 8px 15px;
        margin: 2px;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        text-align: center;
        padding: 20px;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-tags text-primary me-2"></i>
                حقول BTEC
            </h1>
            <p class="text-muted mb-0">إدارة حقول BTEC التعليمية</p>
        </div>
        <div>
            <a href="{% url 'employment:btec_field_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> إضافة حقل جديد
            </a>
            <a href="{% url 'employment:btec_list' %}" class="btn btn-info">
                <i class="fas fa-graduation-cap"></i> معلمي BTEC
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ fields.count }}</div>
                <div class="stats-label">إجمالي الحقول</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ total_teachers|default:0 }}</div>
                <div class="stats-label">إجمالي المعلمين</div>
            </div>
        </div>
    </div>

    <!-- Fields Grid -->
    {% if fields %}
        <div class="row">
            {% for field in fields %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="field-card">
                    <div class="card-header">
                        <div class="field-name">
                            <i class="fas fa-tag me-2"></i>
                            {{ field.name }}
                        </div>
                        {% if field.description %}
                            <div class="field-description">
                                {{ field.description|truncatechars:100 }}
                            </div>
                        {% endif %}
                        <div class="teacher-count">
                            <i class="fas fa-users me-1"></i>
                            {{ field.teacher_count }} معلم
                        </div>
                    </div>
                    <div class="field-actions">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ field.created_at|date:"Y/m/d" }}
                            </small>
                            <div>
                                <a href="{% url 'employment:btec_field_update' field.pk %}"
                                   class="btn btn-warning btn-action" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if field.teacher_count == 0 %}
                                    <a href="{% url 'employment:btec_field_delete' field.pk %}"
                                       class="btn btn-danger btn-action" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                {% else %}
                                    <button class="btn btn-secondary btn-action"
                                            title="لا يمكن الحذف - يوجد معلمين مرتبطين" disabled>
                                        <i class="fas fa-lock"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="field-card">
                    <div class="empty-state">
                        <i class="fas fa-tags"></i>
                        <h4>لا توجد حقول BTEC</h4>
                        <p class="mb-4">لم يتم إضافة أي حقول BTEC بعد. ابدأ بإضافة الحقل الأول.</p>
                        <a href="{% url 'employment:btec_field_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة حقل BTEC الأول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add hover effects
    $('.field-card').hover(
        function() {
            $(this).find('.card-header').css('background', 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)');
        },
        function() {
            $(this).find('.card-header').css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
        }
    );

    // Tooltip for disabled delete buttons
    $('[title]').tooltip();
});
</script>
{% endblock %}
