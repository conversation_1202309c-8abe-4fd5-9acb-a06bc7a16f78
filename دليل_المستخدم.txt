دليل تشغيل نظام الموارد البشرية
==========================

مرحبًا بك في دليل تشغيل نظام الموارد البشرية. هذا الدليل سيساعدك في حل مشكلة "Python was not found" وتشغيل النظام بنجاح.

المشكلة:
-------
تظهر رسالة خطأ "Python was not found" عند محاولة تشغيل النظام، مما يشير إلى أن Python غير مثبت أو غير موجود في متغير PATH.

الحلول:
------

1. استخدام ملف simple_install_python.bat
   - هذا الملف سيساعدك في تثبيت Python من Microsoft Store أو تعطيل اختصارات تنفيذ التطبيقات.
   - انقر نقرًا مزدوجًا على الملف واتبع التعليمات.

2. استخدام ملف install_python_from_web.bat
   - هذا الملف سيفتح موقع Python الرسمي لتنزيل وتثبيت Python.
   - تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت.

3. استخدام ملف run_with_full_path.bat
   - هذا الملف سيحاول تشغيل النظام باستخدام المسار الكامل لـ Python.
   - يبحث في المواقع الشائعة لتثبيت Python.

4. استخدام ملف run_with_py.bat
   - هذا الملف سيحاول تشغيل النظام باستخدام أمر py بدلاً من python.
   - يعمل إذا كان Python Launcher مثبتًا.

5. استخدام ملف run_with_cmd.bat
   - هذا الملف سيحاول تشغيل النظام باستخدام CMD بدلاً من PowerShell.

خطوات الحل:
----------

1. قم بتثبيت Python أولاً باستخدام أحد الخيارات التالية:
   - استخدم ملف simple_install_python.bat واختر الخيار 1 لتثبيت Python من Microsoft Store.
   - استخدم ملف install_python_from_web.bat لتثبيت Python من الموقع الرسمي.

2. بعد تثبيت Python، حاول تشغيل النظام باستخدام أحد الخيارات التالية:
   - استخدم ملف simple_install_python.bat واختر الخيار 3 لتشغيل النظام.
   - استخدم ملف run_with_full_path.bat لتشغيل النظام باستخدام المسار الكامل لـ Python.
   - استخدم ملف run_with_py.bat لتشغيل النظام باستخدام py بدلاً من python.
   - استخدم ملف run_with_cmd.bat لتشغيل النظام باستخدام CMD.

3. إذا استمرت المشكلة، حاول تعطيل اختصارات تنفيذ التطبيقات:
   - استخدم ملف simple_install_python.bat واختر الخيار 2 لتعطيل اختصارات تنفيذ التطبيقات.
   - ابحث عن python.exe و python3.exe في القائمة وقم بإيقاف تشغيلهما.

ملاحظات إضافية:
--------------

- تأكد من تثبيت جميع المكتبات المطلوبة بعد تثبيت Python باستخدام الأمر:
  pip install -r requirements.txt

- إذا كنت تواجه مشاكل في تثبيت المكتبات، يمكنك استخدام الأمر:
  python -m pip install -r requirements.txt

- إذا كنت لا تزال تواجه مشاكل، يمكنك إنشاء بيئة افتراضية جديدة:
  python -m venv venv
  venv\Scripts\activate
  pip install -r requirements.txt
  python manage.py runserver
