{% extends 'base.html' %}
{% load static %}

{% block title %}حذف رقم الهوية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">حذف رقم الهوية</h1>
        <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-danger text-white">
            <h6 class="m-0 font-weight-bold">تأكيد حذف رقم الهوية</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> هل أنت متأكد من حذف رقم الهوية للموظف "{{ identification.employee.full_name }}"؟
            </div>

            <div class="card mb-4">
                <div class="card-header bg-light">
                    معلومات الموظف
                </div>
                <div class="card-body">
                    <p><strong>الرقم الوزاري:</strong> {{ identification.ministry_number }}</p>
                    <p><strong>الاسم الكامل:</strong> {{ identification.employee.full_name }}</p>
                    <p><strong>الرقم الوطني:</strong> {{ identification.national_id }}</p>
                    <p><strong>رقم الهوية:</strong> {{ identification.id_number }}</p>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="alert alert-danger">
                    <p>سيتم حذف رقم الهوية فقط، ولن يتم حذف بيانات الموظف الأخرى.</p>
                </div>
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> تأكيد الحذف
                    </button>
                    <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
