{% extends 'base.html' %}
{% load static %}

{% block title %}أنواع الإجازات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>أنواع الإجازات</h2>
    <div>
        <a href="{% url 'leaves:leave_type_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة نوع إجازة جديد
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع أنواع الإجازات</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>النوع</th>
                        <th>الوصف</th>
                        <th>الحد الأقصى للأيام سنوياً</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave_type in leave_types %}
                    <tr>
                        <td>{{ leave_type.get_name_display }}</td>
                        <td>{{ leave_type.description|default:"-" }}</td>
                        <td>{{ leave_type.max_days_per_year }}</td>
                        <td>
                            <a href="{% url 'leaves:leave_type_update' leave_type.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">لا يوجد أنواع إجازات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}


