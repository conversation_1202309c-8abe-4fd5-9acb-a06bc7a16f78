# Generated by Django 5.2 on 2025-06-17 08:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='عنوان مختصر للإعلان', max_length=200, verbose_name='عنوان الإعلان')),
                ('content', models.TextField(help_text='نص الإعلان المراد عرضه', verbose_name='محتوى الإعلان')),
                ('announcement_type', models.CharField(choices=[('info', 'إعلام'), ('warning', 'تحذير'), ('success', 'نجاح'), ('danger', 'خطر')], default='info', help_text='نوع الإعلان يحدد اللون والأيقونة', max_length=20, verbose_name='نوع الإعلان')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجل')], default='medium', help_text='أولوية الإعلان', max_length=20, verbose_name='الأولوية')),
                ('is_active', models.BooleanField(default=True, help_text='الإعلانات المفعلة فقط تظهر في الموقع', verbose_name='مفعل')),
                ('show_on_homepage', models.BooleanField(default=True, help_text='عرض الإعلان في الشريط المتحرك بالصفحة الرئيسية', verbose_name='عرض في الصفحة الرئيسية')),
                ('start_date', models.DateTimeField(help_text='متى يبدأ عرض الإعلان', verbose_name='تاريخ البداية')),
                ('end_date', models.DateTimeField(blank=True, help_text='متى ينتهي عرض الإعلان (اتركه فارغاً للعرض دائماً)', null=True, verbose_name='تاريخ النهاية')),
                ('link_url', models.URLField(blank=True, help_text='رابط اختياري يتم فتحه عند النقر على الإعلان', null=True, verbose_name='رابط الإعلان')),
                ('link_text', models.CharField(blank=True, help_text='نص يظهر للرابط (مثل: اقرأ المزيد)', max_length=100, null=True, verbose_name='نص الرابط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('views_count', models.PositiveIntegerField(default=0, help_text='عدد مرات عرض الإعلان', verbose_name='عدد المشاهدات')),
                ('clicks_count', models.PositiveIntegerField(default=0, help_text='عدد مرات النقر على الإعلان', verbose_name='عدد النقرات')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to=settings.AUTH_USER_MODEL, verbose_name='أضيف بواسطة')),
            ],
            options={
                'verbose_name': 'إعلان',
                'verbose_name_plural': 'الإعلانات',
                'ordering': ['-priority', '-created_at'],
                'indexes': [models.Index(fields=['is_active', 'show_on_homepage'], name='announcemen_is_acti_fe68a4_idx'), models.Index(fields=['start_date', 'end_date'], name='announcemen_start_d_c54642_idx'), models.Index(fields=['priority', 'announcement_type'], name='announcemen_priorit_603a2b_idx')],
            },
        ),
    ]
