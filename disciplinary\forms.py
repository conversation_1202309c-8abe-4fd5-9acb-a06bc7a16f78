from django import forms
from .models import PenaltyType, Penalty
from employees.models import Employee

class PenaltyTypeForm(forms.ModelForm):
    class Meta:
        model = PenaltyType
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class PenaltyForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    class Meta:
        model = Penalty
        fields = ['employee', 'penalty_type', 'date', 'description', 'decision_number', 'decision_date']
        widgets = {
            'employee': forms.HiddenInput(),
            'date': forms.DateInput(attrs={'type': 'date'}),
            'decision_date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
        }
