{% extends 'base.html' %}
{% load static %}

{% block title %}حذف موظف - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف موظف</h2>
    <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 bg-danger text-white">
        <h6 class="m-0 font-weight-bold">تأكيد الحذف</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تحذير:</strong> هل أنت متأكد من حذف هذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>

        <div class="card mb-4">
            <div class="card-header">
                معلومات التوظيف
            </div>
            <div class="card-body">
                <p><strong>الموظف:</strong> {{ employment.employee.full_name }}</p>
                <p><strong>القسم:</strong> {{ employment.department.name }}</p>
                <p><strong>المنصب:</strong> {{ employment.position.name }}</p>
                <p><strong>الحالة:</strong> {{ employment.status.get_name_display }}</p>
                <p><strong>تاريخ البداية:</strong> {{ employment.start_date }}</p>
                <p><strong>تاريخ النهاية:</strong> {{ employment.end_date|default:"-" }}</p>
                <p><strong>حالي:</strong>
                    {% if employment.is_current %}
                        <span class="badge bg-success">نعم</span>
                    {% else %}
                        <span class="badge bg-danger">لا</span>
                    {% endif %}
                </p>
            </div>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary me-md-2">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
