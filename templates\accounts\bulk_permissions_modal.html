{% load static %}

<div class="bulk-permission-management">
    <!-- Selected Users Info -->
    <div class="alert alert-info">
        <h6 class="mb-2">
            <i class="fas fa-users text-primary"></i>
            المستخدمين المحددين ({{ users.count }})
        </h6>
        <div class="selected-users">
            {% for user in users %}
                <span class="badge bg-primary me-1 mb-1">{{ user.username }}</span>
            {% endfor %}
        </div>
        <small class="text-muted">
            سيتم تطبيق الصلاحيات المحددة أدناه على جميع المستخدمين المحددين
        </small>
    </div>

    <!-- Bulk Action Options -->
    <div class="mb-4">
        <h6 class="text-primary">
            <i class="fas fa-cogs"></i>
            نوع العملية
        </h6>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="bulkAction" id="replacePermissions" value="replace" checked>
            <label class="form-check-label" for="replacePermissions">
                <strong>استبدال الصلاحيات</strong> - حذف جميع الصلاحيات الحالية واستبدالها بالجديدة
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="bulkAction" id="addPermissions" value="add">
            <label class="form-check-label" for="addPermissions">
                <strong>إضافة صلاحيات</strong> - إضافة الصلاحيات الجديدة للصلاحيات الموجودة
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="bulkAction" id="removePermissions" value="remove">
            <label class="form-check-label" for="removePermissions">
                <strong>إزالة صلاحيات</strong> - إزالة الصلاحيات المحددة من المستخدمين
            </label>
        </div>
    </div>

    <!-- Quick Presets -->
    <div class="mb-4">
        <h6 class="text-primary">
            <i class="fas fa-magic"></i>
            قوالب سريعة
        </h6>
        <div class="btn-group flex-wrap" role="group">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="applyBulkPreset('full_admin')">
                <i class="fas fa-crown"></i>
                مدير كامل
            </button>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="applyBulkPreset('admin')">
                <i class="fas fa-user-shield"></i>
                مدير
            </button>
            <button type="button" class="btn btn-outline-warning btn-sm" onclick="applyBulkPreset('supervisor')">
                <i class="fas fa-user-tie"></i>
                مشرف
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" onclick="applyBulkPreset('user')">
                <i class="fas fa-user"></i>
                مستخدم
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="applyBulkPreset('readonly')">
                <i class="fas fa-eye"></i>
                قراءة فقط
            </button>
            <button type="button" class="btn btn-outline-dark btn-sm" onclick="applyBulkPreset('clear')">
                <i class="fas fa-eraser"></i>
                مسح الكل
            </button>
        </div>
    </div>

    <!-- Module Selection -->
    <div class="mb-4">
        <h6 class="text-primary">
            <i class="fas fa-cubes"></i>
            اختيار الوحدات
        </h6>
        <div class="row">
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAllModules" onchange="toggleAllModules(this.checked)">
                    <label class="form-check-label fw-bold" for="selectAllModules">
                        تحديد/إلغاء تحديد جميع الوحدات
                    </label>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            {% for module_name, module_data in modules.items %}
            <div class="col-md-6 mb-3">
                <div class="card bulk-module-card" data-module="{{ module_name }}">
                    <div class="card-body p-3">
                        <div class="form-check mb-2">
                            <input class="form-check-input module-checkbox" type="checkbox" 
                                   id="bulk_module_{{ module_name }}" value="{{ module_name }}"
                                   onchange="toggleModuleCard('{{ module_name }}', this.checked)">
                            <label class="form-check-label fw-bold" for="bulk_module_{{ module_name }}">
                                <i class="fas fa-cube text-primary"></i>
                                {{ module_data.name }}
                            </label>
                        </div>
                        
                        <div class="module-permissions" style="display: none;">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" 
                                               id="bulk_{{ module_name }}_view" name="{{ module_name }}_can_view">
                                        <label class="form-check-label" for="bulk_{{ module_name }}_view">
                                            <i class="fas fa-eye text-info"></i>
                                            عرض
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" 
                                               id="bulk_{{ module_name }}_add" name="{{ module_name }}_can_add">
                                        <label class="form-check-label" for="bulk_{{ module_name }}_add">
                                            <i class="fas fa-plus text-success"></i>
                                            إضافة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" 
                                               id="bulk_{{ module_name }}_edit" name="{{ module_name }}_can_edit">
                                        <label class="form-check-label" for="bulk_{{ module_name }}_edit">
                                            <i class="fas fa-edit text-warning"></i>
                                            تعديل
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check form-check-sm">
                                        <input class="form-check-input" type="checkbox" 
                                               id="bulk_{{ module_name }}_delete" name="{{ module_name }}_can_delete">
                                        <label class="form-check-label" for="bulk_{{ module_name }}_delete">
                                            <i class="fas fa-trash text-danger"></i>
                                            حذف
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <small class="text-muted">الصفحات:</small>
                                <div class="btn-group btn-group-sm w-100">
                                    <button type="button" class="btn btn-outline-success btn-xs" 
                                            onclick="selectAllModulePages('{{ module_name }}')">
                                        تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-xs" 
                                            onclick="clearAllModulePages('{{ module_name }}')">
                                        إلغاء الكل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Summary -->
    <div class="alert alert-warning">
        <h6 class="mb-2">
            <i class="fas fa-exclamation-triangle"></i>
            تنبيه مهم
        </h6>
        <ul class="mb-0">
            <li>سيتم تطبيق هذه الصلاحيات على <strong>{{ users.count }}</strong> مستخدم</li>
            <li>العملية لا يمكن التراجع عنها</li>
            <li>تأكد من اختيار الصلاحيات المناسبة قبل التطبيق</li>
            <li>المديرين الرئيسيين (superuser) لن يتأثروا بهذه العملية</li>
        </ul>
    </div>
</div>

<style>
.bulk-module-card {
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.bulk-module-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.module-permissions {
    border-top: 1px solid #e9ecef;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.form-check-sm .form-check-input {
    margin-top: 0.1rem;
}

.form-check-sm .form-check-label {
    font-size: 0.875rem;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.selected-users {
    max-height: 100px;
    overflow-y: auto;
}
</style>

<script>
// Bulk permission presets
const bulkPresets = {
    'full_admin': {
        name: 'مدير كامل',
        permissions: ['can_view', 'can_add', 'can_edit', 'can_delete'],
        modules: 'all'
    },
    'admin': {
        name: 'مدير',
        permissions: ['can_view', 'can_add', 'can_edit'],
        modules: 'all'
    },
    'supervisor': {
        name: 'مشرف',
        permissions: ['can_view', 'can_add', 'can_edit'],
        excludeModules: ['accounts', 'backup', 'system_logs']
    },
    'user': {
        name: 'مستخدم',
        permissions: ['can_view', 'can_add'],
        excludeModules: ['accounts', 'backup', 'system_logs', 'disciplinary']
    },
    'readonly': {
        name: 'قراءة فقط',
        permissions: ['can_view'],
        excludeModules: ['accounts', 'backup', 'system_logs']
    },
    'clear': {
        name: 'مسح الكل',
        permissions: [],
        modules: 'none'
    }
};

function toggleAllModules(checked) {
    document.querySelectorAll('.module-checkbox').forEach(checkbox => {
        checkbox.checked = checked;
        toggleModuleCard(checkbox.value, checked);
    });
}

function toggleModuleCard(moduleName, checked) {
    const card = document.querySelector(`[data-module="${moduleName}"]`);
    const permissionsDiv = card.querySelector('.module-permissions');
    
    if (checked) {
        card.classList.add('selected');
        permissionsDiv.style.display = 'block';
    } else {
        card.classList.remove('selected');
        permissionsDiv.style.display = 'none';
        // Clear all permissions for this module
        card.querySelectorAll('input[type="checkbox"]:not(.module-checkbox)').forEach(cb => {
            cb.checked = false;
        });
    }
}

function applyBulkPreset(presetName) {
    const preset = bulkPresets[presetName];
    if (!preset) return;
    
    if (!confirm(`هل تريد تطبيق قالب "${preset.name}"؟`)) {
        return;
    }
    
    // Clear all selections first
    document.querySelectorAll('.module-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        toggleModuleCard(checkbox.value, false);
    });
    
    if (preset.modules === 'none') {
        return; // Nothing to select
    }
    
    // Select modules and permissions
    document.querySelectorAll('.bulk-module-card').forEach(card => {
        const moduleName = card.dataset.module;
        
        // Skip excluded modules
        if (preset.excludeModules && preset.excludeModules.includes(moduleName)) {
            return;
        }
        
        // Select module
        const moduleCheckbox = card.querySelector('.module-checkbox');
        moduleCheckbox.checked = true;
        toggleModuleCard(moduleName, true);
        
        // Apply permissions
        preset.permissions.forEach(permission => {
            const permCheckbox = card.querySelector(`[name="${moduleName}_${permission}"]`);
            if (permCheckbox) {
                permCheckbox.checked = true;
            }
        });
    });
    
    // Update select all checkbox
    updateSelectAllCheckbox();
}

function selectAllModulePages(moduleName) {
    // This would select all pages for the module
    // Implementation depends on how pages are handled in bulk operations
    console.log(`Selecting all pages for module: ${moduleName}`);
}

function clearAllModulePages(moduleName) {
    // This would clear all pages for the module
    console.log(`Clearing all pages for module: ${moduleName}`);
}

function updateSelectAllCheckbox() {
    const allModuleCheckboxes = document.querySelectorAll('.module-checkbox');
    const checkedModuleCheckboxes = document.querySelectorAll('.module-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllModules');
    
    if (checkedModuleCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedModuleCheckboxes.length === allModuleCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// Add event listeners to module checkboxes
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.module-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAllCheckbox);
    });
});
</script>