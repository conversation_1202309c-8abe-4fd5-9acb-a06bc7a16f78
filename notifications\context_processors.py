from .models import Notification

def notifications_context(request):
    """
    Context processor to add notifications to all templates
    """
    context = {
        'notifications': [],
        'unread_notifications_count': 0,
    }
    
    if request.user.is_authenticated:
        # Get user notifications
        notifications = Notification.get_user_notifications(request.user, limit=10)
        unread_count = Notification.get_unread_count(request.user)
        
        context.update({
            'notifications': notifications,
            'unread_notifications_count': unread_count,
        })
    
    return context
