/* Sidebar Improvements CSS - Based on Employee Page Design */

/* Sidebar styling */
.sidebar {
    background: #000000 !important;
    border-radius: 0 0 0 15px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Sidebar header */
.sidebar-header {
    padding: 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-align: center;
    display: block;
    transition: all 0.3s ease;
}

.sidebar-brand:hover {
    transform: scale(1.05);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    color: #ffffff;
}

/* Sidebar menu items */
.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-item {
    padding: 0.25rem 1rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #ffffff;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar-link:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    z-index: -1;
}

.sidebar-link:hover:before {
    width: 100%;
}

.sidebar-link:hover,
.sidebar-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sidebar-link i {
    margin-left: 1rem;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.sidebar-link:hover i {
    transform: scale(1.2);
}

.sidebar-link .fa-chevron-down {
    margin-left: 0;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.sidebar-link[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

/* Submenu styling */
.sidebar-submenu {
    padding-right: 1.5rem;
    margin-top: 0.25rem;
}

.sidebar-submenu .sidebar-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 15px;
    color: #ffffff;
}

.sidebar-submenu .sidebar-item {
    padding: 0.15rem 0;
    opacity: 0;
    transform: translateX(20px);
    animation: fadeInRight 0.5s ease forwards;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation delay for submenu items */
.sidebar-submenu .sidebar-item:nth-child(1) { animation-delay: 0.1s; }
.sidebar-submenu .sidebar-item:nth-child(2) { animation-delay: 0.15s; }
.sidebar-submenu .sidebar-item:nth-child(3) { animation-delay: 0.2s; }
.sidebar-submenu .sidebar-item:nth-child(4) { animation-delay: 0.25s; }
.sidebar-submenu .sidebar-item:nth-child(5) { animation-delay: 0.3s; }
.sidebar-submenu .sidebar-item:nth-child(6) { animation-delay: 0.35s; }
.sidebar-submenu .sidebar-item:nth-child(7) { animation-delay: 0.4s; }
.sidebar-submenu .sidebar-item:nth-child(8) { animation-delay: 0.45s; }
.sidebar-submenu .sidebar-item:nth-child(9) { animation-delay: 0.5s; }
.sidebar-submenu .sidebar-item:nth-child(10) { animation-delay: 0.55s; }

/* Divider styling */
.sidebar-item.mt-4 {
    margin-top: 1.5rem !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

/* Responsive sidebar */
@media (max-width: 991.98px) {
    .sidebar {
        border-radius: 0;
    }

    .sidebar-link {
        padding: 0.75rem 0.5rem;
    }

    .sidebar-link i {
        margin-left: 0.5rem;
    }
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}
