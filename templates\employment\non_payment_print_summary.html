<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملخص عدم الصرف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .table th, .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .stats-section {
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .stat-item {
            flex: 1;
            padding: 10px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }

        .button-container {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            margin: 0 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 10px;
            }
            
            .header {
                margin-bottom: 20px;
            }
            
            .stats-section {
                margin-bottom: 20px;
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>المملكة الأردنية الهاشمية</h2>
        <h3>وزارة التربية والتعليم</h3>
        <h4>مديرية التربية والتعليم / لواء قصبة المفرق</h4>
        <h5>قسم شؤون الموظفين</h5>
        <h3 class="mt-4">ملخص عدم الصرف للعام {{ current_year }}</h3>
        <p>التاريخ: {{ today_date }}</p>
        {% if latest_transfer_date %}
        <p>آخر ترحيل: {{ latest_transfer_date|date:"Y-m-d H:i" }}</p>
        {% endif %}
    </div>

    <!-- Statistics Section -->
    <div class="stats-section">
        <h5 class="text-center mb-3">
            <i class="fas fa-chart-bar me-2"></i>
            إحصائيات عدم الصرف
        </h5>
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-number">{{ total_transferred }}</div>
                <div class="stat-label">إجمالي السجلات المرحلة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_days }}</div>
                <div class="stat-label">إجمالي أيام عدم الصرف</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">
                    {% if latest_transfer_date %}
                        {{ latest_transfer_date|date:"Y-m-d" }}
                    {% else %}
                        -
                    {% endif %}
                </div>
                <div class="stat-label">تاريخ آخر ترحيل</div>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الرقم الوزاري</th>
                    <th>الاسم الكامل</th>
                    <th>القسم</th>
                    <th>تاريخ عدم الصرف</th>
                    <th>عدد الأيام</th>
                    <th>تاريخ الترحيل</th>
                    <th>الملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for record in transferred_records %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ record.ministry_number }}</td>
                    <td>{{ record.employee.full_name }}</td>
                    <td>{{ record.department_name }}</td>
                    <td>{{ record.date|date:"Y-m-d" }}</td>
                    <td>{{ record.days_count }}</td>
                    <td>{{ record.transfer_date|date:"Y-m-d H:i" }}</td>
                    <td>{{ record.notes|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center">لا توجد سجلات عدم صرف مرحلة</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Summary Footer -->
    {% if transferred_records %}
    <div class="stats-section">
        <h6 class="text-center mb-2">
            <i class="fas fa-info-circle me-2"></i>
            ملخص الإحصائيات
        </h6>
        <p class="text-center mb-0">
            تم ترحيل <strong>{{ total_transferred }}</strong> سجل عدم صرف 
            بإجمالي <strong>{{ total_days }}</strong> يوم
            {% if latest_transfer_date %}
            - آخر ترحيل في {{ latest_transfer_date|date:"Y-m-d" }}
            {% endif %}
        </p>
    </div>
    {% endif %}

    <div class="button-container no-print">
        <button class="btn btn-primary" onclick="window.print();">
            <i class="fas fa-print"></i> طباعة
        </button>
        <a href="{% url 'employment:non_payment_summary' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لصفحة الملخص
        </a>
    </div>

    <script>
        // Auto-print when the page loads (optional)
        window.onload = function() {
            // Wait a moment for the page to fully render
            setTimeout(function() {
                // Uncomment the next line if you want auto-print
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
