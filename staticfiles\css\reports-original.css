/* Reports Original CSS - Restore original table styling for report pages */

/* Override global table styling for report pages */
.reports-page .table th {
    background: #f8f9fc !important; /* Light gray background */
    color: #4e73df !important; /* Primary blue color for text */
    font-weight: 600;
    border: 1px solid #e3e6f0;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    padding: 0.75rem;
    animation: none !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Special styling for report table headers */
.reports-page .thead-light th {
    background-color: #f8f9fc !important;
    color: #4e73df !important;
    border-color: #e3e6f0;
}

/* Restore original table row styling */
.reports-page .table tbody tr {
    transition: none;
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
}

.reports-page .table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Restore original table cell styling */
.reports-page .table td {
    border: 1px solid #e3e6f0;
    padding: 0.75rem;
    vertical-align: middle;
}

/* Special styling for colored cells in reports */
.reports-page .bg-success {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.reports-page .bg-danger {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

.reports-page .bg-info {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
}

.reports-page .bg-warning {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

.reports-page .bg-success.bg-opacity-25 {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.reports-page .bg-danger.bg-opacity-25 {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

.reports-page .bg-info.bg-opacity-25 {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
}

.reports-page .bg-warning.bg-opacity-25 {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

/* Restore original table styling for bordered tables */
.reports-page .table-bordered {
    border: 1px solid #e3e6f0;
}

.reports-page .table-bordered th,
.reports-page .table-bordered td {
    border: 1px solid #e3e6f0;
}

/* Restore original table styling for striped tables */
.reports-page .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Restore original table styling for hover effect */
.reports-page .table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Restore original styling for table responsive */
.reports-page .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Restore original styling for DataTables */
.reports-page .dataTables_wrapper .dataTables_length,
.reports-page .dataTables_wrapper .dataTables_filter,
.reports-page .dataTables_wrapper .dataTables_info,
.reports-page .dataTables_wrapper .dataTables_processing,
.reports-page .dataTables_wrapper .dataTables_paginate {
    color: #333;
}

.reports-page .dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0 !important;
    margin: 0 0.2rem;
}

.reports-page .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #4e73df !important;
    border-color: #4e73df !important;
    color: white !important;
}

/* Restore original styling for card in reports */
.reports-page .card {
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: none;
}

.reports-page .card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.reports-page .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    color: #4e73df;
    padding: 0.75rem 1.25rem;
}

/* Restore original styling for buttons in reports */
.reports-page .btn {
    border-radius: 0.35rem;
    transition: none;
}

.reports-page .btn:hover {
    transform: none;
    box-shadow: none;
}

/* Restore original styling for badges in reports */
.reports-page .badge {
    border-radius: 0.35rem;
    padding: 0.25rem 0.5rem;
}

/* Restore original styling for form controls in reports */
.reports-page .form-control,
.reports-page .form-select {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

/* Restore original styling for input groups in reports */
.reports-page .input-group {
    box-shadow: none;
    border-radius: 0.35rem;
}

.reports-page .input-group .form-control,
.reports-page .input-group .form-select {
    border: 1px solid #d1d3e2;
}

/* Restore original styling for pagination in reports */
.reports-page .pagination {
    margin-top: 1rem;
}

.reports-page .page-item .page-link {
    border-radius: 0;
    margin: 0;
    color: #4e73df;
    border: 1px solid #dddfeb;
}

.reports-page .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Restore original styling for alerts in reports */
.reports-page .alert {
    border-radius: 0.35rem;
}

/* Restore original styling for modals in reports */
.reports-page .modal-content {
    border-radius: 0.35rem;
}

.reports-page .modal-header {
    background-color: #f8f9fc;
    color: #4e73df;
}

/* Restore original styling for empty state in reports */
.reports-page .empty-state {
    text-align: center;
    padding: 2rem 1rem;
}

/* Restore original styling for page header in reports */
.reports-page .d-flex.justify-content-between.align-items-center.mb-4 h2 {
    color: #5a5c69;
    font-weight: 700;
}
