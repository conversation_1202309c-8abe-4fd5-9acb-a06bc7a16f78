{% extends 'base.html' %}
{% load static %}

{% block title %}حذف اسم الحالة المرضية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        color: #e74a3b;
        font-weight: bold;
        margin-bottom: 20px;
    }
    
    .condition-info {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .condition-info h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }
    
    .info-item {
        margin-bottom: 15px;
    }
    
    .info-label {
        font-weight: bold;
        color: #5a5c69;
    }
    
    .info-value {
        color: #3a3b45;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف اسم الحالة المرضية</h2>
    <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى القائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="delete-warning">
            <i class="fas fa-exclamation-triangle"></i>
            هل أنت متأكد من رغبتك في حذف اسم الحالة المرضية هذا؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <div class="condition-info">
            <h4>معلومات اسم الحالة المرضية</h4>
            <div class="row">
                <div class="col-md-12">
                    <div class="info-item">
                        <div class="info-label">اسم الحالة المرضية</div>
                        <div class="info-value">{{ condition_name.name }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="info-item">
                        <div class="info-label">الوصف</div>
                        <div class="info-value">
                            {% if condition_name.description %}
                                {{ condition_name.description|linebreaks }}
                            {% else %}
                                لا يوجد وصف
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
                <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
