# Generated manually for internal transfer feature

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0006_maternityleave'),
    ]

    operations = [
        migrations.CreateModel(
            name='InternalTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_department', models.CharField(help_text='Department the employee transferred from', max_length=255, verbose_name='Previous Department')),
                ('new_department', models.CharField(help_text='Department the employee transferred to', max_length=255, verbose_name='New Department')),
                ('transfer_date', models.DateField(help_text='Date when the transfer occurred', verbose_name='Transfer Date')),
                ('start_date', models.DateField(help_text='Date when employee started working in new department', verbose_name='Start Date')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the transfer', null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='internal_transfers', to='employees.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Internal Transfer',
                'verbose_name_plural': 'Internal Transfers',
                'ordering': ['-transfer_date'],
            },
        ),
    ]