# Generated by Django 5.2 on 2025-07-06 09:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system_logs', '0003_systemlog_operating_system'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemError',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='التاريخ والوقت')),
                ('error_type', models.CharField(choices=[('syntax_error', 'خطأ في بناء الجملة'), ('type_error', 'خطأ في نوع البيانات'), ('value_error', 'خطأ في قيمة البيانات'), ('attribute_error', 'خطأ في الخاصية'), ('key_error', 'خطأ في المفتاح'), ('index_error', 'خطأ في الفهرس'), ('name_error', 'خطأ في الاسم'), ('import_error', 'خطأ في الاستيراد'), ('permission_error', 'خطأ في الصلاحيات'), ('database_error', 'خطأ في قاعدة البيانات'), ('validation_error', 'خطأ في التحقق'), ('http_error', 'خطأ في HTTP'), ('template_error', 'خطأ في القالب'), ('other_error', 'خطأ آخر')], max_length=50, verbose_name='نوع الخطأ')),
                ('error_message', models.TextField(verbose_name='رسالة الخطأ')),
                ('error_description', models.TextField(help_text='شرح مبسط للخطأ', verbose_name='وصف الخطأ')),
                ('page_url', models.CharField(max_length=500, verbose_name='رابط الصفحة')),
                ('page_name', models.CharField(max_length=255, verbose_name='اسم الصفحة')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='مسار الملف')),
                ('line_number', models.IntegerField(blank=True, null=True, verbose_name='رقم السطر')),
                ('function_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الدالة')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')),
                ('stack_trace', models.TextField(blank=True, null=True, verbose_name='تتبع المكدس')),
                ('request_method', models.CharField(blank=True, max_length=10, null=True, verbose_name='طريقة الطلب')),
                ('request_data', models.TextField(blank=True, null=True, verbose_name='بيانات الطلب')),
                ('module', models.CharField(blank=True, max_length=50, null=True, verbose_name='الوحدة')),
                ('severity', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('critical', 'حرج')], default='medium', max_length=20, verbose_name='مستوى الخطورة')),
                ('status', models.CharField(choices=[('new', 'جديد'), ('in_progress', 'قيد المعالجة'), ('resolved', 'تم الحل'), ('ignored', 'تم التجاهل')], default='new', max_length=20, verbose_name='الحالة')),
                ('resolution_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('occurrence_count', models.IntegerField(default=1, verbose_name='عدد مرات الحدوث')),
                ('last_occurrence', models.DateTimeField(auto_now=True, verbose_name='آخر حدوث')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_errors', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_errors', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'خطأ النظام',
                'verbose_name_plural': 'أخطاء النظام',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['timestamp'], name='system_logs_timesta_7754f5_idx'), models.Index(fields=['error_type'], name='system_logs_error_t_68af51_idx'), models.Index(fields=['severity'], name='system_logs_severit_95c37a_idx'), models.Index(fields=['status'], name='system_logs_status_94ede9_idx'), models.Index(fields=['module'], name='system_logs_module_75775d_idx')],
            },
        ),
    ]
