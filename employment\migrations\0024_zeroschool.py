# Generated by Django 5.2 on 2025-06-20 14:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0023_nonpayment'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZeroSchool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('school_name', models.CharField(max_length=255, verbose_name='اسم المدرسة')),
                ('specialization', models.Char<PERSON>ield(choices=[('teacher', 'معلم'), ('user', 'مستخدم'), ('guard', 'حارس')], max_length=20, verbose_name='التخصص')),
                ('vacancies_count', models.PositiveIntegerField(verbose_name='عدد الشواغر')),
                ('justification', models.TextField(verbose_name='المبرر')),
                ('actions', models.TextField(blank=True, null=True, verbose_name='الإجراءات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'مدرسة صفرية',
                'verbose_name_plural': 'المدارس الصفرية',
                'ordering': ['school_name', 'specialization'],
                'unique_together': {('school_name', 'specialization')},
            },
        ),
    ]
