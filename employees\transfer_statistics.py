"""
Internal Transfer Statistics Module
Provides statistical data about internal transfers
"""

from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import InternalTransfer, Employee


def get_transfer_statistics():
    """Get comprehensive statistics about internal transfers"""
    
    # Basic counts
    total_transfers = InternalTransfer.objects.count()
    total_employees_with_transfers = InternalTransfer.objects.values('employee').distinct().count()
    
    # Time-based statistics
    today = timezone.now().date()
    this_month = today.replace(day=1)
    last_month = (this_month - timedelta(days=1)).replace(day=1)
    this_year = today.replace(month=1, day=1)
    
    transfers_this_month = InternalTransfer.objects.filter(
        transfer_date__gte=this_month
    ).count()
    
    transfers_last_month = InternalTransfer.objects.filter(
        transfer_date__gte=last_month,
        transfer_date__lt=this_month
    ).count()
    
    transfers_this_year = InternalTransfer.objects.filter(
        transfer_date__gte=this_year
    ).count()
    
    # Department statistics
    most_transfers_from = InternalTransfer.objects.values('previous_department').annotate(
        count=Count('id')
    ).order_by('-count').first()
    
    most_transfers_to = InternalTransfer.objects.values('new_department').annotate(
        count=Count('id')
    ).order_by('-count').first()
    
    # Employee with most transfers
    employee_most_transfers = InternalTransfer.objects.values('employee__full_name', 'employee__ministry_number').annotate(
        count=Count('id')
    ).order_by('-count').first()
    
    # Recent transfers (last 30 days)
    recent_transfers = InternalTransfer.objects.filter(
        transfer_date__gte=today - timedelta(days=30)
    ).count()
    
    return {
        'total_transfers': total_transfers,
        'total_employees_with_transfers': total_employees_with_transfers,
        'transfers_this_month': transfers_this_month,
        'transfers_last_month': transfers_last_month,
        'transfers_this_year': transfers_this_year,
        'recent_transfers': recent_transfers,
        'most_transfers_from': most_transfers_from,
        'most_transfers_to': most_transfers_to,
        'employee_most_transfers': employee_most_transfers,
        'statistics_date': today
    }


def get_monthly_transfer_trend(months=12):
    """Get monthly transfer trend for the last N months"""
    
    today = timezone.now().date()
    trends = []
    
    for i in range(months):
        # Calculate the start of the month
        month_start = (today.replace(day=1) - timedelta(days=i*30)).replace(day=1)
        # Calculate the end of the month
        if month_start.month == 12:
            month_end = month_start.replace(year=month_start.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = month_start.replace(month=month_start.month + 1, day=1) - timedelta(days=1)
        
        # Count transfers in this month
        transfer_count = InternalTransfer.objects.filter(
            transfer_date__gte=month_start,
            transfer_date__lte=month_end
        ).count()
        
        trends.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'count': transfer_count
        })
    
    return reversed(trends)


def get_department_transfer_summary():
    """Get summary of transfers by department"""
    
    # Departments that employees transferred FROM
    from_departments = InternalTransfer.objects.values('previous_department').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Departments that employees transferred TO
    to_departments = InternalTransfer.objects.values('new_department').annotate(
        count=Count('id')
    ).order_by('-count')
    
    return {
        'from_departments': from_departments,
        'to_departments': to_departments
    }


def get_recent_transfers(limit=10):
    """Get recent transfers with employee details"""
    
    return InternalTransfer.objects.select_related('employee').order_by('-created_at')[:limit]


def export_statistics_summary():
    """Export statistics summary for reports"""
    
    stats = get_transfer_statistics()
    trends = list(get_monthly_transfer_trend(6))
    department_summary = get_department_transfer_summary()
    recent = list(get_recent_transfers(5))
    
    return {
        'overview': stats,
        'trends': trends,
        'departments': department_summary,
        'recent_transfers': recent,
        'generated_at': timezone.now()
    }