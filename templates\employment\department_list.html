{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الأقسام - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة الأقسام</h2>
    <div>
        <a href="{% url 'employment:department_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة قسم جديد
        </a>
        <a href="{% url 'employment:department_import' %}" class="btn btn-success">
            <i class="fas fa-file-import"></i> استيراد من إكسل
        </a>
        <button type="button" id="exportExcelBtn" class="btn btn-info">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </button>
        <a href="{% url 'employment:school_details' %}" class="btn btn-success">
            <i class="fas fa-school"></i> تفاصيل المدارس
        </a>
        <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للكادر
        </a>
    </div>
</div>

<!-- Alert for success messages -->
<div id="alert-container"></div>

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <form method="GET" id="filterForm">
            <div class="row g-3">
                <!-- Search Filter -->
                <div class="col-md-3">
                    <label class="form-label small">
                        <i class="fas fa-search text-primary"></i> البحث
                    </label>
                    <input type="text" class="form-control form-control-sm" name="search" 
                           value="{{ search_query }}" placeholder="ابحث في الأسماء والأوصاف...">
                </div>

                <!-- Workplace Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-map-marker-alt text-success"></i> مكان العمل
                    </label>
                    <select class="form-select form-select-sm" name="workplace">
                        <option value="">جميع الأماكن</option>
                        {% for value, label in workplace_choices %}
                        <option value="{{ value }}" {% if workplace_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- School Type Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-graduation-cap text-info"></i> تصنيف المدرسة
                    </label>
                    <select class="form-select form-select-sm" name="school_type">
                        <option value="">جميع التصنيفات</option>
                        {% for value, label in school_type_choices %}
                        <option value="{{ value }}" {% if school_type_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- School Gender Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-venus-mars text-warning"></i> جنس المدرسة
                    </label>
                    <select class="form-select form-select-sm" name="school_gender">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in school_gender_choices %}
                        <option value="{{ value }}" {% if school_gender_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Directorate Type Filter -->
                <div class="col-md-2">
                    <label class="form-label small">
                        <i class="fas fa-sitemap text-secondary"></i> يتبع لـ
                    </label>
                    <select class="form-select form-select-sm" name="directorate_type">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in directorate_type_choices %}
                        <option value="{{ value }}" {% if directorate_type_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- School Ownership Filter -->
                <div class="col-md-1">
                    <label class="form-label small">
                        <i class="fas fa-home text-danger"></i> الملكية
                    </label>
                    <select class="form-select form-select-sm" name="school_ownership">
                        <option value="">الكل</option>
                        {% for value, label in school_ownership_choices %}
                        <option value="{{ value }}" {% if school_ownership_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row mt-3">
                <div class="col-12 d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-filter"></i> تطبيق الفلاتر
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilters">
                        <i class="fas fa-undo"></i> إعادة ضبط
                    </button>
                </div>
            </div>
        </form>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    عرض جميع الأقسام
                </small>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع الأقسام</h6>
        <div class="d-flex align-items-center">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" id="searchInput" class="form-control form-control-sm"
                       placeholder="بحث في الأقسام...">
            </div>
            <span class="badge bg-info ms-2">{{ departments|length }} قسم</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="departmentsTable">
                <thead class="table-light">
                    <tr>
                        <th width="3%">
                            <i class="fas fa-hashtag text-primary"></i>
                            الرقم
                        </th>
                        <th width="12%">
                            <i class="fas fa-id-card text-info"></i>
                            الرقم الوطني للمدرسة
                        </th>
                        <th width="16%">
                            <i class="fas fa-building text-primary"></i>
                            القسم
                        </th>
                        <th width="8%">
                            <i class="fas fa-map-marker-alt text-success"></i>
                            مكان العمل
                        </th>
                        <th width="8%">
                            <i class="fas fa-graduation-cap text-info"></i>
                            تصنيف المدرسة
                        </th>
                        <th width="7%">
                            <i class="fas fa-venus-mars text-warning"></i>
                            جنس المدرسة
                        </th>
                        <th width="10%">
                            <i class="fas fa-arrow-up text-success"></i>
                            أعلى صف
                        </th>
                        <th width="10%">
                            <i class="fas fa-arrow-down text-danger"></i>
                            أدنى صف
                        </th>
                        <th width="8%">
                            <i class="fas fa-home text-warning"></i>
                            ملكية المدرسة
                        </th>
                        <th width="8%">
                            <i class="fas fa-sitemap text-secondary"></i>
                            يتبع لـ
                        </th>
                        <th width="6%">
                            <i class="fas fa-users text-primary"></i>
                            عدد الموظفين
                        </th>
                        <th width="12%">
                            <i class="fas fa-file-alt text-muted"></i>
                            الوصف
                        </th>
                        <th width="18%">
                            <i class="fas fa-cogs text-dark"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in departments %}
                    <tr data-department-id="{{ department.id }}">
                        <td>{{ forloop.counter }}</td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-field text-info"
                                  data-field="school_national_id"
                                  data-original="{{ department.school_national_id|default:'' }}"
                                  title="انقر للتعديل">
                                {% if department.school_national_id %}
                                    <i class="fas fa-id-card me-1"></i>{{ department.school_national_id }}
                                {% else %}
                                    <span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span>
                                {% endif %}
                            </span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="editable-field fw-bold text-primary"
                                  data-field="name"
                                  data-original="{{ department.name }}"
                                  title="انقر للتعديل">
                                {{ department.name }}
                            </span>
                            {% if department.employees.count > 0 %}
                            <br><small class="badge bg-success">
                                <i class="fas fa-edit me-1"></i>قابل للتعديل
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="editable-select"
                                  data-field="workplace"
                                  data-original="{{ department.workplace }}"
                                  title="انقر للتعديل">
                                {{ department.get_workplace_display }}
                            </span>
                        </td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-select"
                                  data-field="school_type"
                                  data-original="{{ department.school_type|default:'' }}"
                                  title="انقر للتعديل">
                                {{ department.get_school_type_display|default:"-" }}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-select"
                                  data-field="school_gender"
                                  data-original="{{ department.school_gender|default:'' }}"
                                  title="انقر للتعديل">
                                {{ department.get_school_gender_display|default:"-" }}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-select"
                                  data-field="highest_grade"
                                  data-original="{{ department.highest_grade|default:'' }}"
                                  title="انقر للتعديل">
                                {% if department.highest_grade %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-arrow-up me-1"></i>
                                        {{ department.get_highest_grade_display }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        انقر للإضافة
                                    </span>
                                {% endif %}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-select"
                                  data-field="lowest_grade"
                                  data-original="{{ department.lowest_grade|default:'' }}"
                                  title="انقر للتعديل">
                                {% if department.lowest_grade %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-arrow-down me-1"></i>
                                        {{ department.get_lowest_grade_display }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        انقر للإضافة
                                    </span>
                                {% endif %}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.workplace == 'school' %}
                            <span class="editable-select"
                                  data-field="school_ownership"
                                  data-original="{{ department.school_ownership|default:'' }}"
                                  title="انقر للتعديل">
                                {% if department.school_ownership %}
                                    {% if department.school_ownership == 'owned' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-home me-1"></i>ملك
                                        </span>
                                    {% elif department.school_ownership == 'rented' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-handshake me-1"></i>مستأجر
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-plus-circle me-1"></i>انقر للإضافة
                                    </span>
                                {% endif %}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.workplace == 'directorate' %}
                            <span class="editable-select"
                                  data-field="directorate_type"
                                  data-original="{{ department.directorate_type|default:'' }}"
                                  title="انقر للتعديل">
                                {{ department.get_directorate_type_display|default:"-" }}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-minus-circle me-1"></i>-
                            </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge bg-primary">
                                <i class="fas fa-user-friends me-1"></i>
                                {{ department.employees.count }}
                            </span>
                        </td>
                        <td>
                            <span class="editable-field"
                                  data-field="description"
                                  data-original="{{ department.description|default:'' }}"
                                  title="انقر للتعديل">
                                {% if department.description %}
                                    {{ department.description|truncatechars:50 }}
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-file-alt me-1"></i>لا يوجد وصف
                                    </span>
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'employment:department_detail' department.pk %}"
                                   class="btn btn-info btn-sm" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'employment:department_update' department.pk %}"
                                   class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if department.employees.count == 0 %}
                                <a href="{% url 'employment:department_delete' department.pk %}"
                                   class="btn btn-danger btn-sm" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% else %}
                                <button class="btn btn-danger btn-sm" disabled
                                        title="لا يمكن حذف قسم يحتوي على موظفين">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="11" class="text-center">
                            <div class="alert alert-info mb-0">
                                لا يوجد أقسام. <a href="{% url 'employment:department_create' %}">إضافة قسم جديد</a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .editable-field, .editable-select {
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 3px;
        transition: background-color 0.2s;
    }

    .editable-field:hover, .editable-select:hover {
        background-color: #f8f9fa;
        border: 1px dashed #007bff;
    }

    .editing {
        background-color: #fff3cd !important;
        border: 1px solid #ffc107 !important;
    }

    .edit-input {
        width: 100%;
        border: 1px solid #007bff;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: inherit;
        font-family: inherit;
    }

    .edit-select {
        width: 100%;
        border: 1px solid #007bff;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: inherit;
        font-family: inherit;
    }

    .save-cancel-buttons {
        margin-top: 5px;
    }

    .save-cancel-buttons .btn {
        padding: 2px 8px;
        font-size: 12px;
        margin-right: 5px;
    }

    /* Table enhancements */
    #departmentsTable {
        font-size: 14px;
    }

    #departmentsTable th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #dee2e6;
        padding: 12px 8px;
    }

    #departmentsTable th i {
        margin-left: 8px;
        font-size: 14px;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    #departmentsTable th:hover i {
        opacity: 1;
        transform: scale(1.1);
    }

    #departmentsTable td {
        vertical-align: middle;
        border: 1px solid #dee2e6;
        padding: 8px 6px;
    }

    /* Compact columns styling */
    #departmentsTable td:nth-child(1), /* الرقم */
    #departmentsTable td:nth-child(3), /* مكان العمل */
    #departmentsTable td:nth-child(4), /* تصنيف المدرسة */
    #departmentsTable td:nth-child(5), /* جنس المدرسة */
    #departmentsTable td:nth-child(8), /* يتبع لـ */
    #departmentsTable td:nth-child(9)  /* عدد الموظفين */ {
        text-align: center;
        font-size: 13px;
        padding: 8px 4px;
    }

    /* Actions column styling */
    #departmentsTable td:nth-child(11) { /* الإجراءات */
        text-align: center;
        padding: 8px 16px;
    }

    #departmentsTable tbody tr:hover {
        background-color: #f5f5f5;
    }

    .btn-group .btn {
        margin: 0 3px;
        padding: 8px 12px;
        font-size: 13px;
        min-width: 40px;
    }

    .btn-group {
        display: flex;
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
    }

    .badge {
        font-size: 0.75em;
    }

    /* Search input styling */
    #searchInput {
        border: 2px solid #e3e6f0;
        border-radius: 0.375rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    #searchInput:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        #departmentsTable {
            font-size: 12px;
        }

        .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        #searchInput {
            width: 150px !important;
        }
    }

    /* Filter section styling */
    .filter-select {
        font-size: 13px;
    }

    .select2-container--default .select2-selection--single {
        height: 31px;
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 29px;
        font-size: 13px;
        color: #5a5c69;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 29px;
    }

    .select2-dropdown {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .form-label.small {
        font-weight: 600;
        margin-bottom: 5px;
    }

    #filterInfo {
        background-color: #f8f9fc;
        padding: 8px 12px;
        border-radius: 0.35rem;
        border-left: 4px solid #4e73df;
    }

    /* Active filter styling */
    .select2-container.filter-active .select2-selection--single {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
    }

    .select2-container.filter-active .select2-selection__rendered {
        color: #1976d2 !important;
        font-weight: 600 !important;
    }

    /* Filter section animations */
    .filter-select {
        transition: all 0.3s ease;
    }

    .select2-container {
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize Select2 for filters
    $('.select2-filter').select2({
        placeholder: function() {
            return $(this).find('option:first').text();
        },
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            }
        },
        minimumInputLength: 0,
        templateResult: function(data) {
            if (!data.id) {
                return data.text;
            }

            // Count occurrences for this option
            var count = 0;
            if (data.id !== '') {
                var columnIndex = $(data.element).closest('select').data('column');
                $('#departmentsTable tbody tr:visible').each(function() {
                    if ($(this).find('td').length > 1) { // Skip empty rows
                        var cellText = $(this).find('td:eq(' + columnIndex + ')').text().trim();
                        if (cellText === data.text || (cellText !== '-' && cellText === data.text)) {
                            count++;
                        }
                    }
                });
            }

            var $result = $('<span>' + data.text + (count > 0 ? ' (' + count + ')' : '') + '</span>');
            return $result;
        }
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        applyAllFilters();
    });

    // Filter functionality
    $('.filter-select').on('change', function() {
        // Add visual feedback for active filters
        var $container = $(this).next('.select2-container');
        if ($(this).val()) {
            $container.addClass('filter-active');
        } else {
            $container.removeClass('filter-active');
        }
        applyAllFilters();
    });

    // Reset filters - redirect to clean URL
    $('#resetFilters').on('click', function() {
        window.location.href = "{% url 'employment:department_list' %}";
    });
    
    // Auto-submit form when filters change
    $('#filterForm select, #filterForm input[name="search"]').on('change', function() {
        $('#filterForm').submit();
    });

    // Apply all filters function
    function applyAllFilters() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var departmentFilter = $('#departmentFilter').val();
        var workplaceFilter = $('#workplaceFilter').val();
        var schoolTypeFilter = $('#schoolTypeFilter').val();
        var schoolGenderFilter = $('#schoolGenderFilter').val();
        var directorateTypeFilter = $('#directorateTypeFilter').val();

        var visibleCount = 0;
        var totalCount = 0;
        var activeFilters = [];

        $('#departmentsTable tbody tr').each(function() {
            var $row = $(this);
            var rowText = $row.text().toLowerCase();
            var show = true;
            totalCount++;

            // Skip empty row
            if ($row.find('td').length === 1) {
                $row.hide();
                totalCount--;
                return;
            }

            // Search filter
            if (searchValue && rowText.indexOf(searchValue) === -1) {
                show = false;
            }

            // Department filter
            if (departmentFilter) {
                var departmentText = $row.find('td:eq(1) .editable-field').text().trim();
                if (departmentText !== departmentFilter) {
                    show = false;
                }
            }

            // Workplace filter
            if (workplaceFilter) {
                var workplaceText = $row.find('td:eq(2) .editable-select').text().trim();
                if (workplaceText !== workplaceFilter) {
                    show = false;
                }
            }

            // School type filter
            if (schoolTypeFilter) {
                var schoolTypeText = $row.find('td:eq(3) .editable-select').text().trim();
                if (schoolTypeText === '-' || schoolTypeText !== schoolTypeFilter) {
                    show = false;
                }
            }

            // School gender filter
            if (schoolGenderFilter) {
                var schoolGenderText = $row.find('td:eq(4) .editable-select').text().trim();
                if (schoolGenderText === '-' || schoolGenderText !== schoolGenderFilter) {
                    show = false;
                }
            }

            // Directorate type filter
            if (directorateTypeFilter) {
                var directorateTypeText = $row.find('td:eq(7) .editable-select').text().trim();
                if (directorateTypeText === '-' || directorateTypeText !== directorateTypeFilter) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('.badge.bg-info').text(visibleCount + ' من ' + totalCount + ' قسم');

        // Update filter info
        updateFilterInfo(searchValue, departmentFilter, workplaceFilter, schoolTypeFilter, schoolGenderFilter, directorateTypeFilter, visibleCount);

        // Update filter counts
        updateFilterCounts();
    }

    // Update filter counts function
    function updateFilterCounts() {
        $('.select2-filter').each(function() {
            var $select = $(this);
            var columnIndex = $select.data('column');
            var selectId = $select.attr('id');

            $select.find('option').each(function() {
                var $option = $(this);
                var optionValue = $option.val();
                var count = 0;

                if (optionValue !== '') {
                    $('#departmentsTable tbody tr:visible').each(function() {
                        if ($(this).find('td').length > 1) { // Skip empty rows
                            var cellText = '';

                            // Get text based on filter type
                            if (selectId === 'departmentFilter') {
                                cellText = $(this).find('td:eq(' + columnIndex + ') .editable-field').text().trim();
                            } else if (selectId === 'workplaceFilter') {
                                cellText = $(this).find('td:eq(' + columnIndex + ') .editable-select').text().trim();
                            } else {
                                cellText = $(this).find('td:eq(' + columnIndex + ') .editable-select').text().trim();
                            }

                            if (cellText === optionValue || (cellText !== '-' && cellText === optionValue)) {
                                count++;
                            }
                        }
                    });
                }
            });
        });
    }

    // Update filter info function
    function updateFilterInfo(search, dept, workplace, schoolType, schoolGender, directorateType, count) {
        var filters = [];

        if (search) filters.push('البحث: "' + search + '"');
        if (dept) filters.push('القسم: ' + dept);
        if (workplace) filters.push('مكان العمل: ' + workplace);
        if (schoolType) filters.push('تصنيف المدرسة: ' + schoolType);
        if (schoolGender) filters.push('جنس المدرسة: ' + schoolGender);
        if (directorateType) filters.push('يتبع لـ: ' + directorateType);

        var infoText = '';
        if (filters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + filters.join(' | ') + ' - النتائج: ' + count;
        } else {
            infoText = '<i class="fas fa-info-circle"></i> عرض جميع الأقسام - المجموع: ' + count;
        }

        $('#filterInfo').html(infoText);
    }

    // Initialize filter states on page load
    $('.filter-select').each(function() {
        if ($(this).val()) {
            $(this).next('.select2-container').addClass('filter-active');
        }
    });

    // Initial filter application
    applyAllFilters();

    // Define choice options
    const choiceOptions = {
        workplace: [
            {value: 'directorate', text: 'المديرية'},
            {value: 'school', text: 'المدارس'}
        ],
        school_type: [
            {value: '', text: '-'},
            {value: 'primary', text: 'اساسي'},
            {value: 'secondary', text: 'ثانوي'}
        ],
        school_gender: [
            {value: '', text: '-'},
            {value: 'male', text: 'ذكور'},
            {value: 'female', text: 'اناث'},
            {value: 'mixed', text: 'مختلطة'}
        ],
        directorate_type: [
            {value: '', text: '-'},
            {value: 'manager', text: 'يتبع للمدير'},
            {value: 'administrative', text: 'الأقسام الإدارية'},
            {value: 'educational', text: 'الأقسام التعليمية'}
        ],
        highest_grade: [
            {value: '', text: '-'},
            {value: 'kg', text: 'رياض الأطفال'},
            {value: 'grade1', text: 'الصف الأول'},
            {value: 'grade2', text: 'الصف الثاني'},
            {value: 'grade3', text: 'الصف الثالث'},
            {value: 'grade4', text: 'الصف الرابع'},
            {value: 'grade5', text: 'الصف الخامس'},
            {value: 'grade6', text: 'الصف السادس'},
            {value: 'grade7', text: 'الصف السابع'},
            {value: 'grade8', text: 'الصف الثامن'},
            {value: 'grade9', text: 'الصف التاسع'},
            {value: 'grade10', text: 'الصف العاشر'},
            {value: 'grade11', text: 'الصف الحادي عشر'},
            {value: 'grade12', text: 'الصف الثاني عشر'}
        ],
        lowest_grade: [
            {value: '', text: '-'},
            {value: 'kg', text: 'رياض الأطفال'},
            {value: 'grade1', text: 'الصف الأول'},
            {value: 'grade2', text: 'الصف الثاني'},
            {value: 'grade3', text: 'الصف الثالث'},
            {value: 'grade4', text: 'الصف الرابع'},
            {value: 'grade5', text: 'الصف الخامس'},
            {value: 'grade6', text: 'الصف السادس'},
            {value: 'grade7', text: 'الصف السابع'},
            {value: 'grade8', text: 'الصف الثامن'},
            {value: 'grade9', text: 'الصف التاسع'},
            {value: 'grade10', text: 'الصف العاشر'},
            {value: 'grade11', text: 'الصف الحادي عشر'},
            {value: 'grade12', text: 'الصف الثاني عشر'}
        ],
        school_ownership: [
            {value: '', text: '-'},
            {value: 'owned', text: 'ملك'},
            {value: 'rented', text: 'مستأجر'}
        ]
    };

    // Use event delegation for better handling of dynamic elements
    $(document).on('click', '.editable-field', function() {
        if ($(this).hasClass('editing')) return;
        handleEditableField($(this));
    });

    $(document).on('click', '.editable-select', function() {
        if ($(this).hasClass('editing')) return;
        handleEditableSelect($(this));
    });

    // Handle cancel buttons with event delegation
    $(document).on('click', '.cancel-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const $editingElement = $(this).closest('.editing');
        if ($editingElement.length) {
            const originalHtml = $editingElement.data('original-html');
            if (originalHtml) {
                $editingElement.removeClass('editing').html(originalHtml);
            }
        }
    });

    // Save field function
    function saveField(departmentId, fieldName, fieldValue, $element, originalHtml) {
        $.ajax({
            url: '',
            method: 'POST',
            data: {
                'department_id': departmentId,
                'field_name': fieldName,
                'field_value': fieldValue,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // Update the element
                    $element.removeClass('editing');
                    $element.data('original', fieldValue);
                    $element.html(response.display_value || fieldValue);

                    // Show success message
                    showAlert('success', response.message);

                    // Update related fields if workplace changed
                    if (fieldName === 'workplace') {
                        updateWorkplaceRelatedFields($element.closest('tr'), fieldValue);
                    }

                    // Update employee count
                    updateEmployeeCount($element.closest('tr'), departmentId);
                } else {
                    showAlert('danger', response.error);
                    $element.removeClass('editing').html(originalHtml);
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ أثناء الحفظ');
                $element.removeClass('editing').html(originalHtml);
            }
        });
    }

    // Update workplace related fields
    function updateWorkplaceRelatedFields($row, workplace) {
        if (workplace === 'school') {
            // Show school-related fields
            $row.find('td:eq(3)').html('<span class="editable-select" data-field="school_type" data-original="" title="انقر للتعديل"><span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span></span>');
            $row.find('td:eq(4)').html('<span class="editable-select" data-field="school_gender" data-original="" title="انقر للتعديل"><span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span></span>');
            $row.find('td:eq(5)').html('<span class="editable-select" data-field="highest_grade" data-original="" title="انقر للتعديل"><span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span></span>');
            $row.find('td:eq(6)').html('<span class="editable-select" data-field="lowest_grade" data-original="" title="انقر للتعديل"><span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span></span>');
            $row.find('td:eq(7)').html('<span class="text-muted"><i class="fas fa-minus-circle me-1"></i>-</span>');
        } else {
            // Show directorate-related fields
            $row.find('td:eq(3)').html('<span class="text-muted"><i class="fas fa-minus-circle me-1"></i>-</span>');
            $row.find('td:eq(4)').html('<span class="text-muted"><i class="fas fa-minus-circle me-1"></i>-</span>');
            $row.find('td:eq(5)').html('<span class="text-muted"><i class="fas fa-minus-circle me-1"></i>-</span>');
            $row.find('td:eq(6)').html('<span class="text-muted"><i class="fas fa-minus-circle me-1"></i>-</span>');
            $row.find('td:eq(7)').html('<span class="editable-select" data-field="directorate_type" data-original="" title="انقر للتعديل"><span class="text-muted"><i class="fas fa-plus-circle me-1"></i>انقر للإضافة</span></span>');
        }

        // Re-bind event handlers for new elements
        setTimeout(function() {
            bindEditableEvents();
        }, 100);
    }

    // Update employee count
    function updateEmployeeCount($row, departmentId) {
        // This could be enhanced to fetch actual count via AJAX if needed
        // For now, we'll keep the existing count
    }

    // Bind editable events to new elements
    function bindEditableEvents() {
        // Remove existing handlers to avoid duplicates
        $('.editable-field').off('click');
        $('.editable-select').off('click');

        // Re-bind handlers
        $('.editable-field').click(function() {
            if ($(this).hasClass('editing')) return;
            handleEditableField($(this));
        });

        $('.editable-select').click(function() {
            if ($(this).hasClass('editing')) return;
            handleEditableSelect($(this));
        });
    }

    // Handle editable field logic
    function handleEditableField($this) {
        const field = $this.data('field');
        const original = $this.data('original');
        const departmentId = $this.closest('tr').data('department-id');

        // Create input
        const $input = $('<input type="text" class="edit-input">');
        $input.val(original);

        // Create save/cancel buttons
        const $buttons = $('<div class="save-cancel-buttons">');
        $buttons.append('<button class="btn btn-success btn-sm save-btn">حفظ</button>');
        $buttons.append('<button class="btn btn-secondary btn-sm cancel-btn">إلغاء</button>');

        // Replace content
        $this.addClass('editing');
        const originalHtml = $this.html();
        $this.data('original-html', originalHtml);
        $this.html('').append($input).append($buttons);

        // Focus input
        $input.focus().select();

        // Handle save
        $buttons.find('.save-btn').click(function() {
            const newValue = $input.val().trim();
            saveField(departmentId, field, newValue, $this, originalHtml);
        });

        // Cancel is handled by event delegation

        // Handle enter key
        $input.keypress(function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $buttons.find('.save-btn').click();
            }
        });

        // Handle escape key
        $input.keyup(function(e) {
            if (e.which === 27) {
                e.preventDefault();
                $buttons.find('.cancel-btn').trigger('click');
            }
        });
    }

    // Handle editable select logic
    function handleEditableSelect($this) {
        const field = $this.data('field');
        const original = $this.data('original');
        const departmentId = $this.closest('tr').data('department-id');

        // Create select
        const $select = $('<select class="edit-select">');
        const options = choiceOptions[field] || [];

        options.forEach(function(option) {
            const $option = $('<option>');
            $option.val(option.value).text(option.text);
            if (option.value === original) {
                $option.prop('selected', true);
            }
            $select.append($option);
        });

        // Create save/cancel buttons
        const $buttons = $('<div class="save-cancel-buttons">');
        $buttons.append('<button class="btn btn-success btn-sm save-btn">حفظ</button>');
        $buttons.append('<button class="btn btn-secondary btn-sm cancel-btn">إلغاء</button>');

        // Replace content
        $this.addClass('editing');
        const originalHtml = $this.html();
        $this.data('original-html', originalHtml);
        $this.html('').append($select).append($buttons);

        // Focus select
        $select.focus();

        // Handle save
        $buttons.find('.save-btn').click(function() {
            const newValue = $select.val();
            saveField(departmentId, field, newValue, $this, originalHtml);
        });

        // Cancel is handled by event delegation

        // Handle enter key
        $select.keypress(function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $buttons.find('.save-btn').click();
            }
        });

        // Handle escape key
        $select.keyup(function(e) {
            if (e.which === 27) {
                e.preventDefault();
                $buttons.find('.cancel-btn').trigger('click');
            }
        });
    }

    // Show alert function
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('#alert-container').html(alertHtml);

        // Auto hide after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // Excel Export functionality
    $('#exportExcelBtn').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();

        // Show loading state
        btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التصدير...');
        btn.prop('disabled', true);

        // Get current filter values
        var filters = {
            department: $('#departmentFilter').val(),
            workplace: $('#workplaceFilter').val(),
            school_type: $('#schoolTypeFilter').val(),
            school_gender: $('#schoolGenderFilter').val(),
            directorate_type: $('#directorateTypeFilter').val()
        };

        // Build URL with filters
        var url = "{% url 'employment:export_departments_to_excel' %}";
        var params = [];

        for (var key in filters) {
            if (filters[key]) {
                params.push(key + '=' + encodeURIComponent(filters[key]));
            }
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        // Create a temporary link to download the file
        var link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button state after a short delay
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);

            // Show success message
            showAlert('تم تصدير البيانات إلى Excel بنجاح!', 'success');
        }, 1000);
    });
});
</script>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}