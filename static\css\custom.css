/* Custom CSS for HR System */

:root {
    /* Main theme colors - Teal/Green palette */
    --primary-color: #009688;
    --primary-dark: #00796b;
    --primary-light: #b2dfdb;
    --secondary-color: #ff5722;
    --secondary-dark: #e64a19;
    --secondary-light: #ffccbc;
    --success-color: #4caf50;
    --success-dark: #388e3c;
    --success-light: #c8e6c9;
    --info-color: #03a9f4;
    --info-dark: #0288d1;
    --info-light: #b3e5fc;
    --warning-color: #ff9800;
    --warning-dark: #f57c00;
    --warning-light: #ffe0b2;
    --danger-color: #f44336;
    --danger-dark: #d32f2f;
    --danger-light: #ffcdd2;

    /* Neutral colors */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* Typography */
    --font-family: 'Tajawal', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 60px;
    --content-max-width: 1200px;

    /* Z-index */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;

    /* RGB values for opacity variations */
    --primary-color-rgb: 0, 150, 136;
    --secondary-color-rgb: 255, 87, 34;
    --success-color-rgb: 76, 175, 80;
    --info-color-rgb: 3, 169, 244;
    --warning-color-rgb: 255, 152, 0;
    --danger-color-rgb: 244, 67, 54;
}

/* Global Styles */
html, body {
    height: 100%;
}

body {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family);
    background-color: var(--gray-100);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: var(--font-size-md);
    overflow-x: hidden;
    padding-top: var(--header-height);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
    line-height: 1.3;
}

h1 {
    font-size: var(--font-size-3xl);
}

h2 {
    font-size: var(--font-size-2xl);
}

h3 {
    font-size: var(--font-size-xl);
}

h4 {
    font-size: var(--font-size-lg);
}

h5 {
    font-size: var(--font-size-md);
}

h6 {
    font-size: var(--font-size-sm);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

small {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

strong, b {
    font-weight: 700;
}

code {
    font-family: monospace;
    background-color: var(--gray-200);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    color: var(--primary-dark);
    font-size: var(--font-size-sm);
}

/* Navbar */
.navbar {
    background-color: #222;
    box-shadow: var(--shadow);
    padding: 0;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    display: flex;
    align-items: center;
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: white;
    padding: 0 var(--spacing-md);
    height: 100%;
    display: flex;
    align-items: center;
}

.navbar .container-fluid {
    padding: 0 var(--spacing-md);
}

.navbar .dropdown-toggle::after {
    margin-right: var(--spacing-xs);
    margin-left: 0;
}

.navbar .dropdown-menu {
    border: none;
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) 0;
}

.navbar .dropdown-item {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-700);
    transition: var(--transition-fast);
}

.navbar .dropdown-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.navbar .dropdown-divider {
    margin: var(--spacing-xs) 0;
    border-color: var(--gray-200);
}

.navbar .nav-link {
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    transition: var(--transition-fast);
    border-radius: var(--border-radius);
}

.navbar .nav-link:hover {
    color: var(--gray-300);
}

.navbar .btn-link {
    color: white;
    font-size: var(--font-size-lg);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.navbar .btn-link:hover {
    color: var(--gray-300);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Keyframes for active link glow animation */
@keyframes activeGlow {
    0% {
        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.2);
    }
    100% {
        box-shadow: 0 4px 25px rgba(255, 255, 255, 0.6), inset 0 1px 3px rgba(255, 255, 255, 0.3);
    }
}

/* Keyframes for submenu active link glow animation */
@keyframes subActiveGlow {
    0% {
        box-shadow: 0 3px 15px rgba(255, 255, 255, 0.35), inset 0 1px 2px rgba(255, 255, 255, 0.2);
    }
    100% {
        box-shadow: 0 3px 18px rgba(255, 255, 255, 0.5), inset 0 1px 2px rgba(255, 255, 255, 0.3);
    }
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-color));
    color: white;
    z-index: var(--z-index-fixed);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    overflow-y: auto;
    padding-top: var(--header-height);
}

.sidebar-header {
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
    text-align: center;
    display: block;
    padding: var(--spacing-md) 0;
}

.sidebar-menu {
    padding: var(--spacing-md) 0;
}

.sidebar-item {
    padding: var(--spacing-xs) var(--spacing-md);
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    color: rgba(255, 255, 255, 0.85);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    font-weight: 500;
    border: 2px solid transparent;
}

.sidebar-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.sidebar-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    border: 3px solid white !important;
    border-radius: 10px !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.2) !important;
    font-weight: 700 !important;
    position: relative;
    animation: activeGlow 2s ease-in-out infinite alternate;
}

.sidebar-link.active i {
    color: white !important;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.6) !important;
    transform: scale(1.1);
}

.sidebar-link.active span {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.sidebar-link i {
    margin-left: var(--spacing-md);
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.sidebar-link .fa-chevron-down {
    margin-left: 0;
    font-size: var(--font-size-sm);
    transition: transform 0.3s ease;
}

.sidebar-link[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

.sidebar-submenu {
    padding-right: var(--spacing-lg);
    margin-top: var(--spacing-xs);
}

.sidebar-submenu .sidebar-link {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-radius: 6px;
}

.sidebar-submenu .sidebar-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
    border: 2px solid white !important;
    border-radius: 8px !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 15px rgba(255, 255, 255, 0.35), inset 0 1px 2px rgba(255, 255, 255, 0.2) !important;
    font-weight: 700 !important;
    margin: 2px 0;
    animation: subActiveGlow 3s ease-in-out infinite alternate;
}

.sidebar-submenu .sidebar-link.active i {
    color: white !important;
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.05);
}

.sidebar-submenu .sidebar-link.active span {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.sidebar-submenu .sidebar-item {
    padding: var(--spacing-xs) 0;
}

.sidebar-item.mt-4 {
    margin-top: var(--spacing-xl) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-md);
}

/* Main content */
.main-content {
    margin-right: var(--sidebar-width);
    padding: var(--spacing-xl);
    min-height: calc(100vh - var(--header-height));
    max-width: 100%;
    transition: var(--transition);
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: calc(100% - var(--sidebar-width));
}

.login-content {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
}

/* Responsive sidebar */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
        max-width: 320px;
        z-index: var(--z-index-modal);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
        width: 100%;
        overflow-x: hidden;
    }

    body.sidebar-open {
        overflow: hidden;
    }

    .navbar-brand {
        margin-right: auto;
    }

    /* Improve table responsiveness on small screens */
    .table {
        font-size: var(--font-size-sm);
    }

    .table th, .table td {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* Adjust card padding */
    .card-body {
        padding: var(--spacing-md);
    }

    /* Adjust form elements */
    .form-control, .form-select, .btn {
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* Adjust headings */
    h1 {
        font-size: var(--font-size-2xl);
    }

    h2 {
        font-size: var(--font-size-xl);
    }

    h3 {
        font-size: var(--font-size-lg);
    }
}

/* Extra small devices */
@media (max-width: 575.98px) {
    .main-content {
        padding: var(--spacing-sm);
    }

    .table-responsive {
        margin-right: -1rem;
        margin-left: -1rem;
        width: calc(100% + 2rem);
    }

    /* Stack buttons on small screens */
    .btn-group, .d-flex {
        flex-wrap: wrap;
    }

    .btn {
        margin-bottom: var(--spacing-sm);
    }

    /* Adjust card padding */
    .card-body {
        padding: var(--spacing-sm);
    }

    /* Adjust form elements */
    .form-label {
        font-size: var(--font-size-sm);
    }

    /* Adjust headings */
    h1 {
        font-size: var(--font-size-xl);
    }

    h2 {
        font-size: var(--font-size-lg);
    }

    h3 {
        font-size: var(--font-size-md);
    }

    /* Improve navigation on small screens */
    .navbar .container-fluid {
        padding: 0 var(--spacing-sm);
    }

    /* Adjust dropdown menus */
    .dropdown-menu {
        width: 100%;
        position: fixed;
        top: var(--header-height);
        left: 0;
        right: 0;
        border-radius: 0;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* Fix pagination on small screens */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .page-item {
        margin-bottom: var(--spacing-xs);
    }

    /* Adjust alerts */
    .alert {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }

    /* Fix modal dialogs */
    .modal-dialog {
        margin: var(--spacing-sm);
    }

    /* Ensure images don't overflow */
    img {
        max-width: 100%;
        height: auto;
    }
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: var(--spacing-xl);
    transition: var(--transition);
    overflow: hidden;
    background-color: white;
    width: 100%;
    max-width: 100%;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

/* Responsive cards */
@media (max-width: 767.98px) {
    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card-deck {
        display: block;
    }

    .card-deck .card {
        margin-bottom: var(--spacing-md);
        margin-left: 0;
        margin-right: 0;
    }

    .card-columns {
        column-count: 1;
    }
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h6 {
    font-weight: 700;
    font-size: var(--font-size-md);
    color: var(--primary-color);
    margin: 0;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: white;
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Dashboard cards */
.dashboard-card {
    transition: var(--transition);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.dashboard-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.dashboard-card .card-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--gray-800);
}

.dashboard-card .card-text {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
}

.dashboard-card .btn {
    align-self: flex-start;
}

/* Stat cards */
.stat-card {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    display: flex;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stat-card-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    margin-left: var(--spacing-md);
    color: white;
}

.stat-card-content {
    flex: 1;
}

.stat-card-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    margin-bottom: var(--spacing-xs);
}

.stat-card-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0;
}

/* Border styles */
.border-right-primary {
    border-right: 4px solid var(--primary-color);
}

.border-right-success {
    border-right: 4px solid var(--success-color);
}

.border-right-info {
    border-right: 4px solid var(--info-color);
}

.border-right-warning {
    border-right: 4px solid var(--warning-color);
}

.border-right-danger {
    border-right: 4px solid var(--danger-color);
}

.border-right-secondary {
    border-right: 4px solid var(--secondary-color);
}

/* For RTL compatibility */
.border-left-primary {
    border-right: 4px solid var(--primary-color);
    border-left: none;
}

.border-left-success {
    border-right: 4px solid var(--success-color);
    border-left: none;
}

.border-left-info {
    border-right: 4px solid var(--info-color);
    border-left: none;
}

.border-left-warning {
    border-right: 4px solid var(--warning-color);
    border-left: none;
}

.border-left-danger {
    border-right: 4px solid var(--danger-color);
    border-left: none;
}

.border-left-secondary {
    border-right: 4px solid var(--secondary-color);
    border-left: none;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    transition: var(--transition-fast);
    text-transform: none;
    letter-spacing: normal;
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.5s, height 0.5s;
    z-index: 1;
    pointer-events: none;
}

.btn:active::after {
    width: 300%;
    height: 300%;
}

.btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.btn i {
    margin-left: var(--spacing-sm);
    font-size: 1.1em;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: var(--success-dark);
    border-color: var(--success-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: var(--info-dark);
    border-color: var(--info-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: var(--warning-dark);
    border-color: var(--warning-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: var(--danger-dark);
    border-color: var(--danger-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon-only {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: var(--border-radius);
}

.btn-icon-only.btn-sm {
    width: 30px;
    height: 30px;
}

.btn-icon-only.btn-lg {
    width: 48px;
    height: 48px;
}

.btn-icon-only i {
    margin: 0;
}

.btn-group {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-wrap: wrap;
}

.btn-group .btn {
    box-shadow: none;
    margin-bottom: var(--spacing-xs);
}

.btn-group .btn:hover {
    transform: none;
    box-shadow: none;
}

/* Responsive button groups */
@media (max-width: 767.98px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
        border-radius: var(--border-radius-md) !important;
    }

    /* Fix for action buttons in tables */
    .table .btn {
        margin: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* Make form buttons stack on small screens */
    form .btn {
        margin-bottom: var(--spacing-sm);
    }
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    color: var(--gray-800);
    vertical-align: middle;
    border-collapse: separate;
    border-spacing: 0;
}

.table-responsive {
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
    box-shadow: var(--shadow);
    width: 100%;
    -webkit-overflow-scrolling: touch;
    display: block;
    max-width: 100vw;
    margin-bottom: var(--spacing-lg);
}

/* Force tables to be 100% width but allow horizontal scrolling */
.table {
    width: 100% !important;
    table-layout: auto;
    min-width: 650px; /* Minimum width to ensure readability */
}

/* Make sure table cells don't break content */
.table th,
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px; /* Maximum width for any cell */
}

/* Allow some cells to wrap text if needed */
.table td.wrap-text,
.table th.wrap-text {
    white-space: normal;
    word-break: break-word;
}

/* Ensure table headers are always visible */
.table thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--gray-100);
}

.table-bordered {
    border: 1px solid var(--gray-300);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--gray-300);
}

.table-hover tbody tr {
    transition: var(--transition-fast);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.table th {
    font-weight: 600;
    color: var(--gray-700);
    background-color: var(--gray-100);
    border-bottom-width: 1px;
    padding: var(--spacing-md);
    text-align: right;
    white-space: nowrap;
}

.table td {
    padding: var(--spacing-md);
    vertical-align: middle;
}

.table-light {
    background-color: var(--gray-100);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(var(--primary-color-rgb), 0.02);
}

.table .badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-weight: 500;
}

.table .btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.table .btn-icon-only.btn-sm {
    width: 24px;
    height: 24px;
    padding: 0;
}

/* Pagination */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-md);
    justify-content: center;
}

.page-link {
    position: relative;
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    background-color: #fff;
    border: 1px solid var(--gray-300);
    transition: var(--transition-fast);
}

.page-link:hover {
    z-index: 2;
    color: var(--primary-dark);
    text-decoration: none;
    background-color: var(--gray-200);
    border-color: var(--gray-300);
}

.page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
}

.page-item:last-child .page-link {
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--gray-500);
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: var(--gray-300);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius-md);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    transition: var(--transition-fast);
    color: var(--gray-800);
    background-color: white;
    height: auto;
    line-height: 1.5;
    max-width: 100%;
    width: 100%;
}

/* Make forms more responsive */
.form-row,
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.5rem;
    margin-left: -0.5rem;
}

.form-row > .col,
.form-row > [class*="col-"],
.row > .col,
.row > [class*="col-"] {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

/* Responsive form layout */
@media (max-width: 767.98px) {
    .form-row > .col,
    .form-row > [class*="col-"],
    .row > .col,
    .row > [class*="col-"] {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: var(--spacing-md);
    }

    /* Adjust date inputs on small screens */
    input[type="date"],
    input[type="datetime-local"],
    input[type="month"],
    input[type="time"] {
        min-width: auto;
    }
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: white;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: var(--gray-100);
    opacity: 0.7;
}

.form-control::placeholder {
    color: var(--gray-500);
    opacity: 1;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.form-text {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: block;
}

.form-check {
    display: block;
    min-height: 1.5rem;
    padding-right: 1.5em;
    padding-left: 0;
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 1.25em;
    height: 1.25em;
    margin-top: 0.125em;
    margin-left: 0;
    margin-right: -1.5em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid var(--gray-400);
    appearance: none;
    transition: var(--transition-fast);
    float: right;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.form-check-label {
    color: var(--gray-700);
    cursor: pointer;
}

.form-select {
    border-radius: var(--border-radius-md);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 2.25rem;
    font-size: var(--font-size-md);
    transition: var(--transition-fast);
    color: var(--gray-800);
    background-position: left var(--spacing-sm) center;
    background-size: 16px 12px;
    height: auto;
    line-height: 1.5;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.input-group {
    border-radius: var(--border-radius-md);
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control,
.input-group > .form-select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    background-color: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    border-radius: var(--border-radius-md);
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group > :not(:last-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--danger-color);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: var(--danger-color);
    padding-left: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--danger-color-rgb), 0.25);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius-lg);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}

/* Permanent alerts */
.alert-permanent {
    opacity: 1 !important;
    animation: none !important;
    display: block !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 100 !important;
}

/* Messages container */
.messages {
    position: relative;
    z-index: 1000;
    margin-bottom: var(--spacing-lg);
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
}

.alert-success {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.alert-success::before {
    background-color: var(--success-color);
}

.alert-info {
    background-color: var(--info-light);
    color: var(--info-dark);
}

.alert-info::before {
    background-color: var(--info-color);
}

.alert-warning {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.alert-warning::before {
    background-color: var(--warning-color);
}

.alert-danger {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.alert-danger::before {
    background-color: var(--danger-color);
}

.alert-dismissible {
    padding-left: 3rem;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    padding: var(--spacing-md) var(--spacing-lg);
}

.alert-heading {
    color: inherit;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.alert i {
    margin-left: var(--spacing-sm);
    font-size: 1.1em;
}

/* Badges */
.badge {
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: var(--transition-fast);
}

/* Avatar Styles for User Management */
.avatar-sm {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    text-transform: uppercase;
}

.avatar-lg {
    width: 60px;
    height: 60px;
}

.avatar-lg .avatar-title {
    font-size: 1.5rem;
}

/* Permission Management Styles */
.permission-management {
    max-height: 70vh;
    overflow-y: auto;
}

.permission-module {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-md);
}

.permission-module:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.permission-module.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.permission-module .card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-md);
}

.permission-module .card-body {
    padding: var(--spacing-md);
}

.module-toggle {
    transform: scale(1.2);
}

.pages-section {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.pages-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
    background-color: white;
}

.pages-list .form-check {
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.pages-list .form-check:hover {
    background-color: var(--gray-50);
}

.permission-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    margin: 0.1rem;
    border-radius: var(--border-radius-full);
    font-weight: 500;
}

.permission-count {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: var(--border-radius-full);
    font-size: 0.65rem;
    font-weight: bold;
    display: inline-block;
    margin: 0.1rem;
}

.permission-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 0.2rem;
    align-items: center;
}

.user-permissions {
    max-width: 250px;
    overflow: hidden;
}

.quick-actions {
    display: flex;
    gap: 0.3rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    line-height: 1.2;
    border-radius: var(--border-radius-sm);
    min-width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.user-status-active {
    color: var(--success-color);
    font-size: 0.8rem;
}

.user-status-inactive {
    color: var(--danger-color);
    font-size: 0.8rem;
}

/* Stats Card */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
}

.stats-item {
    text-align: center;
    padding: var(--spacing-md);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Bulk Permission Management */
.bulk-permission-management {
    max-height: 70vh;
    overflow-y: auto;
}

.bulk-module-card {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-sm);
}

.bulk-module-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.2);
}

.bulk-module-card .card-body {
    padding: var(--spacing-md);
}

.module-permissions {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    background-color: rgba(var(--gray-50), 0.5);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
}

.selected-users {
    max-height: 120px;
    overflow-y: auto;
    padding: var(--spacing-sm);
    background-color: rgba(var(--info-color-rgb), 0.05);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(var(--info-color-rgb), 0.2);
}

.selected-users .badge {
    margin: 0.1rem;
    font-size: 0.75rem;
}

/* Form Check Improvements */
.form-check-sm {
    min-height: 1.2rem;
    margin-bottom: var(--spacing-xs);
}

.form-check-sm .form-check-input {
    width: 1rem;
    height: 1rem;
    margin-top: 0.1rem;
}

.form-check-sm .form-check-label {
    font-size: 0.8rem;
    line-height: 1.2;
}

/* Modal Improvements */
.modal-xl {
    max-width: 95%;
}

.modal-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--gray-200);
}

.modal-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .stats-card .row {
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .permission-summary {
        justify-content: center;
    }
    
    .user-permissions {
        max-width: none;
        text-align: center;
    }
    
    .bulk-module-card .row {
        text-align: center;
    }
    
    .btn-group.flex-wrap {
        justify-content: center;
    }
}

/* Table Dark Header */
.table-dark {
    background-color: var(--gray-800);
    color: white;
}

.table-dark th {
    background-color: var(--gray-900);
    border-color: var(--gray-700);
    color: white;
    font-weight: 600;
}

.table-dark td {
    border-color: var(--gray-700);
}

/* Loading Spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

.badge i {
    margin-left: var(--spacing-xs);
    font-size: 0.85em;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-light {
    background-color: var(--gray-100) !important;
}

.bg-dark {
    background-color: var(--gray-800) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-light {
    color: var(--gray-100) !important;
}

.text-dark {
    color: var(--gray-800) !important;
}

.text-white {
    color: white !important;
}

/* Login page */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    position: relative;
    overflow: hidden;
}

.login-page::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    animation: rotate 30s linear infinite;
    z-index: 1;
}

.login-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="rgba(255,255,255,0.03)"/></svg>');
    opacity: 0.5;
    z-index: 2;
}

.login-content {
    position: relative;
    z-index: 3;
    width: 100%;
    max-width: 1200px;
    padding: var(--spacing-xl);
}

.login-card {
    max-width: 450px;
    width: 100%;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: fadeInUp 0.8s ease-out;
    margin: 0 auto;
}

.login-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-bottom: none;
    padding: var(--spacing-xl) var(--spacing-lg);
    text-align: center;
}

.login-card .card-body {
    padding: var(--spacing-xl);
    background-color: white;
}

.login-card .card-footer {
    background-color: white;
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md);
    text-align: center;
}

.login-card .form-control {
    height: 48px;
    font-size: var(--font-size-md);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--gray-50);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.login-card .form-control:focus {
    background-color: white;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.login-card .btn-primary {
    height: 48px;
    font-size: var(--font-size-md);
    font-weight: 600;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    transition: var(--transition);
}

.login-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.login-card .form-check-input {
    width: 1.25em;
    height: 1.25em;
    margin-top: 0.25em;
    margin-left: 0.5rem;
    margin-right: 0;
    vertical-align: top;
    background-color: var(--gray-50);
    border: 1px solid var(--gray-300);
    appearance: none;
    transition: var(--transition-fast);
}

.login-card .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 5px;
    border: 2px solid var(--gray-100);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-in-out;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utility classes */
.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-5px);
}

.rounded-lg {
    border-radius: var(--border-radius-lg) !important;
}

.rounded-md {
    border-radius: var(--border-radius-md) !important;
}

.rounded-sm {
    border-radius: var(--border-radius-sm) !important;
}

.rounded-full {
    border-radius: var(--border-radius-full) !important;
}

/* Print styles */
@media print {
    body {
        background-color: white;
        font-size: 12pt;
        color: black;
    }

    .sidebar, .navbar, .btn, .no-print {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .card-header {
        background-color: var(--gray-100) !important;
        color: black !important;
    }

    .table {
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .table th, .table td {
        padding: 0.5rem !important;
        border: 1px solid var(--gray-300) !important;
    }

    .table th {
        background-color: var(--gray-100) !important;
        color: black !important;
    }

    a {
        text-decoration: none !important;
        color: black !important;
    }

    .badge {
        border: 1px solid var(--gray-300) !important;
        color: black !important;
        background-color: white !important;
    }

    @page {
        margin: 1.5cm;
        size: A4;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        page-break-inside: avoid;
    }

    img {
        max-width: 100% !important;
        page-break-inside: avoid;
    }

    ul, ol, dl {
        page-break-before: avoid;
    }
}

/* Add Tajawal font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');

/* Add CSS variables for RGB values */
:root {
    --primary-color-rgb: 63, 81, 181;
    --secondary-color-rgb: 255, 64, 129;
    --success-color-rgb: 76, 175, 80;
    --info-color-rgb: 3, 169, 244;
    --warning-color-rgb: 255, 152, 0;
    --danger-color-rgb: 244, 67, 54;
}
