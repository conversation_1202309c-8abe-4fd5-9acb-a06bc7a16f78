@echo off
echo Starting Django server...
echo.

REM Try to activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
) else if exist "env\Scripts\activate.bat" (
    call env\Scripts\activate.bat
)

REM Run the server with error handling
python manage.py runserver 0.0.0.0:8000

REM If server fails, pause to see error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Server failed to start. Press any key to exit...
    pause > nul
)
