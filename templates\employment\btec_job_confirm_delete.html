{% extends 'base.html' %}
{% load static %}

{% block title %}حذف وظيفة BTEC - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 50px auto;
    }

    .delete-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(220, 53, 69, 0.2);
        overflow: hidden;
    }

    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .delete-header i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.9;
    }

    .delete-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .delete-body {
        padding: 40px;
        background: #f8f9fa;
        text-align: center;
    }

    .job-info {
        background: white;
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }

    .job-name {
        font-size: 1.3rem;
        font-weight: bold;
        color: #495057;
        margin-bottom: 10px;
    }

    .job-description {
        color: #6c757d;
        font-style: italic;
    }

    .warning-text {
        color: #dc3545;
        font-weight: 600;
        margin: 20px 0;
        font-size: 1.1rem;
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="delete-container">
        <div class="card delete-card">
            <div class="delete-header">
                <i class="fas fa-exclamation-triangle"></i>
                <h2>تأكيد حذف الوظيفة</h2>
            </div>
            <div class="delete-body">
                <p class="warning-text">
                    هل أنت متأكد من حذف هذه الوظيفة؟
                </p>
                
                <div class="job-info">
                    <div class="job-name">
                        <i class="fas fa-briefcase text-primary me-2"></i>
                        {{ job.name }}
                    </div>
                    {% if job.description %}
                    <div class="job-description">
                        {{ job.description }}
                    </div>
                    {% endif %}
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الوظيفة نهائياً من النظام.
                </div>

                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:btec_job_list' %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-delete">
                            <i class="fas fa-trash me-2"></i>حذف نهائياً
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation on form submit
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذه الوظيفة نهائياً؟')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
