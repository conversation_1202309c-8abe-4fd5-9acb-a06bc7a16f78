from django import template

register = template.Library()

@register.filter
def startswith(text, starts):
    """
    فلتر مخصص للتحقق مما إذا كانت السلسلة النصية تبدأ بنص معين
    مثال الاستخدام: {% if page|startswith:"employees:" %}
    """
    if isinstance(text, str):
        return text.startswith(starts)
    return False

@register.filter
def split(value, delimiter):
    """
    يقوم بتقسيم النص إلى قائمة باستخدام الفاصل المحدد
    مثال الاستخدام: {% for item in "employees,employment,leaves"|split:"," %}
    """
    if isinstance(value, str):
        return value.split(delimiter)
    return []
