{% extends 'base.html' %}
{% load static %}

{% block title %}سجل أخطاء النظام{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .error-severity-critical {
        background-color: #dc3545;
        color: white;
    }
    
    .error-severity-high {
        background-color: #fd7e14;
        color: white;
    }
    
    .error-severity-medium {
        background-color: #ffc107;
        color: black;
    }
    
    .error-severity-low {
        background-color: #28a745;
        color: white;
    }
    
    .error-status-new {
        background-color: #dc3545;
        color: white;
    }
    
    .error-status-in_progress {
        background-color: #ffc107;
        color: black;
    }
    
    .error-status-resolved {
        background-color: #28a745;
        color: white;
    }
    
    .error-status-ignored {
        background-color: #6c757d;
        color: white;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stats-item {
        text-align: center;
        padding: 10px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .no-results-message {
        text-align: center;
        padding: 40px;
        background: #f8f9fa;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .no-results-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    .error-message {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .occurrence-badge {
        background-color: #17a2b8;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        سجل أخطاء النظام
                    </h2>
                    <p class="text-muted mb-0">عرض وإدارة أخطاء النظام مع إمكانية التصفية والبحث</p>
                </div>
                <div>
                    <a href="{% url 'system_logs:system_log_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i>
                        سجل حركات النظام
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    {% if show_results and stats %}
    <div class="stats-card">
        <div class="row">
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ stats.total_errors }}</span>
                    <span class="stats-label">إجمالي الأخطاء</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ stats.new_errors }}</span>
                    <span class="stats-label">أخطاء جديدة</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ stats.critical_errors }}</span>
                    <span class="stats-label">أخطاء حرجة</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-item">
                    <span class="stats-number">{{ stats.resolved_errors }}</span>
                    <span class="stats-label">أخطاء محلولة</span>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filters Card -->
    <div class="filter-card">
        <h5 class="mb-3">
            <i class="fas fa-filter"></i>
            فلاتر البحث والتصفية
        </h5>
        
        <form method="get" id="filterForm">
            <div class="row">
                <!-- Error Type Filter -->
                <div class="col-md-3 mb-3">
                    <label for="error_type" class="form-label">نوع الخطأ</label>
                    <select name="error_type" id="error_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in filter_options.error_types %}
                            <option value="{{ value }}" {% if current_filters.error_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Severity Filter -->
                <div class="col-md-3 mb-3">
                    <label for="severity" class="form-label">مستوى الخطورة</label>
                    <select name="severity" id="severity" class="form-select">
                        <option value="">جميع المستويات</option>
                        {% for value, label in filter_options.severities %}
                            <option value="{{ value }}" {% if current_filters.severity == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in filter_options.statuses %}
                            <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Date Range Filter -->
                <div class="col-md-3 mb-3">
                    <label for="date_range" class="form-label">الفترة الزمنية</label>
                    <select name="date_range" id="date_range" class="form-select">
                        <option value="">جميع الفترات</option>
                        {% for value, label in filter_options.date_ranges %}
                            <option value="{{ value }}" {% if current_filters.date_range == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Module Filter -->
                <div class="col-md-3 mb-3">
                    <label for="module" class="form-label">الوحدة</label>
                    <select name="module" id="module" class="form-select">
                        <option value="">جميع الوحدات</option>
                        {% for module in filter_options.modules %}
                            {% if module %}
                                <option value="{{ module }}" {% if current_filters.module == module %}selected{% endif %}>
                                    {{ module }}
                                </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>

                <!-- User Filter -->
                <div class="col-md-3 mb-3">
                    <label for="user" class="form-label">المستخدم</label>
                    <select name="user" id="user" class="form-select">
                        <option value="">جميع المستخدمين</option>
                        {% for user in filter_options.users %}
                            <option value="{{ user.id }}" {% if current_filters.user_id == user.id|stringformat:"s" %}selected{% endif %}>
                                {{ user.username }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Search Filter -->
                <div class="col-md-6 mb-3">
                    <label for="search" class="form-label">البحث في النص</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="البحث في رسالة الخطأ، الوصف، اسم الصفحة..."
                           value="{{ current_filters.search_query }}">
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="{% url 'system_logs:system_error_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    {% if not show_results %}
        <!-- No Filters Applied Message -->
        <div class="no-results-message">
            <div class="no-results-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <h4>يرجى تحديد معايير البحث</h4>
            <p class="text-muted">
                لعرض أخطاء النظام، يرجى اختيار واحد أو أكثر من الفلاتر أعلاه ثم الضغط على "تطبيق الفلاتر"
            </p>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-lightbulb"></i>
                    نصيحة: يمكنك البدء بتحديد فترة زمنية أو نوع خطأ معين
                </small>
            </div>
        </div>
    {% elif errors.count == 0 %}
        <!-- No Results Found -->
        <div class="no-results-message">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h4>لا توجد أخطاء تطابق معايير البحث</h4>
            <p class="text-muted">
                جرب تعديل الفلاتر أو إزالة بعض المعايير للحصول على نتائج أكثر
            </p>
        </div>
    {% else %}
        <!-- Errors Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i>
                    قائمة الأخطاء ({{ errors.count }} خطأ)
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>نوع الخطأ</th>
                                <th>رسالة الخطأ</th>
                                <th>الصفحة</th>
                                <th>المستخدم</th>
                                <th>مستوى الخطورة</th>
                                <th>الحالة</th>
                                <th>عدد التكرار</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for error in errors %}
                            <tr>
                                <td>
                                    <small>
                                        {{ error.timestamp|date:"Y-m-d" }}<br>
                                        {{ error.timestamp|time:"H:i:s" }}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ error.get_error_type_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="error-message" title="{{ error.error_message }}">
                                        {{ error.error_message|truncatechars:50 }}
                                    </div>
                                    <small class="text-muted">
                                        {{ error.error_description|truncatechars:80 }}
                                    </small>
                                </td>
                                <td>
                                    <strong>{{ error.page_name }}</strong>
                                    {% if error.function_name %}
                                        <br><small class="text-muted">{{ error.function_name }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if error.user %}
                                        {{ error.user.username }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge error-severity-{{ error.severity }}">
                                        {{ error.get_severity_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge error-status-{{ error.status }}" id="status-{{ error.id }}">
                                        {{ error.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="occurrence-badge">
                                        {{ error.occurrence_count }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'system_logs:system_error_detail' error.id %}" 
                                           class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if error.status != 'resolved' %}
                                        <button class="btn btn-outline-success" 
                                                onclick="updateErrorStatus({{ error.id }}, 'resolved')"
                                                title="تم الحل">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الخطأ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <div class="mb-3">
                        <label for="newStatus" class="form-label">الحالة الجديدة</label>
                        <select id="newStatus" class="form-select">
                            <option value="new">جديد</option>
                            <option value="in_progress">قيد المعالجة</option>
                            <option value="resolved">تم الحل</option>
                            <option value="ignored">تم التجاهل</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="resolutionNotes" class="form-label">ملاحظات الحل</label>
                        <textarea id="resolutionNotes" class="form-control" rows="3" 
                                  placeholder="اكتب ملاحظات حول الحل..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveStatusUpdate()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentErrorId = null;

function updateErrorStatus(errorId, status) {
    currentErrorId = errorId;
    document.getElementById('newStatus').value = status;
    
    if (status === 'resolved') {
        document.getElementById('resolutionNotes').placeholder = 'اكتب كيف تم حل هذا الخطأ...';
    }
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function saveStatusUpdate() {
    if (!currentErrorId) return;
    
    const status = document.getElementById('newStatus').value;
    const notes = document.getElementById('resolutionNotes').value;
    
    fetch(`/system-logs/errors/${currentErrorId}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            status: status,
            resolution_notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status badge
            const statusBadge = document.getElementById(`status-${currentErrorId}`);
            statusBadge.textContent = data.new_status;
            statusBadge.className = `badge error-status-${status}`;
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusModal'));
            modal.hide();
            
            // Show success message
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في تحديث الحالة');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const filterInputs = document.querySelectorAll('#filterForm select, #filterForm input');
    
    filterInputs.forEach(input => {
        if (input.type !== 'text') {  // Don't auto-submit for text inputs
            input.addEventListener('change', function() {
                // Add a small delay to allow multiple selections
                setTimeout(() => {
                    document.getElementById('filterForm').submit();
                }, 100);
            });
        }
    });
});
</script>
{% endblock %}