{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - الرتب والعلاوات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{{ title }}</h2>
    <a href="{% url 'ranks:employee_allowance_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-money-bill-wave"></i> {{ title }}
        </h6>
    </div>
    <div class="card-body">
        <form method="post" id="allowanceForm">
            {% csrf_token %}
            
            <!-- Employee Search Section (for new allowances) -->
            {% if not allowance %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-search text-primary"></i> البحث عن الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="{{ form.ministry_number.id_for_label }}" class="form-label">{{ form.ministry_number.label }}</label>
                            {{ form.ministry_number }}
                            {% if form.ministry_number.errors %}
                                <div class="text-danger small">{{ form.ministry_number.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" id="searchEmployeeBtn">
                                <i class="fas fa-search"></i> بحث عن الموظف
                            </button>
                        </div>
                    </div>
                    
                    <!-- Employee Info Display -->
                    <div class="row mt-3" id="employeeInfo" style="display: none;">
                        <div class="col-md-6">
                            <label for="{{ form.employee_name.id_for_label }}" class="form-label">{{ form.employee_name.label }}</label>
                            {{ form.employee_name }}
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Display employee info for editing -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-user text-primary"></i> معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الرقم الوزاري</label>
                            <input type="text" class="form-control" value="{{ allowance.ministry_number }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" value="{{ allowance.employee.full_name }}" readonly>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Hidden employee field -->
            {{ form.employee }}

            <!-- Allowances Section -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-money-bill-wave text-primary"></i> العلاوات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.education_allowance.id_for_label }}" class="form-label">
                                <i class="fas fa-graduation-cap text-success"></i> {{ form.education_allowance.label }}
                            </label>
                            {{ form.education_allowance }}
                            {% if form.education_allowance.errors %}
                                <div class="text-danger small">{{ form.education_allowance.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.adjustment_allowance.id_for_label }}" class="form-label">
                                <i class="fas fa-cogs text-info"></i> {{ form.adjustment_allowance.label }}
                            </label>
                            {{ form.adjustment_allowance }}
                            {% if form.adjustment_allowance.errors %}
                                <div class="text-danger small">{{ form.adjustment_allowance.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.transportation_allowance.id_for_label }}" class="form-label">
                                <i class="fas fa-car text-warning"></i> {{ form.transportation_allowance.label }}
                            </label>
                            {{ form.transportation_allowance }}
                            {% if form.transportation_allowance.errors %}
                                <div class="text-danger small">{{ form.transportation_allowance.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.supervisory_allowance.id_for_label }}" class="form-label">
                                <i class="fas fa-user-tie text-secondary"></i> {{ form.supervisory_allowance.label }}
                            </label>
                            {{ form.supervisory_allowance }}
                            {% if form.supervisory_allowance.errors %}
                                <div class="text-danger small">{{ form.supervisory_allowance.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.technical_allowance.id_for_label }}" class="form-label">
                                <i class="fas fa-tools text-dark"></i> {{ form.technical_allowance.label }}
                            </label>
                            {{ form.technical_allowance }}
                            {% if form.technical_allowance.errors %}
                                <div class="text-danger small">{{ form.technical_allowance.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save"></i>
                    {% if allowance %}تحديث العلاوات{% else %}حفظ العلاوات{% endif %}
                </button>
                <a href="{% url 'ranks:employee_allowance_list' %}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('searchEmployeeBtn');
    const ministryNumberInput = document.getElementById('{{ form.ministry_number.id_for_label }}');
    const employeeNameInput = document.getElementById('{{ form.employee_name.id_for_label }}');
    const employeeHiddenInput = document.getElementById('{{ form.employee.id_for_label }}');
    const employeeInfo = document.getElementById('employeeInfo');

    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const ministryNumber = ministryNumberInput.value.trim();
            
            if (!ministryNumber) {
                alert('يرجى إدخال الرقم الوزاري');
                return;
            }

            // Show loading
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
            searchBtn.disabled = true;

            // Make AJAX request to find employee
            fetch(`/employment/get-employee/?ministry_number=${encodeURIComponent(ministryNumber)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Fill employee data
                        employeeNameInput.value = data.employee.full_name;
                        employeeHiddenInput.value = data.employee.id;
                        employeeInfo.style.display = 'block';
                    } else {
                        alert(data.error || 'لم يتم العثور على الموظف');
                        employeeInfo.style.display = 'none';
                        employeeNameInput.value = '';
                        employeeHiddenInput.value = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث');
                })
                .finally(() => {
                    // Reset button
                    searchBtn.innerHTML = '<i class="fas fa-search"></i> بحث عن الموظف';
                    searchBtn.disabled = false;
                });
        });
    }
});
</script>
{% endblock %}
