/* Global Design CSS - Based on Employee Page Design */

/* Card styling */
.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
    border-bottom: none;
    padding: 1rem 1.25rem;
}

.card-header h6, 
.card-header .h6, 
.card-header h5, 
.card-header .h5,
.card-header h4,
.card-header .h4 {
    color: white;
    font-weight: 600;
    margin-bottom: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Table styling */
.table {
    margin-bottom: 0;
}

.table th {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white !important;
    font-weight: 600;
    border: none;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    padding: 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
}

/* Button styling */
.btn {
    border-radius: 20px;
    transition: all 0.3s ease;
    padding: 0.375rem 1rem;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border-color: #224abe;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5fc8, #1a3a9c);
    border-color: #1a3a9c;
}

.btn-success {
    background: linear-gradient(135deg, #1cc88a, #169a6b);
    border-color: #169a6b;
}

.btn-success:hover {
    background: linear-gradient(135deg, #17a673, #13855c);
    border-color: #13855c;
}

.btn-info {
    background: linear-gradient(135deg, #36b9cc, #258391);
    border-color: #258391;
}

.btn-info:hover {
    background: linear-gradient(135deg, #2c9faf, #1f6f7a);
    border-color: #1f6f7a;
}

.btn-warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    border-color: #dda20a;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f4b619, #c59008);
    border-color: #c59008;
}

.btn-danger {
    background: linear-gradient(135deg, #e74a3b, #be2617);
    border-color: #be2617;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d52a1a, #9e1f13);
    border-color: #9e1f13;
}

.btn-secondary {
    background: linear-gradient(135deg, #858796, #60616f);
    border-color: #60616f;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #717384, #4e4f5c);
    border-color: #4e4f5c;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.5rem 1.5rem;
    font-size: 1.25rem;
}

/* Badge styling */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

/* Form controls */
.form-control, .form-select {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* Input groups */
.input-group {
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    border-radius: 20px;
    overflow: hidden;
}

.input-group .form-control,
.input-group .form-select {
    border: none;
    box-shadow: none;
}

.input-group .btn {
    border-radius: 0;
}

/* Search container */
.search-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

/* Animation effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease forwards;
}

.table tbody tr {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

.table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.table tbody tr:nth-child(5) { animation-delay: 0.5s; }
.table tbody tr:nth-child(6) { animation-delay: 0.6s; }
.table tbody tr:nth-child(7) { animation-delay: 0.7s; }
.table tbody tr:nth-child(8) { animation-delay: 0.8s; }
.table tbody tr:nth-child(9) { animation-delay: 0.9s; }
.table tbody tr:nth-child(10) { animation-delay: 1.0s; }

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 3rem;
    color: #d1d3e2;
    margin-bottom: 1rem;
}

.empty-state h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #858796;
    margin-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: stretch !important;
    }
    
    .card-header form {
        margin-top: 1rem;
    }
    
    .input-group {
        width: 100% !important;
    }
    
    .action-buttons {
        justify-content: center;
    }
}

/* Page header styling */
.d-flex.justify-content-between.align-items-center.mb-4 {
    margin-bottom: 1.5rem !important;
}

.d-flex.justify-content-between.align-items-center.mb-4 h2 {
    font-weight: 700;
    color: #4e73df;
}

/* Modal styling */
.modal-content {
    border-radius: 10px;
    border: none;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
    color: white;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* Pagination styling */
.pagination {
    justify-content: center;
    margin-top: 1.5rem;
}

.page-item .page-link {
    border-radius: 50%;
    margin: 0 0.25rem;
    color: #4e73df;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border-color: #224abe;
}

.page-item.disabled .page-link {
    color: #d1d3e2;
}
