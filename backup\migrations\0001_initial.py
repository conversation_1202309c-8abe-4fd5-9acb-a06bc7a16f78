# Generated by Django 5.2 on 2025-04-08 07:51

import backup.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Backup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('file', models.FileField(upload_to=backup.models.backup_file_path, verbose_name='Backup File')),
                ('size', models.PositiveIntegerField(default=0, verbose_name='Size (bytes)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('created_by', models.Char<PERSON>ield(max_length=100, verbose_name='Created By')),
                ('is_auto', models.BooleanField(default=False, verbose_name='Automatic Backup')),
            ],
            options={
                'verbose_name': 'Backup',
                'verbose_name_plural': 'Backups',
                'ordering': ['-created_at'],
            },
        ),
    ]
