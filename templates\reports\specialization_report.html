{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير التخصص - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير التخصص</h1>
        <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للوحة التقارير
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.specialization.id_for_label }}">التخصص</label>
                                    {{ form.specialization }}
                                    {% if form.specialization.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.specialization.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.gender.id_for_label }}">الجنس</label>
                                    {{ form.gender }}
                                    {% if form.gender.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.gender.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-file-export"></i> إنشاء التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">تقرير التخصص</h5>
                        <p>يعرض هذا التقرير معلومات عن الموظفين حسب التخصص، ويمكن تصفية النتائج حسب التخصص والجنس.</p>
                        <hr>
                        <p class="mb-0">يتم تصدير التقرير بصيغة Excel ويحتوي على المعلومات التالية:</p>
                        <ul>
                            <li>الرقم الوزاري</li>
                            <li>الاسم الكامل</li>
                            <li>التخصص</li>
                            <li>المؤهل العلمي (بكالوريس / دبلوم)</li>
                            <li>المؤهل العلمي (دبلوم بعد البكالوريس)</li>
                            <li>المؤهل العلمي (ماجستير)</li>
                            <li>المؤهل العلمي (دكتوراه)</li>
                            <li>القسم</li>
                            <li>تاريخ التعيين</li>
                            <li>المسمى الوظيفي</li>
                            <li>الجنس</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
