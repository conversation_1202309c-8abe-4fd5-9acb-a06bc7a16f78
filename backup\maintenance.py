"""
وظائف إدارة وضع الصيانة للنظام
"""

import os
from datetime import datetime
from django.conf import settings


def enable_maintenance_mode(operation_type="صيانة عامة"):
    """
    تفعيل وضع الصيانة
    
    Args:
        operation_type (str): نوع العملية (نسخ احتياطي، استعادة، إلخ)
    """
    maintenance_file = os.path.join(settings.BASE_DIR, '.maintenance')
    
    # معلومات الصيانة
    maintenance_info = f"{operation_type}\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    try:
        with open(maintenance_file, 'w', encoding='utf-8') as f:
            f.write(maintenance_info)
        print(f"✅ تم تفعيل وضع الصيانة: {operation_type}")
        return True
    except Exception as e:
        print(f"❌ خطأ في تفعيل وضع الصيانة: {e}")
        return False


def disable_maintenance_mode():
    """
    إلغاء وضع الصيانة
    """
    maintenance_file = os.path.join(settings.BASE_DIR, '.maintenance')
    
    try:
        if os.path.exists(maintenance_file):
            os.remove(maintenance_file)
            print("✅ تم إلغاء وضع الصيانة")
        return True
    except Exception as e:
        print(f"❌ خطأ في إلغاء وضع الصيانة: {e}")
        return False


def is_maintenance_mode():
    """
    التحقق من حالة الصيانة
    
    Returns:
        bool: True إذا كان النظام في وضع الصيانة
    """
    maintenance_file = os.path.join(settings.BASE_DIR, '.maintenance')
    return os.path.exists(maintenance_file)


def get_maintenance_info():
    """
    الحصول على معلومات الصيانة
    
    Returns:
        dict: معلومات الصيانة أو None
    """
    maintenance_file = os.path.join(settings.BASE_DIR, '.maintenance')
    
    if not os.path.exists(maintenance_file):
        return None
    
    try:
        with open(maintenance_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        lines = content.split('\n')
        return {
            'operation_type': lines[0] if lines else 'صيانة عامة',
            'start_time': lines[1] if len(lines) > 1 else '',
            'is_active': True
        }
    except Exception as e:
        print(f"❌ خطأ في قراءة معلومات الصيانة: {e}")
        return None


class MaintenanceContext:
    """
    Context manager لإدارة وضع الصيانة
    """
    
    def __init__(self, operation_type="صيانة عامة"):
        self.operation_type = operation_type
    
    def __enter__(self):
        """دخول وضع الصيانة"""
        enable_maintenance_mode(self.operation_type)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """الخروج من وضع الصيانة"""
        disable_maintenance_mode()
        
        # طباعة معلومات الخطأ إن وجد
        if exc_type:
            print(f"❌ حدث خطأ أثناء العملية: {exc_val}")
        else:
            print(f"✅ تمت العملية بنجاح: {self.operation_type}")


def maintenance_required(operation_type="صيانة عامة"):
    """
    Decorator لتفعيل وضع الصيانة أثناء تنفيذ الدالة
    
    Args:
        operation_type (str): نوع العملية
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with MaintenanceContext(operation_type):
                return func(*args, **kwargs)
        return wrapper
    return decorator