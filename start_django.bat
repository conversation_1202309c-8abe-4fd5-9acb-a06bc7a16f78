@echo off
echo Starting Django server...
echo.

REM Set Python path explicitly
set PYTHONPATH=%CD%

REM Try to run with python command
echo Trying with python command...
python manage.py runserver 0.0.0.0:8000

REM If python command fails, try with py command
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Python command failed, trying with py command...
    py manage.py runserver 0.0.0.0:8000
)

REM If both commands fail, show error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to start server. Make sure Python is installed and in your PATH.
    echo Press any key to exit...
    pause > nul
)
