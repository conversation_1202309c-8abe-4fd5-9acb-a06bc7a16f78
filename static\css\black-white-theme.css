/* Black and White Theme with Aesthetic Effects */

:root {
    --primary-color: #000000;
    --secondary-color: #333333;
    --accent-color: #666666;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.15);
    --hover-shadow: rgba(0, 0, 0, 0.25);
    --transition-speed: 0.3s;
}

/* General Styling */
body {
    background-color: var(--light-color);
    color: var(--dark-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color);
    font-weight: 600;
}

a {
    color: var(--secondary-color);
    transition: color var(--transition-speed), transform var(--transition-speed);
}

a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Cards */
.card {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    background-color: #ffffff;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem var(--hover-shadow);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.card-footer {
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
}

/* Buttons */
.btn {
    border-radius: 0.25rem;
    transition: all var(--transition-speed);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
}

.btn-info {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    color: var(--light-color);
}

/* Navbar */
.navbar {
    background-color: #222;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.navbar-brand {
    color: var(--light-color) !important;
    font-weight: 700;
    letter-spacing: 1px;
}

.navbar .nav-link {
    color: var(--light-color) !important;
    transition: transform var(--transition-speed);
}

.navbar .nav-link:hover {
    transform: translateY(-2px);
}

.dropdown-menu {
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
    border: 1px solid var(--border-color);
}

.dropdown-item:hover {
    background-color: var(--light-color);
}

/* Sidebar */
.sidebar {
    background-color: var(--primary-color);
    color: var(--light-color);
    box-shadow: 0 0 1rem var(--shadow-color);
}

.sidebar-brand {
    color: var(--light-color);
    font-weight: 700;
    letter-spacing: 1px;
}

.sidebar-link {
    color: var(--light-color);
    transition: all var(--transition-speed);
    border-right: 3px solid transparent;
}

.sidebar-link:hover, .sidebar-link.active {
    background-color: var(--secondary-color);
    color: var(--light-color);
    border-right-color: var(--light-color);
}

.sidebar-submenu {
    background-color: var(--secondary-color);
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.table-bordered {
    border: 1px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Forms */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
}

.input-group-text {
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
}

/* Alerts */
.alert {
    border-radius: 0.25rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.alert-primary {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--primary-color);
}

.alert-secondary {
    background-color: rgba(51, 51, 51, 0.1);
    color: var(--secondary-color);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--dark-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-info {
    background-color: var(--info-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed);
}

.pagination .page-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
    z-index: 2;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

/* Aesthetic Effects */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-in-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.bounce {
    animation: bounce 1s ease infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-10px);}
    60% {transform: translateY(-5px);}
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

/* Dashboard Cards */
.dashboard-card {
    transition: all var(--transition-speed);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem var(--hover-shadow);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card .card-title {
    font-weight: 700;
    margin-bottom: 1rem;
}

.dashboard-card .card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* Login Page */
.login-card {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
}

.login-card .card-header {
    background: linear-gradient(135deg, #000 0%, #333 100%);
    padding: 2rem 2rem 1.5rem;
    color: var(--light-color);
    border-bottom: 3px solid #666;
    width: 100%;
    position: relative;
}

.login-card .card-header h3 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    font-size: 1.2rem;
    white-space: nowrap;
    display: block;
    color: #ffffff;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    padding-top: 1rem;
}

.login-card .card-header h5 {
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-size: 1.1rem;
    opacity: 1;
    margin-top: 2.5rem;
    display: block;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    position: relative;
    left: 0;
    right: 0;
}

/* Footer Styles */
.footer {
    background-color: #222;
    border-top: 1px solid #444;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 0.75rem 0;
    font-size: 0.85rem;
    color: #fff;
    margin-top: auto;
    z-index: 1000;
    height: 50px;
}

.footer .text-muted {
    color: #ccc !important;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Footer is already fixed for all pages */

/* Ensure content doesn't get hidden behind footer */
.main-content, .login-content {
    padding-bottom: 70px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    body {
        background-color: #ffffff;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000000;
    }

    .btn {
        display: none;
    }
}
