{% extends 'home/base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Add background colors for the table cells to match PDF preview */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Add clear borders to table cells */
    .table-bordered {
        border: 2px solid #dee2e6 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
    }

    .table-bordered thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* Make text in cells bold for better visibility */
    .table-bordered td {
        font-weight: bold;
    }

    /* Styling for detailed leaves table */
    #detailedLeavesTable {
        margin-top: 1rem;
    }

    #detailedLeavesTable thead th {
        background-color: #343a40 !important;
        color: white !important;
        font-weight: bold;
    }

    #detailedLeavesTable tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Badge styling for days count */
    .badge {
        font-size: 0.9em;
    }

    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .search-card .card-body {
        padding: 2rem;
    }

    .search-input {
        border-radius: 25px;
        border: none;
        padding: 12px 20px;
        font-size: 16px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .search-btn {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .result-card {
        display: none;
        animation: fadeInUp 0.5s ease-in-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Employee status alert styling */
    .alert {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
        color: #0c5460;
    }

    .alert-secondary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
    }

    .alert h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .alert .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .alert .row {
        margin-top: 1rem;
    }

    .alert .row:first-child {
        margin-top: 0;
    }

    .alert strong {
        color: #495057;
        font-weight: 600;
    }

    .employee-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .print-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .print-btn {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .employee-info {
            background: #f8f9fa !important;
            color: #000 !important;
            border: 1px solid #dee2e6 !important;
        }

        /* Hide header and footer when printing */
        .navbar,
        .footer,
        header,
        footer,
        nav {
            display: none !important;
        }

        /* Hide page title */
        .container h1,
        .container .h3 {
            display: none !important;
        }

        /* Hide base template elements */
        body::before,
        body::after {
            display: none !important;
        }

        /* Adjust margins for print */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        .container {
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Hide specific base template elements */
        .hero-section,
        .navbar-brand,
        .navbar-nav,
        .navbar-toggler,
        .navbar-collapse,
        .search-card,
        #loadingIndicator,
        #errorAlert,
        #multipleEmployeesAlert {
            display: none !important;
        }

        /* Override any base template styles */
        * {
            background-image: none !important;
            background-color: white !important;
        }

        .employee-info * {
            background-color: #f8f9fa !important;
        }

        /* Print styles for detailed leaves table */
        #detailedLeavesTable thead th {
            background-color: #343a40 !important;
            color: white !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        .badge {
            background-color: #007bff !important;
            color: white !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header with back button -->
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calendar-check me-2"></i>
            {{ title }}
        </h1>
        <div>
            <a href="{% url 'home:home' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Search Card -->
            <div class="card shadow search-card mb-4">
                <div class="card-body text-center">
                    <h2 class="mb-4">
                        <i class="fas fa-calendar-check me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="lead mb-4">الاستعلام عن رصيد الإجازات للموظفين في المديرية</p>

                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="row g-3">
                                <div class="col-md-5">
                                    <label for="ministryNumberInput" class="form-label text-light">
                                        <i class="fas fa-id-badge me-2"></i>الرقم الوزاري
                                    </label>
                                    <input type="text"
                                           id="ministryNumberInput"
                                           class="form-control search-input"
                                           placeholder="أدخل الرقم الوزاري..."
                                           autocomplete="off"
                                           required>
                                </div>
                                <div class="col-md-5">
                                    <label for="nationalIdInput" class="form-label text-light">
                                        <i class="fas fa-id-card me-2"></i>الرقم الوطني
                                    </label>
                                    <input type="text"
                                           id="nationalIdInput"
                                           class="form-control search-input"
                                           placeholder="أدخل الرقم الوطني (10 أرقام)..."
                                           autocomplete="off"
                                           maxlength="10"
                                           pattern="[0-9]{10}"
                                           title="الرقم الوطني يجب أن يكون 10 أرقام"
                                           required>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button class="btn btn-light search-btn w-100"
                                            type="button"
                                            id="searchBtn">
                                        <i class="fas fa-search me-2"></i>
                                        بحث
                                    </button>
                                </div>
                            </div>
                            <div class="mt-3 text-center">
                                <div class="alert alert-light alert-sm d-inline-block mb-0 py-2 px-3">
                                    <i class="fas fa-shield-alt text-primary me-2"></i>
                                    <strong>للأمان:</strong> يجب إدخال كلاً من الرقم الوزاري والرقم الوطني للاستعلام
                                </div>
                                <br>
                                <small class="text-light mt-1 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الرقم الوطني يجب أن يكون مكوناً من 10 أرقام
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-2">جاري البحث...</p>
            </div>

            <!-- Error Alert -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"></span>
            </div>

            <!-- Multiple Employees Alert -->
            <div id="multipleEmployeesAlert" class="alert alert-warning" style="display: none;">
                <h5><i class="fas fa-users me-2"></i>يوجد أكثر من موظف بهذا الاسم</h5>
                <p>يرجى استخدام الرقم الوزاري للبحث الدقيق، أو اختيار أحد الموظفين التاليين:</p>
                <div id="employeesList"></div>
            </div>

            <!-- Results Card -->
            <div id="resultsCard" class="card shadow result-card">
                <div class="card-header bg-primary text-white no-print">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        نتائج البحث
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Employee Info -->
                    <div id="employeeInfo" class="employee-info">
                        <!-- Employee details will be populated here -->
                    </div>

                    <!-- Leave Balance Table -->
                    <div class="table-responsive mb-4">
                        <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i>ملخص رصيد الإجازات</h5>
                        <table class="table table-bordered table-hover" id="balanceTable" width="100%" cellspacing="0">
                            <thead class="thead-light">
                                <tr>
                                    <th rowspan="2" class="text-center align-middle">نوع الإجازة</th>
                                    <th colspan="3" class="text-center">رصيد الإجازات</th>
                                </tr>
                                <tr>
                                    <th class="bg-success text-white text-center">الرصيد الأولي</th>
                                    <th class="bg-danger text-white text-center">الإجازات المستخدمة</th>
                                    <th class="bg-info text-white text-center">الرصيد المتبقي</th>
                                </tr>
                            </thead>
                            <tbody id="balanceTableBody">
                                <!-- Balance data will be populated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Detailed Leaves Table -->
                    <div class="table-responsive" id="detailedLeavesSection" style="display: none;">
                        <h5 class="mb-3"><i class="fas fa-list-alt me-2"></i>تفاصيل الإجازات المستخدمة</h5>
                        <table class="table table-bordered table-hover" id="detailedLeavesTable" width="100%" cellspacing="0">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="text-center">نوع الإجازة</th>
                                    <th class="text-center">تاريخ البدء</th>
                                    <th class="text-center">تاريخ الانتهاء</th>
                                    <th class="text-center">عدد الأيام</th>
                                    <th class="text-center">السبب</th>
                                </tr>
                            </thead>
                            <tbody id="detailedLeavesTableBody">
                                <!-- Detailed leaves data will be populated here -->
                            </tbody>
                            <tfoot id="detailedLeavesTableFooter" style="display: none;">
                                <tr class="table-info">
                                    <th colspan="3" class="text-center">إجمالي الأيام المستخدمة</th>
                                    <th class="text-center" id="totalDaysUsed">0</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- No Leaves Message -->
                    <div id="noLeavesMessage" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد إجازات مستخدمة للموظف في هذه السنة.
                    </div>

                    <!-- Information Section -->
                    <div class="mt-4 no-print">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات عن رصيد الإجازات</h6>
                            <ul class="mb-0">
                                <li><strong>الرصيد الأولي:</strong> رصيد الإجازة في بداية السنة</li>
                                <li><strong>الإجازات المستخدمة:</strong> عدد أيام الإجازة المستخدمة خلال السنة</li>
                                <li><strong>الرصيد المتبقي:</strong> الرصيد المتبقي من الإجازة</li>
                                <li><strong>تفاصيل الإجازات:</strong> يعرض الجدول أدناه تفاصيل جميع الإجازات المعتمدة للموظف في السنة الحالية</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Print Button -->
                    <div class="text-center mt-4 no-print">
                        <button class="btn btn-success btn-lg" onclick="printResults()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary btn-lg ms-2" onclick="clearResults()">
                            <i class="fas fa-times me-2"></i>
                            مسح النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Print Button -->
<button class="btn btn-success print-btn" onclick="printResults()" title="طباعة">
    <i class="fas fa-print"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle search button click
    $('#searchBtn').click(function() {
        performSearch();
    });

    // Handle Enter key press in both inputs
    $('#ministryNumberInput, #nationalIdInput').keypress(function(e) {
        if (e.which == 13) {
            performSearch();
        }
    });

    // Auto-focus on ministry number input
    $('#ministryNumberInput').focus();

    // Allow only numbers in national ID input
    $('#nationalIdInput').on('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
    });

    // Add real-time validation feedback
    $('#nationalIdInput').on('input', function() {
        const value = $(this).val();
        if (value.length > 0 && value.length < 10) {
            $(this).removeClass('is-valid').addClass('is-invalid');
        } else if (value.length === 10) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid is-invalid');
        }
    });
});

function performSearch() {
    const ministryNumber = $('#ministryNumberInput').val().trim();
    const nationalId = $('#nationalIdInput').val().trim();

    if (!ministryNumber) {
        showError('الرجاء إدخال الرقم الوزاري');
        $('#ministryNumberInput').focus();
        return;
    }

    if (!nationalId) {
        showError('الرجاء إدخال الرقم الوطني');
        $('#nationalIdInput').focus();
        return;
    }

    // التحقق من صحة تنسيق الرقم الوطني
    if (!/^\d{10}$/.test(nationalId)) {
        showError('الرقم الوطني يجب أن يكون مكوناً من 10 أرقام فقط');
        $('#nationalIdInput').focus();
        return;
    }

    // Hide previous results and errors
    hideAllAlerts();
    $('#resultsCard').hide();
    $('.print-btn').hide();

    // Show loading indicator
    $('#loadingIndicator').show();

    // Make AJAX request
    $.ajax({
        url: '{% url "home:get_employee_leave_balance" %}',
        method: 'GET',
        data: {
            'ministry_number': ministryNumber,
            'national_id': nationalId
        },
        success: function(response) {
            $('#loadingIndicator').hide();

            if (response.success) {
                displayResults(response);
            } else {
                if (response.multiple_employees) {
                    showMultipleEmployees(response.multiple_employees);
                } else if (response.employee_status) {
                    showEmployeeStatusError(response);
                } else {
                    showError(response.error);
                }
            }
        },
        error: function(xhr, status, error) {
            $('#loadingIndicator').hide();
            showError('حدث خطأ في الاتصال بالخادم');
        }
    });
}

function displayResults(data) {
    const employee = data.employee;
    const balances = data.balances;
    const detailedLeaves = data.detailed_leaves || [];

    // Populate employee info
    const employeeInfoHtml = `
        <div class="row">
            <div class="col-md-6">
                <h4 style="color: black;"><i class="fas fa-user me-2"></i>${employee.full_name}</h4>
                <p class="mb-1" style="color: black;"><strong>الرقم الوزاري:</strong> ${employee.ministry_number}</p>
                <p class="mb-0" style="color: black;"><strong>القسم:</strong> ${employee.department}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <h5 style="color: black;"><i class="fas fa-calendar me-2"></i>السنة: ${employee.year}</h5>
                <p class="mb-1" style="color: black;"><small>تاريخ الاستعلام: ${employee.inquiry_date}</small></p>
                <p class="mb-0" style="color: black;"><small>آخر تحديث للإجازات: ${employee.last_leave_update}</small></p>
            </div>
        </div>
    `;
    $('#employeeInfo').html(employeeInfoHtml);

    // Populate balance table
    let tableBodyHtml = '';

    // تأكد من وجود الأنواع الثلاثة للإجازات
    const requiredTypes = ['سنوية', 'مرضية', 'عرضية'];
    const balanceMap = {};

    // إنشاء خريطة للأرصدة الموجودة
    balances.forEach(function(balance) {
        balanceMap[balance.type_name] = balance;
    });

    // إضافة الأنواع المطلوبة
    requiredTypes.forEach(function(typeName) {
        const balance = balanceMap[typeName] || {
            type_name: typeName,
            initial_balance: 0,
            used_balance: 0,
            remaining_balance: 0
        };

        tableBodyHtml += `
            <tr class="text-center">
                <td class="text-start"><strong>${balance.type_name}</strong></td>
                <td class="bg-success bg-opacity-25">${balance.initial_balance}</td>
                <td class="bg-danger bg-opacity-25">${balance.used_balance}</td>
                <td class="bg-info bg-opacity-25">${balance.remaining_balance}</td>
            </tr>
        `;
    });

    $('#balanceTableBody').html(tableBodyHtml);

    // Populate detailed leaves table
    if (detailedLeaves.length > 0) {
        let detailedTableBodyHtml = '';
        let totalDays = 0;
        
        detailedLeaves.forEach(function(leave) {
            totalDays += parseInt(leave.days_count);
            
            // تحويل التاريخ إلى تنسيق ميلادي (dd/mm/yyyy)
            const startDate = new Date(leave.start_date).toLocaleDateString('en-GB');
            const endDate = new Date(leave.end_date).toLocaleDateString('en-GB');
            
            detailedTableBodyHtml += `
                <tr class="text-center">
                    <td><strong>${leave.type_name}</strong></td>
                    <td><i class="fas fa-calendar-alt me-1"></i>${startDate}</td>
                    <td><i class="fas fa-calendar-alt me-1"></i>${endDate}</td>
                    <td><span class="badge bg-primary">${leave.days_count}</span></td>
                    <td class="text-start">${leave.reason}</td>
                </tr>
            `;
        });
        
        $('#detailedLeavesTableBody').html(detailedTableBodyHtml);
        $('#totalDaysUsed').text(totalDays);
        $('#detailedLeavesTableFooter').show();
        $('#detailedLeavesSection').show();
        $('#noLeavesMessage').hide();
    } else {
        $('#detailedLeavesSection').hide();
        $('#detailedLeavesTableFooter').hide();
        $('#noLeavesMessage').show();
    }

    // Show results
    $('#resultsCard').show();
    $('.print-btn').show();

    // Scroll to results
    $('html, body').animate({
        scrollTop: $('#resultsCard').offset().top - 100
    }, 500);
}

function showError(message) {
    $('#errorMessage').text(message);
    $('#errorAlert').show();
}

function showMultipleEmployees(employees) {
    let employeesListHtml = '<div class="list-group">';
    employees.forEach(function(emp) {
        employeesListHtml += `
            <button type="button" class="list-group-item list-group-item-action"
                    onclick="selectEmployee('${emp.ministry_number}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${emp.full_name}</h6>
                    <small>الرقم الوزاري: ${emp.ministry_number}</small>
                </div>
                <p class="mb-1">${emp.school}</p>
            </button>
        `;
    });
    employeesListHtml += '</div>';

    $('#employeesList').html(employeesListHtml);
    $('#multipleEmployeesAlert').show();
}

function selectEmployee(ministryNumber) {
    $('#searchInput').val(ministryNumber);
    hideAllAlerts();
    performSearch();
}

function showEmployeeStatusError(response) {
    const statusDetails = response.status_details;
    let statusIcon = '';
    let statusClass = '';
    let statusMessage = '';
    
    // تحديد الأيقونة والفئة حسب نوع الحالة
    switch(response.employee_status) {
        case 'retired':
            statusIcon = 'fas fa-user-clock';
            statusClass = 'alert-warning';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-warning me-3"></i>
                    <div>
                        <h5 class="mb-1 text-warning">موظف متقاعد</h5>
                        <p class="mb-0 text-muted">هذا الموظف متقاعد وليس له رصيد إجازات حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <strong>اسم الموظف:</strong><br>
                        <span class="text-primary">${statusDetails.employee_name}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>الرقم الوزاري:</strong><br>
                        <span class="text-primary">${statusDetails.ministry_number}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>سبب التقاعد:</strong><br>
                        <span class="text-primary">${statusDetails.retirement_reason}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4">
                        <strong>تاريخ التقاعد:</strong><br>
                        <span class="text-primary">${statusDetails.date}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>الحالة:</strong>
                        <span class="badge bg-warning">متقاعد</span>
                    </div>
                    <div class="col-md-4">
                        <strong>الملاحظات:</strong><br>
                        <small class="text-muted">${statusDetails.notes}</small>
                    </div>
                </div>
            `;
            break;
            
        case 'transferred':
            statusIcon = 'fas fa-exchange-alt';
            statusClass = 'alert-info';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-info me-3"></i>
                    <div>
                        <h5 class="mb-1 text-info">موظف منقول خارجياً</h5>
                        <p class="mb-0 text-muted">هذا الموظف منقول خارجياً وليس له رصيد إجازات في المديرية حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <strong>اسم الموظف:</strong><br>
                        <span class="text-primary">${statusDetails.employee_name}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>الرقم الوزاري:</strong><br>
                        <span class="text-primary">${statusDetails.ministry_number}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>تاريخ النقل:</strong><br>
                        <span class="text-primary">${statusDetails.date}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>الجهة المنقول إليها:</strong><br>
                        <span class="text-primary">${statusDetails.destination}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <span class="badge bg-info">منقول خارجياً</span>
                    </div>
                </div>
            `;
            break;
            
        case 'service_purchase':
            statusIcon = 'fas fa-shopping-cart';
            statusClass = 'alert-secondary';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-secondary me-3"></i>
                    <div>
                        <h5 class="mb-1 text-secondary">موظف في حالة شراء خدمات</h5>
                        <p class="mb-0 text-muted">هذا الموظف في حالة شراء خدمات وليس له رصيد إجازات في المديرية حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <strong>اسم الموظف:</strong><br>
                        <span class="text-primary">${statusDetails.employee_name}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>الرقم الوزاري:</strong><br>
                        <span class="text-primary">${statusDetails.ministry_number}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>تاريخ الإضافة:</strong><br>
                        <span class="text-primary">${statusDetails.created_date}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>المدرسة المستهدفة:</strong><br>
                        <span class="text-primary">${statusDetails.target_school}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <span class="badge bg-secondary">شراء خدمات</span>
                    </div>
                </div>
            `;
            break;
            
        default:
            showError(response.error);
            return;
    }
    
    // إنشاء HTML للرسالة
    const alertHtml = `
        <div class="alert ${statusClass} alert-dismissible fade show" role="alert">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            ${statusMessage}
        </div>
    `;
    
    // إخفاء النتائج وإظهار رسالة الحالة
    $('#resultsCard').hide();
    $('#detailedLeavesSection').hide();
    $('.print-btn').hide();
    
    // إظهار رسالة الحالة
    $('#errorAlert').hide();
    $('#multipleEmployeesAlert').hide();
    $('#resultsCard').html(alertHtml).show();
}

function hideAllAlerts() {
    $('#errorAlert').hide();
    $('#multipleEmployeesAlert').hide();
}

function printResults() {
    window.print();
}

function clearResults() {
    $('#ministryNumberInput').val('');
    $('#nationalIdInput').val('');
    $('#resultsCard').hide().html(''); // مسح المحتوى أيضاً
    $('.print-btn').hide();
    $('#detailedLeavesSection').hide();
    $('#detailedLeavesTableFooter').hide();
    $('#noLeavesMessage').hide();
    hideAllAlerts();
    $('#searchInput').focus();
}
</script>
{% endblock %}
