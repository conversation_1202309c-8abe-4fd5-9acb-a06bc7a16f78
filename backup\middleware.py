"""
Middleware لتعطيل النظام أثناء عمليات النسخ الاحتياطي والاستعادة
"""

import os
from django.conf import settings
from django.http import HttpResponse
from django.template import Template, Context
from django.utils.deprecation import MiddlewareMixin


class MaintenanceModeMiddleware(MiddlewareMixin):
    """
    Middleware لتعطيل النظام أثناء عمليات الصيانة
    """
    
    def process_request(self, request):
        """
        فحص ما إذا كان النظام في وضع الصيانة
        """
        # مسار ملف الصيانة
        maintenance_file = os.path.join(settings.BASE_DIR, '.maintenance')
        
        # التحقق من وجود ملف الصيانة
        if os.path.exists(maintenance_file):
            # قراءة معلومات الصيانة
            try:
                with open(maintenance_file, 'r', encoding='utf-8') as f:
                    maintenance_info = f.read().strip()
                    
                # تحليل معلومات الصيانة
                lines = maintenance_info.split('\n')
                operation_type = lines[0] if lines else 'صيانة عامة'
                start_time = lines[1] if len(lines) > 1 else ''
                
            except Exception:
                operation_type = 'صيانة عامة'
                start_time = ''
            
            # السماح للمدير بالوصول لصفحات النسخ الاحتياطي فقط
            allowed_paths = [
                '/backup/',
                '/admin/logout/',
                '/static/',
                '/media/',
            ]
            
            # التحقق من المسارات المسموحة
            path_allowed = False
            for allowed_path in allowed_paths:
                if request.path.startswith(allowed_path):
                    path_allowed = True
                    break
            
            # السماح للمدير فقط بالوصول للمسارات المسموحة
            if not (request.user.is_authenticated and request.user.is_superuser and path_allowed):
                # إرجاع صفحة الصيانة
                return self.render_maintenance_page(operation_type, start_time)
        
        return None
    
    def render_maintenance_page(self, operation_type, start_time):
        """
        عرض صفحة الصيانة
        """
        template_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام في حالة صيانة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .maintenance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }
        
        .maintenance-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .maintenance-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .maintenance-subtitle {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .maintenance-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 40px;
        }
        
        .maintenance-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .info-value {
            color: #667eea;
            font-weight: 500;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .maintenance-footer {
            color: #999;
            font-size: 0.9rem;
            margin-top: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 10%; }
            50% { width: 80%; }
            100% { width: 10%; }
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
                margin: 20px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-subtitle {
                font-size: 1.1rem;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
    <script>
        // تحديث الوقت كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // تحديث الصفحة كل 30 ثانية للتحقق من انتهاء الصيانة
        function autoRefresh() {
            setTimeout(function() {
                window.location.reload();
            }, 30000);
        }
        
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 1000);
            autoRefresh();
        };
    </script>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">🔧</div>
        <h1 class="maintenance-title">النظام في حالة صيانة</h1>
        <h2 class="maintenance-subtitle">{{ operation_type }}</h2>
        
        <div class="maintenance-description">
            نعتذر عن الإزعاج. النظام حالياً في وضع الصيانة لضمان أفضل أداء وأمان.
            <br>
            سيتم استكمال العملية قريباً وسيعود النظام للعمل تلقائياً.
        </div>
        
        <div class="maintenance-info">
            <div class="info-item">
                <span class="info-label">نوع العملية:</span>
                <span class="info-value">{{ operation_type }}</span>
            </div>
            {% if start_time %}
            <div class="info-item">
                <span class="info-label">بدء العملية:</span>
                <span class="info-value">{{ start_time }}</span>
            </div>
            {% endif %}
            <div class="info-item">
                <span class="info-label">الوقت الحالي:</span>
                <span class="info-value" id="current-time">--</span>
            </div>
            <div class="info-item">
                <span class="info-label">الحالة:</span>
                <span class="info-value">جاري التحديث...</span>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="loading-spinner"></div>
        
        <div class="maintenance-footer">
            سيتم تحديث هذه الصفحة تلقائياً كل 30 ثانية
            <br>
            شكراً لصبركم وتفهمكم
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(template_content)
        context = Context({
            'operation_type': operation_type,
            'start_time': start_time
        })
        
        return HttpResponse(template.render(context), status=503)