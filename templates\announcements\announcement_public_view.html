<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - نظام شؤون الموظفين</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .announcement-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            margin: 40px auto;
            max-width: 900px;
        }
        
        .announcement-header {
            background: linear-gradient(135deg, {{ announcement.type_color|default:"#007bff" }} 0%, {{ announcement.priority_color|default:"#6c757d" }} 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }
        
        .announcement-header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><path d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,37.9,500,37.9S999.1,23.1,1000,4.3z"></path></svg>') repeat-x bottom;
            pointer-events: none;
        }
        
        .announcement-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .announcement-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .announcement-meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }
        
        .announcement-body {
            padding: 50px;
        }
        
        .announcement-content {
            font-size: 1.2rem;
            line-height: 2;
            color: #444;
            text-align: justify;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            border-right: 5px solid {{ announcement.type_color|default:"#007bff" }};
            margin-bottom: 30px;
        }
        
        .announcement-footer {
            background: #f8f9fa;
            padding: 30px 50px;
            border-top: 1px solid #dee2e6;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn-custom {
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .btn-home {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .btn-external {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }
        
        .urgent-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            animation: urgentBlink 1.5s infinite;
        }
        
        @keyframes urgentBlink {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
        }
        
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-top: 3px solid {{ announcement.type_color|default:"#007bff" }};
        }
        
        .info-card-icon {
            font-size: 2rem;
            color: {{ announcement.type_color|default:"#007bff" }};
            margin-bottom: 10px;
        }
        
        .info-card-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .info-card-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .announcement-container {
                margin: 20px;
                border-radius: 15px;
            }
            
            .announcement-header {
                padding: 30px 20px;
            }
            
            .announcement-title {
                font-size: 1.8rem;
            }
            
            .announcement-body {
                padding: 30px 20px;
            }
            
            .announcement-footer {
                padding: 20px;
            }
            
            .announcement-meta {
                gap: 10px;
            }
            
            .meta-item {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="announcement-container">
            <!-- Header -->
            <div class="announcement-header">
                {% if announcement.priority == 'urgent' %}
                    <div class="urgent-indicator">
                        🚨 عاجل
                    </div>
                {% endif %}
                
                <div class="announcement-icon">
                    <i class="fas {{ announcement.type_icon }}"></i>
                </div>
                
                <h1 class="announcement-title">{{ announcement.title }}</h1>
                
                <div class="announcement-meta">
                    <div class="meta-item">
                        <i class="fas fa-tag me-2"></i>
                        {{ announcement.get_announcement_type_display }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ announcement.get_priority_display }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar me-2"></i>
                        {{ announcement.created_at|date:"Y-m-d" }}
                    </div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="announcement-body">
                <!-- Info Cards -->
                <div class="info-cards">
                    <div class="info-card">
                        <div class="info-card-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-card-title">تاريخ النشر</div>
                        <div class="info-card-value">{{ announcement.created_at|date:"Y/m/d" }}</div>
                    </div>
                    
                    {% if announcement.start_date %}
                    <div class="info-card">
                        <div class="info-card-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="info-card-title">تاريخ البداية</div>
                        <div class="info-card-value">{{ announcement.start_date|date:"Y/m/d" }}</div>
                    </div>
                    {% endif %}
                    
                    {% if announcement.end_date %}
                    <div class="info-card">
                        <div class="info-card-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="info-card-title">تاريخ الانتهاء</div>  
                        <div class="info-card-value">{{ announcement.end_date|date:"Y/m/d" }}</div>
                    </div>
                    {% endif %}
                    
                    <div class="info-card">
                        <div class="info-card-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="info-card-title">المشاهدات</div>
                        <div class="info-card-value">{{ announcement.views_count }}</div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="announcement-content">
                    {{ announcement.content|linebreaks }}
                </div>
            </div>
            
            <!-- Footer -->
            <div class="announcement-footer">
                <div class="action-buttons">
                    <a href="/" class="btn btn-custom btn-home">
                        <i class="fas fa-home me-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                    

                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        هذا الإعلان منشور من نظام شؤون الموظفين
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        function trackExternalClick(announcementId) {
            // Track external link click
            $.ajax({
                url: `/announcements/${announcementId}/track-click/`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Smooth animations on load
        $(document).ready(function() {
            $('.announcement-container').hide().fadeIn(800);
            
            $('.info-card').each(function(index) {
                $(this).delay(index * 100).fadeIn(600);
            });
        });
    </script>
</body>
</html>