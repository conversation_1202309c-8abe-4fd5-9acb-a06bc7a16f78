{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة القياس - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">لوحة القياس</h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm" onclick="window.print()">
            <i class="fas fa-download fa-sm text-white-50 ml-2"></i> تصدير التقرير
        </a>
    </div>

    <!-- Cards Row -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الموظفين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_employees }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المسميات الوظيفية</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_positions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-id-card fa-2x text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الأقسام</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_departments }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-info opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Gender Distribution Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow mb-4 dashboard-chart-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(40deg, #4e73df, #36b9cc); border-radius: 0.35rem 0.35rem 0 0;">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-venus-mars mr-2"></i>
                        توزيع الموظفين حسب الجنس
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle text-white" href="#" role="button" id="dropdownMenuLink1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink1">
                            <div class="dropdown-header">خيارات المخطط:</div>
                            <a class="dropdown-item" href="#" onclick="window.print()">تصدير كصورة</a>
                            <a class="dropdown-item" href="#">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="genderChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Qualification Distribution Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow mb-4 dashboard-chart-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(40deg, #1cc88a, #20c997); border-radius: 0.35rem 0.35rem 0 0;">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        توزيع الموظفين حسب المؤهل العلمي
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle text-white" href="#" role="button" id="dropdownMenuLink2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink2">
                            <div class="dropdown-header">خيارات المخطط:</div>
                            <a class="dropdown-item" href="#" onclick="window.print()">تصدير كصورة</a>
                            <a class="dropdown-item" href="#">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="qualificationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Position Distribution Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow mb-4 dashboard-chart-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(40deg, #6f42c1, #e83e8c); border-radius: 0.35rem 0.35rem 0 0;">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-briefcase mr-2"></i>
                        توزيع الموظفين حسب المسمى الوظيفي
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle text-white" href="#" role="button" id="dropdownMenuLink3" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink3">
                            <div class="dropdown-header">خيارات المخطط:</div>
                            <a class="dropdown-item" href="#" onclick="window.print()">تصدير كصورة</a>
                            <a class="dropdown-item" href="#">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="positionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-card {
        transition: all 0.3s ease;
        border-left-width: 0.25rem;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
    }

    .dashboard-chart-card {
        transition: all 0.3s ease;
        overflow: hidden;
        border: none;
        border-radius: 0.35rem;
    }

    .dashboard-chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
    }

    .opacity-50 {
        opacity: 0.5;
    }

    @media print {
        .sidebar, .navbar, .dropdown-toggle, .btn-primary {
            display: none !important;
        }

        .dashboard-chart-card {
            break-inside: avoid;
            page-break-inside: avoid;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Chart.js and Plugins -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-gradient"></script>

<script>
    // Register Chart.js plugins
    Chart.register(ChartDataLabels);

    // Custom color palettes
    const genderColors = {
        backgroundColors: [
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 99, 132, 0.8)'
        ],
        borderColors: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)'
        ],
        hoverColors: [
            'rgba(54, 162, 235, 0.9)',
            'rgba(255, 99, 132, 0.9)'
        ]
    };

    const qualificationGradient = (ctx) => {
        const gradient = ctx.createLinearGradient(0, 0, ctx.canvas.width, 0);
        gradient.addColorStop(0, 'rgba(75, 192, 192, 0.8)');
        gradient.addColorStop(1, 'rgba(32, 201, 151, 0.8)');
        return gradient;
    };

    const positionGradient = (ctx) => {
        const gradient = ctx.createLinearGradient(0, 0, ctx.canvas.width, 0);
        gradient.addColorStop(0, 'rgba(153, 102, 255, 0.8)');
        gradient.addColorStop(1, 'rgba(102, 16, 242, 0.8)');
        return gradient;
    };

    // Parse data from Django
    const genderData = JSON.parse('{{ gender_data|safe }}');
    const qualificationData = JSON.parse('{{ qualification_data|safe }}');
    const positionData = JSON.parse('{{ position_data|safe }}');

    // Calculate total for percentages
    const genderTotal = genderData.data.reduce((a, b) => a + b, 0);

    // Gender Chart - Doughnut chart with better styling
    const genderCtx = document.getElementById('genderChart').getContext('2d');
    const genderChart = new Chart(genderCtx, {
        type: 'doughnut',
        data: {
            labels: genderData.labels.map(gender => genderData.label_names[gender]),
            datasets: [{
                data: genderData.data,
                backgroundColor: genderColors.backgroundColors,
                borderColor: genderColors.borderColors,
                hoverBackgroundColor: genderColors.hoverColors,
                borderWidth: 2,
                borderRadius: 4,
                hoverOffset: 15
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            animation: {
                animateScale: true,
                animateRotate: true,
                duration: 2000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    rtl: true,
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            family: 'Cairo, sans-serif',
                            size: 12,
                            weight: 'bold'
                        }
                    }
                },
                datalabels: {
                    formatter: (value, ctx) => {
                        const percentage = ((value / genderTotal) * 100).toFixed(1) + '%';
                        return percentage;
                    },
                    color: '#fff',
                    font: {
                        family: 'Cairo, sans-serif',
                        weight: 'bold',
                        size: 14
                    },
                    textStrokeColor: '#000',
                    textStrokeWidth: 1,
                    textShadowBlur: 5,
                    textShadowColor: 'rgba(0, 0, 0, 0.5)'
                },
                tooltip: {
                    callbacks: {
                        label: (context) => {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const percentage = ((value / genderTotal) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    },
                    titleFont: {
                        family: 'Cairo, sans-serif'
                    },
                    bodyFont: {
                        family: 'Cairo, sans-serif'
                    },
                    rtl: true,
                    textDirection: 'rtl'
                }
            }
        }
    });

    // Qualification Chart - Horizontal bar with gradient
    const qualificationCtx = document.getElementById('qualificationChart').getContext('2d');
    const qualificationChart = new Chart(qualificationCtx, {
        type: 'bar',
        data: {
            labels: qualificationData.labels,
            datasets: [{
                label: 'عدد الموظفين',
                data: qualificationData.data,
                backgroundColor: qualificationGradient(qualificationCtx),
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1,
                borderRadius: 6,
                borderSkipped: false,
                barPercentage: 0.7,
                categoryPercentage: 0.8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            animation: {
                delay: (context) => context.dataIndex * 100,
                duration: 1000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    display: false
                },
                datalabels: {
                    align: 'end',
                    anchor: 'end',
                    offset: 4,
                    color: '#333',
                    font: {
                        family: 'Cairo, sans-serif',
                        weight: 'bold'
                    },
                    formatter: (value) => value
                },
                tooltip: {
                    titleFont: {
                        family: 'Cairo, sans-serif'
                    },
                    bodyFont: {
                        family: 'Cairo, sans-serif'
                    },
                    rtl: true,
                    textDirection: 'rtl'
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        drawBorder: true,
                        drawOnChartArea: true,
                        drawTicks: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                y: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif',
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                }
            }
        }
    });

    // Position Chart - Horizontal bar with gradient
    const positionCtx = document.getElementById('positionChart').getContext('2d');
    const positionChart = new Chart(positionCtx, {
        type: 'bar',
        data: {
            labels: positionData.labels,
            datasets: [{
                label: 'عدد الموظفين',
                data: positionData.data,
                backgroundColor: positionGradient(positionCtx),
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1,
                borderRadius: 6,
                borderSkipped: false,
                barPercentage: 0.7,
                categoryPercentage: 0.8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            animation: {
                delay: (context) => context.dataIndex * 100,
                duration: 1000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    display: false
                },
                datalabels: {
                    align: 'end',
                    anchor: 'end',
                    offset: 4,
                    color: '#333',
                    font: {
                        family: 'Cairo, sans-serif',
                        weight: 'bold'
                    },
                    formatter: (value) => value
                },
                tooltip: {
                    titleFont: {
                        family: 'Cairo, sans-serif'
                    },
                    bodyFont: {
                        family: 'Cairo, sans-serif'
                    },
                    rtl: true,
                    textDirection: 'rtl'
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        drawBorder: true,
                        drawOnChartArea: true,
                        drawTicks: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                y: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif',
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                }
            }
        }
    });
</script>
{% endblock %}
