{% extends 'base.html' %}
{% load static %}

{% block title %}{% if leave %}تعديل إجازة{% else %}إضافة إجازة جديدة{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if leave %}تعديل إجازة{% else %}إضافة إجازة جديدة{% endif %}</h2>
    <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للإجازات
    </a>
</div>

<div class="alert alert-info mb-4 alert-permanent">
    <div class="d-flex align-items-center mb-2">
        <i class="fas fa-calendar-alt ml-2"></i>
        <strong>تعليمات إدخال بيانات الإجازة</strong>
    </div>
    <ul>
        <li><strong>الرقم الوزاري:</strong> أدخل الرقم الوزاري للموظف ثم اضغط على زر البحث للحصول على بيانات الموظف.</li>
        <li><strong>نوع الإجازة:</strong> اختر نوع الإجازة المناسب من القائمة.</li>
        <li><strong>تاريخ البداية والنهاية:</strong> حدد تاريخ بداية ونهاية الإجازة وسيتم حساب عدد الأيام تلقائياً.</li>
        <li><strong>السبب:</strong> أدخل سبب الإجازة إن وجد.</li>
    </ul>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات الإجازة</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            {{ form.employee }}
            <input type="hidden" name="employee_id" id="id_employee_id" value="">

            <div class="mb-3">
                <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                <div class="input-group">
                    {{ form.ministry_number }}
                    <button class="btn btn-secondary" type="button" id="search_employee_btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div id="ministry_number_error" class="invalid-feedback d-none"></div>
                {% if form.ministry_number.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.ministry_number.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                {{ form.employee_name }}
                {% if form.employee_name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.employee_name.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.leave_type.id_for_label }}" class="form-label">نوع الإجازة</label>
                    {{ form.leave_type }}
                    {% if form.leave_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.leave_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                <div class="col-md-6 mb-3">
                    <!-- حقل إضافي يمكن إضافته هنا إذا لزم الأمر -->
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.start_date.id_for_label }}" class="form-label">من تاريخ</label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.start_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.end_date.id_for_label }}" class="form-label">إلى تاريخ</label>
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.end_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.days_count.id_for_label }}" class="form-label">عدد الأيام</label>
                    {{ form.days_count }}
                    {% if form.days_count.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.days_count.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">سيتم حساب عدد الأيام تلقائياً إذا تركت هذا الحقل فارغاً</div>
                </div>


            </div>

            <div class="mb-3">
                <label for="{{ form.reason.id_for_label }}" class="form-label">السبب</label>
                {{ form.reason }}
                {% if form.reason.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.reason.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-secondary" id="save_button">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/leaves/get-employee/?ministry_number=${ministryNumber}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        if (response.status === 403) {
                            // Redirect to login page if not authenticated
                            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                            throw new Error('يجب تسجيل الدخول للمتابعة');
                        }
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    // Check if data is successful and employee data exists
                    if (!data.success) {
                        throw new Error(data.error || 'بيانات الموظف غير متوفرة');
                    }

                    if (!data.employee) {
                        throw new Error('بيانات الموظف غير متوفرة');
                    }

                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement && selectElement.options) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.employee.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    } else if (selectElement) {
                        // If options is undefined but selectElement exists, create a new option
                        const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                        selectElement.appendChild(newOption);
                    }
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput && ministryNumberInput.value && employeeNameDisplay && !employeeNameDisplay.value) {
            searchEmployee();
        }

        // Calculate days count when dates change
        const startDateInput = document.getElementById('id_start_date');
        const endDateInput = document.getElementById('id_end_date');
        const daysCountInput = document.getElementById('id_days_count');

        function calculateDays() {
            if (startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

                if (diffDays > 0 && daysCountInput) {
                    daysCountInput.value = diffDays;
                }
            }
        }

        if (startDateInput) {
            startDateInput.addEventListener('change', calculateDays);
        }
        if (endDateInput) {
            endDateInput.addEventListener('change', calculateDays);
        }

        // Add form submission handler
        const form = document.querySelector('form');
        const saveButton = document.getElementById('save_button');

        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Form submitted');

                // Check if employee is selected
                if (!employeeIdInput || !employeeIdInput.value) {
                    e.preventDefault();
                    alert('الرجاء اختيار موظف أولاً');
                    return false;
                }

                // Check if leave type is selected
                const leaveTypeSelect = document.getElementById('id_leave_type');
                if (leaveTypeSelect && !leaveTypeSelect.value) {
                    e.preventDefault();
                    alert('الرجاء اختيار نوع الإجازة');
                    return false;
                }

                // Check if dates are selected
                if (startDateInput && !startDateInput.value) {
                    e.preventDefault();
                    alert('الرجاء اختيار تاريخ بداية الإجازة');
                    return false;
                }

                if (endDateInput && !endDateInput.value) {
                    e.preventDefault();
                    alert('الرجاء اختيار تاريخ نهاية الإجازة');
                    return false;
                }

                // Disable the save button to prevent double submission
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                }

                // Make sure the employee ID is set in the form
                if (employeeIdInput && employeeIdInput.value) {
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == employeeIdInput.value) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(employeeNameDisplay.value, employeeIdInput.value, true, true);
                            selectElement.appendChild(newOption);
                        }
                    }
                }

                return true;
            });
        }
    });
</script>
{% endblock %}
