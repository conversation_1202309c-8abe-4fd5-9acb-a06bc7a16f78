{% extends 'base.html' %}
{% load i18n %}

{% block title %}تنظيف المسميات الوظيفية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-broom"></i> تنظيف المسميات الوظيفية للمعلمين
                    </h6>
                    <a href="{% url 'employment:position_list' %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> العودة للمسميات الوظيفية
                    </a>
                </div>
                <div class="card-body">
                    {% if existing_positions %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تحذير:</strong> سيتم حذف المسميات الوظيفية التالية التي تم إضافتها تلقائياً. 
                            المسميات المرتبطة بموظفين لن يتم حذفها.
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="thead-light">
                                    <tr>
                                        <th>المسمى الوظيفي</th>
                                        <th>الحالة</th>
                                        <th>إمكانية الحذف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for position in existing_positions %}
                                    <tr>
                                        <td>{{ position.name }}</td>
                                        <td>
                                            {% if position.has_employees %}
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-users"></i> مرتبط بموظفين
                                                </span>
                                            {% else %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> غير مرتبط
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if position.can_delete %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-trash"></i> سيتم الحذف
                                                </span>
                                            {% else %}
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-ban"></i> لن يتم الحذف
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <form method="post" onsubmit="return confirm('هل أنت متأكد من حذف المسميات الوظيفية المحددة؟');">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> تأكيد الحذف
                                </button>
                                <a href="{% url 'employment:position_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </form>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد مسميات وظيفية للمعلمين تحتاج للحذف.
                        </div>
                        <a href="{% url 'employment:position_list' %}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> العودة للمسميات الوظيفية
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
