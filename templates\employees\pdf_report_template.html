<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        
        .header .info {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #e9ecef;
        }
        
        .status-active { color: #28a745; font-weight: bold; }
        .status-retired { color: #dc3545; font-weight: bold; }
        .status-transferred { color: #fd7e14; font-weight: bold; }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 11px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        
        .action-buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }
        
        .print-button, .pdf-button, .back-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .pdf-button {
            background-color: #dc3545;
        }
        
        .back-button {
            background-color: #6c757d;
        }
        
        .print-button:hover {
            background-color: #0056b3;
        }
        
        .pdf-button:hover {
            background-color: #c82333;
        }
        
        .back-button:hover {
            background-color: #545b62;
        }
        
        /* تحسين العرض للشاشات الصغيرة */
        @media (max-width: 768px) {
            body { margin: 10px; font-size: 10px; }
            table { font-size: 8px; }
            th, td { padding: 4px; }
        }
        
        /* تحسين الطباعة */
        @page {
            size: A4 landscape;
            margin: 1cm;
        }
        
        @media print {
            .action-buttons { display: none; }
            body { font-size: 10px; }
            table { font-size: 8px; }
            th, td { padding: 4px; }
        }
    </style>
</head>
<body>
    <div class="action-buttons no-print">
        <button class="back-button" onclick="goBack()">🔙 عودة</button>
        <button class="print-button" onclick="window.print()">🖨️ طباعة التقرير</button>
        <button class="pdf-button" onclick="downloadPDF()">📄 تحميل PDF</button>
    </div>
    
    <div class="header">
        <h1>{{ title }}</h1>
        <div class="info">
            <p>تاريخ إنشاء التقرير: {{ report_date }}</p>
            <p>إجمالي عدد الموظفين: {{ total_count }}</p>
        </div>
    </div>
    
    <div class="summary">
        <h3>📊 ملخص التقرير</h3>
        <p>يحتوي هذا التقرير على بيانات شاملة لجميع الموظفين تشمل المعلومات الشخصية، المؤهلات العلمية، بيانات التوظيف، والحالة الوظيفية.</p>
    </div>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>الرقم الوزاري</th>
                    <th>الاسم الكامل</th>
                    <th>الجنس</th>
                    <th>المؤهل العلمي</th>
                    <th>دبلوم عالي</th>
                    <th>ماجستير</th>
                    <th>دكتوراه</th>
                    <th>التخصص</th>
                    <th>القسم الحالي</th>
                    <th>المنصب الحالي</th>
                    <th>نوع التوظيف</th>
                    <th>حالة الموظف</th>
                    <th>تاريخ التعيين</th>
                    <th>رقم الهاتف</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees_data %}
                <tr>
                    <td>{{ employee.ministry_number|default:"-" }}</td>
                    <td>{{ employee.full_name|default:"-" }}</td>
                    <td>{{ employee.gender|default:"-" }}</td>
                    <td>{{ employee.qualification|default:"-" }}</td>
                    <td>{{ employee.post_graduate_diploma|default:"-" }}</td>
                    <td>{{ employee.masters_degree|default:"-" }}</td>
                    <td>{{ employee.phd_degree|default:"-" }}</td>
                    <td>{{ employee.specialization|default:"-" }}</td>
                    <td>{{ employee.school|default:"-" }}</td>
                    <td>{{ employee.current_position|default:"-" }}</td>
                    <td>{{ employee.employment_status|default:"-" }}</td>
                    <td class="{% if employee.employee_status == 'نشط' %}status-active{% elif employee.employee_status == 'متقاعد' %}status-retired{% elif employee.employee_status == 'منقول خارجياً' %}status-transferred{% endif %}">
                        {{ employee.employee_status|default:"-" }}
                    </td>
                    <td>{{ employee.hire_date|default:"-" }}</td>
                    <td>{{ employee.phone_number|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="14" style="text-align: center; color: #666; font-style: italic;">
                        لا توجد بيانات موظفين متاحة
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% if employees_data %}
    <div class="summary">
        <h3>📈 إحصائيات إضافية</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>الموظفين النشطين:</strong>
                {% with active_count=employees_data|length %}
                    {{ active_count }}
                {% endwith %}
            </div>
            <div>
                <strong>الموظفين الذكور:</strong>
                {% with male_count=0 %}
                    {% for emp in employees_data %}
                        {% if emp.الجنس == 'ذكر' %}
                            {% with male_count=male_count|add:1 %}{% endwith %}
                        {% endif %}
                    {% endfor %}
                    {{ male_count }}
                {% endwith %}
            </div>
            <div>
                <strong>الموظفات الإناث:</strong>
                {% with female_count=0 %}
                    {% for emp in employees_data %}
                        {% if emp.الجنس == 'أنثى' %}
                            {% with female_count=female_count|add:1 %}{% endwith %}
                        {% endif %}
                    {% endfor %}
                    {{ female_count }}
                {% endwith %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام شؤون الموظفين</p>
        <p>{{ report_date }} - جميع الحقوق محفوظة</p>
    </div>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التفاعل
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('click', function() {
                    this.style.backgroundColor = '#007bff';
                    this.style.color = 'white';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                        this.style.color = '';
                    }, 1000);
                });
            });
            
            // إضافة اختصار لوحة المفاتيح للطباعة
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    window.print();
                }
            });
        });
        
        
        // دالة العودة لصفحة الاستيراد والتصدير
        function goBack() {
            // العودة لصفحة الاستيراد والتصدير
            window.location.href = window.location.pathname;
        }
        
        // دالة تحميل PDF
        function downloadPDF() {
            // الحصول على المعاملات الحالية
            const urlParams = new URLSearchParams(window.location.search);
            
            // إنشاء رابط تحميل PDF
            let pdfUrl = window.location.pathname + '?export=pdf_download';
            
            // إضافة المعاملات الموجودة
            if (urlParams.get('include_all')) {
                pdfUrl += '&include_all=' + urlParams.get('include_all');
            }
            if (urlParams.get('active_only')) {
                pdfUrl += '&active_only=' + urlParams.get('active_only');
            }
            
            // فتح في نافذة جديدة للتحميل
            window.open(pdfUrl, '_blank');
        }
    </script>
</body>
</html>