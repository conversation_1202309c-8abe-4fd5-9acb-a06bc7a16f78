from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from datetime import date, <PERSON>elta
import openpyxl
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from .models import Employee, MaternityLeave
from .forms import MaternityLeaveForm, AddMaternityLeaveForm


@login_required
def maternity_leaves_list(request):
    """View for listing all maternity leaves"""
    search_query = request.GET.get('search', '')
    
    # Get all maternity leaves
    maternity_leaves = MaternityLeave.objects.select_related('employee').all()
    
    # Apply search filter
    if search_query:
        maternity_leaves = maternity_leaves.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__specialization__icontains=search_query) |
            Q(employee__school__icontains=search_query)
        )
    
    # Update status for expired leaves
    for leave in maternity_leaves:
        if leave.end_date and date.today() > leave.end_date and leave.is_active:
            leave.is_active = False
            if not leave.notes or 'الإجازة انتهت' not in leave.notes:
                if leave.notes:
                    leave.notes += '\n\nالإجازة انتهت تلقائياً في ' + str(leave.end_date)
                else:
                    leave.notes = 'الإجازة انتهت تلقائياً في ' + str(leave.end_date)
            leave.save()
    
    # Get employment information for each employee
    for leave in maternity_leaves:
        employment = leave.employee.employments.filter(is_current=True).first()
        if employment:
            leave.employee.position_name = employment.position.name
        else:
            leave.employee.position_name = '-'
    
    context = {
        'maternity_leaves': maternity_leaves,
        'search_query': search_query,
        'today': date.today()
    }
    
    return render(request, 'employees/maternity_leaves_list.html', context)


@login_required
def add_maternity_leave(request):
    """View for adding a new maternity leave"""
    if request.method == 'POST':
        form = AddMaternityLeaveForm(request.POST)
        if form.is_valid():
            # Create maternity leave from form data
            employee = form.cleaned_data['employee']
            maternity_leave = MaternityLeave.objects.create(
                employee=employee,
                start_date=form.cleaned_data['start_date'],
                notes=form.cleaned_data.get('notes', '')
            )
            messages.success(request, f'تم إضافة إجازة الأمومة للموظفة {employee.full_name} بنجاح')
            return redirect('employees:maternity_leaves_list')
    else:
        form = AddMaternityLeaveForm()
    
    return render(request, 'employees/add_maternity_leave.html', {'form': form})


@login_required
def search_employee_for_maternity(request):
    """AJAX view for searching employees for maternity leave"""
    if request.method == 'GET':
        query = request.GET.get('q', '')
        if query:
            # Search for female employees only
            employees = Employee.objects.filter(
                Q(full_name__icontains=query) | Q(ministry_number__icontains=query),
                gender='female'
            )[:10]
            
            results = []
            for employee in employees:
                # Get current employment
                employment = employee.employments.filter(is_current=True).first()
                position_name = employment.position.name if employment else '-'
                
                results.append({
                    'id': employee.id,
                    'ministry_number': employee.ministry_number,
                    'full_name': employee.full_name,
                    'specialization': employee.specialization,
                    'school': employee.school,
                    'position_name': position_name
                })
            
            return JsonResponse({'results': results})
    
    return JsonResponse({'results': []})


@login_required
def export_maternity_leaves_excel(request):
    """Export active maternity leaves to Excel"""
    # Get only active maternity leaves
    maternity_leaves = MaternityLeave.objects.filter(
        is_active=True
    ).select_related('employee').order_by('-start_date')
    
    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "إجازات الأمومة النشطة"
    
    # Define styles
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')
    
    data_font = Font(name='Arial', size=11)
    data_alignment = Alignment(horizontal='center', vertical='center')
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Headers
    headers = [
        'الرقم الوزاري',
        'الاسم الكامل',
        'التخصص',
        'القسم',
        'بداية الإجازة',
        'تاريخ الانتهاء',
        'الحالة',
        'ملاحظات'
    ]
    
    # Write headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # Write data
    for row, leave in enumerate(maternity_leaves, 2):
        # Get employment information
        employment = leave.employee.employments.filter(is_current=True).first()
        position_name = employment.position.name if employment else '-'
        
        data = [
            leave.employee.ministry_number,
            leave.employee.full_name,
            leave.employee.specialization,
            leave.employee.school,
            leave.start_date.strftime('%Y-%m-%d'),
            leave.end_date.strftime('%Y-%m-%d'),
            'نشطة' if leave.is_active else 'منتهية',
            leave.notes or '-'
        ]
        
        for col, value in enumerate(data, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.border = border
    
    # Auto-adjust column widths
    for col in range(1, len(headers) + 1):
        column_letter = get_column_letter(col)
        max_length = 0
        for row in ws[column_letter]:
            try:
                if len(str(row.value)) > max_length:
                    max_length = len(str(row.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="maternity_leaves_active_{date.today().strftime("%Y%m%d")}.xlsx"'
    
    wb.save(response)
    return response


@login_required
def maternity_leave_detail(request, pk):
    """View for displaying maternity leave details"""
    maternity_leave = get_object_or_404(MaternityLeave, pk=pk)
    
    # Get employment information
    employment = maternity_leave.employee.employments.filter(is_current=True).first()
    if employment:
        maternity_leave.employee.position_name = employment.position.name
    else:
        maternity_leave.employee.position_name = '-'
    
    return render(request, 'employees/maternity_leave_detail.html', {
        'maternity_leave': maternity_leave
    })


@login_required
def maternity_leave_update(request, pk):
    """View for updating maternity leave"""
    maternity_leave = get_object_or_404(MaternityLeave, pk=pk)
    
    if request.method == 'POST':
        form = MaternityLeaveForm(request.POST, instance=maternity_leave)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث إجازة الأمومة بنجاح')
            return redirect('employees:maternity_leave_detail', pk=maternity_leave.pk)
    else:
        form = MaternityLeaveForm(instance=maternity_leave)
    
    return render(request, 'employees/maternity_leave_update.html', {
        'form': form,
        'maternity_leave': maternity_leave
    })


@login_required
def maternity_leave_delete(request, pk):
    """View for deleting maternity leave"""
    maternity_leave = get_object_or_404(MaternityLeave, pk=pk)
    
    if request.method == 'POST':
        employee_name = maternity_leave.employee.full_name
        maternity_leave.delete()
        messages.success(request, f'تم حذف إجازة الأمومة للموظفة {employee_name} بنجاح')
        return redirect('employees:maternity_leaves_list')
    
    return render(request, 'employees/maternity_leave_delete.html', {
        'maternity_leave': maternity_leave
    })