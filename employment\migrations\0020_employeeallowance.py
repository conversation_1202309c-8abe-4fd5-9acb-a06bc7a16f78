# Generated by Django 5.2 on 2025-05-28 05:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0019_btecfield_btecteacher'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeAllowance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('education_allowance', models.CharField(choices=[('yes', 'نعم'), ('no', 'لا')], default='no', max_length=3, verbose_name='علاوة التعليم')),
                ('adjustment_allowance', models.CharField(choices=[('yes', 'نعم'), ('no', 'لا')], default='no', max_length=3, verbose_name='التجيير')),
                ('transportation_allowance', models.Char<PERSON>ield(choices=[('yes', 'نعم'), ('no', 'لا')], default='no', max_length=3, verbose_name='التنقلات')),
                ('supervisory_allowance', models.CharField(choices=[('yes', 'نعم'), ('no', 'لا')], default='no', max_length=3, verbose_name='العلاوة الإشرافية')),
                ('technical_allowance', models.CharField(choices=[('yes', 'نعم'), ('no', 'لا')], default='no', max_length=3, verbose_name='علاوة فنية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='allowances', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'علاوات الموظف',
                'verbose_name_plural': 'علاوات الموظفين',
                'ordering': ['employee__full_name'],
            },
        ),
    ]
