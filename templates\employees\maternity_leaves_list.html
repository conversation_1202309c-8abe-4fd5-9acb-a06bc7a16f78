{% extends 'base.html' %}

{% block title %}إجازات الأمومة - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">إجازات الأمومة</h6>
            <div class="d-flex">
                <form method="get" class="me-2">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث..." value="{{ search_query }}">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{% url 'employees:maternity_leaves_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
                <a href="{% url 'employees:export_maternity_leaves_excel' %}" class="btn btn-success me-2">
                    <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
                </a>
                <a href="{% url 'employees:add_maternity_leave' %}" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-1"></i> إضافة إجازة أمومة
                </a>
                <a href="{% url 'employees:employee_list' %}" class="btn btn-dark text-white">
                    <i class="fas fa-arrow-right me-1"></i> العودة لقائمة الموظفين
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>معلومات إجازات الأمومة</strong>
                </div>
                <p>مدة إجازة الأمومة 90 يوماً تبدأ من اليوم التالي لتاريخ بداية الإجازة.</p>
                <p>تاريخ اليوم: <strong>{{ today|date:"Y-m-d" }}</strong></p>
                <p>عدد الإجازات النشطة: <strong>{{ maternity_leaves|length }}</strong></p>
            </div>

            {% if search_query %}
            <div class="alert alert-success mb-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-search me-2"></i>
                    <span>نتائج البحث عن: <strong>{{ search_query }}</strong> - تم العثور على <strong>{{ maternity_leaves|length }}</strong> إجازة</span>
                </div>
            </div>
            {% endif %}

            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr class="text-center">
                            <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                            <th><i class="fas fa-user me-1"></i> الاسم الكامل</th>
                            <th><i class="fas fa-graduation-cap me-1"></i> التخصص</th>
                            <th><i class="fas fa-building me-1"></i> القسم</th>
                            <th><i class="fas fa-calendar-alt me-1"></i> بداية الإجازة</th>
                            <th><i class="fas fa-calendar-check me-1"></i> تاريخ الانتهاء</th>
                            <th><i class="fas fa-info-circle me-1"></i> الحالة</th>
                            <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in maternity_leaves %}
                        <tr class="text-center">
                            <td>{{ leave.employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' leave.employee.id %}">
                                    {{ leave.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ leave.employee.specialization }}</td>
                            <td>{{ leave.employee.school }}</td>
                            <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if leave.is_active %}
                                    <span class="badge bg-success">نشطة</span>
                                {% else %}
                                    <span class="badge bg-secondary">منتهية</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'employees:maternity_leave_detail' leave.pk %}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'employees:maternity_leave_update' leave.pk %}" class="btn btn-sm btn-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employees:maternity_leave_delete' leave.pk %}" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد إجازات أمومة مسجلة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[4, "desc"]], // Sort by start date column (descending)
            "columnDefs": [
                { "width": "12%", "targets": [4, 5] }, // Make date columns wider
                { "width": "10%", "targets": 6 }, // Status column
                { "width": "15%", "targets": 7 }  // Actions column
            ],
            "pageLength": 25
        });
    });
</script>
{% endblock %}