# Generated by Django 5.2 on 2025-06-24 07:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0005_alter_employee_options_employee_masters_degree_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MaternityLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(help_text='Start date of maternity leave', verbose_name='Start Date')),
                ('end_date', models.DateField(help_text='End date of maternity leave (automatically calculated)', verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the maternity leave is currently active', verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the maternity leave', null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maternity_leaves', to='employees.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Maternity Leave',
                'verbose_name_plural': 'Maternity Leaves',
                'ordering': ['-start_date'],
            },
        ),
    ]
