# دليل ميزة المنقولين خارجي 🚀

## نظرة عامة
تم إضافة ميزة جديدة لإدارة **المنقولين خارجي** في نظام شؤون الموظفين. هذه الميزة تعمل بنفس طريقة نظام المتقاعدين وتسمح بـ:

- ✅ نقل الموظفين خارجياً وإزالتهم من قائمة الموظفين النشطين
- ✅ عرض قائمة المنقولين خارجي منفصلة
- ✅ البحث في المنقولين خارجي
- ✅ إدارة بيانات النقل الخارجي
- ✅ إمكانية إلغاء النقل (إعادة الموظف للخدمة)

## كيفية الوصول إلى الميزة

### 1. من صفحة بيانات الموظفين:
```
بيانات الموظفين → زر "المنقولين خارجي" (باللون الأصفر)
```

### 2. الرابط المباشر:
```
http://127.0.0.1:8000/employees/external-transfers/
```

## إضافة موظف منقول خارجي

### الخطوات:
1. **اذهب إلى صفحة المنقولين خارجي**
2. **انقر على "نقل موظف خارجي"**
3. **ابحث عن الموظف**:
   - 🔍 اكتب الرقم الوزاري (مثل: 12345)
   - 🔍 أو اكتب اسم الموظف (مثل: أحمد محمد)
   - ⚡ النتائج تظهر فوراً أثناء الكتابة
4. **انقر على الموظف من نتائج البحث**
5. **أدخل بيانات النقل**:
   - 📅 تاريخ النقل (افتراضي: اليوم)
   - 🏢 المديرية المنقول إليها (مطلوب)
   - 📝 سبب النقل (افتراضي: "نقل خارجي")
   - 📄 ملاحظات (اختياري)
6. **انقر على "نقل الموظف"**

### مميزات البحث المتقدم:
- ⚡ **بحث فوري**: النتائج تظهر أثناء الكتابة
- 🎯 **بحث ذكي**: يبحث في الرقم الوزاري والاسم معاً
- 📊 **معلومات شاملة**: يعرض التخصص والقسم مع كل نتيجة
- 🛡️ **حماية من الأخطاء**: يمنع نقل الموظفين المتقاعدين أو المنقولين بالفعل

## إدارة المنقولين خارجي

### 📋 قائمة المنقولين خارجي:
- عرض جميع الموظفين المنقولين خارجياً
- جدول يحتوي على:
  - الرقم الوزاري
  - الاسم الكامل  
  - التخصص
  - المديرية المنقول إليها
  - تاريخ النقل
  - الإجراءات (عرض/تعديل/إلغاء)

### 🔍 البحث في القائمة:
يمكن البحث بـ:
- الرقم الوزاري
- اسم الموظف
- المديرية المنقول إليها
- سبب النقل

### 👀 عرض تفاصيل منقول:
- انقر على أيقونة العين (👁️)
- يعرض:
  - معلومات الموظف الكاملة
  - تفاصيل النقل الخارجي
  - معلومات الاتصال
  - تواريخ النظام

### ✏️ تعديل بيانات النقل:
- انقر على أيقونة التعديل (✏️)
- يمكن تعديل:
  - تاريخ النقل
  - المديرية المنقول إليها
  - سبب النقل
  - الملاحظات

### 🔄 إلغاء النقل (إعادة للخدمة):
- انقر على أيقونة الإلغاء (↶)
- سيظهر تحذير مفصل
- عند التأكيد:
  - ✅ يحذف سجل النقل الخارجي
  - ✅ يعود الموظف لقائمة الموظفين النشطين
  - ⚠️ **تحذير**: لا يمكن التراجع عن هذا الإجراء

## التأثير على النظام

### 📊 قائمة الموظفين:
- **قبل**: تعرض جميع الموظفين
- **بعد**: تعرض الموظفين النشطين فقط (تستثني المتقاعدين والمنقولين خارجي)

### 📈 التقارير:
- تقارير الموظفين تشمل النشطين فقط
- يمكن إنشاء تقارير منفصلة للمنقولين خارجي

### 🔍 البحث:
- البحث في الموظفين يشمل النشطين فقط
- البحث في المنقولين خارجي منفصل

## الحماية والأمان

### ✅ التحقق من الصحة:
- لا يمكن نقل موظف منقول بالفعل
- لا يمكن نقل موظف متقاعد
- يجب إدخال تاريخ النقل
- يجب إدخال المديرية المنقول إليها

### 📝 السجلات (System Logs):
- جميع عمليات النقل الخارجي تُسجل
- جميع عمليات التعديل تُسجل
- جميع عمليات الإلغاء تُسجل
- تتضمن: IP address، المستخدم، التوقيت، الوصف

### 🔐 الصلاحيات:
- تتطلب تسجيل الدخول
- تحتاج صلاحيات الموظفين

## الإحصائيات الحالية

### 📊 النتائج من الاختبار:
- **إجمالي الموظفين**: 15
- **الموظفين النشطين**: 13  
- **المنقولين خارجي**: 1
- **المتقاعدين**: 1

### 📈 النسب المئوية:
- **نسبة النشطين**: 86.7%
- **نسبة المنقولين**: 6.7%
- **نسبة المتقاعدين**: 6.7%

## الملفات المضافة/المحدثة

### 🗃️ نماذج البيانات:
- `employees/models.py`: إضافة نموذج `ExternalTransfer`
- `employees/forms.py`: إضافة نماذج النقل الخارجي

### 🎯 العروض (Views):
- `employees/external_transfer_views.py`: عروض المنقولين خارجي (جديد)
- `employees/views.py`: تحديث لاستثناء المنقولين خارجي

### 🎨 القوالب:
- `templates/employees/external_transfers_list.html`: صفحة المنقولين خارجي
- `templates/employees/external_transfer_detail.html`: تفاصيل المنقول
- `templates/employees/external_transfer_form.html`: نموذج التعديل
- `templates/employees/external_transfer_confirm_delete.html`: تأكيد الإلغاء

### 🔗 الروابط:
- `employees/urls.py`: إضافة روابط المنقولين خارجي

### ⚙️ الإدارة:
- `employees/admin.py`: إضافة إدارة المنقولين خارجي

### 🗄️ قاعدة البيانات:
- `employees/migrations/0004_externaltransfer.py`: هجرة إضافة الجدول

## روابط النظام

### 🌐 الروابط الرئيسية:
```
/employees/external-transfers/                    # قائمة المنقولين خارجي
/employees/external-transfers/<id>/               # تفاصيل منقول  
/employees/external-transfers/<id>/edit/          # تعديل بيانات النقل
/employees/external-transfers/<id>/delete/        # إلغاء النقل
/employees/search-employees-for-transfer/         # بحث AJAX للموظفين
```

## استكشاف الأخطاء

### ❌ مشكلة: لا تظهر نتائج البحث
**الحل**:
- تأكد من أن JavaScript مفعل
- تأكد من اتصال الإنترنت
- تحقق من وحدة تحكم المتصفح للأخطاء

### ❌ مشكلة: لا يمكن اختيار الموظف
**الحل**:
- تأكد من النقر على النتيجة المطلوبة
- تأكد من ظهور اسم الموظف في حقل "الموظف المحدد"

### ❌ مشكلة: رسالة خطأ عند النقل
**الحل**:
- تأكد من إدخال جميع البيانات المطلوبة
- تأكد من أن الموظف غير منقول أو متقاعد بالفعل
- تحقق من صحة التاريخ

### ❌ مشكلة: الموظف لا يظهر في البحث
**الحل**:
- تأكد من أن الموظف ليس متقاعداً
- تأكد من أن الموظف ليس منقولاً بالفعل
- جرب البحث بالرقم الوزاري بدلاً من الاسم

## مقارنة مع نظام المتقاعدين

| الميزة | المتقاعدين | المنقولين خارجي |
|--------|------------|-----------------|
| اللون | 🔵 أزرق | 🟡 أصفر |
| السبب | تقاعد | نقل خارجي |
| الوجهة | لا يوجد | مديرية أخرى |
| الإلغاء | إلغاء التقاعد | إلغاء النقل |
| الأيقونة | ⏰ user-clock | 🔄 exchange-alt |

## دعم إضافي

للمساعدة الإضافية أو الإبلاغ عن مشاكل:
- 📧 تواصل مع مطور النظام
- 📋 استخدم نظام السجلات لتتبع العمليات
- 🔍 راجع وحدة تحكم المتصفح للأخطاء التقنية

---

## ✨ الخلاصة

تم إضافة نظام **المنقولين خارجي** بنجاح مع جميع المميزات المطلوبة:

✅ **زر جديد** في صفحة بيانات الموظفين  
✅ **صفحة منفصلة** للمنقولين خارجي  
✅ **جدول شامل** بجميع المعلومات المطلوبة  
✅ **بحث متقدم** بالرقم الوزاري والاسم  
✅ **إضافة وإزالة** الموظفين من النظام  
✅ **إدارة كاملة** للبيانات مع الحماية والأمان  

النظام جاهز للاستخدام! 🎉