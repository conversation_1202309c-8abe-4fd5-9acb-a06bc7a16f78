{% extends 'base.html' %}
{% load static %}

{% block title %}رفع نسخة احتياطية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>رفع نسخة احتياطية</h2>
        <a href="{% url 'backup:backup_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">رفع ملف نسخة احتياطية</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">الاسم</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label for="{{ form.file.id_for_label }}" class="form-label">ملف النسخة الاحتياطية</label>
                    {{ form.file }}
                    {% if form.file.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.file.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">يجب أن يكون الملف بتنسيق SQL.</div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> يمكنك رفع ملف نسخة احتياطية تم إنشاؤه مسبقًا.
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> رفع النسخة الاحتياطية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
