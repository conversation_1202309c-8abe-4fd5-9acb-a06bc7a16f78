@echo off
echo Starting Django server with virtual environment...
echo.

REM Check if venv exists
if exist "venv\Scripts\python.exe" (
    echo Using Python from venv\Scripts\python.exe
    venv\Scripts\python.exe manage.py runserver
    goto :end
)

REM Check if env exists
if exist "env\Scripts\python.exe" (
    echo Using Python from env\Scripts\python.exe
    env\Scripts\python.exe manage.py runserver
    goto :end
)

REM Check common Python installation paths
if exist "C:\Python311\python.exe" (
    echo Using Python from C:\Python311\python.exe
    C:\Python311\python.exe manage.py runserver
    goto :end
)

if exist "C:\Python310\python.exe" (
    echo Using Python from C:\Python310\python.exe
    C:\Python310\python.exe manage.py runserver
    goto :end
)

if exist "C:\Python39\python.exe" (
    echo Using Python from C:\Python39\python.exe
    C:\Python39\python.exe manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python311\python.exe" (
    echo Using Python from C:\Program Files\Python311\python.exe
    "C:\Program Files\Python311\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python310\python.exe" (
    echo Using Python from C:\Program Files\Python310\python.exe
    "C:\Program Files\Python310\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Program Files\Python39\python.exe" (
    echo Using Python from C:\Program Files\Python39\python.exe
    "C:\Program Files\Python39\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" manage.py runserver
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" manage.py runserver
    goto :end
)

echo Python not found in common locations.
echo Please install Python or specify the correct path to python.exe.
echo.
echo You can install Python from the Microsoft Store or from python.org.
echo.
echo Press any key to exit...
pause > nul

:end
