#!/bin/bash

# HR Management System - Install Requirements
# نظام شؤون الموظفين - تثبيت المتطلبات

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "نظام شؤون الموظفين - تثبيت المتطلبات"
echo "HR Management System - Install Requirements"
echo "========================================"
echo

print_info "بدء عملية تثبيت المكتبات المطلوبة..."
print_info "Starting installation of required packages..."
echo

# Check if Python is installed
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    print_success "تم العثور على Python3"
    print_success "Python3 found"
    python3 --version
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    print_success "تم العثور على Python"
    print_success "Python found"
    python --version
else
    print_error "Python غير مثبت على النظام!"
    print_error "Python is not installed on the system!"
    print_info "يرجى تثبيت Python من: https://www.python.org/downloads/"
    print_info "Please install Python from: https://www.python.org/downloads/"
    echo
    print_info "على Ubuntu/Debian: sudo apt-get install python3 python3-pip"
    print_info "على CentOS/RHEL: sudo yum install python3 python3-pip"
    print_info "على macOS: brew install python3"
    exit 1
fi

echo

# Check if pip is available
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
    print_success "تم العثور على pip3"
    print_success "pip3 found"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
    print_success "تم العثور على pip"
    print_success "pip found"
else
    print_warning "pip غير متوفر، جاري تثبيته..."
    print_warning "pip not available, installing..."
    $PYTHON_CMD -m ensurepip --upgrade
    if [ $? -eq 0 ]; then
        PIP_CMD="$PYTHON_CMD -m pip"
        print_success "تم تثبيت pip بنجاح"
        print_success "pip installed successfully"
    else
        print_error "فشل في تثبيت pip!"
        print_error "Failed to install pip!"
        exit 1
    fi
fi

$PIP_CMD --version
echo

# Ask about virtual environment
print_info "هل تريد إنشاء بيئة افتراضية؟ (y/n)"
print_info "Do you want to create a virtual environment? (y/n)"
read -p "Enter choice: " create_venv

if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    print_info "جاري إنشاء البيئة الافتراضية..."
    print_info "Creating virtual environment..."
    
    $PYTHON_CMD -m venv hr_system_env
    
    if [ $? -eq 0 ]; then
        print_success "تم إنشاء البيئة الافتراضية"
        print_success "Virtual environment created"
        
        print_info "جاري تفعيل البيئة الافتراضية..."
        print_info "Activating virtual environment..."
        source hr_system_env/bin/activate
        
        PYTHON_CMD="hr_system_env/bin/python"
        PIP_CMD="hr_system_env/bin/pip"
        
        print_success "تم تفعيل البيئة الافتراضية"
        print_success "Virtual environment activated"
    else
        print_error "فشل في إنشاء البيئة الافتراضية"
        print_error "Failed to create virtual environment"
        exit 1
    fi
    echo
fi

# Upgrade pip
print_info "جاري تحديث pip إلى أحدث إصدار..."
print_info "Upgrading pip to latest version..."
$PIP_CMD install --upgrade pip
echo

# Install basic tools
print_info "جاري تثبيت الأدوات الأساسية..."
print_info "Installing basic tools..."
$PIP_CMD install wheel setuptools
echo

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    print_error "ملف requirements.txt غير موجود!"
    print_error "requirements.txt file not found!"
    print_info "يرجى التأكد من وجود الملف في نفس مجلد هذا الملف"
    print_info "Please make sure the file exists in the same folder as this file"
    exit 1
fi

print_success "تم العثور على ملف requirements.txt"
print_success "requirements.txt file found"
echo

# Install requirements
echo "========================================"
print_info "جاري تثبيت المكتبات من requirements.txt..."
print_info "Installing packages from requirements.txt..."
print_info "هذا قد يستغرق عدة دقائق..."
print_info "This may take several minutes..."
echo "========================================"
echo

$PIP_CMD install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    print_warning "حدث خطأ أثناء تثبيت بعض المكتبات!"
    print_warning "Error occurred while installing some packages!"
    print_info "جاري المحاولة مرة أخرى مع تجاهل الأخطاء..."
    print_info "Trying again with error tolerance..."
    $PIP_CMD install -r requirements.txt --ignore-installed --force-reinstall
    
    if [ $? -ne 0 ]; then
        print_warning "بعض المكتبات قد لا تكون مثبتة بشكل صحيح"
        print_warning "Some packages may not be installed correctly"
        print_info "يمكنك تثبيتها يدوياً لاحقاً"
        print_info "You can install them manually later"
    fi
fi

echo
echo "========================================"
print_success "تم الانتهاء من تثبيت المكتبات!"
print_success "Package installation completed!"
echo "========================================"
echo

# Show installed packages
print_info "عرض المكتبات المثبتة..."
print_info "Showing installed packages..."
$PIP_CMD list
echo

# Check Django installation
print_info "التحقق من تثبيت Django..."
print_info "Checking Django installation..."
$PYTHON_CMD -c "import django; print('Django version:', django.get_version())" 2>/dev/null
if [ $? -eq 0 ]; then
    print_success "تم تثبيت Django بنجاح!"
    print_success "Django installed successfully!"
else
    print_error "Django غير مثبت بشكل صحيح!"
    print_error "Django not installed correctly!"
fi
echo

# Additional setup instructions
echo "========================================"
echo "تعليمات الإعداد التالية - Next Setup Instructions"
echo "========================================"
echo
print_info "الخطوات التالية لتشغيل النظام:"
print_info "Next steps to run the system:"
echo
echo "1. إنشاء قاعدة البيانات:"
echo "   Create database:"
echo "   $PYTHON_CMD manage.py migrate"
echo
echo "2. إنشاء مستخدم مدير:"
echo "   Create admin user:"
echo "   $PYTHON_CMD manage.py createsuperuser"
echo
echo "3. جمع الملفات الثابتة:"
echo "   Collect static files:"
echo "   $PYTHON_CMD manage.py collectstatic"
echo
echo "4. تشغيل الخادم:"
echo "   Run server:"
echo "   $PYTHON_CMD manage.py runserver"
echo
echo "5. فتح المتصفح والذهاب إلى:"
echo "   Open browser and go to:"
echo "   http://127.0.0.1:8000"
echo

# Create a quick start script
print_info "إنشاء ملف تشغيل سريع..."
print_info "Creating quick start script..."
cat > start_server.sh << EOF
#!/bin/bash
echo "جاري تشغيل خادم التطوير..."
echo "Starting development server..."
$PYTHON_CMD manage.py runserver
EOF

chmod +x start_server.sh

print_success "تم إنشاء ملف start_server.sh لتشغيل الخادم"
print_success "Created start_server.sh to run the server"
echo

if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    print_info "لتفعيل البيئة الافتراضية في المرات القادمة:"
    print_info "To activate virtual environment in the future:"
    echo "source hr_system_env/bin/activate"
    echo
fi

echo "========================================"
print_success "تم الانتهاء بنجاح!"
print_success "Installation completed successfully!"
echo "========================================"
echo
print_info "يمكنك الآن تشغيل النظام باستخدام ./start_server.sh"
print_info "You can now run the system using ./start_server.sh"
echo
