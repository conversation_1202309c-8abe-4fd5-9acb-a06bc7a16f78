# 🚀 كيفية تجربة ميزة حركات النقل الداخلي

## خطوات التجربة السريعة

### 1. تشغيل الخادم
```bash
python manage.py runserver
```

### 2. الوصول للميزة
**الطريقة الأولى:**
- انتقل إلى: `http://127.0.0.1:8000/employees/`
- اضغط على زر "تفاصيل حركات النقل" (أزرق)

**الطريقة الثانية:**
- انتقل مباشرة إلى: `http://127.0.0.1:8000/employees/internal-transfers/`

### 3. إنشاء حركة نقل للتجربة
**الطريقة الأولى (التلقائية - مُستحسنة):**
1. انتقل لإدارة الكادر
2. اختر أي موظف واضغط "تعديل"
3. غيّر "القسم/المدرسة" لقسم آخر
4. احفظ التغييرات
5. ✅ سيتم تسجيل حركة النقل تلقائياً!

**الطريقة الثانية (يدوية):**
1. انتقل لـ Admin Panel: `http://127.0.0.1:8000/admin/`
2. ادخل بحساب المدير
3. اختر "Internal transfers"
4. اضغط "Add Internal transfer"
5. املأ البيانات واحفظ

### 4. استكشاف المميزات

#### صفحة قائمة حركات النقل:
- جدول شامل بجميع الحركات
- بحث متقدم
- تصدير Excel
- أزرار الإجراءات

#### صفحة الإحصائيات:
- انتقل إلى: `http://127.0.0.1:8000/employees/internal-transfers/statistics/`
- أو اضغط زر "الإحصائيات" من صفحة القائمة
- مشاهدة الإحصائيات والرسوم البيانية

#### صفحة تفاصيل الموظف:
- اختر أي موظف له حركات نقل
- ستجد قسم "حركات النقل الداخلي" في الأسفل

## المميزات المتاحة للتجربة

### ✅ التسجيل التلقائي
- جرّب تحديث قسم موظف وراقب التسجيل التلقائي

### ✅ البحث والتصفية
- ابحث بالرقم الوزاري أو اسم الموظف
- ابحث بأسماء الأقسام

### ✅ تصدير البيانات
- جرّب تصدير Excel ومراجعة النتيجة

### ✅ الإدارة الكاملة
- عرض تفاصيل حركة النقل
- تعديل البيانات
- حذف السجلات (مع تأكيدات أمنية)

### ✅ الإحصائيات
- إحصائيات شاملة
- رسوم بيانية تفاعلية
- بيانات الأقسام الأكثر نشاطاً

## نصائح للتجربة

1. **ابدأ بإنشاء حركة نقل واحدة** باستخدام التسجيل التلقائي
2. **اطلع على جميع الصفحات** لرؤية التكامل
3. **جرّب البحث والتصفية** لفهم الوظائف
4. **تحقق من الإحصائيات** لرؤية البيانات التحليلية
5. **جرّب تصدير Excel** للتأكد من جودة التصدير

## حل المشاكل

### إذا لم تظهر حركات النقل:
- تأكد من وجود موظفين في النظام
- جرّب إنشاء حركة نقل يدوياً من Admin
- تحقق من تحديث قسم الموظف فعلياً

### إذا لم تعمل الإحصائيات:
- ابدأ بإنشاء بعض حركات النقل أولاً
- الإحصائيات تظهر بوضوح عندما تكون هناك بيانات

---
**🎉 استمتع بتجربة الميزة الجديدة!**