{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .form-section h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }

    .required-field label:after {
        content: " *";
        color: red;
    }

    .employee-info {
        background-color: #e8f4ff;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .employee-info h5 {
        color: #4e73df;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{{ title }}</h2>
    <div>
        <a href="{% url 'employment:medical_condition_name_list' %}" class="btn btn-info">
            <i class="fas fa-list-alt"></i> أسماء الحالات المرضية
        </a>
        <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى القائمة
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form method="post" id="medicalConditionForm">
            {% csrf_token %}

            <!-- Employee Information Section -->
            <div class="form-section">
                <h4>معلومات الموظف</h4>
                <div class="row">
                    <div class="col-md-4 required-field">
                        <div class="mb-3">
                            <label for="id_ministry_number">{{ form.ministry_number.label }}</label>
                            {{ form.ministry_number }}
                            {% if form.ministry_number.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.ministry_number.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="id_employee_name">{{ form.employee_name.label }}</label>
                            {{ form.employee_name }}
                            {% if form.employee_name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.employee_name.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="id_department">{{ form.department.label }}</label>
                            {{ form.department }}
                            {% if form.department.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.department.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {{ form.employee }}
            </div>

            <!-- Medical Condition Information Section -->
            <div class="form-section">
                <h4>معلومات الحالة المرضية</h4>
                <div class="row">
                    <div class="col-md-6 required-field">
                        <div class="mb-3">
                            <label for="id_condition">{{ form.condition.label }}</label>
                            {{ form.condition }}
                            {% if form.condition.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.condition.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 required-field">
                        <div class="mb-3">
                            <label for="id_medical_report_date">{{ form.medical_report_date.label }}</label>
                            {{ form.medical_report_date }}
                            {% if form.medical_report_date.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.medical_report_date.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 required-field">
                        <div class="mb-3">
                            <label for="id_description">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.description.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="id_notes">{{ form.notes.label }}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <!-- Submit Button -->
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize select2 for dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Handle ministry number input to fetch employee data
        $('#ministry_number_input').on('blur', function() {
            var ministryNumber = $(this).val();
            if (ministryNumber) {
                $.ajax({
                    url: "{% url 'employment:get_employee_by_ministry_number_json' %}",
                    data: {
                        'ministry_number': ministryNumber
                    },
                    dataType: 'json',
                    success: function(data) {
                        if (data.success) {
                            $('#employee_name_display').val(data.employee.full_name);
                            $('#department_display').val(data.employee.display_department || 'غير محدد');
                            $('input[name="employee"]').val(data.employee.id);
                            console.log("تم استرجاع بيانات الموظف بنجاح:", data.employee);
                        } else {
                            $('#employee_name_display').val('');
                            $('#department_display').val('');
                            $('input[name="employee"]').val('');
                            alert('لم يتم العثور على موظف بهذا الرقم الوزاري');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("حدث خطأ أثناء استرجاع بيانات الموظف:", error);
                        alert('حدث خطأ أثناء استرجاع بيانات الموظف');
                    }
                });
            } else {
                $('#employee_name_display').val('');
                $('#department_display').val('');
                $('input[name="employee"]').val('');
            }
        });

        // Trigger search on page load if ministry number is already filled
        if ($('#ministry_number_input').val()) {
            $('#ministry_number_input').trigger('blur');
        }
    });
</script>
{% endblock %}
