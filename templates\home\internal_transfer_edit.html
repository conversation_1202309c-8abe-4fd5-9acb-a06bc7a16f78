{% extends 'home/base.html' %}
{% load static %}

{% block title %}تعديل طلب النقل الداخلي{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the modal element
        var idHelpModalElement = document.getElementById('idHelpModal');

        // Get the help button
        var editHelpBtn = document.getElementById('editHelpBtn');

        // Function to show the modal
        function showHelpModal() {
            // Try different methods to show the modal
            try {
                // Method 1: Using Bootstrap 5
                if (typeof bootstrap !== 'undefined') {
                    var modal = new bootstrap.Modal(idHelpModalElement);
                    modal.show();
                    return;
                }

                // Method 2: Using jQuery (Bootstrap 4)
                if (typeof $ !== 'undefined') {
                    $(idHelpModalElement).modal('show');
                    return;
                }

                // Method 3: Manual display
                idHelpModalElement.style.display = 'block';
                idHelpModalElement.classList.add('show');
                document.body.classList.add('modal-open');

                // Add backdrop
                var backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            } catch (e) {
                console.error('Error showing modal:', e);
            }
        }

        // Function to close the help modal - defined globally
        window.closeHelpModal = function() {
            try {
                // Method 1: Using Bootstrap 5
                if (typeof bootstrap !== 'undefined') {
                    var modal = bootstrap.Modal.getInstance(idHelpModalElement);
                    if (modal) {
                        modal.hide();
                        return;
                    }
                }

                // Method 2: Using jQuery (Bootstrap 4)
                if (typeof $ !== 'undefined') {
                    $(idHelpModalElement).modal('hide');
                    return;
                }

                // Method 3: Manual hide
                idHelpModalElement.style.display = 'none';
                idHelpModalElement.classList.remove('show');
                document.body.classList.remove('modal-open');

                // Remove backdrop
                var backdrops = document.getElementsByClassName('modal-backdrop');
                if (backdrops.length > 0) {
                    for (var i = 0; i < backdrops.length; i++) {
                        backdrops[i].parentNode.removeChild(backdrops[i]);
                    }
                }
            } catch (e) {
                console.error('Error closing modal:', e);
            }
        }

        // Add click event listener to help button
        if (editHelpBtn) {
            editHelpBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showHelpModal();
            });
        }
    });
</script>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-edit me-2"></i> تعديل طلب النقل الداخلي</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك تعديل طلب النقل الداخلي الخاص بك من خلال هذه الصفحة. يرجى التأكد من صحة البيانات قبل حفظ التعديلات.
                    </div>

                    {% if verification_required %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i> التحقق من الهوية</h5>
                        </div>
                        <div class="card-body">
                            <p>يرجى إدخال الرقم الوطني ورقم الهوية للتحقق من هويتك قبل تعديل الطلب.</p>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="national_id" class="form-label">الرقم الوطني <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="national_id" name="national_id" placeholder="أدخل الرقم الوطني" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="id_number" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="id_number" name="id_number" placeholder="أدخل رقم الهوية" required>
                                        <button class="btn btn-outline-secondary" type="button" id="editHelpBtn">
                                            <i class="fas fa-question-circle"></i> مساعدة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="border-bottom pb-2 mb-3">معلومات الموظف</h5>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.ministry_number.id_for_label }}" class="form-label">{{ form.ministry_number.label }}</label>
                                {{ form.ministry_number }}
                                {% if form.ministry_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.ministry_number.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.employee_name.id_for_label }}" class="form-label">{{ form.employee_name.label }}</label>
                                {{ form.employee_name }}
                                {% if form.employee_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.employee_name.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.employee_id.id_for_label }}" class="form-label">{{ form.employee_id.label }}</label>
                                {{ form.employee_id }}
                                {% if form.employee_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.employee_id.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="border-bottom pb-2 mb-3">معلومات الوظيفة الحالية</h5>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.current_department.id_for_label }}" class="form-label">{{ form.current_department.label }}</label>
                                {{ form.current_department }}
                                {% if form.current_department.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.current_department.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.hire_date.id_for_label }}" class="form-label">{{ form.hire_date.label }}</label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.hire_date.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.actual_service.id_for_label }}" class="form-label">{{ form.actual_service.label }}</label>
                                {{ form.actual_service }}
                                {% if form.actual_service.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.actual_service.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.last_position.id_for_label }}" class="form-label">{{ form.last_position.label }}</label>
                                {{ form.last_position }}
                                {% if form.last_position.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.last_position.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.qualification.id_for_label }}" class="form-label">{{ form.qualification.label }}</label>
                                {{ form.qualification }}
                                {% if form.qualification.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.qualification.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.specialization.id_for_label }}" class="form-label">{{ form.specialization.label }}</label>
                                {{ form.specialization }}
                                {% if form.specialization.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.specialization.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.last_rank.id_for_label }}" class="form-label">{{ form.last_rank.label }}</label>
                                {{ form.last_rank }}
                                {% if form.last_rank.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.last_rank.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.address.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                                {{ form.gender }}
                                {% if form.gender.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.gender.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="border-bottom pb-2 mb-3">خيارات النقل</h5>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.first_choice.id_for_label }}" class="form-label">{{ form.first_choice.label }} <span class="text-danger">*</span></label>
                                {{ form.first_choice }}
                                {% if form.first_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.first_choice.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.second_choice.id_for_label }}" class="form-label">{{ form.second_choice.label }}</label>
                                {{ form.second_choice }}
                                {% if form.second_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.second_choice.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="{{ form.third_choice.id_for_label }}" class="form-label">{{ form.third_choice.label }}</label>
                                {{ form.third_choice }}
                                {% if form.third_choice.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.third_choice.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-12 mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }} <span class="text-danger">*</span></label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reason.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="border-bottom pb-2 mb-3">معلومات الاتصال</h5>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }} <span class="text-danger">*</span></label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone_number.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="alert alert-warning mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ملاحظة: سيتم تخزين طلبك وخيارات النقل وترتيبها حسب الخدمة الفعلية ولحين إجراء النقل الداخلي.
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'home:home' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Modal for ID Help -->
<div class="modal fade" id="idHelpModal" tabindex="-1" aria-labelledby="idHelpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="idHelpModalLabel"><i class="fas fa-info-circle me-2"></i> مساعدة في إدخال رقم الهوية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 mb-3">كيفية العثور على رقم الهوية</h5>
                        <p>رقم الهوية هو الرقم المكون من عدة أرقام الموجود على الجانب الخلفي من بطاقة الهوية الأردنية. يرجى اتباع الخطوات التالية:</p>
                        <ol>
                            <li>قم بقلب بطاقة الهوية الأردنية إلى الجانب الخلفي.</li>
                            <li>ابحث عن الرقم الموجود في المنطقة المحددة باللون الأحمر في الصورة أدناه.</li>
                            <li>أدخل هذا الرقم بالكامل في حقل "رقم الهوية" دون ترك مسافات.</li>
                        </ol>
                    </div>
                    <div class="col-md-12 text-center">
                        <img src="{% static 'img/jordan_id_back.jpg' %}?v={% now 'U' %}" alt="صورة توضيحية للهوية الأردنية من الخلف" class="img-fluid border rounded shadow-sm" style="max-width: 400px;" onerror="this.onerror=null; this.src='{% static 'img/jordan_id_card_with_arrow.svg' %}?v={% now 'U' %}';">
                        <p class="mt-3 text-muted">صورة توضيحية للهوية الأردنية - المنطقة المحددة باللون الأحمر تشير إلى مكان رقم الهوية</p>
                    </div>
                    <div class="col-md-12 mt-4">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>ملاحظة هامة:</strong> يجب إدخال رقم الهوية بدقة كما هو مكتوب على البطاقة. أي خطأ في إدخال الرقم سيؤدي إلى فشل عملية التحقق.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeHelpModal()">إغلاق</button>
            </div>
        </div>
    </div>
</div>
