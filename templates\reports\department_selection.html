{% extends 'base.html' %}
{% load static %}

{% block title %}اختيار قسم للتقرير - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>اختيار قسم للتقرير</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">اختر قسماً لإنشاء تقرير عنه</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم القسم</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in departments %}
                    <tr>
                        <td>{{ department.name }}</td>
                        <td>{{ department.description|default:"-" }}</td>
                        <td>
                            <a href="{% url 'reports:department_report' department.pk %}" class="btn btn-dark btn-sm text-white">
                                <i class="fas fa-file-alt"></i> إنشاء تقرير
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center">لا يوجد أقسام</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const table = document.querySelector('table');
        const rows = table.querySelectorAll('tbody tr');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}
