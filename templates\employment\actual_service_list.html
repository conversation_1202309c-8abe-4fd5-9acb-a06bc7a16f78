{% extends 'base.html' %}
{% load static %}

{% block title %}الخدمة الفعلية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الخدمة الفعلية</h2>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">قائمة الموظفين والخدمة الفعلية</h6>
        <form method="get">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="بحث بالرقم الوزاري أو الاسم..." value="{{ search_query }}">
                <button class="btn btn-outline-primary" type="submit">
                    <i class="fas fa-search"></i>
                </button>
                {% if search_query %}
                <a href="{% url 'employment:actual_service_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-redo"></i>
                </a>
                {% endif %}
            </div>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> الاسم الرباعي</th>
                        <th><i class="fas fa-briefcase me-1"></i> المسمى الوظيفي</th>
                        <th><i class="fas fa-building me-1"></i> القسم</th>
                        <th><i class="fas fa-calendar-alt me-1"></i> تاريخ التعيين</th>
                        <th><i class="fas fa-clock me-1"></i> الخدمة الفعلية</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees_data %}
                    <tr class="text-center">
                        <td>{{ employee.ministry_number }}</td>
                        <td>
                            <a href="{% url 'employees:employee_detail' employee.id %}">
                                {{ employee.full_name }}
                            </a>
                        </td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department }}</td>
                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                        <td>{{ employee.actual_service }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا يوجد موظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معلومات عن الخدمة الفعلية</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h5 class="alert-heading">كيفية حساب الخدمة الفعلية</h5>
            <p>يتم حساب الخدمة الفعلية للموظف بناءً على المعادلة التالية:</p>
            <hr>
            <p class="mb-0">الخدمة الفعلية = (تاريخ اليوم - تاريخ التعيين) - مجموع أيام الإجازات بدون راتب</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add any additional JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Add horizontal scrollbar to table if needed
        const tableContainer = document.querySelector('.table-responsive');
        if (tableContainer.scrollWidth > tableContainer.clientWidth) {
            tableContainer.style.overflowX = 'auto';
        }
    });
</script>
{% endblock %}
