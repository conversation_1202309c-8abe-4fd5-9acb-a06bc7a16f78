from django.db import models
from django.utils.translation import gettext_lazy as _
import os


def backup_file_path(instance, filename):
    """Generate file path for backup files"""
    return os.path.join('backups', filename)


class Backup(models.Model):
    """Model for storing database backups"""
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)
    file = models.FileField(_('Backup File'), upload_to=backup_file_path)
    size = models.PositiveIntegerField(_('Size (bytes)'), default=0)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    created_by = models.CharField(_('Created By'), max_length=100)
    is_auto = models.BooleanField(_('Automatic Backup'), default=False)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Backup')
        verbose_name_plural = _('Backups')

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        # Delete the file when the model instance is deleted
        if self.file:
            if os.path.isfile(self.file.path):
                os.remove(self.file.path)
        super().delete(*args, **kwargs)
