{% extends 'base.html' %}
{% load static %}

{% block title %}{% if department %}تعديل قسم {{ department.name }}{% else %}إضافة قسم جديد{% endif %} - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if department %}تعديل قسم {{ department.name }}{% else %}إضافة قسم جديد{% endif %}</h2>
    <a href="{% url 'employment:department_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للأقسام
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات القسم</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3 school-field">
                <label for="{{ form.school_national_id.id_for_label }}" class="form-label">
                    <i class="fas fa-id-card text-info me-2"></i>الرقم الوطني للمدرسة
                </label>
                {{ form.school_national_id }}
                {% if form.school_national_id.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.school_national_id.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">أدخل الرقم الوطني للمدرسة (للمدارس فقط)</div>
            </div>

            <div class="mb-3">
                <label for="{{ form.name.id_for_label }}" class="form-label">اسم القسم</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">الوصف</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.workplace.id_for_label }}" class="form-label">مكان العمل</label>
                {{ form.workplace }}
                {% if form.workplace.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.workplace.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 directorate-field">
                <label for="{{ form.directorate_type.id_for_label }}" class="form-label">يتبع لـ</label>
                {{ form.directorate_type }}
                {% if form.directorate_type.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.directorate_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 school-field">
                <label for="{{ form.school_type.id_for_label }}" class="form-label">تصنيف المدرسة</label>
                {{ form.school_type }}
                {% if form.school_type.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.school_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 school-field">
                <label for="{{ form.school_gender.id_for_label }}" class="form-label">جنس المدرسة</label>
                {{ form.school_gender }}
                {% if form.school_gender.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.school_gender.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 school-field">
                <label for="{{ form.highest_grade.id_for_label }}" class="form-label">أعلى صف</label>
                {{ form.highest_grade }}
                {% if form.highest_grade.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.highest_grade.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 school-field">
                <label for="{{ form.lowest_grade.id_for_label }}" class="form-label">أدنى صف</label>
                {{ form.lowest_grade }}
                {% if form.lowest_grade.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.lowest_grade.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 school-field">
                <label for="{{ form.school_ownership.id_for_label }}" class="form-label">
                    <i class="fas fa-home text-warning me-2"></i>ملكية المدرسة
                </label>
                {{ form.school_ownership }}
                {% if form.school_ownership.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.school_ownership.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">حدد ما إذا كانت المدرسة ملك أم مستأجرة</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-dark">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields and handle workplace selection
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Handle workplace selection
        const workplaceSelect = document.getElementById('id_workplace');
        const schoolFields = document.querySelectorAll('.school-field');
        const directorateFields = document.querySelectorAll('.directorate-field');

        // Function to toggle fields visibility based on workplace
        function toggleWorkplaceFields() {
            const workplace = workplaceSelect.value;

            // Handle school fields
            schoolFields.forEach(function(field) {
                if (workplace === 'school') {
                    field.style.display = 'block';
                } else {
                    field.style.display = 'none';
                    // Clear values for hidden fields instead of disabling them
                    const inputs = field.querySelectorAll('select');
                    inputs.forEach(input => {
                        if (input.value !== '') {
                            input.value = '';
                        }
                    });
                }
            });

            // Handle directorate fields
            directorateFields.forEach(function(field) {
                if (workplace === 'directorate') {
                    field.style.display = 'block';
                } else {
                    field.style.display = 'none';
                    // Clear values for hidden fields instead of disabling them
                    const inputs = field.querySelectorAll('select');
                    inputs.forEach(input => {
                        if (input.value !== '') {
                            input.value = '';
                        }
                    });
                }
            });
        }

        // Handle form submission to ensure disabled fields don't prevent submission
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            // Re-enable all fields before submission
            const allInputs = document.querySelectorAll('input, select, textarea');
            allInputs.forEach(input => {
                input.disabled = false;
            });
        });

        // Initial toggle based on current value
        toggleWorkplaceFields();

        // Add event listener for changes
        workplaceSelect.addEventListener('change', toggleWorkplaceFields);
    });
</script>

<style>
    /* Add some styling for the school fields */
    .school-field {
        padding: 10px;
        border-radius: 5px;
        background-color: #f8f9fa;
        margin-bottom: 15px;
    }

    /* Add some styling for the directorate fields */
    .directorate-field {
        padding: 10px;
        border-radius: 5px;
        background-color: #e3f2fd;
        margin-bottom: 15px;
    }
</style>
{% endblock %}
