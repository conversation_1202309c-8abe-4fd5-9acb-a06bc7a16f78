{% extends 'base.html' %}
{% load static %}

{% block title %}{% if appointment_type %}تعديل صفة تعيين {{ appointment_type.name }}{% else %}إضافة صفة تعيين جديدة{% endif %} - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if appointment_type %}تعديل صفة تعيين {{ appointment_type.name }}{% else %}إضافة صفة تعيين جديدة{% endif %}</h2>
    <a href="{% url 'employment:appointment_type_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة لصفات التعيين
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% if appointment_type %}تعديل صفة تعيين{% else %}إضافة صفة تعيين جديدة{% endif %}</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_name" class="form-label">الاسم</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="mb-3">
                <label for="id_description" class="form-label">الوصف</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
