{% extends 'base.html' %}
{% load static %}

{% block title %}تقارير الإجازات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Chart Card Styles */
    .dashboard-chart-card {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .dashboard-chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }

    .chart-container {
        position: relative;
        margin: auto;
        height: 300px;
    }

    /* Table styles with clear borders - matching directorate employees report */
    .table-bordered {
        border: 2px solid #dee2e6 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
        text-align: center;
    }

    .table-bordered thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* Make text in cells bold for better visibility */
    .table-bordered td {
        font-weight: bold;
    }

    /* Make numbers larger and more visible */
    .table-bordered td:nth-child(n+2) {
        font-size: 16px;
    }

    /* Add background colors for the table cells */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Dropdown menu animation */
    .animated--fade-in {
        animation-name: fadeIn;
        animation-duration: 0.3s;
        animation-timing-function: ease-in-out;
    }

    @keyframes fadeIn {
        0% {
            opacity: 0;
            transform: translateY(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }

        .card {
            break-inside: avoid;
            box-shadow: none !important;
            border: 1px solid #ddd;
        }

        .chart-container {
            page-break-inside: avoid;
            height: 400px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تقارير الإجازات</h2>
    <div>
        <a href="{% url 'reports:directorate_employees_report' %}" class="btn btn-primary me-2">
            <i class="fas fa-users"></i> تقرير موظفي المديرية
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
        <button class="btn btn-primary shadow-sm btn-print">
            <i class="fas fa-print me-1"></i> طباعة التقرير
        </button>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">أرصدة الإجازات للموظفين</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                <thead class="thead-light">
                    <tr>
                        <th rowspan="2">الموظف</th>
                        {% for leave_type in leave_types %}
                        <th colspan="3" class="text-center">{{ leave_type.get_name_display }}</th>
                        {% endfor %}
                    </tr>
                    <tr>
                        {% for leave_type in leave_types %}
                        <th class="bg-success text-white">الرصيد</th>
                        <th class="bg-danger text-white">المستخدم</th>
                        <th class="bg-info text-white">المتبقي</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for item in employee_balances %}
                    <tr class="text-center">
                        <td>
                            <a href="{% url 'employees:employee_detail' item.employee.pk %}">
                                {{ item.employee.full_name }}
                            </a>
                        </td>
                        {% for balance in item.type_balances %}
                        <td class="bg-success bg-opacity-25 fw-bold fs-5">{{ balance.initial }}</td>
                        <td class="bg-danger bg-opacity-25 fw-bold fs-5">{{ balance.used }}</td>
                        <td class="bg-info bg-opacity-25 fw-bold fs-5">{{ balance.remaining }}</td>
                        {% endfor %}
                    </tr>
                    {% empty %}
                    <tr class="text-center">
                        <td colspan="{{ leave_types.count|default:1|add:leave_types.count|add:leave_types.count|add:1 }}">لا يوجد بيانات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4 dashboard-chart-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(40deg, #4e73df, #36b9cc); border-radius: 0.35rem 0.35rem 0 0;">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-chart-pie mr-2"></i>
                    إحصائيات الإجازات حسب النوع
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle text-white" href="#" role="button" id="dropdownMenuLink1" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end shadow animated--fade-in" aria-labelledby="dropdownMenuLink1">
                        <div class="dropdown-header">خيارات المخطط:</div>
                        <a class="dropdown-item chart-type" href="#" data-chart="leaveType" data-type="pie">رسم دائري</a>
                        <a class="dropdown-item chart-type" href="#" data-chart="leaveType" data-type="doughnut">رسم حلقي</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" onclick="exportChart('leaveTypeChart', 'إحصائيات الإجازات حسب النوع')">تصدير كصورة</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="leaveTypeChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="me-2"><i class="fas fa-circle text-primary"></i> سنوية</span>
                    <span class="me-2"><i class="fas fa-circle text-success"></i> مرضية</span>
                    <span class="me-2"><i class="fas fa-circle text-info"></i> عرضية</span>
                    <span class="me-2"><i class="fas fa-circle text-warning"></i> أخرى</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4 dashboard-chart-card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(40deg, #20c997, #36b9cc); border-radius: 0.35rem 0.35rem 0 0;">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-chart-bar mr-2"></i>
                    إحصائيات الإجازات حسب الشهر
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle text-white" href="#" role="button" id="dropdownMenuLink2" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end shadow animated--fade-in" aria-labelledby="dropdownMenuLink2">
                        <div class="dropdown-header">خيارات المخطط:</div>
                        <a class="dropdown-item chart-type" href="#" data-chart="leaveMonth" data-type="bar">رسم شريطي</a>
                        <a class="dropdown-item chart-type" href="#" data-chart="leaveMonth" data-type="line">رسم خطي</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" onclick="exportChart('leaveMonthChart', 'إحصائيات الإجازات حسب الشهر')">تصدير كصورة</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:300px;">
                    <canvas id="leaveMonthChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js and Plugins -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

<!-- DataTables initialization -->
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[0, "asc"]],
            "pageLength": 25
        });
    });
</script>

<script>
    // Register Chart.js plugins
    Chart.register(ChartDataLabels);

    // Set Chart.js defaults
    Chart.defaults.font.family = 'Tajawal, sans-serif';
    Chart.defaults.font.size = 14;
    Chart.defaults.color = '#666';
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    Chart.defaults.plugins.tooltip.padding = 10;
    Chart.defaults.plugins.tooltip.cornerRadius = 6;
    Chart.defaults.plugins.tooltip.titleFont = { weight: 'bold' };
    Chart.defaults.plugins.datalabels.color = '#fff';
    Chart.defaults.plugins.datalabels.font = { weight: 'bold' };

    // Chart instances
    let leaveTypeChart, leaveMonthChart;

    document.addEventListener('DOMContentLoaded', function() {
        // Print button functionality
        const printButtons = document.querySelectorAll('.btn-print');
        printButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });

        // Custom color palettes
        const leaveTypeColors = {
            backgroundColors: [
                'rgba(54, 162, 235, 0.8)',  // سنوية - أزرق
                'rgba(40, 167, 69, 0.8)',   // مرضية - أخضر
                'rgba(23, 162, 184, 0.8)',  // عرضية - سماوي
                'rgba(255, 193, 7, 0.8)',   // لجان طبية - أصفر
                'rgba(111, 66, 193, 0.8)',  // مغادرة خاصة - بنفسجي
                'rgba(220, 53, 69, 0.8)'    // مغادرة رسمية - أحمر
            ],
            borderColors: [
                'rgba(54, 162, 235, 1)',
                'rgba(40, 167, 69, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(255, 193, 7, 1)',
                'rgba(111, 66, 193, 1)',
                'rgba(220, 53, 69, 1)'
            ],
            hoverColors: [
                'rgba(54, 162, 235, 0.9)',
                'rgba(40, 167, 69, 0.9)',
                'rgba(23, 162, 184, 0.9)',
                'rgba(255, 193, 7, 0.9)',
                'rgba(111, 66, 193, 0.9)',
                'rgba(220, 53, 69, 0.9)'
            ]
        };

        // Sample data for charts - replace with actual data
        const leaveTypeData = {
            labels: ['سنوية', 'مرضية', 'عرضية', 'لجان طبية', 'مغادرة خاصة', 'مغادرة رسمية'],
            datasets: [{
                label: 'عدد الإجازات',
                data: [12, 19, 5, 3, 2, 3],
                backgroundColor: leaveTypeColors.backgroundColors,
                borderColor: leaveTypeColors.borderColors,
                hoverBackgroundColor: leaveTypeColors.hoverColors,
                borderWidth: 2,
                borderRadius: 4,
                hoverOffset: 15
            }]
        };

        const leaveMonthData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'عدد الإجازات',
                data: [5, 8, 10, 12, 15, 20, 25, 18, 12, 10, 8, 5],
                backgroundColor: function(context) {
                    const chart = context.chart;
                    const {ctx, chartArea} = chart;
                    if (!chartArea) {
                        return null;
                    }
                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(32, 201, 151, 0.8)');
                    gradient.addColorStop(1, 'rgba(54, 185, 204, 0.8)');
                    return gradient;
                },
                borderColor: 'rgba(32, 201, 151, 1)',
                borderWidth: 2,
                borderRadius: 6,
                tension: 0.3
            }]
        };

        // Create Leave Type Chart
        createLeaveTypeChart('doughnut');

        // Create Leave Month Chart
        createLeaveMonthChart('bar');

        // Handle chart type changes
        document.querySelectorAll('.chart-type').forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const chartId = this.getAttribute('data-chart');
                const chartType = this.getAttribute('data-type');

                if (chartId === 'leaveType') {
                    if (leaveTypeChart) {
                        leaveTypeChart.destroy();
                    }
                    createLeaveTypeChart(chartType);
                } else if (chartId === 'leaveMonth') {
                    if (leaveMonthChart) {
                        leaveMonthChart.destroy();
                    }
                    createLeaveMonthChart(chartType);
                }
            });
        });
    });

    // Function to create Leave Type Chart
    function createLeaveTypeChart(type) {
        const ctx = document.getElementById('leaveTypeChart').getContext('2d');

        // Calculate total for percentages
        const data = [12, 19, 5, 3, 2, 3];
        const total = data.reduce((a, b) => a + b, 0);

        leaveTypeChart = new Chart(ctx, {
            type: type,
            data: {
                labels: ['سنوية', 'مرضية', 'عرضية', 'لجان طبية', 'مغادرة خاصة', 'مغادرة رسمية'],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: data,
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',  // سنوية - أزرق
                        'rgba(40, 167, 69, 0.8)',   // مرضية - أخضر
                        'rgba(23, 162, 184, 0.8)',  // عرضية - سماوي
                        'rgba(255, 193, 7, 0.8)',   // لجان طبية - أصفر
                        'rgba(111, 66, 193, 0.8)',  // مغادرة خاصة - بنفسجي
                        'rgba(220, 53, 69, 0.8)'    // مغادرة رسمية - أحمر
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(111, 66, 193, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 4,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: type === 'doughnut' ? '60%' : 0,
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        formatter: (value) => {
                            const percentage = Math.round((value / total) * 100);
                            return percentage + '%';
                        },
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 12
                        }
                    }
                }
            }
        });
    }

    // Function to create Leave Month Chart
    function createLeaveMonthChart(type) {
        const ctx = document.getElementById('leaveMonthChart').getContext('2d');

        leaveMonthChart = new Chart(ctx, {
            type: type,
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: [5, 8, 10, 12, 15, 20, 25, 18, 12, 10, 8, 5],
                    backgroundColor: function(context) {
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) {
                            return null;
                        }
                        const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                        gradient.addColorStop(0, 'rgba(32, 201, 151, 0.8)');
                        gradient.addColorStop(1, 'rgba(54, 185, 204, 0.8)');
                        return gradient;
                    },
                    borderColor: 'rgba(32, 201, 151, 1)',
                    borderWidth: 2,
                    borderRadius: 6,
                    tension: type === 'line' ? 0.3 : 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    datalabels: {
                        display: type === 'bar',
                        color: '#fff',
                        anchor: 'end',
                        align: 'top',
                        formatter: (value) => value,
                        font: {
                            weight: 'bold',
                            size: 12
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Function to export chart as image
    function exportChart(chartId, title) {
        const canvas = document.getElementById(chartId);
        const image = canvas.toDataURL('image/png', 1.0);

        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = image;
        link.download = title + '.png';
        link.click();
    }
</script>
{% endblock %}
