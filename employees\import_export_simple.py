#!/usr/bin/env python3
"""
📊 نظام استيراد وتصدير الموظفين المبسط
Simple Employee Import/Export System - HR System
"""

import pandas as pd
from io import BytesIO
from django.http import HttpResponse
from django.contrib import messages
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import datetime, date
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

from .models import Employee, RetiredEmployee, ExternalTransfer, MaternityLeave, InternalTransfer
from employment.models import Employment, EmployeePosition, Department, Position

def get_all_employee_fields():
    """الحصول على جميع حقول الموظف مع البيانات المرتبطة"""
    return {
        # الحقول الأساسية
        'الرقم الوزاري': 'ministry_number',
        'الرقم الوطني': 'national_id', 
        'الاسم الكامل': 'full_name',
        'الجنس': 'gender',
        'تاريخ الميلاد': 'birth_date',
        'العنوان': 'address',
        'رقم الهاتف': 'phone_number',
        'تاريخ التعيين': 'hire_date',
        'القسم الحالي': 'school',
        
        # المؤهلات العلمية
        'المؤهل العلمي': 'qualification',
        'دبلوم عالي': 'post_graduate_diploma',
        'ماجستير': 'masters_degree',
        'دكتوراه': 'phd_degree',
        'التخصص': 'specialization',
        
        # بيانات التوظيف
        'المنصب الحالي': 'current_position',
        'تاريخ المنصب': 'position_date',
        'نوع التوظيف': 'employment_status',
        'صفة التعيين': 'appointment_type',
        'تاريخ بداية التوظيف': 'employment_start_date',
        
        # الحالة الوظيفية
        'حالة الموظف': 'employee_status',
        'تاريخ التقاعد': 'retirement_date',
        'سبب التقاعد': 'retirement_reason',
        'تاريخ النقل الخارجي': 'transfer_date',
        'جهة النقل': 'transfer_destination',
        'سبب النقل': 'transfer_reason',
        
        # إجازة الأمومة
        'في إجازة أمومة': 'on_maternity_leave',
        'تاريخ بداية إجازة الأمومة': 'maternity_start_date',
        'تاريخ نهاية إجازة الأمومة': 'maternity_end_date',
        
        # النقل الداخلي
        'آخر نقل داخلي': 'last_internal_transfer',
        'من قسم': 'transfer_from_department',
        'إلى قسم': 'transfer_to_department',
        'تاريخ النقل الداخلي': 'internal_transfer_date',
        
        # تواريخ النظام
        'تاريخ الإنشاء': 'created_at',
        'تاريخ التحديث': 'updated_at'
    }

def get_employee_comprehensive_data(employee):
    """الحصول على البيانات الشاملة للموظف"""
    data = {}
    
    # الحقول الأساسية
    data['الرقم الوزاري'] = employee.ministry_number or ''
    data['الرقم الوطني'] = employee.national_id or ''
    data['الاسم الكامل'] = employee.full_name or ''
    data['الجنس'] = 'ذكر' if employee.gender == 'male' else 'أنثى' if employee.gender == 'female' else ''
    data['تاريخ الميلاد'] = employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else ''
    data['العنوان'] = employee.address or ''
    data['رقم الهاتف'] = employee.phone_number or ''
    data['تاريخ التعيين'] = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else ''
    data['القسم الحالي'] = employee.school or ''
    
    # المؤهلات العلمية
    data['المؤهل العلمي'] = employee.qualification or ''
    data['دبلوم عالي'] = employee.post_graduate_diploma or ''
    data['ماجستير'] = employee.masters_degree or ''
    data['دكتوراه'] = employee.phd_degree or ''
    data['التخصص'] = employee.specialization or ''
    
    # بيانات التوظيف
    try:
        current_employment = Employment.objects.filter(employee=employee, is_current=True).first()
        if current_employment:
            data['المنصب الحالي'] = current_employment.position.name if current_employment.position else ''
            data['نوع التوظيف'] = current_employment.status.get_name_display() if current_employment.status else ''
            data['صفة التعيين'] = current_employment.appointment_type.name if current_employment.appointment_type else ''
            data['تاريخ بداية التوظيف'] = current_employment.start_date.strftime('%Y-%m-%d') if current_employment.start_date else ''
        else:
            data['المنصب الحالي'] = employee.get_latest_position() if hasattr(employee, 'get_latest_position') else ''
            data['نوع التوظيف'] = ''
            data['صفة التعيين'] = ''
            data['تاريخ بداية التوظيف'] = ''
    except Exception as e:
        print(f"خطأ في الحصول على بيانات التوظيف: {e}")
        data['المنصب الحالي'] = ''
        data['نوع التوظيف'] = ''
        data['صفة التعيين'] = ''
        data['تاريخ بداية التوظيف'] = ''
    
    # آخر منصب من تاريخ الحراك الوظيفي
    try:
        latest_position = EmployeePosition.objects.filter(employee=employee).order_by('-date_obtained').first()
        if latest_position:
            data['تاريخ المنصب'] = latest_position.date_obtained.strftime('%Y-%m-%d')
        else:
            data['تاريخ المنصب'] = ''
    except Exception:
        data['تاريخ المنصب'] = ''
    
    # حالة الموظف
    try:
        if hasattr(employee, 'retirement'):
            data['حالة الموظف'] = 'متقاعد'
            data['تاريخ التقاعد'] = employee.retirement.retirement_date.strftime('%Y-%m-%d')
            data['سبب التقاعد'] = employee.retirement.retirement_reason or ''
        elif hasattr(employee, 'external_transfer'):
            data['حالة الموظف'] = 'منقول خارجياً'
            data['تاريخ النقل الخارجي'] = employee.external_transfer.transfer_date.strftime('%Y-%m-%d')
            data['جهة النقل'] = employee.external_transfer.destination_directorate or ''
            data['سبب النقل'] = employee.external_transfer.transfer_reason or ''
        else:
            data['حالة الموظف'] = 'نشط'
            data['تاريخ التقاعد'] = ''
            data['سبب التقاعد'] = ''
            data['تاريخ النقل الخارجي'] = ''
            data['جهة النقل'] = ''
            data['سبب النقل'] = ''
    except Exception:
        data['حالة الموظف'] = 'نشط'
        data['تاريخ التقاعد'] = ''
        data['سبب التقاعد'] = ''
        data['تاريخ النقل الخارجي'] = ''
        data['جهة النقل'] = ''
        data['سبب النقل'] = ''
    
    # إجازة الأمومة
    try:
        active_maternity = MaternityLeave.objects.filter(employee=employee, is_active=True).first()
        if active_maternity:
            data['في إجازة أمومة'] = 'نعم'
            data['تاريخ بداية إجازة الأمومة'] = active_maternity.start_date.strftime('%Y-%m-%d')
            data['تاريخ نهاية إجازة الأمومة'] = active_maternity.end_date.strftime('%Y-%m-%d')
        else:
            data['في إجازة أمومة'] = 'لا'
            data['تاريخ بداية إجازة الأمومة'] = ''
            data['تاريخ نهاية إجازة الأمومة'] = ''
    except Exception:
        data['في إجازة أمومة'] = 'لا'
        data['تاريخ بداية إجازة الأمومة'] = ''
        data['تاريخ نهاية إجازة الأمومة'] = ''
    
    # النقل الداخلي
    try:
        last_internal_transfer = InternalTransfer.objects.filter(employee=employee).order_by('-transfer_date').first()
        if last_internal_transfer:
            data['آخر نقل داخلي'] = 'نعم'
            data['من قسم'] = last_internal_transfer.previous_department or ''
            data['إلى قسم'] = last_internal_transfer.new_department or ''
            data['تاريخ النقل الداخلي'] = last_internal_transfer.transfer_date.strftime('%Y-%m-%d')
        else:
            data['آخر نقل داخلي'] = 'لا'
            data['من قسم'] = ''
            data['إلى قسم'] = ''
            data['تاريخ النقل الداخلي'] = ''
    except Exception:
        data['آخر نقل داخلي'] = 'لا'
        data['من قسم'] = ''
        data['إلى قسم'] = ''
        data['تاريخ النقل الداخلي'] = ''
    
    # تواريخ النظام
    data['تاريخ الإنشاء'] = employee.created_at.strftime('%Y-%m-%d %H:%M') if employee.created_at else ''
    data['تاريخ التحديث'] = employee.updated_at.strftime('%Y-%m-%d %H:%M') if employee.updated_at else ''
    
    return data

@login_required
def export_employees_excel_enhanced(request):
    """تصدير بيانات الموظفين إلى Excel مع جميع الحقول"""
    
    # الحصول على الموظفين النشطين فقط (حسب الطلب)
    include_all = request.GET.get('include_all', 'false') == 'true'
    active_only = request.GET.get('active_only', 'false') == 'true'
    
    if include_all:
        employees = Employee.objects.all()
        filename = 'جميع_الموظفين_شامل.xlsx'
    elif active_only:
        # استبعاد المتقاعدين والمنقولين خارجياً
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        excluded_ids = list(retired_ids) + list(transferred_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)
        filename = 'الموظفين_النشطين_شامل.xlsx'
    else:
        employees = Employee.objects.all()
        filename = 'الموظفين_شامل.xlsx'
    
    # ترتيب الموظفين حسب الرقم الوزاري
    try:
        from .utils import order_employees_by_ministry_number
        employees = order_employees_by_ministry_number(employees)
    except ImportError:
        employees = employees.order_by('ministry_number')
    
    # إنشاء البيانات
    data_rows = []
    for employee in employees:
        employee_data = get_employee_comprehensive_data(employee)
        data_rows.append(employee_data)
    
    # إنشاء DataFrame
    df = pd.DataFrame(data_rows)
    
    # إنشاء ملف Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='بيانات الموظفين الشاملة')
        
        # تنسيق الورقة
        workbook = writer.book
        worksheet = writer.sheets['بيانات الموظفين الشاملة']
        
        # تعيين اتجاه القراءة من اليمين لليسار
        worksheet.sheet_view.rightToLeft = True
        
        # تنسيق الخلايا
        header_font = Font(bold=True, size=12, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # تطبيق التنسيق على رأس الجدول
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # تطبيق الحدود على جميع الخلايا
        for row in worksheet.iter_rows():
            for cell in row:
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # تعديل عرض الأعمدة تلقائياً
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max(max_length + 2, 10), 50)  # بين 10 و 50
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

def create_employee_template_enhanced():
    """إنشاء قالب Excel محسن لاستيراد الموظفين"""
    
    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "قالب الموظفين"
    
    # تعيين اتجاه القراءة
    ws.sheet_view.rightToLeft = True
    
    # الحصول على جميع الحقول
    field_mapping = get_all_employee_fields()
    headers = list(field_mapping.keys())
    
    # إضافة الرؤوس
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, size=12, color='FFFFFF')
        cell.fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # إضافة صف مثال
    example_data = {
        'الرقم الوزاري': '123456',
        'الرقم الوطني': '987654321',
        'الاسم الكامل': 'أحمد محمد علي',
        'الجنس': 'ذكر',
        'تاريخ الميلاد': '1985-01-15',
        'العنوان': 'عمان - الأردن',
        'رقم الهاتف': '0791234567',
        'تاريخ التعيين': '2010-09-01',
        'القسم الحالي': 'مدرسة الأمل الأساسية',
        'المؤهل العلمي': 'بكالوريوس',
        'دبلوم عالي': 'دبلوم تربية',
        'ماجستير': '',
        'دكتوراه': '',
        'التخصص': 'رياضيات',
        'المنصب الحالي': 'معلم',
        'تاريخ المنصب': '2010-09-01',
        'نوع التوظيف': 'دائم',
        'صفة التعيين': 'تعيين جديد',
        'تاريخ بداية التوظيف': '2010-09-01',
        'حالة الموظف': 'نشط',
        'تاريخ التقاعد': '',
        'سبب التقاعد': '',
        'تاريخ النقل الخارجي': '',
        'جهة النقل': '',
        'سبب النقل': '',
        'في إجازة أمومة': 'لا',
        'تاريخ بداية إجازة الأمومة': '',
        'تاريخ نهاية إجازة الأمومة': '',
        'آخر نقل داخلي': 'لا',
        'من قسم': '',
        'إلى قسم': '',
        'تاريخ النقل الداخلي': '',
        'تاريخ الإنشاء': '',
        'تاريخ التحديث': ''
    }
    
    for col, header in enumerate(headers, 1):
        value = example_data.get(header, '')
        cell = ws.cell(row=2, column=col, value=value)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # تعديل عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = 20
    
    # حفظ الملف
    output = BytesIO()
    wb.save(output)
    output.seek(0)
    
    return output

def import_employees_enhanced(request, uploaded_file):
    """استيراد الموظفين مع دعم الحقول الجديدة"""
    
    try:
        # قراءة الملف
        if uploaded_file.name.endswith('.xlsx') or uploaded_file.name.endswith('.xls'):
            df = pd.read_excel(uploaded_file)
        elif uploaded_file.name.endswith('.csv'):
            df = pd.read_csv(uploaded_file)
        else:
            messages.error(request, 'نوع الملف غير مدعوم. يرجى استخدام Excel أو CSV.')
            return False
        
        # تنظيف البيانات
        df = df.dropna(how='all')
        
        # تعيين الأعمدة
        field_mapping = get_all_employee_fields()
        column_mapping = {}
        
        # ربط الأعمدة الموجودة بالحقول المطلوبة
        for arabic_name, field_name in field_mapping.items():
            for col in df.columns:
                if str(col).strip() == arabic_name:
                    column_mapping[field_name] = col
                    break
        
        # التحقق من وجود الحقول الأساسية
        required_fields = ['ministry_number', 'full_name']
        missing_fields = [field for field in required_fields if field not in column_mapping]
        
        if missing_fields:
            messages.error(request, f'الحقول المطلوبة مفقودة: {missing_fields}')
            return False
        
        # معالجة البيانات
        imported_count = 0
        skipped_count = 0
        errors = []
        
        for index, row in df.iterrows():
            try:
                # الحصول على القيم
                def get_value(field_name, default=''):
                    if field_name in column_mapping:
                        value = row.get(column_mapping[field_name], default)
                        if pd.isna(value):
                            return default
                        return str(value).strip()
                    return default
                
                # التحقق من الرقم الوزاري
                ministry_number = get_value('ministry_number')
                if not ministry_number:
                    skipped_count += 1
                    errors.append(f'صف {index + 2}: الرقم الوزاري مفقود')
                    continue
                
                # التحقق من وجود الموظف
                if Employee.objects.filter(ministry_number=ministry_number).exists():
                    skipped_count += 1
                    errors.append(f'الرقم الوزاري {ministry_number}: موجود بالفعل')
                    continue
                
                # إنشاء الموظف
                employee_data = {
                    'ministry_number': ministry_number,
                    'national_id': get_value('national_id'),
                    'full_name': get_value('full_name'),
                    'gender': 'male' if get_value('gender') == 'ذكر' else 'female',
                    'qualification': get_value('qualification'),
                    'post_graduate_diploma': get_value('post_graduate_diploma'),
                    'masters_degree': get_value('masters_degree'),
                    'phd_degree': get_value('phd_degree'),
                    'specialization': get_value('specialization'),
                    'school': get_value('school'),
                    'address': get_value('address'),
                    'phone_number': get_value('phone_number'),
                }
                
                # معالجة التواريخ
                def parse_date(date_str):
                    if not date_str:
                        return None
                    try:
                        return datetime.strptime(date_str, '%Y-%m-%d').date()
                    except:
                        try:
                            return datetime.strptime(date_str, '%d/%m/%Y').date()
                        except:
                            return None
                
                hire_date = parse_date(get_value('hire_date'))
                birth_date = parse_date(get_value('birth_date'))
                
                if hire_date:
                    employee_data['hire_date'] = hire_date
                if birth_date:
                    employee_data['birth_date'] = birth_date
                
                # إنشاء الموظف
                employee = Employee.objects.create(**employee_data)
                imported_count += 1
                
            except Exception as e:
                skipped_count += 1
                errors.append(f'صف {index + 2}: خطأ في المعالجة - {str(e)}')
        
        # رسائل النتائج
        if imported_count > 0:
            messages.success(request, f'تم استيراد {imported_count} موظف بنجاح.')
        
        if skipped_count > 0:
            messages.warning(request, f'تم تخطي {skipped_count} موظف.')
            
        if errors:
            error_msg = 'الأخطاء:\n' + '\n'.join(errors[:10])  # أول 10 أخطاء فقط
            if len(errors) > 10:
                error_msg += f'\n... و {len(errors) - 10} خطأ آخر'
            messages.error(request, error_msg)
        
        return imported_count > 0
        
    except Exception as e:
        messages.error(request, f'خطأ في قراءة الملف: {str(e)}')
        return False