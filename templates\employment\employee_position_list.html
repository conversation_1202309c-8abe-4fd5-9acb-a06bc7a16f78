{% extends 'base.html' %}
{% load static %}

{% block title %}الحراك الوظيفي{% endblock %}

{% block extra_css %}
<style>
    .table th {
        text-align: center;
        vertical-align: middle;
        font-size: 0.9rem;
        white-space: nowrap;
    }
    .table td {
        vertical-align: middle;
        font-size: 0.85rem;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
    .btn-group {
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الحراك الوظيفي</h2>
    <div>
        <a href="{% url 'employment:employee_position_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مسمى وظيفي جديد
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">عدد السجلات <span class="badge bg-primary ms-2 fs-5" style="font-size: 1.2rem; padding: 0.5rem 0.8rem;">{{ employee_positions.count }}</span></h6>
        <form class="d-flex" method="get">
            <input class="form-control me-2" type="search" placeholder="بحث عن موظف أو مسمى وظيفي..." name="search" value="{{ search_query }}">
            <button class="btn btn-outline-primary" type="submit">بحث</button>
            {% if search_query %}
            <a href="{% url 'employment:employee_position_list' %}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times"></i>
            </a>
            {% endif %}
        </form>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-4">
            <h5><i class="fas fa-info-circle"></i> سجلات الحراك الوظيفي</h5>
            <p>يعرض هذا الجدول سجلات الحراك الوظيفي للموظفين، حيث يمكنك رؤية تاريخ حصول كل موظف على المسمى الوظيفي.</p>
            <p>يتم عرض آخر مسمى وظيفي للموظف في صفحة بيانات الموظفين.</p>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th width="10%">الرقم الوزاري</th>
                        <th width="20%">اسم الموظف</th>
                        <th width="15%">المسمى الوظيفي</th>
                        <th width="15%">تاريخ الحصول عليه</th>
                        <th width="15%">ملاحظات</th>
                        <th width="10%">تاريخ الإضافة</th>
                        <th width="15%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for position in employee_positions %}
                    <tr>
                        <td>{{ position.employee.ministry_number }}</td>
                        <td>{{ position.employee.full_name }}</td>
                        <td>{{ position.position.name }}</td>
                        <td>{{ position.date_obtained|date:"Y-m-d" }}</td>
                        <td>{{ position.notes|default:"-" }}</td>
                        <td>{{ position.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'employment:employee_position_update' position.id %}" class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'employment:employee_position_delete' position.id %}" class="btn btn-danger btn-sm" title="حذف">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد مسميات وظيفية مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
