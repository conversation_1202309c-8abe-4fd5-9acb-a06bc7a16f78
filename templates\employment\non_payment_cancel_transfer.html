{% extends 'base.html' %}
{% load static %}

{% block title %}إلغاء ترحيل عدم الصرف - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .cancel-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 2px solid #dc3545;
    }
    
    .cancel-card .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 20px;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }
    
    .btn-cancel-transfer {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel-transfer:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .btn-back {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .record-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border-left: 4px solid #dc3545;
        transition: all 0.3s ease;
    }

    .record-item:hover {
        background: #e9ecef;
        transform: translateX(-5px);
    }

    .transfer-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-undo text-danger me-2"></i>
                إلغاء ترحيل عدم الصرف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:non_payment_list' %}">عدم الصرف</a></li>
                    <li class="breadcrumb-item active">إلغاء الترحيل</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Cancel Transfer Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="cancel-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد إلغاء آخر عملية ترحيل
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-undo warning-icon"></i>
                        <h4 class="text-danger">تحذير!</h4>
                        <p class="text-muted">هل أنت متأكد من رغبتك في إلغاء آخر عملية ترحيل؟</p>
                        <p class="text-danger"><strong>سيتم إرجاع السجلات التالية إلى حالة "معلق" ويمكن تعديلها وحذفها مرة أخرى!</strong></p>
                    </div>

                    <!-- Transfer Information -->
                    <div class="transfer-info">
                        <h6 class="mb-2">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات آخر عملية ترحيل:
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>تاريخ الترحيل:</strong> {{ latest_transfer_date|date:"Y-m-d H:i" }}
                            </div>
                            <div class="col-md-4">
                                <strong>عدد السجلات:</strong> {{ latest_transferred_records.count }}
                            </div>
                            <div class="col-md-4">
                                <strong>إجمالي الأيام:</strong> 
                                {% for record in latest_transferred_records %}
                                    {% if forloop.first %}{{ record.days_count }}{% else %}{{ record.days_count|add:forloop.counter0 }}{% endif %}
                                {% empty %}0{% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Records List -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-list me-2"></i>
                            السجلات التي سيتم إلغاء ترحيلها:
                        </h6>
                        <div class="row">
                            {% for record in latest_transferred_records %}
                            <div class="col-md-6 mb-3">
                                <div class="record-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-user me-2"></i>
                                                {{ record.employee.full_name }}
                                            </h6>
                                            <p class="mb-1 text-muted">
                                                <i class="fas fa-id-card me-1"></i>
                                                {{ record.ministry_number }}
                                            </p>
                                            <p class="mb-1 text-muted">
                                                <i class="fas fa-building me-1"></i>
                                                {{ record.department_name }}
                                            </p>
                                            <p class="mb-0 text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ record.date|date:"Y-m-d" }}
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-warning text-dark">
                                                {{ record.days_count }} يوم
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                {{ record.transfer_date|date:"H:i" }}
                                            </small>
                                        </div>
                                    </div>
                                    {% if record.notes %}
                                    <div class="mt-2 pt-2 border-top">
                                        <small class="text-muted">
                                            <i class="fas fa-sticky-note me-1"></i>
                                            {{ record.notes|truncatechars:50 }}
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Warning Message -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تنبيه مهم:
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم إرجاع جميع السجلات المذكورة أعلاه إلى حالة "معلق"</li>
                            <li>ستصبح السجلات قابلة للتعديل والحذف مرة أخرى</li>
                            <li>سيتم حذف تاريخ الترحيل من السجلات</li>
                            <li>لن تظهر السجلات في ملخص عدم الصرف بعد الإلغاء</li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:non_payment_list' %}" class="btn btn-back text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-cancel-transfer text-white">
                                <i class="fas fa-undo me-2"></i>تأكيد إلغاء الترحيل
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog for cancel transfer button
    $('form').on('submit', function(e) {
        const recordCount = {{ latest_transferred_records.count }};
        if (!confirm(`هل أنت متأكد من رغبتك في إلغاء ترحيل ${recordCount} سجل؟ ستصبح السجلات قابلة للتعديل مرة أخرى!`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
