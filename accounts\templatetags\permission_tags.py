from django import template

register = template.Library()

@register.filter
def get_permission(user_permissions, module_name):
    """Get permission data for a specific module"""
    return user_permissions.get(module_name, {})

@register.filter
def has_permission(user_permissions, permission_key):
    """Check if user has a specific permission"""
    module_name, permission_type = permission_key.split('.')
    module_perms = user_permissions.get(module_name, {})
    return module_perms.get(permission_type, False)

@register.filter
def get_visible_pages(user_permissions, module_name):
    """Get visible pages for a module"""
    module_perms = user_permissions.get(module_name, {})
    return module_perms.get('visible_pages', [])

@register.filter
def page_is_visible(user_permissions, page_key):
    """Check if a page is visible for user"""
    module_name, page_url = page_key.split('|')
    module_perms = user_permissions.get(module_name, {})
    visible_pages = module_perms.get('visible_pages', [])
    return page_url in visible_pages