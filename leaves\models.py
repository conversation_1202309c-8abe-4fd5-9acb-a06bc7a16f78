from django.db import models
from django.utils.translation import gettext_lazy as _
from employees.models import Employee
from employment.models import Department

class LeaveType(models.Model):
    """Model for storing leave types"""
    ANNUAL = 'annual'
    SICK = 'sick'
    MEDICAL_COMMITTEE = 'medical_committee'
    CASUAL = 'casual'
    PERSONAL_DEPARTURE = 'personal_departure'
    OFFICIAL_DEPARTURE = 'official_departure'
    UNPAID = 'unpaid'
    HAJJ = 'hajj'
    MATERNITY = 'maternity'
    PATERNITY = 'paternity'
    HUSBAND_DEATH = 'husband_death'
    WIFE_DEATH = 'wife_death'

    TYPE_CHOICES = [
        (ANNUAL, _('سنوية')),
        (SICK, _('مرضية')),
        (MEDICAL_COMMITTEE, _('لجان طبية')),
        (CASUAL, _('عرضية')),
        (PERSONAL_DEPARTURE, _('مغادرة خاصة')),
        (OFFICIAL_DEPARTURE, _('مغادرة رسمية')),
        (UNPAID, _('بدون راتب')),
        (HAJJ, _('اجازة الحج')),
        (MATERNITY, _('امومة')),
        (PATERNITY, _('الابوة')),
        (HUSBAND_DEATH, _('وفاة الزوج')),
        (WIFE_DEATH, _('وفاة الزوجة')),
    ]

    name = models.CharField(_('Name'), max_length=50, choices=TYPE_CHOICES, unique=True)
    description = models.TextField(_('Description'), blank=True, null=True)
    max_days_per_year = models.PositiveIntegerField(_('Maximum Days Per Year'), default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Leave Type')
        verbose_name_plural = _('Leave Types')

    def __str__(self):
        return self.get_name_display()

class LeaveBalance(models.Model):
    """Model for storing employee leave balances"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_balances')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, related_name='employee_balances')
    year = models.PositiveIntegerField(_('Year'))
    initial_balance = models.PositiveIntegerField(_('Initial Balance'))
    used_balance = models.PositiveIntegerField(_('Used Balance'), default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def remaining_balance(self):
        """Calculate remaining balance based on actual approved leaves"""
        from django.db.models import Sum

        # حساب الإجازات المستخدمة من جدول الإجازات مباشرة
        used_leaves = Leave.objects.filter(
            employee=self.employee,
            leave_type=self.leave_type,
            start_date__year=self.year,
            status='approved'
        ).aggregate(total_days=Sum('days_count'))

        used_days = used_leaves['total_days'] or 0
        remaining = self.initial_balance - used_days
        return remaining if remaining >= 0 else 0

    @property
    def actual_used_balance(self):
        """Calculate actual used balance from approved leaves"""
        from django.db.models import Sum

        used_leaves = Leave.objects.filter(
            employee=self.employee,
            leave_type=self.leave_type,
            start_date__year=self.year,
            status='approved'
        ).aggregate(total_days=Sum('days_count'))

        return used_leaves['total_days'] or 0

    class Meta:
        verbose_name = _('Leave Balance')
        verbose_name_plural = _('Leave Balances')
        unique_together = ['employee', 'leave_type', 'year']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type} - {self.year}"

class Leave(models.Model):
    """Model for storing employee leaves"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leaves')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, related_name='leaves')
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'))
    days_count = models.PositiveIntegerField(_('Days Count'))
    reason = models.TextField(_('Reason'), blank=True, null=True)
    status = models.CharField(_('Status'), max_length=20, choices=[
        ('pending', _('قيد الانتظار')),
        ('approved', _('مقبولة')),
        ('rejected', _('مرفوضة')),
    ], default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Leave')
        verbose_name_plural = _('Leaves')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type} - {self.start_date}"

class Departure(models.Model):
    """Model for storing employee departures"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='departures')
    departure_type = models.CharField(_('Departure Type'), max_length=20, choices=[
        ('personal', _('خاصة')),
        ('official', _('رسمية')),
    ])
    date = models.DateField(_('Date'))
    time_from = models.TimeField(_('Time From'))
    time_to = models.TimeField(_('Time To'))
    reason = models.TextField(_('Reason'), blank=True, null=True)
    status = models.CharField(_('Status'), max_length=20, choices=[
        ('pending', _('قيد الانتظار')),
        ('approved', _('مقبولة')),
        ('rejected', _('مرفوضة')),
    ], default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Departure')
        verbose_name_plural = _('Departures')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_departure_type_display()} - {self.date}"
