# Generated by Django 5.2 on 2025-06-17 05:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='RetiredEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('retirement_date', models.DateField(verbose_name='Retirement Date')),
                ('retirement_reason', models.CharField(default='تقاعد عادي', max_length=255, verbose_name='Retirement Reason')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='retirement', to='employees.employee')),
            ],
            options={
                'verbose_name': 'Retired Employee',
                'verbose_name_plural': 'Retired Employees',
                'ordering': ['-retirement_date'],
            },
        ),
    ]
