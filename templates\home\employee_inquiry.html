{% extends 'home/base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .search-card .card-body {
        padding: 2rem;
    }

    .search-input {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        font-size: 16px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: border-color 0.3s ease;
    }

    .search-input:focus {
        border-color: #007bff;
        box-shadow: 0 4px 6px rgba(0, 123, 255, 0.25);
    }

    .search-input.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 4px 6px rgba(220, 53, 69, 0.25);
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
    }

    .search-btn {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .result-card {
        display: none;
        animation: fadeInUp 0.5s ease-in-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .employee-info {
        background: #f8f9fa;
        color: #000;
        border: 2px solid #dee2e6;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: bold;
        color: #000 !important;
    }

    .info-value {
        font-weight: 600;
        color: #000 !important;
    }

    /* Table styles with black text */
    .table {
        color: #000 !important;
    }

    .table th,
    .table td {
        color: #000 !important;
        border: 1px solid #dee2e6 !important;
    }

    .table thead th {
        background-color: #f8f9fa !important;
        color: #000 !important;
        font-weight: bold;
    }

    .print-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        font-size: 20px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        display: none;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    /* Qualification styling */
    .qualification-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
    }

    .qualification-title {
        font-weight: bold;
        color: #495057;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .qualification-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #dee2e6;
    }

    .qualification-item:last-child {
        border-bottom: none;
    }

    /* Employee status alert styling */
    .alert {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
        color: #0c5460;
    }

    .alert-secondary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
    }

    .alert h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .alert .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .alert .row {
        margin-top: 1rem;
    }

    .alert .row:first-child {
        margin-top: 0;
    }

    .alert strong {
        color: #495057;
        font-weight: 600;
    }

    .qualification-label {
        font-weight: 600;
        color: #6c757d;
        flex: 1;
    }

    .qualification-value {
        flex: 1;
        text-align: right;
        font-weight: 500;
    }

    .qualification-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        min-width: 80px;
    }

    .qualification-bachelor {
        background-color: #007bff;
        color: white;
    }

    .qualification-diploma {
        background-color: #28a745;
        color: white;
    }

    .qualification-masters {
        background-color: #17a2b8;
        color: white;
    }

    .qualification-phd {
        background-color: #ffc107;
        color: #212529;
    }

    .qualification-none {
        background-color: #6c757d;
        color: white;
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }

        .print-btn {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .employee-info {
            background: #f8f9fa !important;
            color: #000 !important;
            border: 1px solid #dee2e6 !important;
        }

        .info-item {
            border-bottom: 1px solid #333 !important;
        }

        .info-label,
        .info-value {
            color: #333 !important;
        }

        /* Hide header and footer when printing */
        .navbar,
        .footer,
        header,
        footer,
        nav {
            display: none !important;
        }

        /* Hide page title */
        .container h1,
        .container .h3 {
            display: none !important;
        }

        /* Hide base template elements */
        body::before,
        body::after {
            display: none !important;
        }

        /* Adjust margins for print */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: white !important;
        }

        .container {
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Hide specific base template elements */
        .hero-section,
        .navbar-brand,
        .navbar-nav,
        .navbar-toggler,
        .navbar-collapse,
        .search-card,
        #loadingIndicator,
        #errorAlert {
            display: none !important;
        }

        /* Override any base template styles */
        * {
            background-image: none !important;
            background-color: white !important;
        }

        .employee-info * {
            background-color: #f8f9fa !important;
        }

        /* Ensure table text is black in print */
        .table,
        .table th,
        .table td {
            color: #000 !important;
            border: 1px solid #000 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header with back button -->
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-search me-2"></i>
            {{ title }}
        </h1>
        <div>
            <a href="{% url 'home:home' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Search Card -->
            <div class="card shadow search-card mb-4">
                <div class="card-body text-center">
                    <h2 class="mb-4">
                        <i class="fas fa-search me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="lead mb-4">البحث عن الموظفين باستخدام الرقم الوزاري والرقم الوطني معاً</p>
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه مهم:</strong>
                        <p class="mb-0 mt-2">يجب إدخال <strong>كل من الرقم الوزاري والرقم الوطني معاً</strong> للبحث عن الموظف. هذا يضمن دقة البيانات والتأكد من هوية الموظف.</p>
                    </div>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="ministryNumberInput" class="form-label text-white">
                                        <i class="fas fa-id-card me-1"></i>
                                        الرقم الوزاري <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control search-input"
                                           id="ministryNumberInput"
                                           placeholder="أدخل الرقم الوزاري"
                                           maxlength="20"
                                           required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="nationalIdInput" class="form-label text-white">
                                        <i class="fas fa-id-badge me-1"></i>
                                        الرقم الوطني <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control search-input"
                                           id="nationalIdInput"
                                           placeholder="أدخل الرقم الوطني"
                                           maxlength="20"
                                           required>
                                </div>
                            </div>
                            <button type="button" class="btn btn-warning search-btn" id="searchBtn">
                                <i class="fas fa-search me-2"></i> بحث
                            </button>
                            <button type="button" class="btn btn-outline-light ms-2" onclick="clearResults()">
                                <i class="fas fa-times me-2"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div class="loading-spinner" id="loadingIndicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-3">جاري البحث عن الموظف...</p>
            </div>

            <!-- Error Alert -->
            <div class="alert alert-danger" id="errorAlert" style="display: none;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"></span>
            </div>

            <!-- Results Card -->
            <div class="card shadow result-card" id="resultsCard">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        بيانات الموظف
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Employee Information -->
                    <div class="employee-info" id="employeeInfo">
                        <!-- Employee data will be populated here -->
                    </div>

                    <!-- Annual Reports Table -->
                    <div class="mt-4" id="annualReportsSection" style="display: none;">
                        <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>التقارير السنوية</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center">السنة</th>
                                        <th class="text-center">الدرجة</th>
                                        <th class="text-center">الدرجة القصوى</th>
                                        <th class="text-center">النسبة المئوية</th>
                                        <th class="text-center">الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="annualReportsTableBody">
                                    <!-- Annual reports data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Penalties Table -->
                    <div class="mt-4" id="penaltiesSection" style="display: none;">
                        <h5 class="mb-3"><i class="fas fa-exclamation-triangle me-2"></i>العقوبات</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center">التاريخ</th>
                                        <th class="text-center">نوع العقوبة</th>
                                        <th class="text-center">الوصف</th>
                                        <th class="text-center">رقم القرار</th>
                                        <th class="text-center">تاريخ القرار</th>
                                    </tr>
                                </thead>
                                <tbody id="penaltiesTableBody">
                                    <!-- Penalties data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Unpaid Leaves Table -->
                    <div class="mt-4" id="unpaidLeavesSection" style="display: none;">
                        <h5 class="mb-3"><i class="fas fa-calendar-times me-2"></i>الإجازات بدون راتب</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center">تاريخ البداية</th>
                                        <th class="text-center">تاريخ النهاية</th>
                                        <th class="text-center">المدة (أيام)</th>
                                        <th class="text-center">السبب</th>
                                       
                                    </tr>
                                </thead>
                                <tbody id="unpaidLeavesTableBody">
                                    <!-- Unpaid leaves data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Print Button -->
                    <div class="text-center mt-4 no-print">
                        <button class="btn btn-success btn-lg" onclick="printResults()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary btn-lg ms-2" onclick="clearResults()">
                            <i class="fas fa-times me-2"></i>
                            مسح النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Print Button -->
<button class="btn btn-success print-btn" onclick="printResults()" title="طباعة">
    <i class="fas fa-print"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle search button click
    $('#searchBtn').click(function() {
        performSearch();
    });

    // Handle Enter key press in search inputs
    $('#ministryNumberInput, #nationalIdInput').keypress(function(e) {
        if (e.which == 13) {
            performSearch();
        }
    });

    // Remove error styling when user starts typing
    $('#ministryNumberInput, #nationalIdInput').on('input', function() {
        $(this).removeClass('is-invalid');
        hideAllAlerts();
    });

    // Auto-focus on ministry number input
    $('#ministryNumberInput').focus();
});

function performSearch() {
    const ministryNumber = $('#ministryNumberInput').val().trim();
    const nationalId = $('#nationalIdInput').val().trim();

    // التحقق من وجود كلا الرقمين
    if (!ministryNumber || !nationalId) {
        if (!ministryNumber && !nationalId) {
            showError('يجب إدخال كل من الرقم الوزاري والرقم الوطني للبحث عن الموظف');
        } else if (!ministryNumber) {
            showError('يجب إدخال الرقم الوزاري مع الرقم الوطني للبحث عن الموظف');
        } else {
            showError('يجب إدخال الرقم الوطني مع الرقم الوزاري للبحث عن الموظف');
        }

        // تمييز الحقول المفقودة
        if (!ministryNumber) {
            $('#ministryNumberInput').addClass('is-invalid');
        } else {
            $('#ministryNumberInput').removeClass('is-invalid');
        }

        if (!nationalId) {
            $('#nationalIdInput').addClass('is-invalid');
        } else {
            $('#nationalIdInput').removeClass('is-invalid');
        }

        return;
    }

    // إزالة تمييز الخطأ من الحقول
    $('#ministryNumberInput').removeClass('is-invalid');
    $('#nationalIdInput').removeClass('is-invalid');

    // تنظيف رسائل الخطأ السابقة
    hideAllAlerts();

    // Hide previous results and errors
    hideAllAlerts();
    $('#resultsCard').hide();
    $('.print-btn').hide();

    // Show loading indicator
    $('#loadingIndicator').show();

    // Make AJAX request
    $.ajax({
        url: '{% url "home:get_employee_details" %}',
        method: 'GET',
        data: {
            'ministry_number': ministryNumber,
            'national_id': nationalId
        },
        success: function(response) {
            $('#loadingIndicator').hide();
            console.log('Server response:', response); // للتشخيص

            if (response.success) {
                displayResults(response);
            } else {
                // التحقق من حالة الموظف
                if (response.employee_status) {
                    showEmployeeStatusError(response);
                } else {
                    showError(response.error);
                }
            }
        },
        error: function(xhr, status, error) {
            $('#loadingIndicator').hide();
            console.error('AJAX Error:', xhr.responseText); // للتشخيص
            showError('حدث خطأ في الاتصال بالخادم: ' + error);
        }
    });
}

function displayResults(data) {
    const employee = data.employee;
    const annualReports = data.annual_reports;
    const penalties = data.penalties;

    let employeeInfoHtml = `
        <div class="info-item">
            <span class="info-label">الرقم الوزاري:</span>
            <span class="info-value">${employee.ministry_number}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الرقم الوطني:</span>
            <span class="info-value">${employee.national_id}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الاسم الكامل:</span>
            <span class="info-value">${employee.full_name}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الجنس:</span>
            <span class="info-value">${employee.gender}</span>
        </div>
        <div class="info-item">
            <span class="info-label">تاريخ الميلاد:</span>
            <span class="info-value">${employee.birth_date}</span>
        </div>
        <div class="info-item">
            <span class="info-label">العمر:</span>
            <span class="info-value">${employee.age} </span>
        </div>
        <div class="qualification-section">
            <div class="qualification-title">
                <i class="fas fa-graduation-cap me-2"></i>المؤهلات العلمية
            </div>
            <div class="qualification-item">
                <span class="qualification-label">بكالوريس / دبلوم:</span>
                <span class="qualification-value">
                    <span class="qualification-badge ${employee.qualification ? 'qualification-bachelor' : 'qualification-none'}">
                        ${employee.qualification || 'غير محدد'}
                    </span>
                </span>
            </div>
            <div class="qualification-item">
                <span class="qualification-label">دبلوم بعد البكالوريس:</span>
                <span class="qualification-value">
                    <span class="qualification-badge ${employee.post_graduate_diploma ? 'qualification-diploma' : 'qualification-none'}">
                        ${employee.post_graduate_diploma || 'غير محدد'}
                    </span>
                </span>
            </div>
            <div class="qualification-item">
                <span class="qualification-label">ماجستير:</span>
                <span class="qualification-value">
                    <span class="qualification-badge ${employee.masters_degree ? 'qualification-masters' : 'qualification-none'}">
                        ${employee.masters_degree || 'غير محدد'}
                    </span>
                </span>
            </div>
            <div class="qualification-item">
                <span class="qualification-label">دكتوراه:</span>
                <span class="qualification-value">
                    <span class="qualification-badge ${employee.phd_degree ? 'qualification-phd' : 'qualification-none'}">
                        ${employee.phd_degree || 'غير محدد'}
                    </span>
                </span>
            </div>
        </div>
        <div class="info-item">
            <span class="info-label">التخصص:</span>
            <span class="info-value">${employee.specialization}</span>
        </div>
        <div class="info-item">
            <span class="info-label">تاريخ التعيين:</span>
            <span class="info-value">${employee.hire_date}</span>
        </div>
        <div class="info-item">
            <span class="info-label">الخدمة الفعلية:</span>
            <span class="info-value">${employee.service_years}</span>
        </div>
        <div class="info-item">
            <span class="info-label">مكان العمل:</span>
            <span class="info-value">${employee.school}</span>
        </div>
        <div class="info-item">
            <span class="info-label">القسم:</span>
            <span class="info-value">${employee.department}</span>
        </div>
        <div class="info-item">
            <span class="info-label">المسمى الوظيفي:</span>
            <span class="info-value">${employee.position}</span>
        </div>
        <div class="info-item">
            <span class="info-label">العنوان:</span>
            <span class="info-value">${employee.address}</span>
        </div>
        <div class="info-item">
            <span class="info-label">رقم الهاتف:</span>
            <span class="info-value">${employee.phone_number}</span>
        </div>
    `;

    $('#employeeInfo').html(employeeInfoHtml);

    // Display Annual Reports
    if (annualReports && annualReports.length > 0) {
        let annualReportsHtml = '';
        annualReports.forEach(function(report) {
            annualReportsHtml += `
                <tr class="text-center">
                    <td>${report.year}</td>
                    <td>${report.score}</td>
                    <td>${report.max_score}</td>
                    <td>${report.percentage}%</td>
                    <td>${report.comments}</td>
                </tr>
            `;
        });
        $('#annualReportsTableBody').html(annualReportsHtml);
        $('#annualReportsSection').show();
    } else {
        $('#annualReportsTableBody').html('<tr><td colspan="6" class="text-center">لا توجد تقارير سنوية</td></tr>');
        $('#annualReportsSection').show();
    }

    // Display Penalties
    if (penalties && penalties.length > 0) {
        let penaltiesHtml = '';
        penalties.forEach(function(penalty) {
            penaltiesHtml += `
                <tr class="text-center">
                    <td>${penalty.date}</td>
                    <td>${penalty.penalty_type}</td>
                    <td>${penalty.description}</td>
                    <td>${penalty.decision_number}</td>
                    <td>${penalty.decision_date}</td>
                </tr>
            `;
        });
        $('#penaltiesTableBody').html(penaltiesHtml);
        $('#penaltiesSection').show();
    } else {
        $('#penaltiesTableBody').html('<tr><td colspan="5" class="text-center">لا توجد عقوبات</td></tr>');
        $('#penaltiesSection').show();
    }

    // Display Unpaid Leaves
    const unpaidLeaves = data.unpaid_leaves;
    console.log('Unpaid leaves data:', unpaidLeaves); // للتشخيص

    if (unpaidLeaves && unpaidLeaves.length > 0) {
        let unpaidLeavesHtml = '';
        unpaidLeaves.forEach(function(leave) {
            unpaidLeavesHtml += `
                <tr class="text-center">
                    <td>${leave.start_date}</td>
                    <td>${leave.end_date}</td>
                    <td>${leave.duration}</td>
                    <td>${leave.reason}</td>
                   
                </tr>
            `;
        });
        $('#unpaidLeavesTableBody').html(unpaidLeavesHtml);
        $('#unpaidLeavesSection').show();
    } else {
        $('#unpaidLeavesTableBody').html('<tr><td colspan="6" class="text-center">لا توجد إجازات بدون راتب</td></tr>');
        $('#unpaidLeavesSection').show();
    }

    // Show results
    $('#resultsCard').show();
    $('.print-btn').show();

    // Scroll to results
    $('html, body').animate({
        scrollTop: $('#resultsCard').offset().top - 100
    }, 500);
}

function showError(message) {
    $('#errorMessage').text(message);
    $('#errorAlert').show();
}

function showEmployeeStatusError(response) {
    const statusDetails = response.status_details;
    let statusIcon = '';
    let statusClass = '';
    let statusMessage = '';
    
    // تحديد الأيقونة والفئة حسب نوع الحالة
    switch(response.employee_status) {
        case 'retired':
            statusIcon = 'fas fa-user-clock';
            statusClass = 'alert-warning';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-warning me-3"></i>
                    <div>
                        <h5 class="mb-1 text-warning">موظف متقاعد</h5>
                        <p class="mb-0 text-muted">هذا الموظف ليس على رأس العمل حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <strong>سبب التقاعد:</strong><br>
                        <span class="text-primary">${statusDetails.retirement_reason}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ التقاعد:</strong><br>
                        <span class="text-primary">${statusDetails.date}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong><br>
                        <span class="badge bg-warning">متقاعد</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الملاحظات:</strong><br>
                        <small class="text-muted">${statusDetails.notes}</small>
                    </div>
                </div>
            `;
            break;
            
        case 'transferred':
            statusIcon = 'fas fa-exchange-alt';
            statusClass = 'alert-info';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-info me-3"></i>
                    <div>
                        <h5 class="mb-1 text-info">موظف منقول خارجياً</h5>
                        <p class="mb-0 text-muted">هذا الموظف ليس على رأس العمل حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>تاريخ النقل:</strong><br>
                        <span class="text-primary">${statusDetails.date}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الجهة المنقول إليها:</strong><br>
                        <span class="text-primary">${statusDetails.destination}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        <strong>الحالة:</strong>
                        <span class="badge bg-info">منقول خارجياً</span>
                    </div>
                </div>
            `;
            break;
            
        case 'service_purchase':
            statusIcon = 'fas fa-shopping-cart';
            statusClass = 'alert-secondary';
            statusMessage = `
                <div class="d-flex align-items-center mb-3">
                    <i class="${statusIcon} fa-2x text-secondary me-3"></i>
                    <div>
                        <h5 class="mb-1 text-secondary">موظف في حالة شراء خدمات</h5>
                        <p class="mb-0 text-muted">هذا الموظف ليس على رأس العمل حالياً</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>تاريخ إضافة شراء الخدمات:</strong><br>
                        <span class="text-primary">${statusDetails.created_date}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>المدرسة المستهدفة:</strong><br>
                        <span class="text-primary">${statusDetails.target_school}</span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <span class="badge bg-secondary">شراء خدمات</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الملاحظات:</strong><br>
                        <small class="text-muted">${statusDetails.notes}</small>
                    </div>
                </div>
            `;
            break;
            
        default:
            showError(response.error);
            return;
    }
    
    // إنشاء HTML للرسالة
    const alertHtml = `
        <div class="alert ${statusClass} alert-dismissible fade show" role="alert">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            ${statusMessage}
        </div>
    `;
    
    // إخفاء النتائج وإظهار رسالة الحالة
    $('#resultsCard').hide();
    $('#annualReportsSection').hide();
    $('#penaltiesSection').hide();
    $('#unpaidLeavesSection').hide();
    $('.print-btn').hide();
    
    // إظهار رسالة الحالة
    $('#errorAlert').hide();
    $('#resultsCard').html(alertHtml).show();
}

function hideAllAlerts() {
    $('#errorAlert').hide();
}

function printResults() {
    window.print();
}

function clearResults() {
    $('#ministryNumberInput').val('').removeClass('is-invalid');
    $('#nationalIdInput').val('').removeClass('is-invalid');
    $('#resultsCard').hide().html(''); // مسح المحتوى أيضاً
    $('#annualReportsSection').hide();
    $('#penaltiesSection').hide();
    $('#unpaidLeavesSection').hide();
    $('.print-btn').hide();
    hideAllAlerts();
    $('#ministryNumberInput').focus();
}
</script>
{% endblock %}
