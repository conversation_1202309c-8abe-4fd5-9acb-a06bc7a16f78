{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير المسمى الوظيفي - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير المسمى الوظيفي</h1>
        <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للوحة التقارير
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.position.id_for_label }}">المسمى الوظيفي</label>
                                    {{ form.position }}
                                    {% if form.position.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.position.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.gender.id_for_label }}">الجنس</label>
                                    {{ form.gender }}
                                    {% if form.gender.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.gender.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-file-export"></i> إنشاء التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">تقرير المسمى الوظيفي</h5>
                        <p>يعرض هذا التقرير معلومات عن الموظفين حسب المسمى الوظيفي، ويمكن تصفية النتائج حسب المسمى الوظيفي والجنس.</p>
                        <hr>
                        <p class="mb-0">يتم تصدير التقرير بصيغة Excel ويحتوي على المعلومات التالية:</p>
                        <ul>
                            <li>الرقم الوزاري</li>
                            <li>الاسم الكامل</li>
                            <li>المسمى الوظيفي</li>
                            <li>القسم</li>
                            <li>تاريخ التعيين</li>
                            <li>الجنس</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
