from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from django.core.paginator import Paginator
from django.conf import settings as django_settings
import pandas as pd
import io
import pytz
import time
import json
import os
import mimetypes
from datetime import date
from .forms import InternalTransferForm, ApprovedFormForm, TechnicalTransferForm, AdminInternalTransferForm
from .models import InternalTransfer, SystemSettings, ApprovedForm, TechnicalTransfer, EmployeeTransfer
from employees.models import Employee
from employment.models import Employment, Position, EmployeePosition, Department
from ranks.models import EmployeeRank


def format_job_title(position):
    """
    Format job title with correct prefix based on position name.

    Args:
        position (str): The position name

    Returns:
        str: Formatted job title with correct prefix
    """
    print(f"FORMAT JOB TITLE - Input position: '{position}'")

    if not position:
        print("FORMAT JOB TITLE - Empty position, returning 'الموظف'")
        return "الموظف"

    # List of positions that need "ال" prefix - ONLY معلم and حارس need "ال"
    positions_with_al = ["معلم", "حارس"]

    # First, remove any existing "ال" prefix
    if position.startswith("ال"):
        position_without_al = position[2:]
        print(f"FORMAT JOB TITLE - Removed 'ال' prefix: '{position_without_al}'")
    else:
        position_without_al = position
        print(f"FORMAT JOB TITLE - No 'ال' prefix to remove: '{position_without_al}'")

    # Check if the position is in the list that needs "ال"
    for pos in positions_with_al:
        if position_without_al == pos or position_without_al.startswith(pos + " "):
            # This position needs "ال"
            result = "ال" + position_without_al
            print(f"FORMAT JOB TITLE - Position needs 'ال', returning: '{result}'")
            return result

    # If we get here, the position doesn't need "ال"
    print(f"FORMAT JOB TITLE - Position doesn't need 'ال', returning: '{position_without_al}'")
    return position_without_al

def convert_to_local_timezone(obj):
    """
    تحويل حقول التاريخ في الكائن إلى المنطقة الزمنية المحلية لنظام التشغيل
    """
    # استخدام المنطقة الزمنية المحددة في إعدادات Django (Asia/Amman)
    # هذا يضمن استخدام نفس المنطقة الزمنية بغض النظر عن نظام التشغيل
    local_timezone = timezone.get_current_timezone()
    print(f"Using Django timezone: {local_timezone}")

    # طباعة معلومات عن المنطقة الزمنية
    now = timezone.now()
    utc_now = now.astimezone(pytz.UTC)
    local_now = now.astimezone(local_timezone)
    print(f"Current UTC time: {utc_now}")
    print(f"Current local time: {local_now}")
    print(f"Offset from UTC: {local_now.utcoffset()}")

    # تحويل التواريخ إلى المنطقة الزمنية المحلية
    if hasattr(obj, 'created_at') and obj.created_at and obj.created_at.tzinfo is not None:
        original_created_at = obj.created_at
        obj.created_at = obj.created_at.astimezone(local_timezone)
        print(f"Converted created_at from {original_created_at} to local time: {obj.created_at}")

    if hasattr(obj, 'updated_at') and obj.updated_at and obj.updated_at.tzinfo is not None:
        original_updated_at = obj.updated_at
        obj.updated_at = obj.updated_at.astimezone(local_timezone)
        print(f"Converted updated_at from {original_updated_at} to local time: {obj.updated_at}")

    return obj

def home_view(request):
    """View for the home page"""
    return render(request, 'home/home.html')

def important_links_view(request):
    """View for the important links page"""
    from .models import ImportantLink
    links = ImportantLink.objects.filter(is_active=True).order_by('order', 'name')
    return render(request, 'home/important_links.html', {'links': links})

def search_employee(request):
    """API view to search for an employee by ministry number, national ID, and ID number"""
    ministry_number = request.GET.get('ministry_number', '')
    national_id = request.GET.get('national_id', '')
    id_number = request.GET.get('id_number', '')

    print(f"Searching for employee with ministry number: {ministry_number}, national ID: {national_id}, ID number: {id_number}")

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    # Remove requirement for national_id and id_number - allow employees without these
    # if not national_id:
    #     return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوطني'})

    # if not id_number:
    #     return JsonResponse({'success': False, 'error': 'الرجاء إدخال رقم الهوية'})

    try:
        # Find employee by ministry number
        employee = Employee.objects.get(ministry_number=ministry_number)
        print(f"Found employee: {employee.full_name}")

        # Verify national ID
        if employee.national_id and employee.national_id != national_id:
            return JsonResponse({
                'success': False,
                'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة الرقم الوطني المدخل والمحاولة مرة أخرى.'
            })

        # Check if employee has identification data with ID number
        from employment.models import EmployeeIdentification, BtecTeacher
        try:
            identification = EmployeeIdentification.objects.get(employee=employee)

            # Verify ID number
            if identification.id_number != id_number:
                return JsonResponse({
                    'success': False,
                    'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة رقم الهوية المدخل والمحاولة مرة أخرى.'
                })

            if not identification.id_number:
                return JsonResponse({
                    'success': False,
                    'error': 'البيانات المدخلة غير مكتملة في النظام. يرجى مراجعة قسم شؤون الموظفين لاستكمال البيانات المطلوبة.'
                })
        except EmployeeIdentification.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'البيانات المدخلة غير مكتملة في النظام. يرجى مراجعة قسم شؤون الموظفين لاستكمال البيانات المطلوبة.'
            })

        # Check if employee is a BTEC teacher
        btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
        if btec_teacher:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.'
            })

        # Check if employee already has a transfer request in the current year
        from datetime import date
        current_year = date.today().year
        existing_transfer = InternalTransfer.objects.filter(
            ministry_number=ministry_number,
            created_at__year=current_year
        ).order_by('-created_at').first()

        if existing_transfer:
            print(f"Employee already has a transfer request in {current_year}: {existing_transfer.id}")
            return JsonResponse({
                'success': False,
                'error': 'لديك طلب نقل داخلي مسبق خلال هذه السنة. لا يمكنك تقديم طلب جديد، ولكن يمكنك تعديل طلبك الحالي.',
                'has_existing_transfer': True,
                'transfer': {
                    'id': existing_transfer.id,
                    'employee_name': existing_transfer.employee_name,
                    'edit_token': existing_transfer.edit_token,
                    'created_at': existing_transfer.created_at.strftime('%Y-%m-%d')
                }
            })

        # Get latest position
        latest_position = employee.get_latest_position()
        print(f"Latest position: {latest_position}")

        # Get latest rank
        latest_rank = EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained').first()
        latest_rank_name = latest_rank.rank_type.name if latest_rank else '-'
        print(f"Latest rank: {latest_rank_name}")

        # Calculate actual service
        actual_service = "غير محدد"
        if employee.hire_date:
            today = date.today()
            years = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
            months = today.month - employee.hire_date.month
            if months < 0:
                months += 12
            actual_service = f"{years} سنة و {months} شهر"
            print(f"Actual service: {actual_service}")

        # Prepare response data
        response_data = {
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'national_id': employee.national_id,
                'gender': employee.gender,
                'qualification': employee.qualification,
                'specialization': employee.specialization,
                'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                'actual_service': actual_service,
                'department': employee.school,
                'last_position': latest_position,
                'last_rank': latest_rank_name,
                'address': employee.address,
                'phone_number': employee.phone_number
            }
        }

        print(f"Response data: {response_data}")

        # Return employee data
        return JsonResponse(response_data)
    except Employee.DoesNotExist:
        print(f"Employee not found with ministry number: {ministry_number}")
        return JsonResponse({'success': False, 'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة البيانات المدخلة والمحاولة مرة أخرى.'})
    except Exception as e:
        print(f"Error searching for employee: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})

def internal_transfer_view(request):
    """View for the internal transfer page"""
    # Check if internal transfer is enabled
    settings = SystemSettings.get_settings()
    print(f"Internal transfer view called. Enabled: {settings.internal_transfer_enabled}")

    if not settings.internal_transfer_enabled:
        print(f"Internal transfer is disabled. Showing closed page with message: {settings.internal_transfer_message}")
        return render(request, 'home/internal_transfer_closed_new.html', {
            'message': settings.internal_transfer_message
        })

    print("Internal transfer is enabled. Showing form page.")
    if request.method == 'POST':
        form = InternalTransferForm(request.POST)
        if form.is_valid():
            transfer = form.save()
            messages.success(request, _(
                'تم تقديم طلب النقل الداخلي بنجاح. سيتم تخزين طلبك وخيارات النقل وترتيبها حسب الخدمة الفعلية. '
                f'يمكنك تعديل طلبك من خلال الرابط: /internal-transfer/edit/{transfer.edit_token}/'
            ))
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    else:
        form = InternalTransferForm()

    return render(request, 'home/internal_transfer.html', {'form': form})

def internal_transfer_success(request, token):
    """View for the internal transfer success page"""
    transfer = get_object_or_404(InternalTransfer, edit_token=token)
    return render(request, 'home/internal_transfer_success.html', {'transfer': transfer})

def internal_transfer_edit(request, token):
    """View for editing an internal transfer request"""
    # Check if internal transfer is enabled
    settings = SystemSettings.get_settings()
    if not settings.internal_transfer_enabled:
        return render(request, 'home/internal_transfer_closed_new.html', {
            'message': settings.internal_transfer_message
        })

    transfer = get_object_or_404(InternalTransfer, edit_token=token)

    # Check if employee has identification data with ID number
    try:
        employee = Employee.objects.get(ministry_number=transfer.ministry_number)
        try:
            from employment.models import EmployeeIdentification, BtecTeacher
            identification = EmployeeIdentification.objects.get(employee=employee)
            if not identification.id_number:
                messages.error(request, f'الموظف {employee.full_name} ليس لديه رقم هوية مسجل في النظام. يرجى إضافة رقم الهوية أولاً.')
                return redirect('home:internal_transfer_success', token=transfer.edit_token)
        except EmployeeIdentification.DoesNotExist:
            messages.error(request, f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام. يرجى إضافة البيانات التعريفية أولاً.')
            return redirect('home:internal_transfer_success', token=transfer.edit_token)

        # Check if employee is a BTEC teacher
        btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
        if btec_teacher:
            messages.error(request, f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.')
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    except Employee.DoesNotExist:
        # Si no se encuentra el empleado, permitir la edición de todos modos
        pass

    if request.method == 'POST':
        # Verificar el número nacional y el número de identificación si se proporcionan
        national_id = request.POST.get('national_id', '')
        id_number = request.POST.get('id_number', '')

        if national_id and id_number:
            try:
                employee = Employee.objects.get(ministry_number=transfer.ministry_number)

                # Verificar el número nacional
                if employee.national_id and employee.national_id != national_id:
                    messages.error(request, f'الرقم الوطني المدخل ({national_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})')
                    form = InternalTransferForm(instance=transfer)
                    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})

                # Verificar el número de identificación
                try:
                    identification = EmployeeIdentification.objects.get(employee=employee)
                    if identification.id_number != id_number:
                        messages.error(request, f'رقم الهوية المدخل ({id_number}) لا يتطابق مع رقم الهوية المسجل للموظف ({identification.id_number})')
                        form = InternalTransferForm(instance=transfer)
                        return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})
                except EmployeeIdentification.DoesNotExist:
                    messages.error(request, f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام.')
                    form = InternalTransferForm(instance=transfer)
                    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})
            except Employee.DoesNotExist:
                # Si no se encuentra el empleado, permitir la edición de todos modos
                pass

        form = InternalTransferForm(request.POST, instance=transfer)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث طلب النقل الداخلي بنجاح.'))
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    else:
        form = InternalTransferForm(instance=transfer)

    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer, 'verification_required': True})

def search_transfer_request(request):
    """API view to search for an internal transfer request by ministry number, national ID, and ID number"""
    ministry_number = request.GET.get('ministry_number', '')
    national_id = request.GET.get('national_id', '')
    id_number = request.GET.get('id_number', '')

    print(f"Searching for transfer request with ministry number: {ministry_number}, national ID: {national_id}, ID number: {id_number}")

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    if not national_id:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوطني'})

    if not id_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال رقم الهوية'})

    try:
        # Verify employee identity
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)

            # Verify national ID
            if employee.national_id and employee.national_id != national_id:
                return JsonResponse({
                    'success': False,
                    'error': f'الرقم الوطني المدخل ({national_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})'
                })

            # Verify ID number
            try:
                from employment.models import EmployeeIdentification, BtecTeacher
                identification = EmployeeIdentification.objects.get(employee=employee)

                if identification.id_number != id_number:
                    return JsonResponse({
                        'success': False,
                        'error': f'رقم الهوية المدخل ({id_number}) لا يتطابق مع رقم الهوية المسجل للموظف ({identification.id_number})'
                    })
            except EmployeeIdentification.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام.'
                })

            # Check if employee is a BTEC teacher
            btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
            if btec_teacher:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.'
                })
        except Employee.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

        # Find transfer request by ministry number
        transfer = InternalTransfer.objects.filter(ministry_number=ministry_number).order_by('-created_at').first()

        if not transfer:
            return JsonResponse({
                'success': False,
                'error': 'لم يتم العثور على طلب نقل داخلي لهذا الرقم الوزاري'
            })

        print(f"Found transfer request for: {transfer.employee_name}")

        # Return transfer data with edit token
        return JsonResponse({
            'success': True,
            'transfer': {
                'id': transfer.id,
                'employee_name': transfer.employee_name,
                'edit_token': transfer.edit_token,
                'created_at': transfer.created_at.strftime('%Y-%m-%d')
            }
        })
    except Exception as e:
        print(f"Error searching for transfer request: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})

def about_view(request):
    """View for the about page"""
    return render(request, 'home/about.html')


def search_view(request):
    """View for the search page"""
    query = request.GET.get('q', '')
    results = []

    if query:
        # تقسيم النص المدخل إلى كلمات للبحث عن كل كلمة بشكل منفصل
        search_terms = query.split()

        # إنشاء استعلام للبحث في الموظفين
        employee_query = Q()
        for term in search_terms:
            employee_query |= Q(full_name__icontains=term)
            employee_query |= Q(ministry_number__icontains=term)
            employee_query |= Q(national_id__icontains=term)
            employee_query |= Q(school__icontains=term)
            # إضافة حقول أخرى للبحث
            # البحث في العلاقات
            employee_query |= Q(positions__position__name__icontains=term)  # البحث في المسميات الوظيفية
            employee_query |= Q(specialization__icontains=term)
            employee_query |= Q(qualification__icontains=term)
            # لا يوجد حقل notes في نموذج Employee

        # البحث في الموظفين
        employees = Employee.objects.filter(employee_query).distinct()[:20]

        # إنشاء استعلام للبحث في الأقسام
        department_query = Q()
        for term in search_terms:
            department_query |= Q(name__icontains=term)
            department_query |= Q(description__icontains=term)
            department_query |= Q(workplace__icontains=term)

        # البحث في الأقسام
        departments = Department.objects.filter(department_query).distinct()[:10]

        # إنشاء استعلام للبحث في المسميات الوظيفية
        position_query = Q()
        for term in search_terms:
            position_query |= Q(name__icontains=term)
            position_query |= Q(description__icontains=term) if hasattr(Position, 'description') else Q()

        # البحث في المسميات الوظيفية
        positions = Position.objects.filter(position_query).distinct()[:10]

        # إنشاء استعلام للبحث في طلبات النقل الداخلي
        transfer_query = Q()
        for term in search_terms:
            transfer_query |= Q(employee_name__icontains=term)
            transfer_query |= Q(ministry_number__icontains=term)
            transfer_query |= Q(current_department__icontains=term)
            transfer_query |= Q(first_choice__icontains=term)
            transfer_query |= Q(second_choice__icontains=term)
            transfer_query |= Q(third_choice__icontains=term)
            transfer_query |= Q(specialization__icontains=term)
            transfer_query |= Q(qualification__icontains=term)
            # البحث في الملاحظات إذا كانت موجودة
            if hasattr(InternalTransfer, 'notes'):
                transfer_query |= Q(notes__icontains=term)

        # البحث في طلبات النقل الداخلي
        transfers = InternalTransfer.objects.filter(transfer_query).distinct()[:10]

        # إنشاء استعلام للبحث في النماذج المعتمدة
        form_query = Q()
        for term in search_terms:
            form_query |= Q(title__icontains=term)
            form_query |= Q(description__icontains=term)

        # البحث في النماذج المعتمدة
        forms = ApprovedForm.objects.filter(form_query).distinct()[:10]

        # Combine results
        results = {
            'employees': employees,
            'departments': departments,
            'positions': positions,
            'transfers': transfers,
            'forms': forms
        }

    return render(request, 'home/search_results.html', {
        'query': query,
        'results': results
    })


def approved_forms_list(request):
    """View for listing approved forms"""
    # Get all active approved forms
    forms = ApprovedForm.objects.filter(is_active=True).order_by('order', '-created_at')

    return render(request, 'home/approved_forms_list.html', {
        'forms': forms,
        'title': 'النماذج المعتمدة'
    })


@login_required
def approved_forms_admin(request):
    """View for managing approved forms (admin)"""
    # Get all approved forms
    forms = ApprovedForm.objects.all().order_by('order', '-created_at')

    # Paginate the results
    paginator = Paginator(forms, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'home/approved_forms_admin.html', {
        'forms': page_obj,
        'title': 'إدارة النماذج المعتمدة'
    })


@login_required
def approved_form_create(request):
    """View for creating a new approved form"""
    if request.method == 'POST':
        form = ApprovedFormForm(request.POST, request.FILES)
        if form.is_valid():
            approved_form = form.save()
            messages.success(request, 'تم إضافة النموذج بنجاح.')
            return redirect('home:approved_forms_admin')
    else:
        form = ApprovedFormForm()

    return render(request, 'home/approved_form_form.html', {
        'form': form,
        'title': 'إضافة نموذج جديد'
    })


@login_required
def approved_form_update(request, pk):
    """View for updating an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    if request.method == 'POST':
        form = ApprovedFormForm(request.POST, request.FILES, instance=approved_form)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث النموذج بنجاح.')
            return redirect('home:approved_forms_admin')
    else:
        form = ApprovedFormForm(instance=approved_form)

    return render(request, 'home/approved_form_form.html', {
        'form': form,
        'approved_form': approved_form,
        'title': 'تعديل النموذج'
    })


@login_required
def approved_form_delete(request, pk):
    """View for deleting an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    if request.method == 'POST':
        approved_form.delete()
        messages.success(request, 'تم حذف النموذج بنجاح.')
        return redirect('home:approved_forms_admin')

    return render(request, 'home/approved_form_confirm_delete.html', {
        'approved_form': approved_form,
        'title': 'حذف النموذج'
    })


def approved_form_download(request, pk):
    """View for downloading an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    # Get the file path
    file_path = approved_form.file.path

    # Check if file exists
    if os.path.exists(file_path):
        # Determine the content type
        content_type, encoding = mimetypes.guess_type(file_path)
        if content_type is None:
            content_type = 'application/octet-stream'

        # Open the file
        with open(file_path, 'rb') as file:
            response = HttpResponse(file.read(), content_type=content_type)

            # Set the Content-Disposition header to force download
            response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'

            return response

    # If file doesn't exist, return 404
    return HttpResponse('الملف غير موجود', status=404)

@login_required
def internal_transfer_list_view(request):
    """View for listing internal transfer requests (for admin users)"""
    # Import Employee model
    from employees.models import Employee

    # Get filters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    year_filter = request.GET.get('year', '')
    gender_filter = request.GET.get('gender', '')
    specialization_filter = request.GET.get('specialization', '')

    # Filter transfers
    transfers = InternalTransfer.objects.all().order_by('-created_at')

    # Update current_department for transfers that don't have it
    for transfer in transfers:
        if not transfer.current_department or transfer.current_department.strip() == '':
            try:
                employee = Employee.objects.get(ministry_number=transfer.ministry_number)
                if employee.department:
                    transfer.current_department = employee.department.name
                    transfer.save()
            except Employee.DoesNotExist:
                # If employee not found, keep the current value
                pass
            except Exception as e:
                # Handle any other errors silently
                pass

    if search_query:
        transfers = transfers.filter(
            Q(employee_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(current_department__icontains=search_query) |
            Q(first_choice__icontains=search_query)
        )

    if status_filter:
        transfers = transfers.filter(status=status_filter)

    if year_filter:
        transfers = transfers.filter(created_at__year=year_filter)

    if gender_filter:
        transfers = transfers.filter(gender=gender_filter)

    if specialization_filter:
        transfers = transfers.filter(specialization=specialization_filter)

    # Get unique years, genders and specializations for filters
    from django.db.models import Count
    years = InternalTransfer.objects.dates('created_at', 'year').order_by('-created_at')
    years = [date.year for date in years]

    genders = InternalTransfer.objects.values('gender').annotate(count=Count('id')).order_by('gender')

    specializations = InternalTransfer.objects.exclude(specialization__isnull=True).exclude(specialization='').values('specialization').annotate(count=Count('id')).order_by('specialization')

    # Update actual service for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"
        except Employee.DoesNotExist:
            # Keep the existing actual_service if employee not found
            pass

    # Get system settings
    settings = SystemSettings.get_settings()

    # Calculate counts by status
    pending_count = InternalTransfer.objects.filter(status='pending').count()
    approved_count = InternalTransfer.objects.filter(status='approved').count()
    rejected_count = InternalTransfer.objects.filter(status='rejected').count()

    # Get all school departments for the new department selection
    departments = Department.objects.filter(workplace='school').order_by('name')

    return render(request, 'home/internal_transfer_list.html', {
        'transfers': transfers,
        'search_query': search_query,
        'years': years,
        'genders': genders,
        'specializations': specializations,
        'year_filter': year_filter,
        'gender_filter': gender_filter,
        'specialization_filter': specialization_filter,
        'status_filter': status_filter,
        'settings': settings,
        'pending_count': pending_count,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
        'departments': departments
    })

@login_required
def internal_transfer_detail_view(request, pk):
    """View for viewing internal transfer request details (for admin users)"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)

    if request.method == 'POST':
        status = request.POST.get('status')
        notes = request.POST.get('notes')

        if status:
            transfer.status = status
        if notes is not None:
            transfer.notes = notes

        transfer.save()
        messages.success(request, _('تم تحديث حالة طلب النقل الداخلي بنجاح.'))
        return redirect('home:internal_transfer_list')

    # تحويل التواريخ إلى المنطقة الزمنية المحلية لنظام التشغيل
    transfer = convert_to_local_timezone(transfer)

    # Inicializar las propiedades para evitar errores
    transfer.actual_service_detailed = None
    transfer.latest_rank_details = None

    # Imprimir información de depuración
    print(f"\n\nDEBUG INFO FOR TRANSFER ID {pk}:")
    print(f"Ministry Number: {transfer.ministry_number}")
    print(f"Employee Name: {transfer.employee_name}")
    print(f"Last Rank: {transfer.last_rank}")
    print(f"Actual Service: {transfer.actual_service}")

    # الحصول على الخدمة الفعلية وآخر رتبة من النظام
    try:
        from employees.models import Employee
        from leaves.models import Leave, LeaveType
        from ranks.models import EmployeeRank, RankType
        from datetime import date
        import sys

        # Buscar el empleado por número ministerial
        employee = Employee.objects.filter(ministry_number=transfer.ministry_number).first()

        if employee:
            print(f"Found employee: {employee.full_name} (ID: {employee.id})")

            # Verificar si hay rangos para este empleado
            all_ranks = list(EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained'))
            print(f"Found {len(all_ranks)} ranks for employee")

            # Obtener el último rango
            if all_ranks:
                latest_rank = all_ranks[0]
                print(f"Latest rank: {latest_rank}")

                # Crear un diccionario con los detalles de la clasificación
                transfer.latest_rank_details = {
                    'name': latest_rank.rank_type.name,
                    'date': latest_rank.date_obtained.strftime('%Y-%m-%d'),
                    'degree': getattr(latest_rank, 'degree', "غير محدد")
                }
                print(f"Set latest_rank_details: {transfer.latest_rank_details}")
            else:
                print("No ranks found for employee")
                # Usar el valor existente en transfer.last_rank si está disponible
                if transfer.last_rank and transfer.last_rank != '-':
                    transfer.latest_rank_details = {
                        'name': transfer.last_rank,
                        'date': 'غير متوفر',
                        'degree': 'غير متوفر'
                    }

            # حساب الخدمة الفعلية إذا كان تاريخ التعيين متوفر
            if employee.hire_date:
                print(f"Employee hire date: {employee.hire_date}")

                # حساب إجمالي الأيام بين تاريخ التعيين واليوم
                today = date.today()
                total_days = (today - employee.hire_date).days

                # الحصول على الإجازات بدون راتب
                unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
                unpaid_leave_days = 0

                if unpaid_leave_type:
                    unpaid_leaves = Leave.objects.filter(
                        employee=employee,
                        leave_type=unpaid_leave_type,
                        status='approved'
                    )

                    # حساب مجموع أيام الإجازات بدون راتب
                    for leave in unpaid_leaves:
                        unpaid_leave_days += leave.days_count

                # حساب أيام الخدمة الفعلية
                actual_service_days = total_days - unpaid_leave_days

                # تحويل إلى سنوات وشهور وأيام
                years = actual_service_days // 365
                remaining_days = actual_service_days % 365
                months = remaining_days // 30
                days = remaining_days % 30

                # تخزين الخدمة الفعلية المحسوبة
                transfer.actual_service_detailed = f"{years} سنة, {months} شهر, {days} يوم"
                print(f"Set actual_service_detailed: {transfer.actual_service_detailed}")
            else:
                print("Employee has no hire date")
                # Usar el valor existente en transfer.actual_service si está disponible
                if transfer.actual_service and transfer.actual_service != '-':
                    transfer.actual_service_detailed = transfer.actual_service
                else:
                    transfer.actual_service_detailed = "غير متوفر"
        else:
            print(f"No employee found with ministry number: {transfer.ministry_number}")
            # Usar los valores existentes en transfer si están disponibles
            if transfer.actual_service and transfer.actual_service != '-':
                transfer.actual_service_detailed = transfer.actual_service
            else:
                transfer.actual_service_detailed = "غير متوفر"

            if transfer.last_rank and transfer.last_rank != '-':
                transfer.latest_rank_details = {
                    'name': transfer.last_rank,
                    'date': 'غير متوفر',
                    'degree': 'غير متوفر'
                }
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

        # Usar los valores existentes en transfer si están disponibles
        if transfer.actual_service and transfer.actual_service != '-':
            transfer.actual_service_detailed = transfer.actual_service
        else:
            transfer.actual_service_detailed = "غير متوفر"

        if transfer.last_rank and transfer.last_rank != '-':
            transfer.latest_rank_details = {
                'name': transfer.last_rank,
                'date': 'غير متوفر',
                'degree': 'غير متوفر'
            }

    return render(request, 'home/internal_transfer_detail.html', {'transfer': transfer})

@login_required
def admin_internal_transfer_edit(request, pk):
    """View for admin to edit internal transfer request"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)

    if request.method == 'POST':
        # Update transfer fields
        transfer.employee_name = request.POST.get('employee_name', transfer.employee_name)
        transfer.ministry_number = request.POST.get('ministry_number', transfer.ministry_number)
        transfer.employee_id = request.POST.get('employee_id', transfer.employee_id)
        transfer.specialization = request.POST.get('specialization', transfer.specialization)
        transfer.actual_service = request.POST.get('actual_service', transfer.actual_service)
        transfer.current_department = request.POST.get('current_department', transfer.current_department)
        transfer.first_choice = request.POST.get('first_choice', transfer.first_choice)
        transfer.second_choice = request.POST.get('second_choice', transfer.second_choice)
        transfer.third_choice = request.POST.get('third_choice', transfer.third_choice)
        transfer.status = request.POST.get('status', transfer.status)
        transfer.notes = request.POST.get('notes', transfer.notes)

        try:
            transfer.save()
            messages.success(request, 'تم تحديث طلب النقل الداخلي بنجاح.')
            return redirect('home:internal_transfer_list')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء التحديث: {str(e)}')

    # Get all departments for choices
    departments = Department.objects.filter(workplace='school').order_by('name')

    context = {
        'transfer': transfer,
        'departments': departments,
    }

    return render(request, 'home/admin_internal_transfer_edit.html', context)

@login_required
def internal_transfer_delete(request, pk):
    """View for deleting an internal transfer request"""
    print(f"Delete view called for transfer ID: {pk}, Method: {request.method}")

    # Get the transfer object
    try:
        transfer = get_object_or_404(InternalTransfer, pk=pk)
    except:
        messages.error(request, 'طلب النقل الداخلي غير موجود.')
        return redirect('home:internal_transfer_list')

    # Store information for logging
    ministry_number = transfer.ministry_number
    employee_name = transfer.employee_name

    print(f"Found transfer for deletion: {employee_name} (ID: {pk}, Ministry #: {ministry_number})")

    # If this is a POST request, process the deletion
    if request.method == 'POST':
        try:
            # Delete the transfer
            transfer.delete()
            print(f"Successfully deleted transfer request for {employee_name} (Ministry #: {ministry_number})")
            messages.success(request, f'تم حذف طلب النقل الداخلي للموظف {employee_name} بنجاح.')
            return redirect('home:internal_transfer_list')
        except Exception as e:
            print(f"Error deleting transfer request with ID {pk}: {str(e)}")
            messages.error(request, 'حدث خطأ أثناء محاولة حذف طلب النقل الداخلي.')
            return redirect('home:internal_transfer_list')

    # إذا كان هذا طلب GET، قم بعرض صفحة تأكيد الحذف
    # تحويل التواريخ إلى المنطقة الزمنية المحلية لنظام التشغيل
    transfer = convert_to_local_timezone(transfer)

    return render(request, 'home/internal_transfer_confirm_delete.html', {'transfer': transfer})

@login_required
def toggle_internal_transfer(request):
    """View for toggling internal transfer page"""
    settings = SystemSettings.get_settings()

    print(f"Toggle internal transfer view called. Method: {request.method}")
    print(f"Current settings: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")

    if request.method == 'POST':
        action = request.POST.get('action')
        message = request.POST.get('message', '')

        print(f"POST data: action={action}, message={message}")

        if action == 'enable':
            # Force update to True
            settings.internal_transfer_enabled = True
            settings.save()
            print(f"Enabled internal transfer. New settings: Enabled={settings.internal_transfer_enabled}")
            messages.success(request, _('تم تفعيل صفحة النقل الداخلي بنجاح.'))
        elif action == 'disable':
            # Force update to False
            settings.internal_transfer_enabled = False
            if message:
                settings.internal_transfer_message = message
            settings.save()
            print(f"Disabled internal transfer. New settings: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")
            messages.success(request, _('تم تعطيل صفحة النقل الداخلي بنجاح.'))

        # Verify the update was successful
        settings.refresh_from_db()
        print(f"After save: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")

    return redirect('home:internal_transfer_list')

@login_required
def enable_internal_transfer(request):
    """View for enabling internal transfer page"""
    settings = SystemSettings.get_settings()
    settings.internal_transfer_enabled = True
    settings.save()
    messages.success(request, _('تم تفعيل صفحة النقل الداخلي بنجاح.'))
    return redirect('home:internal_transfer_list')

@login_required
def disable_internal_transfer(request):
    """View for disabling internal transfer page"""
    settings = SystemSettings.get_settings()
    settings.internal_transfer_enabled = False
    message = request.POST.get('message', '')
    if message:
        settings.internal_transfer_message = message
    settings.save()
    messages.success(request, _('تم تعطيل صفحة النقل الداخلي بنجاح.'))
    return redirect('home:internal_transfer_list')

@login_required
def dashboard_view(request):
    """View for the dashboard page with employee statistics"""
    # Get employee statistics by gender
    gender_stats = Employee.objects.values('gender').annotate(count=Count('id')).order_by('gender')
    gender_data = {
        'labels': [item['gender'] for item in gender_stats],
        'data': [item['count'] for item in gender_stats],
        'label_names': {
            'male': 'ذكر',
            'female': 'أنثى'
        }
    }

    # Get employee statistics by qualification
    qualification_stats = Employee.objects.values('qualification').annotate(count=Count('id')).order_by('-count')[:10]
    qualification_data = {
        'labels': [item['qualification'] for item in qualification_stats],
        'data': [item['count'] for item in qualification_stats]
    }

    # Get employee statistics by position
    position_stats = EmployeePosition.objects.values('position__name').annotate(count=Count('id')).order_by('-count')[:10]
    position_data = {
        'labels': [item['position__name'] for item in position_stats],
        'data': [item['count'] for item in position_stats]
    }

    context = {
        'gender_data': json.dumps(gender_data),
        'qualification_data': json.dumps(qualification_data),
        'position_data': json.dumps(position_data),
        'total_employees': Employee.objects.count(),
        'total_positions': Position.objects.count(),
        'total_departments': Employment.objects.values('department').distinct().count()
    }

    return render(request, 'home/dashboard.html', context)

@login_required
def analytics_dashboard_view(request):
    """View for the analytics dashboard with advanced statistics and visualizations"""
    # Import Count again to ensure it's available in this function scope
    from django.db.models import Count

    # Get employee statistics by gender
    gender_stats = Employee.objects.values('gender').annotate(count=Count('id')).order_by('gender')
    gender_data = {
        'labels': [item['gender'] for item in gender_stats],
        'data': [item['count'] for item in gender_stats],
        'label_names': {
            'male': 'ذكر',
            'female': 'أنثى'
        }
    }

    # Get employee statistics by qualification
    qualification_stats = Employee.objects.values('qualification').annotate(count=Count('id')).order_by('-count')[:10]
    qualification_data = {
        'labels': [item['qualification'] for item in qualification_stats],
        'data': [item['count'] for item in qualification_stats]
    }

    # Get employee statistics by position
    position_stats = EmployeePosition.objects.values('position__name').annotate(count=Count('id')).order_by('-count')[:10]
    position_data = {
        'labels': [item['position__name'] for item in position_stats],
        'data': [item['count'] for item in position_stats]
    }

    # Get employee growth data (simulated for now)
    from datetime import datetime, timedelta
    today = datetime.now()
    months = []
    counts = []

    # Simulate growth data for the last 6 months
    total_employees = Employee.objects.count()
    base_count = max(total_employees - 50, 0)  # Start with a reasonable base

    for i in range(5, -1, -1):
        month_date = today - timedelta(days=30*i)
        month_name = month_date.strftime('%B')

        # Translate month names to Arabic
        arabic_months = {
            'January': 'يناير', 'February': 'فبراير', 'March': 'مارس',
            'April': 'أبريل', 'May': 'مايو', 'June': 'يونيو',
            'July': 'يوليو', 'August': 'أغسطس', 'September': 'سبتمبر',
            'October': 'أكتوبر', 'November': 'نوفمبر', 'December': 'ديسمبر'
        }

        months.append(arabic_months.get(month_name, month_name))

        # Simulate growth pattern
        if i == 0:
            counts.append(total_employees)
        else:
            counts.append(base_count + int((total_employees - base_count) * (5-i)/5))

    growth_data = {
        'labels': months,
        'data': counts
    }

    # Calculate employee growth percentage
    employee_growth = 5  # Default value
    if len(counts) >= 2:
        if counts[0] > 0:
            employee_growth = round(((counts[-1] - counts[0]) / counts[0]) * 100, 1)

    # Get departments with employee counts
    departments = Employment.objects.values('department__name').annotate(
        count=Count('employee', distinct=True)
    ).order_by('-count')

    total_count = sum(dept['count'] for dept in departments)

    # Calculate gender distribution by department
    departments_data = []
    for dept in departments:
        if not dept['department__name']:
            continue

        dept_name = dept['department__name']
        dept_count = dept['count']

        # Get gender counts for this department
        gender_counts = Employment.objects.filter(
            department__name=dept_name
        ).values(
            'employee__gender'
        ).annotate(
            count=Count('employee', distinct=True)
        )

        male_count = 0
        female_count = 0

        for gender_count in gender_counts:
            if gender_count['employee__gender'] == 'male':
                male_count = gender_count['count']
            elif gender_count['employee__gender'] == 'female':
                female_count = gender_count['count']

        # Calculate percentages
        percentage = round((dept_count / total_count) * 100, 1) if total_count > 0 else 0
        male_percentage = round((male_count / dept_count) * 100) if dept_count > 0 else 0
        female_percentage = round((female_count / dept_count) * 100) if dept_count > 0 else 0

        departments_data.append({
            'name': dept_name,
            'count': dept_count,
            'percentage': percentage,
            'male_count': male_count,
            'female_count': female_count,
            'male_percentage': male_percentage,
            'female_percentage': female_percentage
        })

    # Get active leaves count
    from django.utils import timezone
    from leaves.models import Leave
    today = timezone.now().date()
    active_leaves = Leave.objects.filter(
        start_date__lte=today,
        end_date__gte=today,
        status='approved'
    ).count()

    # Calculate leave percentage
    total_employees = Employee.objects.count()
    leave_percentage = round((active_leaves / total_employees) * 100, 1) if total_employees > 0 else 0

    # Count active departments (departments with employees)
    departments_with_employees = Department.objects.filter(
        id__in=Employment.objects.values('department').distinct()
    ).count()

    # Count positions with employees
    positions_with_employees = Employment.objects.values('position').distinct().count()

    context = {
        'gender_data': json.dumps(gender_data),
        'qualification_data': json.dumps(qualification_data),
        'position_data': json.dumps(position_data),
        'growth_data': json.dumps(growth_data),
        'total_employees': Employee.objects.count(),
        'total_positions': Position.objects.count(),
        'total_departments': Department.objects.count(),
        'departments_data': departments_data,
        'active_leaves': active_leaves,
        'leave_percentage': leave_percentage,
        'employee_growth': employee_growth,
        'departments_with_employees': departments_with_employees,
        'positions_with_employees': positions_with_employees
    }

    return render(request, 'home/analytics_dashboard.html', context)

@login_required
def delete_all_internal_transfers(request):
    """Delete all internal transfer requests"""
    if request.method == 'POST':
        try:
            # Get confirmation from the form
            confirmation = request.POST.get('confirmation', '')

            if confirmation != 'DELETE_ALL':
                messages.error(request, 'يرجى كتابة "DELETE_ALL" للتأكيد على حذف جميع الطلبات.')
                return redirect('home:internal_transfer_list')

            # Count transfers before deletion
            count = InternalTransfer.objects.count()

            # Delete all transfers
            InternalTransfer.objects.all().delete()

            messages.success(request, f'تم حذف جميع طلبات النقل الداخلي بنجاح. عدد الطلبات المحذوفة: {count}')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء محاولة حذف جميع طلبات النقل الداخلي: {str(e)}')

    return redirect('home:internal_transfer_list')

@login_required
def print_transfer_letters(request):
    """View for printing internal transfer letters"""
    # Get current year
    current_year = timezone.now().year

    # Get transfers for current year
    transfers = InternalTransfer.objects.filter(
        created_at__year=current_year
    ).order_by('specialization', '-actual_service')

    # Get all school departments
    departments = Department.objects.filter(workplace='school').order_by('name')

    # Update actual service and current department for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)

            # Update actual service
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"

            # Update current department from Employee model
            current_dept = employee.get_current_department()
            if current_dept and current_dept != '-':
                transfer.current_department = current_dept
            elif employee.school:
                # Fallback to school field if no employment department
                transfer.current_department = str(employee.school)

        except Employee.DoesNotExist:
            # Keep the existing data if employee not found
            pass

    # Handle AJAX request to save new department
    if request.method == 'POST' and (request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.POST.get('X-Requested-With') == 'XMLHttpRequest'):
        transfer_id = request.POST.get('transfer_id')
        department_id = request.POST.get('department_id')

        if not transfer_id or not department_id:
            return JsonResponse({
                'success': False,
                'error': 'معرف النقل أو معرف القسم غير صالح'
            })

        try:
            transfer = InternalTransfer.objects.get(id=transfer_id)
            department = Department.objects.get(id=department_id)
            notes = request.POST.get('notes', '')

            # Update the notes in the database
            transfer.notes = notes
            transfer.save()

            # Store in the session
            if 'transfer_departments' not in request.session:
                request.session['transfer_departments'] = {}

            request.session['transfer_departments'][str(transfer_id)] = {
                'department_id': department_id,
                'department_name': department.name,
                'notes': notes
            }
            request.session.modified = True

            # Log the saved department for debugging
            print(f"Saved department for transfer {transfer_id}: {department.name}")
            print(f"Saved notes for transfer {transfer_id}: {notes}")
            print(f"Session data: {request.session['transfer_departments']}")

            # Return JSON response for AJAX requests
            return JsonResponse({
                'success': True,
                'message': f'تم تعيين {transfer.employee_name} إلى {department.name} بنجاح',
                'department_name': department.name,
                'notes': notes
            })
        except InternalTransfer.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'لم يتم العثور على طلب النقل بالمعرف {transfer_id}'
            })
        except Department.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'لم يتم العثور على القسم بالمعرف {department_id}'
            })
        except Exception as e:
            # Log the error for debugging
            import traceback
            print(f"Error saving department: {str(e)}")
            print(traceback.format_exc())

            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ غير متوقع: {str(e)}'
            })

    # Add new_department from session
    if 'transfer_departments' in request.session:
        for transfer in transfers:
            transfer_id_str = str(transfer.id)
            if transfer_id_str in request.session['transfer_departments']:
                # Add new_department as a dynamic attribute
                transfer.new_department = request.session['transfer_departments'][transfer_id_str]['department_name']

                # Notes are already in the database, but we'll update from session if available
                if 'notes' in request.session['transfer_departments'][transfer_id_str]:
                    session_notes = request.session['transfer_departments'][transfer_id_str]['notes']
                    # Only update if session notes are different from database notes
                    if session_notes != transfer.notes:
                        transfer.notes = session_notes
                        # Save to database to ensure consistency
                        transfer.save()

    # Render the print template
    return render(request, 'home/internal_transfer_print.html', {
        'transfers': transfers,
        'departments': departments,
        'current_year': current_year,
        'title': 'طباعة كتب النقل الداخلي'
    })

@login_required
def print_single_transfer_letter(request, transfer_id):
    """View for printing a single internal transfer letter"""
    # Get the transfer
    transfer = get_object_or_404(InternalTransfer, id=transfer_id)

    # Get new department from session
    new_department = ""
    new_department_obj = None
    current_department_obj = None
    notes = ""

    if 'transfer_departments' in request.session and str(transfer_id) in request.session['transfer_departments']:
        new_department = request.session['transfer_departments'][str(transfer_id)]['department_name']
        if 'notes' in request.session['transfer_departments'][str(transfer_id)]:
            notes = request.session['transfer_departments'][str(transfer_id)]['notes']

        # Try to get new department object for workplace determination
        if new_department:
            try:
                # Try exact match first
                new_department_obj = Department.objects.filter(name=new_department).first()
                if not new_department_obj:
                    # Try partial match
                    new_department_obj = Department.objects.filter(name__icontains=new_department).first()
                if not new_department_obj:
                    # Try reverse partial match
                    for dept in Department.objects.all():
                        if new_department.strip() in dept.name or dept.name in new_department.strip():
                            new_department_obj = dept
                            break
            except Exception as e:
                print(f"Error finding new department: {e}")
                new_department_obj = None
        else:
            new_department_obj = None

    # Try to get current department object for workplace determination
    try:
        # Try exact match first
        current_department_obj = Department.objects.filter(name=transfer.current_department).first()
        if not current_department_obj:
            # Try partial match
            current_department_obj = Department.objects.filter(name__icontains=transfer.current_department).first()
        if not current_department_obj:
            # Try reverse partial match
            for dept in Department.objects.all():
                if transfer.current_department in dept.name or dept.name in transfer.current_department:
                    current_department_obj = dept
                    break
    except Exception as e:
        print(f"Error finding current department: {e}")
        current_department_obj = None

    # Get employee information for position handling
    try:
        employee = Employee.objects.get(ministry_number=transfer.ministry_number)
        position = employee.get_latest_position() or ""

        # Format job title
        job_title = format_job_title(position)

        print(f"Single Transfer Letter - Original Position: {position}")
        print(f"Single Transfer Letter - Formatted Job Title: {job_title}")
        print(f"Single Transfer Letter - Current Department: '{transfer.current_department}'")
        print(f"Single Transfer Letter - Current Department Object: {current_department_obj}")
        if current_department_obj:
            print(f"Single Transfer Letter - Current Department Workplace: {current_department_obj.workplace}")
            print(f"Single Transfer Letter - Current Department Name: {current_department_obj.name}")
        else:
            print("Single Transfer Letter - No current department object found")
        print(f"Single Transfer Letter - New Department: '{new_department}'")
        print(f"Single Transfer Letter - New Department Object: {new_department_obj}")
        if new_department_obj:
            print(f"Single Transfer Letter - New Department Workplace: {new_department_obj.workplace}")
    except Employee.DoesNotExist:
        job_title = "الموظف"
        print("Single Transfer Letter - Employee not found")

    # Render the single transfer letter template
    return render(request, 'home/single_transfer_letter.html', {
        'transfer': transfer,
        'new_department': new_department,
        'new_department_obj': new_department_obj,
        'current_department_obj': current_department_obj,
        'notes': notes,
        'job_title': job_title
    })

@login_required
def print_transfer_summary(request):
    """View for printing internal transfer summary"""
    # Get current year
    current_year = timezone.now().year

    # Get transfers for current year
    transfers = InternalTransfer.objects.filter(
        created_at__year=current_year
    ).order_by('specialization', '-actual_service')

    # Add new_department from session
    if 'transfer_departments' in request.session:
        for transfer in transfers:
            transfer_id_str = str(transfer.id)
            if transfer_id_str in request.session['transfer_departments']:
                # Add new_department as a dynamic attribute
                transfer.new_department = request.session['transfer_departments'][transfer_id_str]['department_name']

                # Notes are already in the database, but we'll update from session if available
                if 'notes' in request.session['transfer_departments'][transfer_id_str]:
                    session_notes = request.session['transfer_departments'][transfer_id_str]['notes']
                    # Only update if session notes are different from database notes
                    if session_notes != transfer.notes:
                        transfer.notes = session_notes
                        # Save to database to ensure consistency
                        transfer.save()

    # Filter transfers to only include those with a new department
    transfers_with_new_department = [t for t in transfers if hasattr(t, 'new_department')]

    # Get today's date
    from datetime import date
    today_date = date.today().strftime('%Y-%m-%d')

    # Render the summary template
    return render(request, 'home/internal_transfer_summary.html', {
        'transfers': transfers_with_new_department,
        'current_year': current_year,
        'today_date': today_date
    })

@login_required
def export_internal_transfers_to_excel(request):
    """Export internal transfers to Excel"""
    # Get filters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    year_filter = request.GET.get('year', '')
    gender_filter = request.GET.get('gender', '')
    specialization_filter = request.GET.get('specialization', '')

    # Filter transfers
    transfers = InternalTransfer.objects.all().order_by('-created_at')

    if search_query:
        transfers = transfers.filter(
            Q(employee_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(current_department__icontains=search_query) |
            Q(first_choice__icontains=search_query)
        )

    if status_filter:
        transfers = transfers.filter(status=status_filter)

    if year_filter:
        transfers = transfers.filter(created_at__year=year_filter)

    if gender_filter:
        transfers = transfers.filter(gender=gender_filter)

    if specialization_filter:
        transfers = transfers.filter(specialization=specialization_filter)

    # Update actual service for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"
        except Employee.DoesNotExist:
            # Keep the existing actual_service if employee not found
            pass

    # Create DataFrame
    data = []
    for transfer in transfers:
        status_text = 'قيد الانتظار'
        if transfer.status == 'approved':
            status_text = 'تمت الموافقة'
        elif transfer.status == 'rejected':
            status_text = 'مرفوض'

        gender_text = 'ذكر' if transfer.gender == 'male' else 'أنثى'

        data.append({
            'اسم الموظف': transfer.employee_name,
            'الرقم الوزاري': transfer.ministry_number,
            'الرقم الوطني': transfer.employee_id,
            'الخدمة الفعلية': transfer.actual_service or '',
            'القسم الحالي': transfer.current_department,
            'الخيار الأول': transfer.first_choice,
            'الخيار الثاني': transfer.second_choice or '',
            'الخيار الثالث': transfer.third_choice or '',
            'تاريخ الطلب': transfer.created_at.strftime('%Y-%m-%d'),
            'الحالة': status_text,
            'الجنس': gender_text,
            'المؤهل العلمي': transfer.qualification or '',
            'التخصص': transfer.specialization or '',
            'رقم الهاتف': transfer.phone_number,
            'البريد الإلكتروني': transfer.email or '',
            'سبب النقل': transfer.reason,
            'ملاحظات': transfer.notes or ''
        })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create Excel file
    output = io.BytesIO()
    try:
        # Try to use xlsxwriter engine
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='طلبات النقل الداخلي')
            worksheet = writer.sheets['طلبات النقل الداخلي']

            # Set RTL direction
            worksheet.right_to_left()

            # Auto-fit columns
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
    except (ImportError, ModuleNotFoundError):
        # Fallback to openpyxl engine if xlsxwriter is not available
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='طلبات النقل الداخلي')
            # Note: openpyxl doesn't support RTL direction and column width adjustment directly
            # We'll need to inform the user about this limitation

    # Prepare response
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=internal_transfers.xlsx'

    # Add a message if we used the fallback engine
    try:
        import xlsxwriter
    except ImportError:
        messages.warning(
            request,
            _('تم تصدير البيانات بنجاح، ولكن قد لا يتم عرض النص بشكل صحيح من اليمين إلى اليسار. '
              'يرجى تثبيت مكتبة xlsxwriter للحصول على أفضل النتائج.')
        )

    return response


def technical_transfer_view(request):
    """View for the technical transfer page"""
    if request.method == 'POST':
        form = TechnicalTransferForm(request.POST)
        if form.is_valid():
            # Check if employee already has a technical transfer this year
            ministry_number = form.cleaned_data['ministry_number']
            current_year = timezone.now().year

            existing_transfer = TechnicalTransfer.objects.filter(
                ministry_number=ministry_number,
                created_at__year=current_year
            ).exists()

            if existing_transfer:
                messages.error(request, _('لا يمكن إضافة كتاب نقل فني جديد. يوجد كتاب نقل فني سابق للموظف خلال السنة الحالية.'))
            else:
                technical_transfer = form.save()
                messages.success(request, _('تم حفظ النقل الفني بنجاح.'))
                return redirect('home:technical_transfer_list')
    else:
        form = TechnicalTransferForm()

    return render(request, 'home/technical_transfer.html', {'form': form})


@login_required
def technical_transfer_delete(request, pk):
    """View for deleting a technical transfer"""
    technical_transfer = get_object_or_404(TechnicalTransfer, pk=pk)

    if request.method == 'POST':
        technical_transfer.delete()
        messages.success(request, _('تم حذف النقل الفني بنجاح.'))
        return redirect('home:technical_transfer_list')

    return render(request, 'home/technical_transfer_delete.html', {
        'transfer': technical_transfer
    })


@login_required
def technical_transfer_list(request):
    """View for listing technical transfers"""
    # Get all technical transfers
    technical_transfers = TechnicalTransfer.objects.all().order_by('-created_at')

    return render(request, 'home/technical_transfer_list.html', {
        'technical_transfers': technical_transfers,
        'title': 'قائمة النقل الفني'
    })


def technical_transfer_search(request):
    """API view to search for an employee by ministry number for technical transfer"""
    ministry_number = request.GET.get('ministry_number', '')

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        # Find employee by ministry number
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Get latest position
        latest_position = employee.get_latest_position()

        # Get current department
        current_department = employee.school or "غير محدد"

        # Prepare response data
        response_data = {
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'current_department': current_department,
                'last_position': latest_position
            }
        }

        # Return employee data
        return JsonResponse(response_data)
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def technical_transfer_print(request, pk):
    """View for printing a technical transfer letter"""
    technical_transfer = get_object_or_404(TechnicalTransfer, pk=pk)

    # Get department information
    new_department = technical_transfer.new_department

    # Get employee information
    try:
        employee = Employee.objects.get(ministry_number=technical_transfer.ministry_number)
        position = employee.get_latest_position() or ""

        # Format job title
        job_title = format_job_title(position)

        print(f"Technical Transfer - Original Position: {position}")
        print(f"Technical Transfer - Formatted Job Title: {job_title}")
    except Employee.DoesNotExist:
        job_title = "الموظف"
        print("Technical Transfer - Employee not found")

    return render(request, 'home/technical_transfer_letter.html', {
        'transfer': technical_transfer,
        'new_department': new_department,
        'job_title': job_title
    })


def leave_balance_inquiry(request):
    """View for leave balance inquiry page"""
    return render(request, 'home/leave_balance_inquiry.html', {
        'title': 'رصيد الإجازات'
    })


def get_employee_leave_balance(request):
    """AJAX view to get employee leave balance by ministry number and national ID"""
    ministry_number = request.GET.get('ministry_number', '').strip()
    national_id = request.GET.get('national_id', '').strip()

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})
    
    if not national_id:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوطني'})

    try:
        from leaves.models import LeaveBalance, LeaveType, Leave
        from employment.models import Employment
        from django.db.models import Sum

        # التحقق من صحة تنسيق الرقم الوطني (10 أرقام)
        if not national_id.isdigit() or len(national_id) != 10:
            return JsonResponse({
                'success': False, 
                'error': 'الرقم الوطني يجب أن يكون مكوناً من 10 أرقام فقط'
            })

        # البحث عن الموظف بالرقم الوزاري والرقم الوطني معاً
        try:
            employee = Employee.objects.get(
                ministry_number=ministry_number,
                national_id=national_id
            )
        except Employee.DoesNotExist:
            return JsonResponse({
                'success': False, 
                'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري والرقم الوطني. يرجى التأكد من صحة البيانات المدخلة.'
            })
        except Employee.MultipleObjectsReturned:
            return JsonResponse({
                'success': False, 
                'error': 'خطأ في قاعدة البيانات: يوجد أكثر من موظف بنفس البيانات. يرجى الاتصال بالدعم الفني.'
            })

        # فحص حالة الموظف - التحقق من كونه متقاعد أو منقول خارجي أو شراء خدمات
        from employees.models import RetiredEmployee, ExternalTransfer
        
        # فحص المتقاعدين
        retired_employee = RetiredEmployee.objects.filter(employee=employee).first()
        if retired_employee:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} متقاعد وليس على رأس العمل حالياً.\nتاريخ التقاعد: {retired_employee.retirement_date.strftime("%Y-%m-%d")}\nسبب التقاعد: {retired_employee.retirement_reason}',
                'employee_status': 'retired',
                'status_details': {
                    'type': 'متقاعد',
                    'date': retired_employee.retirement_date.strftime('%Y-%m-%d'),
                    'retirement_reason': retired_employee.retirement_reason,
                    'employee_name': employee.full_name,
                    'ministry_number': employee.ministry_number,
                    'notes': retired_employee.notes or 'لا توجد ملاحظات'
                }
            })
        
        # فحص المنقولين خارجي
        external_transfer = ExternalTransfer.objects.filter(employee=employee).first()
        if external_transfer:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} منقول خارجياً وليس على رأس العمل حالياً.\nتاريخ النقل: {external_transfer.transfer_date.strftime("%Y-%m-%d")}\nالجهة المنقول إليها: {external_transfer.destination}',
                'employee_status': 'transferred',
                'status_details': {
                    'type': 'منقول خارجياً',
                    'date': external_transfer.transfer_date.strftime('%Y-%m-%d'),
                    'destination': external_transfer.destination,
                    'employee_name': employee.full_name,
                    'ministry_number': employee.ministry_number
                }
            })
        
        # فحص شراء الخدمات
        try:
            from employment.models import ServicePurchase
            service_purchase = ServicePurchase.objects.filter(employee=employee, is_active=True).first()
            if service_purchase:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} في حالة شراء خدمات وليس على رأس العمل حالياً.\nتاريخ إضافة شراء الخدمات: {service_purchase.created_at.strftime("%Y-%m-%d")}\nالمدرسة المستهدفة: {service_purchase.target_school}',
                    'employee_status': 'service_purchase',
                    'status_details': {
                        'type': 'شراء خدمات',
                        'created_date': service_purchase.created_at.strftime('%Y-%m-%d'),
                        'target_school': service_purchase.target_school,
                        'notes': service_purchase.notes or 'لا توجد ملاحظات',
                        'employee_name': employee.full_name,
                        'ministry_number': employee.ministry_number
                    }
                })
        except ImportError:
            pass

        # التحقق من أن الموظف يعمل في المديرية
        directorate_employment = Employment.objects.filter(
            employee=employee,
            is_current=True,
            department__workplace='directorate'
        ).first()

        if not directorate_employment:
            return JsonResponse({
                'success': False,
                'error': 'هذا الموظف لا يعمل في المديرية حالياً'
            })

        # الحصول على السنة الحالية
        current_year = timezone.now().year

        # الحصول على أرصدة الإجازات للموظف
        leave_balances = LeaveBalance.objects.filter(
            employee=employee,
            year=current_year
        ).select_related('leave_type')

        # تنظيم البيانات - استخدام نفس طريقة تقرير موظفي المديرية
        balance_data = []
        leave_types = ['annual', 'sick', 'casual']  # الأنواع الرئيسية للإجازات

        for leave_type_name in leave_types:
            try:
                leave_type = LeaveType.objects.get(name=leave_type_name)
                balance = leave_balances.filter(leave_type=leave_type).first()

                if balance:
                    # حساب الإجازات المستخدمة من جدول الإجازات مباشرة (نفس طريقة تقرير موظفي المديرية)
                    used_leaves = Leave.objects.filter(
                        employee=employee,
                        leave_type=leave_type,
                        start_date__year=current_year,
                        status='approved'
                    ).aggregate(total_days=Sum('days_count'))

                    used_days = used_leaves['total_days'] or 0
                    remaining = balance.initial_balance - used_days

                    balance_data.append({
                        'type_name': leave_type.get_name_display(),
                        'initial_balance': balance.initial_balance,
                        'used_balance': used_days,
                        'remaining_balance': remaining if remaining >= 0 else 0
                    })
                else:
                    # إذا لم يوجد رصيد، أضف صف بقيم صفر
                    balance_data.append({
                        'type_name': leave_type.get_name_display(),
                        'initial_balance': 0,
                        'used_balance': 0,
                        'remaining_balance': 0
                    })
            except LeaveType.DoesNotExist:
                # إذا لم يوجد نوع الإجازة، أضف صف بقيم صفر مع اسم افتراضي
                type_names = {
                    'annual': 'سنوية',
                    'sick': 'مرضية',
                    'casual': 'عرضية'
                }
                balance_data.append({
                    'type_name': type_names.get(leave_type_name, leave_type_name),
                    'initial_balance': 0,
                    'used_balance': 0,
                    'remaining_balance': 0
                })

        # إضافة تاريخ الاستعلام بالتقويم الميلادي
        from datetime import datetime
        current_date = datetime.now().strftime('%Y-%m-%d')

        # البحث عن آخر تحديث للإجازات
        last_leave_update = None
        if leave_balances.exists():
            # البحث عن آخر تحديث في أرصدة الإجازات
            latest_balance = leave_balances.order_by('-updated_at').first()
            if latest_balance:
                last_leave_update = latest_balance.updated_at.strftime('%Y-%m-%d')

        # إذا لم يوجد تحديث في الأرصدة، ابحث عن آخر إجازة
        if not last_leave_update:
            from leaves.models import Leave
            latest_leave = Leave.objects.filter(
                employee=employee,
                status='approved'
            ).order_by('-updated_at').first()
            if latest_leave:
                last_leave_update = latest_leave.updated_at.strftime('%Y-%m-%d')

        # الحصول على تفاصيل الإجازات المعتمدة للموظف في السنة الحالية
        detailed_leaves = []
        leave_types_for_details = ['annual', 'sick', 'casual']  # الأنواع المطلوبة
        
        for leave_type_name in leave_types_for_details:
            try:
                leave_type = LeaveType.objects.get(name=leave_type_name)
                leaves = Leave.objects.filter(
                    employee=employee,
                    leave_type=leave_type,
                    start_date__year=current_year,
                    status='approved'
                ).order_by('-start_date')
                
                for leave in leaves:
                    detailed_leaves.append({
                        'type_name': leave_type.get_name_display(),
                        'start_date': leave.start_date.strftime('%Y-%m-%d'),
                        'end_date': leave.end_date.strftime('%Y-%m-%d'),
                        'days_count': leave.days_count,
                        'reason': leave.reason or 'غير محدد'
                    })
            except LeaveType.DoesNotExist:
                continue

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'department': directorate_employment.department.name,
                'year': current_year,
                'inquiry_date': current_date,
                'last_leave_update': last_leave_update or 'لا يوجد'
            },
            'balances': balance_data,
            'detailed_leaves': detailed_leaves
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


def employee_inquiry(request):
    """View for employee inquiry page"""
    return render(request, 'home/employee_inquiry.html', {
        'title': 'استعلام الموظفين'
    })


def get_employee_details(request):
    """AJAX view to get employee details by ministry number or national ID"""
    ministry_number = request.GET.get('ministry_number', '').strip()
    national_id = request.GET.get('national_id', '').strip()

    if not ministry_number and not national_id:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري أو الرقم الوطني'})

    try:
        from employment.models import Employment
        from employees.models import RetiredEmployee, ExternalTransfer

        # البحث عن الموظف - يتطلب كلا الرقمين معاً
        employee = None

        # التحقق من وجود كلا الرقمين
        if not ministry_number or not national_id:
            return JsonResponse({
                'success': False,
                'error': 'يجب إدخال كل من الرقم الوزاري والرقم الوطني معاً للبحث عن الموظف'
            })

        # البحث بكلا الرقمين معاً - التحقق من التطابق الكامل
        try:
            # البحث بالرقم الوزاري أولاً
            employee_by_ministry = Employee.objects.filter(ministry_number=ministry_number).first()
            # البحث بالرقم الوطني
            employee_by_national = Employee.objects.filter(national_id=national_id).first()

            if not employee_by_ministry and not employee_by_national:
                return JsonResponse({
                    'success': False,
                    'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري أو الرقم الوطني'
                })
            elif not employee_by_ministry:
                return JsonResponse({
                    'success': False,
                    'error': f'الرقم الوزاري ({ministry_number}) غير صحيح. لم يتم العثور على موظف بهذا الرقم الوزاري'
                })
            elif not employee_by_national:
                return JsonResponse({
                    'success': False,
                    'error': f'الرقم الوطني ({national_id}) غير صحيح. لم يتم العثور على موظف بهذا الرقم الوطني'
                })
            elif employee_by_ministry.id != employee_by_national.id:
                return JsonResponse({
                    'success': False,
                    'error': f'الرقم الوزاري ({ministry_number}) والرقم الوطني ({national_id}) لا يتطابقان.\nالرقم الوزاري ينتمي للموظف: {employee_by_ministry.full_name}\nالرقم الوطني ينتمي للموظف: {employee_by_national.full_name}'
                })
            else:
                # كلا الرقمين صحيحين ويشيران لنفس الموظف
                employee = employee_by_ministry

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ أثناء البحث: {str(e)}'
            })

        # فحص حالة الموظف - التحقق من كونه متقاعد أو منقول خارجي أو شراء خدمات
        
        # فحص المتقاعدين
        retired_employee = RetiredEmployee.objects.filter(employee=employee).first()
        if retired_employee:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} متقاعد وليس على رأس العمل حالياً.\nتاريخ التقاعد: {retired_employee.retirement_date.strftime("%Y-%m-%d")}\nسبب التقاعد: {retired_employee.retirement_reason}',
                'employee_status': 'retired',
                'status_details': {
                    'type': 'متقاعد',
                    'date': retired_employee.retirement_date.strftime('%Y-%m-%d'),
                    'retirement_reason': retired_employee.retirement_reason,
                    'notes': retired_employee.notes or 'لا توجد ملاحظات'
                }
            })
        
        # فحص المنقولين خارجي
        external_transfer = ExternalTransfer.objects.filter(employee=employee).first()
        if external_transfer:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} منقول خارجياً وليس على رأس العمل حالياً.\nتاريخ النقل: {external_transfer.transfer_date.strftime("%Y-%m-%d")}\nالجهة المنقول إليها: {external_transfer.destination}',
                'employee_status': 'transferred',
                'status_details': {
                    'type': 'منقول خارجياً',
                    'date': external_transfer.transfer_date.strftime('%Y-%m-%d'),
                    'destination': external_transfer.destination
                }
            })
        
        # فحص شراء الخدمات
        try:
            from employment.models import ServicePurchase
            service_purchase = ServicePurchase.objects.filter(employee=employee, is_active=True).first()
            if service_purchase:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} في حالة شراء خدمات وليس على رأس العمل حالياً.\nتاريخ إضافة شراء الخدمات: {service_purchase.created_at.strftime("%Y-%m-%d")}\nالمدرسة المستهدفة: {service_purchase.target_school}',
                    'employee_status': 'service_purchase',
                    'status_details': {
                        'type': 'شراء خدمات',
                        'created_date': service_purchase.created_at.strftime('%Y-%m-%d'),
                        'target_school': service_purchase.target_school,
                        'notes': service_purchase.notes or 'لا توجد ملاحظات'
                    }
                })
        except ImportError:
            pass

        # الحصول على معلومات التوظيف الحالية
        current_employment = Employment.objects.filter(
            employee=employee,
            is_current=True
        ).select_related('department', 'position', 'status').first()

        # حساب العمر بالتفصيل (سنة وشهر ويوم)
        from datetime import date
        from calendar import monthrange
        today = date.today()

        # حساب العمر
        age_years = today.year - employee.birth_date.year
        age_months = today.month - employee.birth_date.month
        age_days = today.day - employee.birth_date.day

        if age_days < 0:
            age_months -= 1
            # حساب عدد الأيام في الشهر السابق
            if today.month == 1:
                prev_month = 12
                prev_year = today.year - 1
            else:
                prev_month = today.month - 1
                prev_year = today.year

            days_in_prev_month = monthrange(prev_year, prev_month)[1]
            age_days += days_in_prev_month

        if age_months < 0:
            age_years -= 1
            age_months += 12

        # حساب الخدمة الفعلية (مثل صفحة الخدمة الفعلية)
        if employee.hire_date:
            # حساب إجمالي الأيام بين تاريخ التعيين واليوم
            total_days = (today - employee.hire_date).days

            # الحصول على الإجازات بدون راتب
            from leaves.models import Leave, LeaveType
            unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
            unpaid_leave_days = 0

            if unpaid_leave_type:
                unpaid_leaves = Leave.objects.filter(
                    employee=employee,
                    leave_type=unpaid_leave_type,
                    status='approved'
                )

                # حساب مجموع أيام الإجازات بدون راتب
                for leave in unpaid_leaves:
                    unpaid_leave_days += leave.days_count

            # حساب أيام الخدمة الفعلية
            actual_service_days = total_days - unpaid_leave_days

            # تحويل إلى سنوات وشهور وأيام
            actual_years = actual_service_days // 365
            remaining_days = actual_service_days % 365
            actual_months = remaining_days // 30
            actual_days = remaining_days % 30

            actual_service_text = f"{actual_years} سنة و {actual_months} شهر و {actual_days} يوم"
        else:
            actual_service_text = 'غير متوفر'
            actual_years = 0
            actual_months = 0
            actual_days = 0

        # حساب سنوات الخدمة العادية بالتفصيل (بدون خصم الإجازات)
        service_years = today.year - employee.hire_date.year
        service_months = today.month - employee.hire_date.month
        service_days = today.day - employee.hire_date.day

        if service_days < 0:
            service_months -= 1
            if today.month == 1:
                prev_month = 12
                prev_year = today.year - 1
            else:
                prev_month = today.month - 1
                prev_year = today.year

            days_in_prev_month = monthrange(prev_year, prev_month)[1]
            service_days += days_in_prev_month

        if service_months < 0:
            service_years -= 1
            service_months += 12

        # الحصول على التقارير السنوية من تطبيق performance
        from performance.models import PerformanceEvaluation
        annual_reports = PerformanceEvaluation.objects.filter(employee=employee).order_by('-year')
        annual_reports_data = []
        for report in annual_reports:
            annual_reports_data.append({
                'year': report.year,
                'score': float(report.score),
                'max_score': float(report.max_score),
                'percentage': round(report.percentage, 2),
                'evaluator': report.evaluator or 'غير محدد',
                'comments': report.comments or 'لا توجد ملاحظات'
            })

        # الحصول على العقوبات
        from disciplinary.models import Penalty
        penalties = Penalty.objects.filter(employee=employee).order_by('-date')
        penalties_data = []
        for penalty in penalties:
            penalties_data.append({
                'date': penalty.date.strftime('%Y-%m-%d'),
                'penalty_type': penalty.penalty_type.name,
                'description': penalty.description,
                'decision_number': penalty.decision_number or 'غير محدد',
                'decision_date': penalty.decision_date.strftime('%Y-%m-%d') if penalty.decision_date else 'غير محدد'
            })

        # الحصول على الإجازات بدون راتب من كلا النموذجين
        unpaid_leaves_data = []

        # 1. من نموذج UnpaidLeave في تطبيق disciplinary
        try:
            from disciplinary.models import UnpaidLeave
            unpaid_leaves_disciplinary = UnpaidLeave.objects.filter(employee=employee).order_by('-start_date')
            for leave in unpaid_leaves_disciplinary:
                unpaid_leaves_data.append({
                    'start_date': leave.start_date.strftime('%Y-%m-%d'),
                    'end_date': leave.end_date.strftime('%Y-%m-%d'),
                    'duration': leave.duration,
                    'reason': leave.reason or 'غير محدد',
                    'decision_number': leave.decision_number or 'غير محدد',
                    'decision_date': leave.decision_date.strftime('%Y-%m-%d') if leave.decision_date else 'غير محدد',
                    'source': 'disciplinary'
                })
        except ImportError:
            pass

        # 2. من نموذج Leave مع نوع الإجازة "بدون راتب" في تطبيق leaves
        try:
            from leaves.models import Leave, LeaveType
            unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
            if unpaid_leave_type:
                unpaid_leaves_regular = Leave.objects.filter(
                    employee=employee,
                    leave_type=unpaid_leave_type,
                    status='approved'
                ).order_by('-start_date')

                for leave in unpaid_leaves_regular:
                    unpaid_leaves_data.append({
                        'start_date': leave.start_date.strftime('%Y-%m-%d'),
                        'end_date': leave.end_date.strftime('%Y-%m-%d'),
                        'duration': leave.days_count,
                        'reason': leave.reason or 'غير محدد',
                        'decision_number': getattr(leave, 'decision_number', 'غير محدد'),
                        'decision_date': getattr(leave, 'decision_date', None),
                        'source': 'leaves'
                    })
                    # تحديث تاريخ القرار إذا كان موجود
                    if unpaid_leaves_data[-1]['decision_date']:
                        unpaid_leaves_data[-1]['decision_date'] = unpaid_leaves_data[-1]['decision_date'].strftime('%Y-%m-%d')
                    else:
                        unpaid_leaves_data[-1]['decision_date'] = 'غير محدد'
        except ImportError:
            pass

        # ترتيب الإجازات بدون راتب حسب تاريخ البداية (الأحدث أولاً)
        unpaid_leaves_data.sort(key=lambda x: x['start_date'], reverse=True)

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'national_id': employee.national_id,
                'full_name': employee.full_name,
                'gender': employee.get_gender_display(),
                'birth_date': employee.birth_date.strftime('%Y-%m-%d'),
                'age': f"{age_years} سنة و {age_months} شهر و {age_days} يوم",
                'age_years': age_years,
                'age_months': age_months,
                'age_days': age_days,
                'qualification': employee.qualification,
                'post_graduate_diploma': employee.post_graduate_diploma,
                'masters_degree': employee.masters_degree,
                'phd_degree': employee.phd_degree,
                'specialization': employee.specialization,
                'hire_date': employee.hire_date.strftime('%Y-%m-%d'),
                'service_years': f"{actual_years} سنة و {actual_months} شهر و {actual_days} يوم",
                'service_years_num': actual_years,
                'service_months_num': actual_months,
                'service_days_num': actual_days,
                'school': employee.school,
                'address': employee.address,
                'phone_number': employee.phone_number,
                'department': current_employment.department.name if current_employment else 'غير محدد',
                'position': current_employment.position.name if current_employment else 'غير محدد',
                'workplace': current_employment.department.get_workplace_display() if current_employment else 'غير محدد'
            },
            'annual_reports': annual_reports_data,
            'penalties': penalties_data,
            'unpaid_leaves': unpaid_leaves_data
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in get_employee_details: {error_details}")
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


# Important Links Management Views
@login_required
def important_links_admin(request):
    """View for managing important links"""
    from .models import ImportantLink
    links = ImportantLink.objects.all().order_by('order', 'name')
    return render(request, 'home/important_links_admin.html', {
        'links': links,
        'title': 'إدارة الروابط المهمة'
    })


@login_required
def important_link_add(request):
    """View for adding a new important link"""
    from .forms import ImportantLinkForm
    if request.method == 'POST':
        form = ImportantLinkForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الرابط بنجاح.')
            return redirect('home:important_links_admin')
    else:
        form = ImportantLinkForm()

    return render(request, 'home/important_link_form.html', {
        'form': form,
        'title': 'إضافة رابط مهم',
        'action': 'add'
    })


@login_required
def important_link_update(request, pk):
    """View for updating an important link"""
    from .models import ImportantLink
    from .forms import ImportantLinkForm
    link = get_object_or_404(ImportantLink, pk=pk)

    if request.method == 'POST':
        form = ImportantLinkForm(request.POST, instance=link)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الرابط بنجاح.')
            return redirect('home:important_links_admin')
    else:
        form = ImportantLinkForm(instance=link)

    return render(request, 'home/important_link_form.html', {
        'form': form,
        'link': link,
        'title': 'تعديل رابط مهم',
        'action': 'update'
    })


@login_required
def important_link_delete(request, pk):
    """View for deleting an important link"""
    from .models import ImportantLink
    link = get_object_or_404(ImportantLink, pk=pk)

    if request.method == 'POST':
        link.delete()
        messages.success(request, 'تم حذف الرابط بنجاح.')
        return redirect('home:important_links_admin')

    return render(request, 'home/important_link_confirm_delete.html', {
        'link': link,
        'title': 'حذف رابط مهم'
    })


# Search Employee for Internal Transfer
def search_employee(request):
    """Search for employee by ministry number, national ID, and ID number"""
    ministry_number = request.GET.get('ministry_number', '').strip()
    national_id = request.GET.get('national_id', '').strip()
    id_number = request.GET.get('id_number', '').strip()

    if not all([ministry_number, national_id, id_number]):
        return JsonResponse({
            'success': False,
            'error': 'يجب إدخال جميع البيانات المطلوبة (الرقم الوزاري، الرقم الوطني، رقم الهوية)'
        })

    try:
        # Search for employee by ministry number
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Verify national ID
        if employee.national_id != national_id:
            return JsonResponse({
                'success': False,
                'error': f'الرقم الوطني المدخل ({national_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})'
            })

        # Verify ID number from EmployeeIdentification
        from employment.models import EmployeeIdentification
        try:
            identification = EmployeeIdentification.objects.get(employee=employee)
            if identification.id_number != id_number:
                return JsonResponse({
                    'success': False,
                    'error': f'رقم الهوية المدخل ({id_number}) لا يتطابق مع رقم الهوية المسجل ({identification.id_number})'
                })
        except EmployeeIdentification.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام'
            })

        # Check if employee already has a transfer request
        existing_transfer = InternalTransfer.objects.filter(
            ministry_number=ministry_number,
            status='pending'
        ).first()

        if existing_transfer:
            return JsonResponse({
                'success': False,
                'error': 'يوجد طلب نقل داخلي سابق لهذا الموظف',
                'has_existing_transfer': True,
                'transfer': {
                    'employee_name': existing_transfer.employee_name,
                    'created_at': existing_transfer.created_at.strftime('%Y-%m-%d'),
                    'edit_token': existing_transfer.edit_token
                }
            })

        # Get current employment info
        from employment.models import Employment
        current_employment = Employment.objects.filter(
            employee=employee,
            is_current=True
        ).first()

        # Calculate actual service
        from datetime import date
        today = date.today()
        if employee.hire_date:
            # Calculate total days
            total_days = (today - employee.hire_date).days

            # Get unpaid leaves
            from leaves.models import Leave, LeaveType
            unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
            unpaid_leave_days = 0

            if unpaid_leave_type:
                unpaid_leaves = Leave.objects.filter(
                    employee=employee,
                    leave_type=unpaid_leave_type,
                    status='approved'
                )
                for leave in unpaid_leaves:
                    unpaid_leave_days += leave.days_count

            # Calculate actual service days
            actual_service_days = total_days - unpaid_leave_days

            # Convert to years, months, days
            years = actual_service_days // 365
            remaining_days = actual_service_days % 365
            months = remaining_days // 30
            days = remaining_days % 30

            actual_service = f"{years} سنة و {months} شهر و {days} يوم"
        else:
            actual_service = 'غير متوفر'

        # Get last rank for the employee
        from ranks.models import EmployeeRank
        last_rank = EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained').first()
        last_rank_name = last_rank.rank_type.name if last_rank else 'غير محدد'

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'national_id': employee.national_id,
                'gender': employee.gender,
                'department': current_employment.department.name if current_employment else 'غير محدد',
                'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                'actual_service': actual_service,
                'last_position': current_employment.position.name if current_employment else 'غير محدد',
                'qualification': employee.qualification or 'غير محدد',
                'specialization': employee.specialization or 'غير محدد',
                'last_rank': last_rank_name,
                'address': employee.address or '',
                'phone_number': employee.phone_number or ''
            }
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': f'لم يتم العثور على موظف بالرقم الوزاري ({ministry_number})'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })


def search_transfer_request(request):
    """Search for existing transfer request"""
    ministry_number = request.GET.get('ministry_number', '').strip()
    national_id = request.GET.get('national_id', '').strip()
    id_number = request.GET.get('id_number', '').strip()

    if not all([ministry_number, national_id, id_number]):
        return JsonResponse({
            'success': False,
            'error': 'يجب إدخال جميع البيانات المطلوبة'
        })

    try:
        # Verify employee exists and data matches
        employee = Employee.objects.get(ministry_number=ministry_number)

        if employee.national_id != national_id:
            return JsonResponse({
                'success': False,
                'error': 'البيانات المدخلة لا تتطابق مع بيانات الموظف'
            })

        # Verify ID number
        from employment.models import EmployeeIdentification
        try:
            identification = EmployeeIdentification.objects.get(employee=employee)
            if identification.id_number != id_number:
                return JsonResponse({
                    'success': False,
                    'error': 'البيانات المدخلة لا تتطابق مع بيانات الموظف'
                })
        except EmployeeIdentification.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'بيانات التعريف غير متوفرة لهذا الموظف'
            })

        # Search for transfer request
        transfer = InternalTransfer.objects.filter(
            ministry_number=ministry_number,
            status='pending'
        ).first()

        if not transfer:
            return JsonResponse({
                'success': False,
                'error': 'لا يوجد طلب نقل داخلي لهذا الموظف'
            })

        return JsonResponse({
            'success': True,
            'transfer': {
                'employee_name': transfer.employee_name,
                'created_at': transfer.created_at.strftime('%Y-%m-%d'),
                'edit_token': transfer.edit_token
            }
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ: {str(e)}'
        })


@login_required
def admin_internal_transfer_create(request):
    """View for admin to create internal transfer request for employees"""
    if request.method == 'POST':
        # Check if this is a search request
        if 'search_term' in request.POST:
            return admin_search_employee(request)
        
        # Otherwise, process the transfer form
        form = AdminInternalTransferForm(request.POST)
        if form.is_valid():
            transfer = form.save()
            messages.success(request, 
                f'تم تقديم طلب النقل الداخلي بنجاح للموظف {transfer.employee_name}. '
                f'الرقم الوزاري: {transfer.ministry_number}'
            )
            return redirect('home:internal_transfer_list')
        else:
            # Print form errors for debugging
            print(f"Form errors: {form.errors}")
    else:
        form = AdminInternalTransferForm()

    return render(request, 'home/admin_internal_transfer_create.html', {'form': form})


def admin_search_employee(request):
    """API view for admin to search employees by ministry number or name only"""
    search_term = request.POST.get('search_term', '').strip()
    
    if not search_term:
        return JsonResponse({'success': False, 'error': 'يرجى إدخال الرقم الوزاري أو الاسم للبحث'})

    try:
        # Search by ministry number first
        if search_term.isdigit():
            try:
                employee = Employee.objects.get(ministry_number=search_term)
            except Employee.DoesNotExist:
                employee = None
        else:
            employee = None
        
        # If not found by ministry number, search by name
        if not employee:
            employees = Employee.objects.filter(
                Q(full_name__icontains=search_term) |
                Q(ministry_number__icontains=search_term)
            )
            
            if employees.count() == 1:
                employee = employees.first()
            elif employees.count() > 1:
                # Return multiple results for selection
                employee_list = []
                for emp in employees[:10]:  # Limit to 10 results
                    employee_list.append({
                        'ministry_number': emp.ministry_number,
                        'full_name': emp.full_name,
                        'current_department': emp.get_latest_position() or 'غير محدد'
                    })
                return JsonResponse({
                    'success': False,
                    'multiple_results': True,
                    'employees': employee_list,
                    'error': f'تم العثور على {employees.count()} موظف. يرجى تحديد الرقم الوزاري للموظف المطلوب.'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'لم يتم العثور على موظف بهذا الاسم أو الرقم الوزاري'
                })

        if not employee:
            return JsonResponse({
                'success': False,
                'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري أو الاسم'
            })

        print(f"Admin search found employee: {employee.full_name}")

        # Check if employee is a BTEC teacher
        from employment.models import BtecTeacher
        btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
        if btec_teacher:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.'
            })

        # Check if employee already has a transfer request in the current year
        from datetime import date
        current_year = date.today().year
        existing_transfer = InternalTransfer.objects.filter(
            ministry_number=employee.ministry_number,
            created_at__year=current_year
        ).order_by('-created_at').first()

        if existing_transfer:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} لديه طلب نقل داخلي مسبق خلال هذه السنة (تاريخ التقديم: {existing_transfer.created_at.strftime("%Y-%m-%d")}). لا يمكن تقديم طلب جديد.',
                'has_existing_transfer': True,
                'transfer': {
                    'id': existing_transfer.id,
                    'employee_name': existing_transfer.employee_name,
                    'created_at': existing_transfer.created_at.strftime('%Y-%m-%d'),
                    'status': existing_transfer.status
                }
            })

        # Get employee information
        latest_position = employee.get_latest_position()
        
        # Get latest rank
        latest_rank = EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained').first()
        latest_rank_name = latest_rank.rank_type.name if latest_rank else '-'

        # Calculate actual service
        actual_service = "غير محدد"
        if employee.hire_date:
            today = date.today()
            years = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
            months = today.month - employee.hire_date.month
            if months < 0:
                months += 12
            
            if years > 0:
                actual_service = f"{years} سنة"
                if months > 0:
                    actual_service += f" و {months} شهر"
            elif months > 0:
                actual_service = f"{months} شهر"
            else:
                actual_service = "أقل من شهر"

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'employee_id': employee.national_id or '',
                'full_name': employee.full_name,
                'current_department': latest_position or 'غير محدد',
                'specialization': employee.specialization or 'غير محدد',
                'actual_service': actual_service,
                'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد',
                'rank': latest_rank_name
            }
        })

    except Exception as e:
        print(f"Admin search error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'حدث خطأ أثناء البحث: {str(e)}'
        })


@login_required
def employee_transfer_management(request):
    """View for managing employee transfers"""
    transfers = EmployeeTransfer.objects.all().order_by('-created_at')
    departments = Department.objects.filter(workplace='school').order_by('name')

    context = {
        'transfers': transfers,
        'departments': departments,
        'title': 'إدارة نقل الموظفين'
    }
    return render(request, 'home/employee_transfer_management.html', context)


@login_required
def search_employee_for_transfer(request):
    """AJAX view to search for employee by ministry number or name"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    search_term = request.POST.get('search_term', '').strip()

    if not search_term:
        return JsonResponse({'success': False, 'error': 'يرجى إدخال الرقم الوزاري أو اسم الموظف'})

    try:
        # Search by ministry number or name
        employee = None
        if search_term.isdigit():
            # Search by ministry number
            employee = Employee.objects.filter(ministry_number=search_term).first()
        else:
            # Search by name
            employee = Employee.objects.filter(full_name__icontains=search_term).first()

        if not employee:
            return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري أو الاسم'})

        # Get current department
        current_dept = employee.get_current_department()
        if current_dept == '-' and employee.school:
            current_dept = str(employee.school)

        # Calculate actual service
        actual_service = '-'
        if employee.hire_date:
            today = date.today()
            years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
            months_service = today.month - employee.hire_date.month
            if months_service < 0:
                months_service += 12
            actual_service = f"{years_service} سنة و {months_service} شهر"

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'specialization': employee.specialization or '-',
                'actual_service': actual_service,
                'current_department': current_dept
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def add_employee_transfer(request):
    """AJAX view to add new employee transfer"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    try:
        ministry_number = request.POST.get('ministry_number')
        employee_name = request.POST.get('employee_name')
        specialization = request.POST.get('specialization')
        actual_service = request.POST.get('actual_service')
        current_department = request.POST.get('current_department')
        new_department_id = request.POST.get('new_department_id')
        transfer_type = request.POST.get('transfer_type')
        endorsement = request.POST.get('endorsement')
        notes = request.POST.get('notes', '')

        # Validate required fields
        if not all([ministry_number, employee_name, current_department, new_department_id, transfer_type, endorsement]):
            return JsonResponse({'success': False, 'error': 'جميع الحقول مطلوبة'})

        # Get new department
        new_department = Department.objects.get(id=new_department_id)

        # Create transfer record
        transfer = EmployeeTransfer.objects.create(
            ministry_number=ministry_number,
            employee_name=employee_name,
            specialization=specialization,
            actual_service=actual_service,
            current_department=current_department,
            new_department=new_department,
            transfer_type=transfer_type,
            endorsement=endorsement,
            notes=notes
        )

        return JsonResponse({
            'success': True,
            'message': 'تم إضافة عملية النقل بنجاح',
            'transfer_id': transfer.id
        })

    except Department.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'القسم المحدد غير موجود'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def print_transfer_letter(request, transfer_id):
    """View for printing individual transfer letter"""
    transfer = get_object_or_404(EmployeeTransfer, id=transfer_id)

    # Get employee information for position handling
    job_title = "الموظف"
    current_department_obj = None

    try:
        employee = Employee.objects.get(ministry_number=transfer.ministry_number)
        position = employee.get_latest_position() or ""

        # Format job title
        job_title = format_job_title(position)

        # Try to get current department object for workplace determination
        try:
            current_department_obj = Department.objects.filter(
                name__icontains=transfer.current_department
            ).first()
        except:
            current_department_obj = None

        print(f"Transfer Letter - Original Position: {position}")
        print(f"Transfer Letter - Formatted Job Title: {job_title}")
        print(f"Transfer Letter - Current Department Object: {current_department_obj}")
    except Employee.DoesNotExist:
        job_title = "الموظف"
        print("Transfer Letter - Employee not found")

    context = {
        'transfer': transfer,
        'job_title': job_title,
        'current_department_obj': current_department_obj,
        'title': 'طباعة كتاب النقل'
    }
    return render(request, 'home/print_transfer_letter.html', context)


@login_required
def update_employee_transfer(request):
    """AJAX view to update employee transfer"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    try:
        transfer_id = request.POST.get('transfer_id')
        ministry_number = request.POST.get('ministry_number')
        employee_name = request.POST.get('employee_name')
        specialization = request.POST.get('specialization')
        actual_service = request.POST.get('actual_service')
        current_department = request.POST.get('current_department')
        new_department_id = request.POST.get('new_department_id')
        transfer_type = request.POST.get('transfer_type')
        endorsement = request.POST.get('endorsement')
        notes = request.POST.get('notes', '')

        # Validate required fields
        if not all([transfer_id, ministry_number, employee_name, current_department, new_department_id, transfer_type, endorsement]):
            return JsonResponse({'success': False, 'error': 'جميع الحقول مطلوبة'})

        # Get transfer and new department
        transfer = EmployeeTransfer.objects.get(id=transfer_id)
        new_department = Department.objects.get(id=new_department_id)

        # Update transfer record
        transfer.ministry_number = ministry_number
        transfer.employee_name = employee_name
        transfer.specialization = specialization
        transfer.actual_service = actual_service
        transfer.current_department = current_department
        transfer.new_department = new_department
        transfer.transfer_type = transfer_type
        transfer.endorsement = endorsement
        transfer.notes = notes
        transfer.save()

        return JsonResponse({
            'success': True,
            'message': 'تم تحديث عملية النقل بنجاح'
        })

    except EmployeeTransfer.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'عملية النقل غير موجودة'})
    except Department.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'القسم المحدد غير موجود'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})


@login_required
def delete_employee_transfer(request):
    """AJAX view to delete employee transfer"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'طريقة الطلب غير صحيحة'})

    try:
        transfer_id = request.POST.get('transfer_id')

        if not transfer_id:
            return JsonResponse({'success': False, 'error': 'معرف عملية النقل مطلوب'})

        # Get and delete transfer
        transfer = EmployeeTransfer.objects.get(id=transfer_id)
        employee_name = transfer.employee_name
        transfer.delete()

        return JsonResponse({
            'success': True,
            'message': f'تم حذف نقل الموظف {employee_name} بنجاح'
        })

    except EmployeeTransfer.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'عملية النقل غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})