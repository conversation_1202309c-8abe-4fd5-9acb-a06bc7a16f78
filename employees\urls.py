from django.urls import path
from . import views
from . import retired_views
from . import external_transfer_views
from . import qualification_views
from . import maternity_views
from . import internal_transfer_views

app_name = 'employees'

urlpatterns = [
    path('', views.employee_list, name='employee_list'),
    path('ajax/', views.get_employees_ajax, name='get_employees_ajax'),
    path('add/', views.employee_create, name='employee_create'),
    path('<int:pk>/', views.employee_detail, name='employee_detail'),
    path('<int:pk>/edit/', views.employee_update, name='employee_update'),
    path('<int:pk>/delete/', views.employee_delete, name='employee_delete'),
    path('import-export/', views.employee_import_export, name='employee_import_export'),
    path('get-by-ministry-number/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
    path('annual-report/add/', views.annual_report_create, name='annual_report_create'),
    path('calculate-age/', views.calculate_age, name='calculate_age'),
    path('add-qualification/', qualification_views.add_qualification, name='add_qualification'),
    path('search-employee-for-qualification/', qualification_views.search_employee_for_qualification, name='search_employee_for_qualification'),
    
    # Maternity leaves URLs
    path('maternity-leaves/', maternity_views.maternity_leaves_list, name='maternity_leaves_list'),
    path('maternity-leaves/add/', maternity_views.add_maternity_leave, name='add_maternity_leave'),
    path('maternity-leaves/<int:pk>/', maternity_views.maternity_leave_detail, name='maternity_leave_detail'),
    path('maternity-leaves/<int:pk>/edit/', maternity_views.maternity_leave_update, name='maternity_leave_update'),
    path('maternity-leaves/<int:pk>/delete/', maternity_views.maternity_leave_delete, name='maternity_leave_delete'),
    path('search-employee-for-maternity/', maternity_views.search_employee_for_maternity, name='search_employee_for_maternity'),
    path('export-maternity-leaves-excel/', maternity_views.export_maternity_leaves_excel, name='export_maternity_leaves_excel'),
    
    # Retired employees URLs
    path('retired/', retired_views.retired_employees_list, name='retired_employees_list'),
    path('retired/<int:pk>/', retired_views.retired_employee_detail, name='retired_employee_detail'),
    path('retired/<int:pk>/edit/', retired_views.retired_employee_update, name='retired_employee_update'),
    path('retired/<int:pk>/delete/', retired_views.retired_employee_delete, name='retired_employee_delete'),
    path('search-employees-for-retirement/', retired_views.search_employees_for_retirement, name='search_employees_for_retirement'),
    
    # External transfers URLs
    path('external-transfers/', external_transfer_views.external_transfers_list, name='external_transfers_list'),
    path('external-transfers/<int:pk>/', external_transfer_views.external_transfer_detail, name='external_transfer_detail'),
    path('external-transfers/<int:pk>/edit/', external_transfer_views.external_transfer_update, name='external_transfer_update'),
    path('external-transfers/<int:pk>/delete/', external_transfer_views.external_transfer_delete, name='external_transfer_delete'),
    path('search-employees-for-transfer/', external_transfer_views.search_employees_for_transfer, name='search_employees_for_transfer'),
    
    # Internal transfers URLs
    path('internal-transfers/', internal_transfer_views.internal_transfers_list, name='internal_transfers_list'),
    path('internal-transfers/<int:pk>/', internal_transfer_views.internal_transfer_detail, name='internal_transfer_detail'),
    path('internal-transfers/<int:pk>/edit/', internal_transfer_views.internal_transfer_update, name='internal_transfer_update'),
    path('internal-transfers/<int:pk>/delete/', internal_transfer_views.internal_transfer_delete, name='internal_transfer_delete'),
    path('internal-transfers/statistics/', internal_transfer_views.internal_transfers_statistics, name='internal_transfers_statistics'),
    path('internal-transfers/statistics/api/', internal_transfer_views.internal_transfers_statistics_api, name='internal_transfers_statistics_api'),
    path('export-internal-transfers-excel/', internal_transfer_views.export_internal_transfers_excel, name='export_internal_transfers_excel'),
]
