from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.core.paginator import Paginator
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from .models import InternalTransfer
from .transfer_statistics import get_transfer_statistics, get_monthly_transfer_trend, get_department_transfer_summary


@login_required
def internal_transfers_list(request):
    """View for displaying all internal transfers"""
    search_query = request.GET.get('search', '')
    per_page = request.GET.get('per_page', '25')
    
    # Start with all internal transfers
    transfers = InternalTransfer.objects.select_related('employee').all()
    
    # Apply search filter
    if search_query:
        transfers = transfers.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(previous_department__icontains=search_query) |
            Q(new_department__icontains=search_query)
        )
    
    # Order by transfer date (newest first)
    transfers = transfers.order_by('-transfer_date')
    
    # Get total count before pagination
    total_transfers = transfers.count()
    
    # Handle pagination
    if per_page != 'all':
        try:
            per_page = int(per_page)
        except (ValueError, TypeError):
            per_page = 25
            
        paginator = Paginator(transfers, per_page)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        transfers = page_obj
    else:
        page_obj = None
    
    context = {
        'transfers': transfers,
        'search_query': search_query,
        'per_page': str(per_page),
        'total_transfers': total_transfers,
        'page_obj': page_obj,
    }
    
    return render(request, 'employees/internal_transfers_list.html', context)


@login_required
def internal_transfer_detail(request, pk):
    """View for displaying internal transfer details"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)
    
    context = {
        'transfer': transfer,
    }
    
    return render(request, 'employees/internal_transfer_detail.html', context)


@login_required
def internal_transfer_update(request, pk):
    """View for updating internal transfer"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)
    
    if request.method == 'POST':
        # Update fields
        transfer.previous_department = request.POST.get('previous_department', transfer.previous_department)
        transfer.new_department = request.POST.get('new_department', transfer.new_department)
        transfer.transfer_date = request.POST.get('transfer_date', transfer.transfer_date)
        transfer.start_date = request.POST.get('start_date', transfer.start_date)
        transfer.notes = request.POST.get('notes', transfer.notes)
        
        try:
            transfer.save()
            messages.success(request, 'تم تحديث بيانات النقل بنجاح')
            return redirect('employees:internal_transfer_detail', pk=transfer.pk)
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء التحديث: {str(e)}')
    
    context = {
        'transfer': transfer,
    }
    
    return render(request, 'employees/internal_transfer_form.html', context)


@login_required
def internal_transfer_delete(request, pk):
    """View for deleting internal transfer"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)
    
    if request.method == 'POST':
        try:
            transfer.delete()
            messages.success(request, 'تم حذف سجل النقل بنجاح')
            return redirect('employees:internal_transfers_list')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء الحذف: {str(e)}')
    
    context = {
        'transfer': transfer,
    }
    
    return render(request, 'employees/internal_transfer_delete.html', context)


@login_required
def export_internal_transfers_excel(request):
    """Export internal transfers to Excel"""
    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "حركات النقل الداخلي"
    
    # Define headers
    headers = [
        'الرقم الوزاري',
        'الاسم الكامل', 
        'التخصص',
        'القسم السابق',
        'القسم الجديد',
        'تاريخ النقل',
        'تاريخ المباشرة',
        'الملاحظات'
    ]
    
    # Style headers
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Add headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Get data
    transfers = InternalTransfer.objects.select_related('employee').order_by('-transfer_date')
    
    # Add data rows
    for row, transfer in enumerate(transfers, 2):
        ws.cell(row=row, column=1, value=transfer.employee.ministry_number)
        ws.cell(row=row, column=2, value=transfer.employee.full_name)
        ws.cell(row=row, column=3, value=transfer.employee.specialization)
        ws.cell(row=row, column=4, value=transfer.previous_department)
        ws.cell(row=row, column=5, value=transfer.new_department)
        ws.cell(row=row, column=6, value=transfer.transfer_date.strftime('%Y-%m-%d'))
        ws.cell(row=row, column=7, value=transfer.start_date.strftime('%Y-%m-%d'))
        ws.cell(row=row, column=8, value=transfer.notes or '')
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Create response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="internal_transfers.xlsx"'
    
    # Save workbook to response
    wb.save(response)
    return response


@login_required
def internal_transfers_statistics(request):
    """View for displaying internal transfers statistics"""
    stats = get_transfer_statistics()
    trends = list(get_monthly_transfer_trend(12))
    department_summary = get_department_transfer_summary()
    
    context = {
        'stats': stats,
        'trends': trends,
        'department_summary': department_summary,
    }
    
    return render(request, 'employees/internal_transfers_statistics.html', context)


@login_required
def internal_transfers_statistics_api(request):
    """API endpoint for statistics data (for charts)"""
    stats = get_transfer_statistics()
    trends = list(get_monthly_transfer_trend(12))
    department_summary = get_department_transfer_summary()
    
    return JsonResponse({
        'stats': stats,
        'trends': trends,
        'department_summary': department_summary,
    })