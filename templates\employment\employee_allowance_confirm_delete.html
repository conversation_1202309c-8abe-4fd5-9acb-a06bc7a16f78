{% extends 'base.html' %}
{% load static %}

{% block title %}حذف علاوات الموظف{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 15px 15px;
    }

    .delete-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 40px;
        margin-bottom: 30px;
        text-align: center;
    }

    .warning-icon {
        color: #dc3545;
        font-size: 4rem;
        margin-bottom: 20px;
    }

    .employee-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        text-align: right;
    }

    .allowance-status {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }

    .allowance-item {
        background: #f1f3f4;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
    }

    .allowance-item strong {
        display: block;
        margin-bottom: 8px;
        color: #495057;
    }

    .badge-yes {
        background-color: #28a745;
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .badge-no {
        background-color: #6c757d;
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .warning-text {
        color: #721c24;
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
        padding: 15px;
        margin: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-trash me-3"></i>
                    حذف علاوات الموظف
                </h1>
                <p class="mb-0 mt-2">تأكيد حذف سجل العلاوات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'employment:employee_allowance_list' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="delete-container">
        <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h3 class="mb-4">هل أنت متأكد من حذف سجل العلاوات؟</h3>
        
        <div class="warning-text">
            <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات العلاوات للموظف نهائياً.
        </div>

        <!-- Employee Information -->
        <div class="employee-info">
            <div class="row">
                <div class="col-md-6">
                    <strong>الرقم الوزاري:</strong>
                    <span>{{ allowance.ministry_number|default:'-' }}</span>
                </div>
                <div class="col-md-6">
                    <strong>اسم الموظف:</strong>
                    <span>{{ allowance.employee.full_name }}</span>
                </div>
            </div>
        </div>

        <!-- Current Allowances Status -->
        <h5 class="mb-3">العلاوات الحالية:</h5>
        <div class="allowance-status">
            <div class="allowance-item">
                <strong>علاوة التعليم</strong>
                {% if allowance.education_allowance == 'yes' %}
                    <span class="badge-yes">نعم</span>
                {% else %}
                    <span class="badge-no">لا</span>
                {% endif %}
            </div>
            <div class="allowance-item">
                <strong>التجيير</strong>
                {% if allowance.adjustment_allowance == 'yes' %}
                    <span class="badge-yes">نعم</span>
                {% else %}
                    <span class="badge-no">لا</span>
                {% endif %}
            </div>
            <div class="allowance-item">
                <strong>التنقلات</strong>
                {% if allowance.transportation_allowance == 'yes' %}
                    <span class="badge-yes">نعم</span>
                {% else %}
                    <span class="badge-no">لا</span>
                {% endif %}
            </div>
            <div class="allowance-item">
                <strong>العلاوة الإشرافية</strong>
                {% if allowance.supervisory_allowance == 'yes' %}
                    <span class="badge-yes">نعم</span>
                {% else %}
                    <span class="badge-no">لا</span>
                {% endif %}
            </div>
            <div class="allowance-item">
                <strong>علاوة فنية</strong>
                {% if allowance.technical_allowance == 'yes' %}
                    <span class="badge-yes">نعم</span>
                {% else %}
                    <span class="badge-no">لا</span>
                {% endif %}
            </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post" class="mt-4">
            {% csrf_token %}
            <div class="d-flex justify-content-center gap-3">
                <button type="submit" class="btn btn-danger btn-lg">
                    <i class="fas fa-trash me-2"></i>
                    نعم، احذف السجل
                </button>
                <a href="{% url 'employment:employee_allowance_list' %}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
