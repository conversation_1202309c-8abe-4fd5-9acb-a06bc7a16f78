{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الحالة المرضية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        color: #e74a3b;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .condition-info {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .condition-info h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }

    .info-item {
        margin-bottom: 15px;
    }

    .info-label {
        font-weight: bold;
        color: #5a5c69;
    }

    .info-value {
        color: #3a3b45;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>حذف الحالة المرضية</h2>
    <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى القائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="delete-warning">
            <i class="fas fa-exclamation-triangle"></i>
            هل أنت متأكد من رغبتك في حذف هذه الحالة المرضية؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>

        <div class="condition-info">
            <h4>معلومات الحالة المرضية</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">الرقم الوزاري</div>
                        <div class="info-value">{{ medical_condition.employee.ministry_number }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">اسم الموظف</div>
                        <div class="info-value">{{ medical_condition.employee.full_name }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">الحالة المرضية</div>
                        <div class="info-value">{{ medical_condition.condition.name }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">نوع الحالة</div>
                        <div class="info-value">{{ medical_condition.get_condition_type_display }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">تاريخ التقرير الطبي</div>
                        <div class="info-value">{{ medical_condition.medical_report_date|date:"Y-m-d" }}</div>
                    </div>
                </div>

            </div>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
                <a href="{% url 'employment:medical_condition_detail' medical_condition.pk %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
