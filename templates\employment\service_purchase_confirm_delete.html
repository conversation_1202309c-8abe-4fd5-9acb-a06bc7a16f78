{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف شراء الخدمة{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 2px solid #e74c3c;
    }
    
    .delete-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 1.5rem;
    }
    
    .employee-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .btn-custom {
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }
    
    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #e74c3c;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
            تأكيد حذف شراء الخدمة
        </h1>
        <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card delete-card">
                <div class="delete-header text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h4 class="mb-0">تحذير - حذف شراء الخدمة</h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h5 class="text-danger">هل أنت متأكد من حذف شراء الخدمة لهذا الموظف؟</h5>
                        <p class="text-muted">هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>

                    <!-- Employee Information -->
                    <div class="employee-info-card">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات شراء الخدمة المراد حذفها
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الموظف:</strong> {{ service_purchase.employee.full_name }}</p>
                                <p><strong>الرقم الوزاري:</strong> {{ service_purchase.employee.ministry_number|default:"-" }}</p>
                                <p><strong>التخصص:</strong> {{ service_purchase.employee.specialization|default:"-" }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>المدرسة:</strong> {{ service_purchase.target_school|default:"-" }}</p>
                                <p><strong>تاريخ الإنشاء:</strong> {{ service_purchase.created_at|date:"Y-m-d H:i" }}</p>
                                <p><strong>الحالة:</strong> {% if service_purchase.is_active %}نشط{% else %}غير نشط{% endif %}</p>
                            </div>
                        </div>
                        {% if service_purchase.notes %}
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <p><strong>الملاحظات:</strong> {{ service_purchase.notes }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Warning Message -->
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> بعد الحذف، سيتم إرجاع الموظف إلى قائمة الموظفين العادية ويمكن إضافة شراء خدمة جديد له لاحقاً.
                    </div>

                    <!-- Action Buttons -->
                    <form method="post" class="text-center">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-center">
                            <a href="{% url 'employment:service_purchase_list' %}" class="btn btn-secondary btn-custom me-md-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger btn-custom">
                                <i class="fas fa-trash"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
{% if messages %}
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endfor %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Add confirmation dialog
    $('form').on('submit', function(e) {
        if (!confirm('هل أنت متأكد من حذف شراء الخدمة لهذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}