{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة صفات التعيين - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة صفات التعيين</h2>
    <div>
        <a href="{% url 'employment:appointment_type_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة صفة تعيين جديدة
        </a>
        <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للكادر
        </a>
    </div>
</div>

<div class="row">
    {% for appointment_type in appointment_types %}
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{ appointment_type.name }}</h6>
            </div>
            <div class="card-body">
                <p>{{ appointment_type.description|default:"لا يوجد وصف"|truncatechars:100 }}</p>
                <p>
                    <strong>عدد الموظفين:</strong>
                    {{ appointment_type.employee_count }}
                </p>
            </div>
            <div class="card-footer">
                <a href="{% url 'employment:appointment_type_detail' appointment_type.pk %}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye"></i> عرض
                </a>
                <a href="{% url 'employment:appointment_type_update' appointment_type.pk %}" class="btn btn-warning btn-sm">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{% url 'employment:appointment_type_delete' appointment_type.pk %}" class="btn btn-danger btn-sm">
                    <i class="fas fa-trash"></i> حذف
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="alert alert-info">
            لا يوجد صفات تعيين. <a href="{% url 'employment:appointment_type_create' %}">إضافة صفة تعيين جديدة</a>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}
