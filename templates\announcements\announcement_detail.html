{% extends 'base.html' %}
{% load static %}

{% block title %}{{ announcement.title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .announcement-detail-header {
        background: linear-gradient(135deg, #{{ announcement.type_color }} 0%, #{{ announcement.priority_color }} 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    
    .detail-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .status-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    
    .info-item i {
        width: 20px;
        margin-right: 10px;
    }
    
    .content-section {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 15px;
        padding: 25px;
        border: 1px solid #e9ecef;
    }
    
    .stats-section {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 15px;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Announcement Header -->
    <div class="announcement-detail-header">
        <div class="row align-items-center">
            <div class="col-md-1">
                <i class="fas {{ announcement.type_icon }} fa-3x"></i>
            </div>
            <div class="col-md-9">
                <h1 class="mb-2">{{ announcement.title }}</h1>
                <div class="d-flex align-items-center flex-wrap">
                    <span class="badge bg-{{ announcement.type_color }} me-2 mb-2">
                        <i class="fas {{ announcement.type_icon }}"></i> {{ announcement.get_announcement_type_display }}
                    </span>
                    <span class="badge bg-{{ announcement.priority_color }} me-2 mb-2">
                        <i class="fas fa-flag"></i> {{ announcement.get_priority_display }}
                    </span>
                    {% if announcement.is_active %}
                        <span class="badge bg-success me-2 mb-2">
                            <i class="fas fa-check-circle"></i> مفعل
                        </span>
                    {% else %}
                        <span class="badge bg-danger me-2 mb-2">
                            <i class="fas fa-times-circle"></i> غير مفعل
                        </span>
                    {% endif %}
                    {% if announcement.show_on_homepage %}
                        <span class="badge bg-info me-2 mb-2">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-2 text-end">
                <div class="btn-group-vertical">
                    <a href="{% url 'announcements:announcement_update' announcement.pk %}" class="btn btn-warning mb-2">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="{% url 'announcements:announcements_list' %}" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card detail-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i> محتوى الإعلان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="content-section">
                        <p class="lead">{{ announcement.content|linebreaks }}</p>
                        

                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Information Card -->
            <div class="card detail-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> معلومات الإعلان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <i class="fas fa-user text-primary"></i>
                        <div>
                            <strong>أضيف بواسطة:</strong><br>
                            {{ announcement.created_by.get_full_name|default:announcement.created_by.username }}
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-calendar-plus text-success"></i>
                        <div>
                            <strong>تاريخ البداية:</strong><br>
                            {{ announcement.start_date|date:"Y-m-d H:i" }}
                        </div>
                    </div>
                    
                    {% if announcement.end_date %}
                    <div class="info-item">
                        <i class="fas fa-calendar-minus text-danger"></i>
                        <div>
                            <strong>تاريخ النهاية:</strong><br>
                            {{ announcement.end_date|date:"Y-m-d H:i" }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="info-item">
                        <i class="fas fa-clock text-warning"></i>
                        <div>
                            <strong>تاريخ الإضافة:</strong><br>
                            {{ announcement.created_at|date:"Y-m-d H:i" }}
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-edit text-secondary"></i>
                        <div>
                            <strong>آخر تحديث:</strong><br>
                            {{ announcement.updated_at|date:"Y-m-d H:i" }}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Card -->
            <div class="card detail-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> إحصائيات الإعلان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stats-section">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="mb-3">
                                    <i class="fas fa-eye fa-2x text-info mb-2"></i>
                                    <h4 class="text-info">{{ announcement.views_count }}</h4>
                                    <small class="text-muted">مشاهدة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-3">
                                    <i class="fas fa-mouse-pointer fa-2x text-success mb-2"></i>
                                    <h4 class="text-success">{{ announcement.clicks_count }}</h4>
                                    <small class="text-muted">نقرة</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if announcement.clicks_count > 0 and announcement.views_count > 0 %}
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                معدل النقر: {{ announcement.clicks_count }}/{{ announcement.views_count }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Status Card -->
            <div class="card detail-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-toggle-on"></i> حالة الإعلان
                    </h5>
                </div>
                <div class="card-body text-center">
                    {% if announcement.is_currently_active %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h5>الإعلان نشط</h5>
                            <p class="mb-0">يظهر للمستخدمين حالياً</p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-pause-circle fa-2x mb-2"></i>
                            <h5>الإعلان غير نشط</h5>
                            <p class="mb-0">لا يظهر للمستخدمين حالياً</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function trackClick(announcementId) {
    $.ajax({
        url: `/announcements/${announcementId}/track-click/`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(response) {
            if (response.success) {
                // Update clicks count display
                const clicksElement = document.querySelector('.text-success h4');
                if (clicksElement) {
                    clicksElement.textContent = response.clicks_count;
                }
            }
        }
    });
}

// Add CSRF token to all forms
$(document).ready(function() {
    $('meta[name=csrf-token]').remove();
    $('head').append('<meta name="csrf-token" content="{% csrf_token %}">');
});
</script>
{% endblock %}