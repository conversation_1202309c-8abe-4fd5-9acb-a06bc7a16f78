from django import forms
from django.utils import timezone
from .models import Announcement


class AnnouncementForm(forms.ModelForm):
    """Form for creating and editing announcements"""
    
    class Meta:
        model = Announcement
        fields = [
            'title', 'content', 'announcement_type', 'priority',
            'is_active', 'show_on_homepage', 'start_date', 'end_date',
            'link_url', 'link_text'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان الإعلان...'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'اكتب محتوى الإعلان هنا...'
            }),
            'announcement_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'show_on_homepage': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'end_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'link_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com'
            }),
            'link_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اقرأ المزيد'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default start_date to now if creating new announcement
        if not self.instance.pk:
            self.fields['start_date'].initial = timezone.now()
        
        # Add help text
        self.fields['end_date'].help_text = 'اتركه فارغاً للعرض دائماً'
        self.fields['link_text'].help_text = 'يظهر فقط إذا تم إدخال رابط'

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        link_url = cleaned_data.get('link_url')
        link_text = cleaned_data.get('link_text')
        
        # Validate date range
        if start_date and end_date:
            if start_date >= end_date:
                raise forms.ValidationError('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')
        
        # Validate link text
        if link_url and not link_text:
            cleaned_data['link_text'] = 'اقرأ المزيد'
        
        return cleaned_data


class AnnouncementSearchForm(forms.Form):
    """Form for searching announcements"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث في العنوان أو المحتوى...'
        })
    )
    
    announcement_type = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الأنواع')] + Announcement.TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    priority = forms.ChoiceField(
        required=False,
        choices=[('', 'جميع الأولويات')] + Announcement.PRIORITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[
            ('', 'الكل'),
            ('active', 'مفعل'),
            ('inactive', 'غير مفعل'),
            ('expired', 'منتهي الصلاحية')
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    show_on_homepage = forms.ChoiceField(
        required=False,
        choices=[
            ('', 'الكل'),
            ('yes', 'يظهر في الرئيسية'),
            ('no', 'لا يظهر في الرئيسية')
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )