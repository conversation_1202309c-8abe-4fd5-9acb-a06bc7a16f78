# Generated by Django 5.2 on 2025-05-05 05:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0006_employeeposition_school_level'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeIdentification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ministry_number', models.Char<PERSON><PERSON>(max_length=20, verbose_name='الرقم الوزاري')),
                ('national_id', models.Char<PERSON>ield(max_length=20, verbose_name='الرقم الوطني')),
                ('id_number', models.Char<PERSON><PERSON>(max_length=20, verbose_name='رقم الهوية')),
                ('birth_day', models.Integer<PERSON>ield(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6), (7, 7), (8, 8), (9, 9), (10, 10), (11, 11), (12, 12), (13, 13), (14, 14), (15, 15), (16, 16), (17, 17), (18, 18), (19, 19), (20, 20), (21, 21), (22, 22), (23, 23), (24, 24), (25, 25), (26, 26), (27, 27), (28, 28), (29, 29), (30, 30), (31, 31)], verbose_name='يوم الميلاد')),
                ('birth_month', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6), (7, 7), (8, 8), (9, 9), (10, 10), (11, 11), (12, 12)], verbose_name='شهر الميلاد')),
                ('birth_year', models.IntegerField(verbose_name='سنة الميلاد')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='identifications', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'بيانات تعريفية للموظف',
                'verbose_name_plural': 'البيانات التعريفية للموظفين',
                'ordering': ['employee__full_name'],
            },
        ),
    ]
