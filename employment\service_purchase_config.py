"""
Configuration settings for Service Purchase feature
"""

# Cache settings
CACHE_TIMEOUT = 300  # 5 minutes
CACHE_KEY_PREFIX = 'service_purchase_'

# Export settings
EXCEL_EXPORT_SETTINGS = {
    'filename': 'شراء_الخدمات.xlsx',
    'sheet_name': 'شراء الخدمات',
    'max_rows': 10000,  # Maximum rows to export
    'include_formatting': True,
}

# Pagination settings
PAGINATION_SETTINGS = {
    'per_page_options': [10, 25, 50, 100, 'all'],
    'default_per_page': 25,
}

# Search settings
SEARCH_SETTINGS = {
    'min_search_length': 2,
    'search_delay': 500,  # milliseconds
    'highlight_results': True,
}

# Validation settings
VALIDATION_RULES = {
    'max_service_years': 40,
    'max_service_months': 11,
    'max_service_days': 30,
    'min_purchase_amount': 1,
    'max_purchase_amount': 999999999.99,
}

# Display settings
DISPLAY_SETTINGS = {
    'date_format': 'Y-m-d',
    'currency_symbol': 'د.ع',
    'decimal_places': 2,
    'thousand_separator': ',',
}

# Notification settings
NOTIFICATION_SETTINGS = {
    'show_notifications': True,
    'auto_hide_delay': 5000,  # milliseconds
    'notification_types': {
        'create': 'success',
        'update': 'info',
        'delete': 'warning',
        'error': 'danger',
    }
}

# Security settings
SECURITY_SETTINGS = {
    'require_login': True,
    'log_all_actions': True,
    'validate_csrf': True,
}

# Performance settings
PERFORMANCE_SETTINGS = {
    'use_select_related': True,
    'use_prefetch_related': True,
    'enable_caching': True,
    'optimize_queries': True,
}