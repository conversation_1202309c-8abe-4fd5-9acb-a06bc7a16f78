{% extends 'base.html' %}
{% load static %}

{% block title %}{% if employee_rank %}تعديل رتبة موظف{% else %}إضافة رتبة جديدة للموظف{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if employee_rank %}تعديل رتبة موظف{% else %}إضافة رتبة جديدة للموظف{% endif %}</h2>
    <div>
        {% if not employee_rank %}
        <a href="{% url 'ranks:employee_rank_import' %}" class="btn btn-success">
            <i class="fas fa-file-import"></i> استيراد من Excel
        </a>
        {% endif %}
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة لقائمة رتب الموظفين
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">بيانات الرتبة</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <input type="hidden" name="employee_id" id="id_employee_id" value="{{ form.employee_id.value|default:'' }}">

            <div class="mb-3">
                <label for="ministry_number_input" class="form-label">الرقم الوزاري</label>
                <div class="input-group">
                    <input type="text" name="ministry_number" id="ministry_number_input" class="form-control" value="{{ form.ministry_number.value|default:'' }}" required {% if employee_rank %}readonly{% endif %}>
                    {% if not employee_rank %}
                    <button class="btn btn-secondary" type="button" id="search_employee_btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% endif %}
                </div>
                {% if form.ministry_number.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.ministry_number.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="employee_name_display" class="form-label">اسم الموظف</label>
                <input type="text" name="employee_name" id="employee_name_display" class="form-control" value="{{ form.employee_name.value|default:'' }}" readonly>
                {% if form.employee_name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.employee_name.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.rank_type.id_for_label }}" class="form-label">نوع الرتبة</label>
                    {{ form.rank_type }}
                    {% if form.rank_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.rank_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.date_obtained.id_for_label }}" class="form-label">تاريخ الحصول عليها</label>
                    {{ form.date_obtained }}
                    {% if form.date_obtained.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.date_obtained.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                {{ form.notes }}
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-secondary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/employees/get-by-ministry-number/?ministry_number=${ministryNumber}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Response data:', data);

                    if (data.success) {
                        // Update form fields
                        employeeNameDisplay.value = data.employee.full_name;
                        employeeIdInput.value = data.employee.id;
                    } else {
                        alert(data.error || 'لم يتم العثور على موظف بهذا الرقم الوزاري');
                        employeeNameDisplay.value = '';
                        employeeIdInput.value = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث');
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }
    });
</script>
{% endblock %}
