{% extends 'base.html' %}
{% load static %}

{% block title %}معالجة الموظف الزائد{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .detail-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 15px 20px;
        font-weight: bold;
        color: #495057;
    }

    .detail-card .card-body {
        padding: 20px;
    }

    .detail-row {
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: bold;
        color: #495057;
    }

    .detail-value {
        color: #212529;
    }

    .resolution-form {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .resolution-form .form-label {
        font-weight: bold;
    }

    .required-field::after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title mb-4">معالجة الموظف الزائد</h1>

            <!-- Employee Information -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-user me-2"></i> معلومات الموظف
                </div>
                <div class="card-body">
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">الرقم الوزاري</div>
                            <div class="detail-value">{{ excess_employee.employee.ministry_number }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">الاسم الكامل</div>
                            <div class="detail-value">{{ excess_employee.employee.full_name }}</div>
                        </div>
                    </div>
                    <div class="row detail-row">
                        <div class="col-md-6">
                            <div class="detail-label">القسم الحالي</div>
                            <div class="detail-value">{{ excess_employee.current_department.name }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-label">المسمى الوظيفي</div>
                            <div class="detail-value">{{ excess_employee.position.name }}</div>
                        </div>
                    </div>
                    <div class="row detail-row">
                        <div class="col-md-12">
                            <div class="detail-label">سبب اعتبار الموظف زائد</div>
                            <div class="detail-value">{{ excess_employee.reason }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="detail-card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i> إحصائيات الموظفين الزوائد
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 text-center">
                            <div class="detail-label">اجمالي عدد الموظفين الزوائد</div>
                            <div class="detail-value fs-4 text-warning">{{ pending_count }}</div>
                        </div>
                        <div class="col-md-6 text-center">
                            <div class="detail-label">تم تزويب الزوائد</div>
                            <div class="detail-value fs-4 text-success">{{ resolved_count }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resolution Form -->
            <div class="resolution-form">
                <h4 class="mb-3">معالجة الموظف الزائد</h4>
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.resolution_notes.id_for_label }}" class="form-label required-field">ملاحظات المعالجة</label>
                        {{ form.resolution_notes }}
                        <div class="form-text">أدخل ملاحظات حول كيفية معالجة حالة الموظف الزائد</div>
                        {% if form.resolution_notes.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.resolution_notes.errors }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'employment:excess_employee_detail' excess_employee.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> تأكيد المعالجة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
