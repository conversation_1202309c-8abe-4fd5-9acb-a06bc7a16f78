/* Hyper Navbar CSS - Dark Elegant Theme */

/* Navbar styles */
.navbar {
    background-color: #000000;
    box-shadow: 0 0 35px 0 rgba(0, 0, 0, 0.15);
    padding: 0;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1040;
    display: flex;
    align-items: center;
    transition: var(--sidebar-transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    color: #ffffff;
    padding: 0 1rem;
    height: 100%;
    display: flex;
    align-items: center;
}

.navbar-brand i {
    color: var(--hyper-primary);
    margin-left: 0.5rem;
}

.navbar .container-fluid {
    padding: 0 1rem;
}

/* Navbar buttons */
.navbar-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #333333;
    color: #ffffff;
    border: none;
    margin: 0 0.25rem;
    transition: var(--sidebar-transition);
    position: relative;
    cursor: pointer;
}

.navbar-btn:hover {
    background-color: var(--hyper-primary);
    color: #ffffff;
    transform: translateY(-2px);
}

/* Dark mode toggle button */
#darkModeToggle {
    transition: all 0.3s ease;
}

#darkModeToggle:hover {
    transform: rotate(20deg) translateY(-2px);
}

body.dark-mode #darkModeToggle {
    background-color: #6366f1;
}

body.dark-mode #darkModeToggle:hover {
    background-color: #4f46e5;
    transform: rotate(-20deg) translateY(-2px);
}

.navbar-btn i {
    font-size: 1rem;
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: var(--hyper-danger);
    color: #ffffff;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Search box */
.search-box {
    position: fixed;
    top: 70px;
    left: 1rem;
    right: 1rem;
    background-color: #000000;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    display: none !important;
    z-index: 1050;
    transition: none;
}

.search-box.show {
    display: block !important;
    animation: fadeInDown 0.3s ease;
}

.search-box .form-control {
    background-color: #333333;
    border: none;
    color: #ffffff;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
}

.search-box .form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.search-box .form-control::placeholder {
    color: var(--hyper-text-muted);
}

/* Notifications dropdown */
.notifications-dropdown {
    position: absolute;
    top: 70px;
    left: 1rem;
    right: 1rem;
    max-width: 360px;
    background-color: var(--hyper-sidebar-bg);
    border-radius: 0.5rem;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    z-index: 1050;
    transition: var(--sidebar-transition);
    max-height: 400px;
    overflow-y: auto;
}

@media (min-width: 576px) {
    .notifications-dropdown {
        left: auto;
        right: 1rem;
        width: 360px;
    }

    .search-box {
        left: auto;
        right: 1rem;
        width: 360px;
    }
}

.notifications-header {
    padding: 1rem;
    border-bottom: 1px solid var(--hyper-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notifications-header h6 {
    margin: 0;
    color: var(--hyper-text-dark);
    font-weight: 600;
}

.notifications-header a {
    color: var(--hyper-primary);
    font-size: 0.875rem;
    text-decoration: none;
}

.notifications-body {
    padding: 0;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid var(--hyper-border);
    display: flex;
    align-items: flex-start;
    transition: var(--sidebar-transition);
}

.notification-item:hover {
    background-color: var(--hyper-sidebar-hover);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--hyper-primary-light);
    color: var(--hyper-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.notification-icon.success {
    background-color: rgba(16, 185, 129, 0.2);
    color: var(--hyper-success);
}

.notification-icon.warning {
    background-color: rgba(245, 158, 11, 0.2);
    color: var(--hyper-warning);
}

.notification-icon.danger {
    background-color: rgba(239, 68, 68, 0.2);
    color: var(--hyper-danger);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--hyper-text-dark);
    margin-bottom: 0.25rem;
}

.notification-text {
    font-size: 0.8125rem;
    color: var(--hyper-text-muted);
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--hyper-text-muted);
}

.notifications-footer {
    padding: 0.75rem;
    text-align: center;
    border-top: 1px solid var(--hyper-border);
}

.notifications-footer a {
    color: var(--hyper-primary);
    font-size: 0.875rem;
    text-decoration: none;
    font-weight: 500;
}

/* User profile dropdown */
.user-dropdown {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: var(--sidebar-transition);
}

.user-dropdown:hover {
    background-color: var(--hyper-sidebar-hover);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--hyper-primary);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-left: 0.5rem;
}

.user-info {
    display: none;
}

@media (min-width: 768px) {
    .user-info {
        display: block;
    }
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--hyper-text-dark);
    margin: 0;
}

.user-role {
    font-size: 0.75rem;
    color: var(--hyper-text-muted);
    margin: 0;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode adjustments */
body.dark-mode .navbar {
    background-color: var(--dark-card-bg);
}

body.dark-mode .search-box,
body.dark-mode .notifications-dropdown {
    background-color: var(--dark-card-bg);
}

body.dark-mode .notification-item {
    border-color: var(--dark-border);
}

body.dark-mode .notifications-header,
body.dark-mode .notifications-footer {
    border-color: var(--dark-border);
}

body.dark-mode .search-box .form-control {
    background-color: var(--dark-bg);
}
