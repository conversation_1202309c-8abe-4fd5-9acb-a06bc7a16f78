{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-link me-2"></i>
            {{ title }}
        </h1>
        <a href="{% url 'home:important_links_admin' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        {% if action == 'add' %}
                        إضافة رابط مهم جديد
                        {% else %}
                        تعديل الرابط المهم
                        {% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.order.id_for_label }}" class="form-label">
                                    <i class="fas fa-sort-numeric-up me-1"></i>
                                    {{ form.order.label }}
                                </label>
                                {{ form.order }}
                                <div class="form-text">ترتيب الرابط في القائمة (0 = الأول)</div>
                                {% if form.order.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.order.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.url.id_for_label }}" class="form-label">
                                <i class="fas fa-link me-1"></i>
                                {{ form.url.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.url }}
                            {% if form.url.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.url.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.image.id_for_label }}" class="form-label">
                                    <i class="fas fa-upload me-1"></i>
                                    {{ form.image.label }}
                                </label>
                                {{ form.image }}
                                <div class="form-text">رفع صورة للرابط (يفضل 64x64 بكسل)</div>
                                {% if form.image.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.image.errors.0 }}
                                </div>
                                {% endif %}
                                {% if form.instance.image %}
                                <div class="mt-2">
                                    <small class="text-muted">الصورة الحالية:</small><br>
                                    <img src="{{ form.instance.image.url }}" alt="Current image"
                                         style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.favicon_url.id_for_label }}" class="form-label">
                                    <i class="fas fa-link me-1"></i>
                                    {{ form.favicon_url.label }}
                                </label>
                                {{ form.favicon_url }}
                                <div class="form-text">أو رابط أيقونة الموقع (اختياري)</div>
                                {% if form.favicon_url.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.favicon_url.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> يمكنك رفع صورة مخصصة أو استخدام رابط أيقونة الموقع. الصورة المرفوعة لها الأولوية.
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.description.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.is_active.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'home:important_links_admin' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if action == 'add' %}
                                إضافة الرابط
                                {% else %}
                                حفظ التغييرات
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معاينة الرابط</h6>
                </div>
                <div class="card-body">
                    <div id="linkPreview" class="text-center">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <p class="text-muted">أدخل بيانات الرابط لرؤية المعاينة</p>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">نصائح</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            استخدم أسماء واضحة ومفهومة للروابط
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            تأكد من صحة الروابط قبل الحفظ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            استخدم الترتيب لتنظيم الروابط حسب الأهمية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            يمكنك رفع صورة مخصصة أو استخدام رابط أيقونة
                        </li>
                        <li>
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            الصورة المرفوعة لها الأولوية على رابط الأيقونة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let uploadedImageUrl = null;

    // Handle image upload preview
    $('#{{ form.image.id_for_label }}').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedImageUrl = e.target.result;
                updatePreview();
            };
            reader.readAsDataURL(file);
        } else {
            uploadedImageUrl = null;
            updatePreview();
        }
    });

    // Update preview when form fields change
    function updatePreview() {
        const name = $('#{{ form.name.id_for_label }}').val();
        const url = $('#{{ form.url.id_for_label }}').val();
        const faviconUrl = $('#{{ form.favicon_url.id_for_label }}').val();
        const description = $('#{{ form.description.id_for_label }}').val();
        const isActive = $('#{{ form.is_active.id_for_label }}').is(':checked');

        if (name && url) {
            let previewHtml = '<div class="link-preview text-center">';

            // Prioritize uploaded image over favicon URL
            if (uploadedImageUrl) {
                previewHtml += `<img src="${uploadedImageUrl}" alt="uploaded image" class="mb-2" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">`;
            } else if (faviconUrl) {
                previewHtml += `<img src="${faviconUrl}" alt="favicon" class="mb-2" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">`;
                previewHtml += '<i class="fas fa-link mb-2" style="display: none; font-size: 32px;"></i>';
            } else {
                previewHtml += '<i class="fas fa-link mb-2" style="font-size: 32px;"></i>';
            }

            previewHtml += `<br><strong>${name}</strong><br>`;
            previewHtml += `<small class="text-muted">${url}</small><br>`;

            if (description) {
                previewHtml += `<small class="text-info">${description}</small><br>`;
            }

            previewHtml += `<span class="badge badge-${isActive ? 'success' : 'danger'} mt-2">${isActive ? 'نشط' : 'غير نشط'}</span>`;
            previewHtml += '</div>';

            $('#linkPreview').html(previewHtml);
        } else {
            $('#linkPreview').html('<i class="fas fa-link fa-3x text-muted mb-3"></i><p class="text-muted">أدخل بيانات الرابط لرؤية المعاينة</p>');
        }
    }

    // Bind events
    $('#{{ form.name.id_for_label }}, #{{ form.url.id_for_label }}, #{{ form.favicon_url.id_for_label }}, #{{ form.description.id_for_label }}').on('input', updatePreview);
    $('#{{ form.is_active.id_for_label }}').on('change', updatePreview);

    // Initial preview
    updatePreview();

    // Check if there's an existing image
    {% if form.instance.image %}
    uploadedImageUrl = '{{ form.instance.image.url }}';
    updatePreview();
    {% endif %}
});
</script>
{% endblock %}
