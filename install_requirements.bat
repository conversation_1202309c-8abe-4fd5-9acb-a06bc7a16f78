@echo off
chcp 65001 >nul
title نظام شؤون الموظفين - تثبيت المتطلبات
color 0A

echo ========================================
echo نظام شؤون الموظفين - تثبيت المتطلبات
echo HR Management System - Install Requirements
echo ========================================
echo.

echo [INFO] بدء عملية تثبيت المكتبات المطلوبة...
echo [INFO] Starting installation of required packages...
echo.

REM Check if Python is installed and accessible via PATH
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] تم العثور على Python في PATH
    echo [SUCCESS] Python found in PATH
    python --version
    set PYTHON_CMD=python
    goto :check_pip
)

REM Check if venv exists
if exist "venv\Scripts\python.exe" (
    echo [SUCCESS] تم العثور على البيئة الافتراضية venv
    echo [SUCCESS] Found virtual environment venv
    set PYTHON_CMD=venv\Scripts\python.exe
    goto :check_pip
)

REM Check if env exists
if exist "env\Scripts\python.exe" (
    echo [SUCCESS] تم العثور على البيئة الافتراضية env
    echo [SUCCESS] Found virtual environment env
    set PYTHON_CMD=env\Scripts\python.exe
    goto :check_pip
)

REM Check if hr_system_env exists
if exist "hr_system_env\Scripts\python.exe" (
    echo [SUCCESS] تم العثور على البيئة الافتراضية hr_system_env
    echo [SUCCESS] Found virtual environment hr_system_env
    set PYTHON_CMD=hr_system_env\Scripts\python.exe
    goto :check_pip
)

REM Check common Python installation paths
if exist "C:\Python313\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.13
    echo [SUCCESS] Found Python 3.13
    set PYTHON_CMD=C:\Python313\python.exe
    goto :check_pip
)

if exist "C:\Python312\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.12
    echo [SUCCESS] Found Python 3.12
    set PYTHON_CMD=C:\Python312\python.exe
    goto :check_pip
)

if exist "C:\Python311\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.11
    echo [SUCCESS] Found Python 3.11
    set PYTHON_CMD=C:\Python311\python.exe
    goto :check_pip
)

if exist "C:\Python310\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.10
    echo [SUCCESS] Found Python 3.10
    set PYTHON_CMD=C:\Python310\python.exe
    goto :check_pip
)

if exist "C:\Program Files\Python313\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.13 في Program Files
    echo [SUCCESS] Found Python 3.13 in Program Files
    set PYTHON_CMD="C:\Program Files\Python313\python.exe"
    goto :check_pip
)

if exist "C:\Program Files\Python312\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.12 في Program Files
    echo [SUCCESS] Found Python 3.12 in Program Files
    set PYTHON_CMD="C:\Program Files\Python312\python.exe"
    goto :check_pip
)

if exist "C:\Program Files\Python311\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.11 في Program Files
    echo [SUCCESS] Found Python 3.11 in Program Files
    set PYTHON_CMD="C:\Program Files\Python311\python.exe"
    goto :check_pip
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.13 في AppData
    echo [SUCCESS] Found Python 3.13 in AppData
    set PYTHON_CMD="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
    goto :check_pip
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.12 في AppData
    echo [SUCCESS] Found Python 3.12 in AppData
    set PYTHON_CMD="C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
    goto :check_pip
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo [SUCCESS] تم العثور على Python 3.11 في AppData
    echo [SUCCESS] Found Python 3.11 in AppData
    set PYTHON_CMD="C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
    goto :check_pip
)

echo [ERROR] لم يتم العثور على Python!
echo [ERROR] Python not found in common locations!
echo [INFO] يرجى تثبيت Python من: https://www.python.org/downloads/
echo [INFO] Please install Python from: https://www.python.org/downloads/
echo [INFO] تأكد من إضافة Python إلى PATH أثناء التثبيت
echo [INFO] Make sure to add Python to PATH during installation
echo.
echo [INFO] اضغط أي مفتاح للخروج...
echo [INFO] Press any key to exit...
pause >nul
exit /b 1

:check_pip
echo.
echo [INFO] التحقق من pip...
echo [INFO] Checking pip...
%PYTHON_CMD% -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] pip غير متوفر، جاري تثبيته...
    echo [WARNING] pip not available, installing...
    %PYTHON_CMD% -m ensurepip --upgrade
    if %errorlevel% neq 0 (
        echo [ERROR] فشل في تثبيت pip!
        echo [ERROR] Failed to install pip!
        goto :error_exit
    )
)

echo [SUCCESS] تم العثور على pip
echo [SUCCESS] pip found
%PYTHON_CMD% -m pip --version
echo.

REM Upgrade pip
echo [INFO] جاري تحديث pip...
echo [INFO] Upgrading pip...
%PYTHON_CMD% -m pip install --upgrade pip
echo.

REM Install basic tools
echo [INFO] جاري تثبيت الأدوات الأساسية...
echo [INFO] Installing basic tools...
%PYTHON_CMD% -m pip install wheel setuptools
echo.

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo [ERROR] ملف requirements.txt غير موجود!
    echo [ERROR] requirements.txt file not found!
    echo [INFO] يرجى التأكد من وجود الملف في نفس مجلد هذا الملف
    echo [INFO] Please make sure the file exists in the same folder as this file
    goto :error_exit
)

echo [SUCCESS] تم العثور على ملف requirements.txt
echo [SUCCESS] requirements.txt file found
echo.

goto :install_packages

:install_packages
echo ========================================
echo [INFO] جاري تثبيت المكتبات من requirements.txt...
echo [INFO] Installing packages from requirements.txt...
echo [INFO] هذا قد يستغرق عدة دقائق...
echo [INFO] This may take several minutes...
echo ========================================
echo.

%PYTHON_CMD% -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo [WARNING] حدث خطأ أثناء تثبيت بعض المكتبات!
    echo [WARNING] Error occurred while installing some packages!
    echo [INFO] جاري المحاولة مرة أخرى مع تجاهل الأخطاء...
    echo [INFO] Trying again with error tolerance...
    %PYTHON_CMD% -m pip install -r requirements.txt --ignore-installed --force-reinstall

    if %errorlevel% neq 0 (
        echo [WARNING] بعض المكتبات قد لا تكون مثبتة بشكل صحيح
        echo [WARNING] Some packages may not be installed correctly
        echo [INFO] يمكنك تثبيتها يدوياً لاحقاً
        echo [INFO] You can install them manually later
    )
)

echo.
echo ========================================
echo [SUCCESS] تم الانتهاء من تثبيت المكتبات!
echo [SUCCESS] Package installation completed!
echo ========================================
echo.

REM Show installed packages
echo [INFO] عرض المكتبات المثبتة...
echo [INFO] Showing installed packages...
%PYTHON_CMD% -m pip list
echo.

REM Check Django installation
echo [INFO] التحقق من تثبيت Django...
echo [INFO] Checking Django installation...
%PYTHON_CMD% -c "import django; print('Django version:', django.get_version())" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] تم تثبيت Django بنجاح!
    echo [SUCCESS] Django installed successfully!
) else (
    echo [ERROR] Django غير مثبت بشكل صحيح!
    echo [ERROR] Django not installed correctly!
)
echo.

REM Additional setup instructions
echo ========================================
echo تعليمات الإعداد التالية - Next Setup Instructions
echo ========================================
echo.
echo [INFO] الخطوات التالية لتشغيل النظام:
echo [INFO] Next steps to run the system:
echo.
echo 1. إنشاء قاعدة البيانات:
echo    Create database:
echo    %PYTHON_CMD% manage.py migrate
echo.
echo 2. إنشاء مستخدم مدير:
echo    Create admin user:
echo    %PYTHON_CMD% manage.py createsuperuser
echo.
echo 3. جمع الملفات الثابتة:
echo    Collect static files:
echo    %PYTHON_CMD% manage.py collectstatic
echo.
echo 4. تشغيل الخادم:
echo    Run server:
echo    %PYTHON_CMD% manage.py runserver
echo.
echo 5. فتح المتصفح والذهاب إلى:
echo    Open browser and go to:
echo    http://127.0.0.1:8000
echo.

REM Create a quick start script
echo [INFO] إنشاء ملف تشغيل سريع...
echo [INFO] Creating quick start script...
echo @echo off > start_server.bat
echo title نظام شؤون الموظفين - خادم التطوير >> start_server.bat
echo echo جاري تشغيل خادم التطوير... >> start_server.bat
echo echo Starting development server... >> start_server.bat
echo %PYTHON_CMD% manage.py runserver >> start_server.bat
echo pause >> start_server.bat

echo [SUCCESS] تم إنشاء ملف start_server.bat لتشغيل الخادم
echo [SUCCESS] Created start_server.bat to run the server
echo.

goto :success_exit

:error_exit
echo.
echo ========================================
echo [ERROR] حدث خطأ أثناء التثبيت!
echo [ERROR] An error occurred during installation!
echo ========================================
echo.
echo [INFO] اضغط أي مفتاح للخروج...
echo [INFO] Press any key to exit...
pause >nul
exit /b 1

:success_exit
echo ========================================
echo [SUCCESS] تم الانتهاء بنجاح!
echo [SUCCESS] Installation completed successfully!
echo ========================================
echo.
echo [INFO] يمكنك الآن تشغيل النظام باستخدام start_server.bat
echo [INFO] You can now run the system using start_server.bat
echo.
echo [INFO] اضغط أي مفتاح للخروج...
echo [INFO] Press any key to exit...
pause >nul
