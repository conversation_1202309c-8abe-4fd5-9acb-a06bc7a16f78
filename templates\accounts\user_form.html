{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}{% if user_obj %}تعديل بيانات {{ user_obj.username }}{% else %}إضافة مستخدم جديد{% endif %} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-group {
        margin-bottom: 1rem;
    }
    .form-group label {
        font-weight: bold;
    }
    .nav-tabs .nav-link {
        color: #6c757d;
        font-weight: 500;
    }
    .nav-tabs .nav-link.active {
        color: #0d6efd;
        font-weight: 600;
    }
    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 20px;
        border-radius: 0 0 5px 5px;
    }
    .module-tab-content {
        max-height: 300px;
        overflow-y: auto;
    }
    .permission-header {
        background-color: #f8f9fa;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
    }
    .permission-header .form-check {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% if user_obj %}تعديل بيانات {{ user_obj.username }}{% else %}إضافة مستخدم جديد{% endif %}</h2>
    <a href="{% if user_obj %}{% url 'accounts:user_detail' user_obj.pk %}{% else %}{% url 'accounts:user_list' %}{% endif %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة
    </a>
</div>

<div class="row">
    <div class="{% if user_obj %}col-md-8{% else %}col-md-8{% endif %}">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات المستخدم</h6>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.username.id_for_label }}">اسم المستخدم</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        {% if not user_obj %}
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password1.id_for_label }}">كلمة المرور</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.password2.id_for_label }}">تأكيد كلمة المرور</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}">الاسم الأول</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}">الاسم الأخير</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">البريد الإلكتروني</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone_number.id_for_label }}">رقم الهاتف</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.address.id_for_label }}">العنوان</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="m-0">صلاحيات المستخدم</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                {{ form.is_full_admin }}
                                                <label class="form-check-label" for="{{ form.is_full_admin.id_for_label }}">
                                                    مدير النظام (جميع الصلاحيات)
                                                </label>
                                                <div class="form-text text-muted small">
                                                    منح المستخدم جميع الصلاحيات وإمكانية الوصول إلى جميع الصفحات
                                                </div>
                                            </div>
                                            <!-- Hidden field for backward compatibility -->
                                            {{ form.is_admin_user }}
                                            <!-- Hidden fields for is_admin and is_staff -->
                                            {{ form.is_admin }}
                                            {{ form.is_staff }}
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="{{ form.user_permission_level.id_for_label }}">{{ form.user_permission_level.label }}</label>
                                                {{ form.user_permission_level }}
                                                {% if form.user_permission_level.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.user_permission_level.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                                <div class="form-text text-muted small">
                                                    اختر مستوى الصلاحية للمستخدم. سيتم تطبيق هذه الصلاحية على جميع صفحات النظام الجديدة والمحدثة.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if user_obj %}
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    نشط
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- معلومات الصفحات الجديدة -->
                    <div class="col-md-12">
                        <div class="card mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0"><i class="fas fa-star me-2"></i>الصفحات الجديدة المضافة للنظام</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> تم إضافة صفحات جديدة للنظام. الصلاحيات المحددة أعلاه تشمل هذه الصفحات الجديدة.
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3"><i class="fas fa-cogs me-2"></i>صفحات الإدارة والمراقبة</h6>
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-history text-info me-2"></i>
                                                <strong>سجل حركات النظام:</strong> مراقبة جميع العمليات والأنشطة
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-chart-line text-success me-2"></i>
                                                <strong>لوحة تحليلات البيانات:</strong> إحصائيات وتحليلات متقدمة
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-bell text-warning me-2"></i>
                                                <strong>نظام الإشعارات:</strong> إدارة التنبيهات والرسائل
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3"><i class="fas fa-tools me-2"></i>صفحات الخدمات والأدوات</h6>
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-link text-primary me-2"></i>
                                                <strong>إدارة الروابط المهمة:</strong> إضافة وتعديل الروابط مع رفع الصور
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-file-signature text-info me-2"></i>
                                                <strong>النماذج المعتمدة:</strong> إدارة النماذج الرسمية
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-exchange-alt text-success me-2"></i>
                                                <strong>النقل الداخلي:</strong> إدارة طلبات النقل الداخلي
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-search text-warning me-2"></i>
                                                <strong>استعلام رصيد الإجازات:</strong> البحث عن أرصدة الإجازات
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>تحديثات حديثة:</strong> تم تحسين جميع الصفحات لتدعم AJAX والتحديث السريع، مع إضافة خيار "عرض الكل" في جداول البيانات.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


</div>


{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(function(element) {
            element.classList.add('form-check-input');
        });

        // Handle interaction between is_full_admin and user_permission_level
        const fullAdminCheckbox = document.getElementById('{{ form.is_full_admin.id_for_label }}');
        const permissionLevelSelect = document.getElementById('{{ form.user_permission_level.id_for_label }}');
        const form = document.querySelector('form');

        if (fullAdminCheckbox && permissionLevelSelect && form) {
            // When full admin is checked, hide permission level but don't disable it
            fullAdminCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    permissionLevelSelect.parentElement.style.display = 'none';
                    permissionLevelSelect.value = '';
                } else {
                    permissionLevelSelect.parentElement.style.display = 'block';
                }
            });

            // When permission level is selected, uncheck full admin
            permissionLevelSelect.addEventListener('change', function() {
                if (this.value) {
                    fullAdminCheckbox.checked = false;
                }
            });

            // Initialize state on page load
            if (fullAdminCheckbox.checked) {
                permissionLevelSelect.parentElement.style.display = 'none';
                permissionLevelSelect.value = '';
            }

            // Add a hidden input for is_full_admin when the form is submitted
            form.addEventListener('submit', function(e) {
                // Make sure all fields are enabled before submitting
                permissionLevelSelect.disabled = false;

                // Add a hidden input to explicitly indicate if is_full_admin is unchecked
                if (!fullAdminCheckbox.checked) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'is_full_admin_explicitly_unchecked';
                    hiddenInput.value = 'true';
                    form.appendChild(hiddenInput);
                }
            });
        }
    });
</script>
{% endblock %}
