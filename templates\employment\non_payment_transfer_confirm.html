{% extends 'base.html' %}
{% load static %}

{% block title %}ترحيل سجلات عدم الصرف - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .transfer-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 2px solid #ffc107;
    }

    .transfer-card .card-header {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
        border: none;
        padding: 20px;
    }

    .warning-icon {
        font-size: 4rem;
        color: #ffc107;
        margin-bottom: 1rem;
    }

    .btn-transfer {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        color: #212529;
    }

    .btn-transfer:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        color: #212529;
    }

    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .record-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border-left: 4px solid #ffc107;
        transition: all 0.3s ease;
    }

    .record-item:hover {
        background: #e9ecef;
        transform: translateX(-5px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-exchange-alt text-warning me-2"></i>
                ترحيل سجلات عدم الصرف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employment:non_payment_list' %}">عدم الصرف</a></li>
                    <li class="breadcrumb-item active">ترحيل السجلات</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Transfer Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="transfer-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد ترحيل سجلات عدم الصرف
                    </h5>
                </div>
                <div class="card-body p-4">
                    {% if pending_records %}
                    <div class="text-center mb-4">
                        <i class="fas fa-exchange-alt warning-icon"></i>
                        <h4 class="text-warning">تأكيد الترحيل</h4>
                        <p class="text-muted">هل أنت متأكد من رغبتك في ترحيل السجلات التالية؟</p>
                        <p class="text-warning"><strong>بعد الترحيل لن تتمكن من تعديل أو حذف هذه السجلات!</strong></p>
                    </div>

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h3 class="text-warning">{{ pending_records.count }}</h3>
                                    <p class="mb-0">سجل للترحيل</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h3 class="text-info">
                                        {% for record in pending_records %}
                                            {% if forloop.first %}{{ record.days_count }}{% else %}{{ record.days_count|add:forloop.counter0 }}{% endif %}
                                        {% empty %}0{% endfor %}
                                    </h3>
                                    <p class="mb-0">إجمالي الأيام</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h3 class="text-success">{{ pending_records|length }}</h3>
                                    <p class="mb-0">موظف متأثر</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Records List -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-list me-2"></i>
                            السجلات المراد ترحيلها:
                        </h6>
                        <div class="row">
                            {% for record in pending_records %}
                            <div class="col-md-6 mb-3">
                                <div class="record-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-user me-2"></i>
                                                {{ record.employee.full_name }}
                                            </h6>
                                            <p class="mb-1 text-muted">
                                                <i class="fas fa-id-card me-1"></i>
                                                {{ record.ministry_number }}
                                            </p>
                                            <p class="mb-1 text-muted">
                                                <i class="fas fa-building me-1"></i>
                                                {{ record.department_name }}
                                            </p>
                                            <p class="mb-0 text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ record.date|date:"Y-m-d" }}
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-warning text-dark">
                                                {{ record.days_count }} يوم
                                            </span>
                                        </div>
                                    </div>
                                    {% if record.notes %}
                                    <div class="mt-2 pt-2 border-top">
                                        <small class="text-muted">
                                            <i class="fas fa-sticky-note me-1"></i>
                                            {{ record.notes|truncatechars:50 }}
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'employment:non_payment_list' %}" class="btn btn-cancel text-white">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-transfer">
                                <i class="fas fa-exchange-alt me-2"></i>تأكيد الترحيل
                            </button>
                        </form>
                    </div>

                    {% else %}
                    <!-- No Records to Transfer -->
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-4x text-info mb-3"></i>
                        <h4 class="text-info">لا توجد سجلات للترحيل</h4>
                        <p class="text-muted">جميع سجلات عدم الصرف تم ترحيلها مسبقاً أو لا توجد سجلات معلقة.</p>
                        <a href="{% url 'employment:non_payment_list' %}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>العودة إلى القائمة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add confirmation dialog for transfer button
    $('form').on('submit', function(e) {
        const recordCount = {{ pending_records.count }};
        if (!confirm(`هل أنت متأكد من رغبتك في ترحيل ${recordCount} سجل؟ لن تتمكن من تعديلها بعد الترحيل!`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
