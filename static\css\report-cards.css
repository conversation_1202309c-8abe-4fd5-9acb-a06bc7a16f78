/* Report Cards Styling */

/* Add border and shadow to report cards */
.card {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-right: 4px solid #222;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Style card headers */
.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Style card titles */
.card-title {
    font-weight: bold;
    margin-bottom: 0.75rem;
}

/* Style card text */
.card-text {
    color: #6c757d;
}

/* Style buttons in cards */
.card .btn {
    margin-top: 0.5rem;
}

/* Add different border colors for different card types */
.card:nth-child(3n+1) {
    border-right-color: #4e73df;
}

.card:nth-child(3n+2) {
    border-right-color: #1cc88a;
}

.card:nth-child(3n+3) {
    border-right-color: #36b9cc;
}

/* Add some padding to card body */
.card-body {
    padding: 1.5rem;
}
