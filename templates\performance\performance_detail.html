{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل التقرير السنوي - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل التقرير السنوي</h2>
    <div>
        <a href="{% url 'performance:performance_update' evaluation.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'performance:performance_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للتقارير السنوية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات التقييم</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الموظف</th>
                        <td>
                            <a href="{% url 'employees:employee_detail' evaluation.employee.pk %}">
                                {{ evaluation.employee.full_name }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">السنة</th>
                        <td>{{ evaluation.year }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الدرجة</th>
                        <td>{{ evaluation.score }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الدرجة القصوى</th>
                        <td>{{ evaluation.max_score }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">النسبة المئوية</th>
                        <td>{{ evaluation.percentage|floatformat:2 }}%</td>
                    </tr>
                    <tr>
                        <th class="bg-light">المقيم</th>
                        <td>{{ evaluation.evaluator|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الملاحظات</th>
                        <td>{{ evaluation.comments|default:"-" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">تقييمات الموظف السابقة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>السنة</th>
                                <th>الدرجة</th>
                                <th>النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prev_eval in previous_evaluations %}
                            <tr>
                                <td>{{ prev_eval.year }}</td>
                                <td>{{ prev_eval.score }} / {{ prev_eval.max_score }}</td>
                                <td>{{ prev_eval.percentage|floatformat:2 }}%</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center">لا يوجد تقييمات سابقة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-primary text-white">
                <h6 class="m-0 font-weight-bold">مستوى التقييم</h6>
            </div>
            <div class="card-body text-center">
                <div class="display-4 mb-3">
                    {{ evaluation.percentage|floatformat:0 }}%
                </div>

                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar
                        {% if evaluation.percentage >= 90 %}
                            bg-success
                        {% elif evaluation.percentage >= 70 %}
                            bg-info
                        {% elif evaluation.percentage >= 50 %}
                            bg-warning
                        {% else %}
                            bg-danger
                        {% endif %}"
                        role="progressbar"
                        style="width: {{ evaluation.percentage }}%;"
                        aria-valuenow="{{ evaluation.percentage }}"
                        aria-valuemin="0"
                        aria-valuemax="100">
                        {{ evaluation.percentage|floatformat:0 }}%
                    </div>
                </div>

                <div class="mt-3">
                    {% if evaluation.percentage >= 90 %}
                        <span class="badge bg-success">ممتاز</span>
                    {% elif evaluation.percentage >= 70 %}
                        <span class="badge bg-info">جيد جداً</span>
                    {% elif evaluation.percentage >= 50 %}
                        <span class="badge bg-warning">جيد</span>
                    {% else %}
                        <span class="badge bg-danger">ضعيف</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
