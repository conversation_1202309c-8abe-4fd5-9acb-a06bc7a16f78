"""
Script to generate placeholder images for the instructions page.
This script creates simple placeholder images with text indicating what should be shown.
"""

from PIL import Image, ImageDraw, ImageFont
import os

# Create the directory if it doesn't exist
os.makedirs(os.path.dirname(os.path.abspath(__file__)), exist_ok=True)

# Define the image size and background color
width, height = 800, 600
bg_color = (240, 240, 240)  # Light gray background

# Define the images to create
images = [
    {"filename": "login.png", "title": "شاشة تسجيل الدخول"},
    {"filename": "dashboard.png", "title": "لوحة التحكم الرئيسية"},
    {"filename": "employee_list.png", "title": "قائمة الموظفين"},
    {"filename": "add_employee.png", "title": "نموذج إضافة موظف جديد"},
    {"filename": "leave_list.png", "title": "قائمة الإجازات"},
    {"filename": "add_leave.png", "title": "نموذج إضافة إجازة جديدة"},
    {"filename": "reports.png", "title": "صفحة التقارير"},
    {"filename": "profile.png", "title": "صفحة الملف الشخصي"},
    {"filename": "change_password.png", "title": "نموذج تغيير كلمة المرور"},
]

# Create each image
for img_info in images:
    # Create a new image with the background color
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Draw a border
    border_color = (200, 200, 200)  # Lighter gray for the border
    draw.rectangle([(10, 10), (width-10, height-10)], outline=border_color, width=2)
    
    # Add text
    text_color = (50, 50, 50)  # Dark gray for text
    
    # Draw the title
    title_text = img_info["title"]
    title_position = (width // 2, height // 2 - 50)
    draw.text(title_position, title_text, fill=text_color, anchor="mm")
    
    # Draw instructions
    instructions_text = "يرجى استبدال هذه الصورة بلقطة شاشة حقيقية من النظام"
    instructions_position = (width // 2, height // 2 + 50)
    draw.text(instructions_position, instructions_text, fill=text_color, anchor="mm")
    
    # Save the image
    img.save(os.path.join(os.path.dirname(os.path.abspath(__file__)), img_info["filename"]))
    
    print(f"Created {img_info['filename']}")

print("All placeholder images have been created.")
