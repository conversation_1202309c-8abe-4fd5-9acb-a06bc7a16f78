{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-trash me-2"></i>
            {{ title }}
        </h1>
        <a href="{% url 'home:important_links_admin' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الرابط المهم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير!
                        </h5>
                        <p>هل أنت متأكد من أنك تريد حذف هذا الرابط المهم؟</p>
                        <hr>
                        <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>

                    <!-- Link Details -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                {% if link.favicon_url %}
                                <img src="{{ link.favicon_url }}" alt="favicon" class="me-2" style="width: 16px; height: 16px;">
                                {% else %}
                                <i class="fas fa-link me-2"></i>
                                {% endif %}
                                {{ link.name }}
                            </h6>
                            <p class="card-text">
                                <strong>الرابط:</strong> 
                                <a href="{{ link.url }}" target="_blank" class="text-primary">
                                    {{ link.url }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </p>
                            {% if link.description %}
                            <p class="card-text">
                                <strong>الوصف:</strong> {{ link.description }}
                            </p>
                            {% endif %}
                            <p class="card-text">
                                <small class="text-muted">
                                    <strong>الترتيب:</strong> {{ link.order }} | 
                                    <strong>الحالة:</strong> 
                                    {% if link.is_active %}
                                    <span class="badge badge-success">نشط</span>
                                    {% else %}
                                    <span class="badge badge-danger">غير نشط</span>
                                    {% endif %}
                                </small>
                            </p>
                        </div>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'home:important_links_admin' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
