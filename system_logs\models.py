from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()

class SystemLog(models.Model):
    """Model for storing system logs"""
    # Log types
    CREATE = 'create'
    UPDATE = 'update'
    DELETE = 'delete'
    VIEW = 'view'
    LOGIN = 'login'
    LOGOUT = 'logout'
    EXPORT = 'export'
    IMPORT = 'import'
    BACKUP = 'backup'
    RESTORE = 'restore'
    OTHER = 'other'

    LOG_TYPE_CHOICES = [
        (CREATE, 'إضافة'),
        (UPDATE, 'تعديل'),
        (DELETE, 'حذف'),
        (VIEW, 'عرض'),
        (LOGIN, 'تسجيل دخول'),
        (LOGOUT, 'تسجيل خروج'),
        (EXPORT, 'تصدير'),
        (IMPORT, 'استيراد'),
        (BACKUP, 'نسخ احتياطي'),
        (RESTORE, 'استعادة'),
        (OTHER, 'أخرى'),
    ]

    # Log modules
    EMPLOYEES = 'employees'
    EMPLOYMENT = 'employment'
    LEAVES = 'leaves'
    PERFORMANCE = 'performance'
    REPORTS = 'reports'
    ACCOUNTS = 'accounts'
    BACKUP = 'backup'
    DISCIPLINARY = 'disciplinary'
    FILES = 'files'
    RANKS = 'ranks'
    SYSTEM = 'system'

    MODULE_CHOICES = [
        (EMPLOYEES, 'الموظفين'),
        (EMPLOYMENT, 'الكادر'),
        (LEAVES, 'الإجازات'),
        (PERFORMANCE, 'التقارير السنوية'),
        (REPORTS, 'تقارير النظام'),
        (ACCOUNTS, 'المستخدمين'),
        (BACKUP, 'النسخ الاحتياطية'),
        (DISCIPLINARY, 'الإجراءات'),
        (FILES, 'الملفات'),
        (RANKS, 'الرتب'),
        (SYSTEM, 'النظام'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='system_logs')
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    operating_system = models.CharField(_('Operating System'), max_length=255, blank=True, null=True)
    module = models.CharField(_('Module'), max_length=20, choices=MODULE_CHOICES)
    action = models.CharField(_('Action'), max_length=20, choices=LOG_TYPE_CHOICES)
    page = models.CharField(_('Page'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    object_id = models.CharField(_('Object ID'), max_length=50, blank=True, null=True)
    object_repr = models.CharField(_('Object Representation'), max_length=255, blank=True, null=True)

    class Meta:
        verbose_name = _('System Log')
        verbose_name_plural = _('System Logs')
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.get_action_display()} - {self.get_module_display()} - {self.timestamp}"


class SystemError(models.Model):
    """Model for storing system errors"""
    
    # Error types
    SYNTAX_ERROR = 'syntax_error'
    TYPE_ERROR = 'type_error'
    VALUE_ERROR = 'value_error'
    ATTRIBUTE_ERROR = 'attribute_error'
    KEY_ERROR = 'key_error'
    INDEX_ERROR = 'index_error'
    NAME_ERROR = 'name_error'
    IMPORT_ERROR = 'import_error'
    PERMISSION_ERROR = 'permission_error'
    DATABASE_ERROR = 'database_error'
    VALIDATION_ERROR = 'validation_error'
    HTTP_ERROR = 'http_error'
    TEMPLATE_ERROR = 'template_error'
    OTHER_ERROR = 'other_error'

    ERROR_TYPE_CHOICES = [
        (SYNTAX_ERROR, 'خطأ في بناء الجملة'),
        (TYPE_ERROR, 'خطأ في نوع البيانات'),
        (VALUE_ERROR, 'خطأ في قيمة البيانات'),
        (ATTRIBUTE_ERROR, 'خطأ في الخاصية'),
        (KEY_ERROR, 'خطأ في المفتاح'),
        (INDEX_ERROR, 'خطأ في الفهرس'),
        (NAME_ERROR, 'خطأ في الاسم'),
        (IMPORT_ERROR, 'خطأ في الاستيراد'),
        (PERMISSION_ERROR, 'خطأ في الصلاحيات'),
        (DATABASE_ERROR, 'خطأ في قاعدة البيانات'),
        (VALIDATION_ERROR, 'خطأ في التحقق'),
        (HTTP_ERROR, 'خطأ في HTTP'),
        (TEMPLATE_ERROR, 'خطأ في القالب'),
        (OTHER_ERROR, 'خطأ آخر'),
    ]

    # Severity levels
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'

    SEVERITY_CHOICES = [
        (LOW, 'منخفض'),
        (MEDIUM, 'متوسط'),
        (HIGH, 'عالي'),
        (CRITICAL, 'حرج'),
    ]

    # Status
    NEW = 'new'
    IN_PROGRESS = 'in_progress'
    RESOLVED = 'resolved'
    IGNORED = 'ignored'

    STATUS_CHOICES = [
        (NEW, 'جديد'),
        (IN_PROGRESS, 'قيد المعالجة'),
        (RESOLVED, 'تم الحل'),
        (IGNORED, 'تم التجاهل'),
    ]

    # Basic information
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='التاريخ والوقت')
    error_type = models.CharField(max_length=50, choices=ERROR_TYPE_CHOICES, verbose_name='نوع الخطأ')
    error_message = models.TextField(verbose_name='رسالة الخطأ')
    error_description = models.TextField(verbose_name='وصف الخطأ', help_text='شرح مبسط للخطأ')
    
    # Location information
    page_url = models.CharField(max_length=500, verbose_name='رابط الصفحة')
    page_name = models.CharField(max_length=255, verbose_name='اسم الصفحة')
    file_path = models.CharField(max_length=500, verbose_name='مسار الملف', blank=True, null=True)
    line_number = models.IntegerField(verbose_name='رقم السطر', blank=True, null=True)
    function_name = models.CharField(max_length=255, verbose_name='اسم الدالة', blank=True, null=True)
    
    # User and request information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                           related_name='system_errors', verbose_name='المستخدم')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='عنوان IP')
    user_agent = models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')
    
    # Error details
    stack_trace = models.TextField(verbose_name='تتبع المكدس', blank=True, null=True)
    request_method = models.CharField(max_length=10, verbose_name='طريقة الطلب', blank=True, null=True)
    request_data = models.TextField(verbose_name='بيانات الطلب', blank=True, null=True)
    
    # Classification
    module = models.CharField(max_length=50, verbose_name='الوحدة', blank=True, null=True)
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, 
                              default=MEDIUM, verbose_name='مستوى الخطورة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, 
                            default=NEW, verbose_name='الحالة')
    
    # Resolution
    resolution_notes = models.TextField(verbose_name='ملاحظات الحل', blank=True, null=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='resolved_errors', verbose_name='تم الحل بواسطة')
    resolved_at = models.DateTimeField(verbose_name='تاريخ الحل', blank=True, null=True)
    
    # Occurrence tracking
    occurrence_count = models.IntegerField(default=1, verbose_name='عدد مرات الحدوث')
    last_occurrence = models.DateTimeField(auto_now=True, verbose_name='آخر حدوث')
    
    class Meta:
        verbose_name = 'خطأ النظام'
        verbose_name_plural = 'أخطاء النظام'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp']),
            models.Index(fields=['error_type']),
            models.Index(fields=['severity']),
            models.Index(fields=['status']),
            models.Index(fields=['module']),
        ]

    def __str__(self):
        return f"{self.get_error_type_display()} - {self.page_name} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

    def get_severity_color(self):
        """Return color class for severity level"""
        colors = {
            self.LOW: 'success',
            self.MEDIUM: 'warning', 
            self.HIGH: 'danger',
            self.CRITICAL: 'dark'
        }
        return colors.get(self.severity, 'secondary')

    def get_status_color(self):
        """Return color class for status"""
        colors = {
            self.NEW: 'danger',
            self.IN_PROGRESS: 'warning',
            self.RESOLVED: 'success',
            self.IGNORED: 'secondary'
        }
        return colors.get(self.status, 'secondary')

    def increment_occurrence(self):
        """Increment occurrence count and update last occurrence"""
        self.occurrence_count += 1
        self.save(update_fields=['occurrence_count', 'last_occurrence'])
