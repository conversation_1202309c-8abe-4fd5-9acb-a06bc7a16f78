{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body {
            background-color: white;
            font-size: 12pt;
        }

        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }

        .card {
            border: none;
            box-shadow: none;
        }

        .card-header {
            background-color: white !important;
            color: black !important;
            border-bottom: 1px solid #ddd;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        .page-break {
            page-break-after: always;
        }
    }

    .print-only {
        display: none;
    }

    .transfer-letter {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        background-color: #f9f9f9;
    }

    .letter-header {
        text-align: center;
        margin-bottom: 20px;
    }

    .letter-body {
        line-height: 1.6;
    }

    .letter-footer {
        margin-top: 30px;
        text-align: left;
    }

    .signature-line {
        display: inline-block;
        width: 200px;
        border-top: 1px solid #000;
        margin-top: 50px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row no-print">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
                    <div>
                        <a href="{% url 'home:print_transfer_summary' %}" class="btn btn-outline-primary" id="print-summary-btn">
                            <i class="fas fa-print"></i> طباعة ملخص النقل
                        </a>
                        <a href="{% url 'home:employee_transfer_management' %}" class="btn btn-outline-primary" id="employee-transfer-btn">
                            <i class="fas fa-user-plus"></i> نقل موظف
                        </a>
                        <a href="{% url 'home:technical_transfer_list' %}" class="btn btn-outline-primary" id="technical-transfer-btn">
                            <i class="fas fa-exchange-alt"></i> النقل الفني
                        </a>
                        <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يتم عرض طلبات النقل الداخلي للعام {{ current_year }} مرتبة حسب التخصص والخدمة الفعلية.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="transferLettersTable">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i> #</th>
                                    <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                                    <th><i class="fas fa-user me-1"></i> الاسم</th>
                                    <th><i class="fas fa-graduation-cap me-1"></i> التخصص</th>
                                    <th><i class="fas fa-clock me-1"></i> الخدمة الفعلية</th>
                                    <th><i class="fas fa-building me-1"></i> القسم الحالي</th>
                                    <th><i class="fas fa-check-circle me-1"></i> الخيار الأول</th>
                                    <th><i class="fas fa-check-circle me-1"></i> الخيار الثاني</th>
                                    <th><i class="fas fa-check-circle me-1"></i> الخيار الثالث</th>
                                    <th><i class="fas fa-map-marker-alt me-1"></i> مركز العمل الجديد</th>
                                    <th><i class="fas fa-sticky-note me-1"></i> ملاحظات</th>
                                    <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ transfer.ministry_number }}</td>
                                    <td>{{ transfer.employee_name }}</td>
                                    <td>{{ transfer.specialization|default:"-" }}</td>
                                    <td>{{ transfer.actual_service|default:"-" }}</td>
                                    <td>{{ transfer.current_department }}</td>
                                    <td>{{ transfer.first_choice }}</td>
                                    <td>{{ transfer.second_choice|default:"-" }}</td>
                                    <td>{{ transfer.third_choice|default:"-" }}</td>
                                    <td>
                                        <select class="form-select new-department-select" data-transfer-id="{{ transfer.id }}">
                                            <option value="">-- اختر مركز العمل الجديد --</option>
                                            {% for department in departments %}
                                            <option value="{{ department.id }}" {% if transfer.new_department == department.name %}selected{% endif %}>{{ department.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <textarea class="form-control notes-input" data-transfer-id="{{ transfer.id }}" rows="2" placeholder="أدخل الملاحظات هنا">{{ transfer.notes }}</textarea>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-success btn-sm save-new-department" data-transfer-id="{{ transfer.id }}" onclick="saveNewDepartmentDirect({{ transfer.id }}, this)">
                                                <i class="fas fa-save"></i> حفظ
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm print-single-letter" data-transfer-id="{{ transfer.id }}" onclick="printSingleLetterDirect({{ transfer.id }}, this)">
                                                <i class="fas fa-print"></i> طباعة
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center">لا توجد طلبات نقل داخلي للعام الحالي</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Section -->
    <div class="print-only">
        <div class="letter-header">
            <h2>المملكة الأردنية الهاشمية</h2>
            <h3>وزارة التربية والتعليم</h3>
            <h4>مديرية التربية والتعليم</h4>
            <h5>قسم شؤون الموظفين</h5>
            <p>التاريخ: {% now "Y-m-d" %}</p>
        </div>

        {% for transfer in transfers %}
        <div class="transfer-letter {% if not forloop.last %}page-break{% endif %}" data-transfer-id="{{ transfer.id }}">
            <div class="letter-body">
                <h4 class="text-center mb-4">كتاب نقل داخلي</h4>

                <p>السيد/ة: {{ transfer.employee_name }} المحترم/ة</p>
                <p>الرقم الوزاري: {{ transfer.ministry_number }}</p>
                <p>التخصص: {{ transfer.specialization|default:"-" }}</p>
                <p>الخدمة الفعلية: {{ transfer.actual_service|default:"-" }}</p>

                <p>تحية طيبة وبعد،</p>

                <p>
                    بناءً على طلب النقل الداخلي المقدم من قبلكم بتاريخ {{ transfer.created_at|date:"Y-m-d" }}،
                    وبعد دراسة الطلب والموافقة عليه، نود إعلامكم بأنه تقرر نقلكم من {{ transfer.current_department }}
                    إلى <span id="print-new-department-{{ transfer.id }}">
                        {% if transfer.new_department %}
                            {{ transfer.new_department }}
                        {% else %}
                            _____________________
                        {% endif %}
                    </span>
                    اعتباراً من تاريخ _____________________
                </p>

                <p>
                    نرجو منكم مراجعة قسم شؤون الموظفين لاستكمال إجراءات النقل.
                </p>

                <p>
                    <strong>ملاحظات:</strong> <span id="print-notes-{{ transfer.id }}">
                        {% if transfer.notes %}
                            {{ transfer.notes }}
                        {% else %}
                            -
                        {% endif %}
                    </span>
                </p>

                <p>وتفضلوا بقبول فائق الاحترام والتقدير،</p>
            </div>

            <div class="letter-footer">
                <div class="signature-line">
                    <p>مدير التربية والتعليم</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log("Script loaded");

    // Global direct functions
    function saveNewDepartmentDirect(transferId, button) {
        console.log("Direct save function called for transfer:", transferId);

        const departmentId = $(`select[data-transfer-id="${transferId}"]`).val();
        const departmentName = $(`select[data-transfer-id="${transferId}"] option:selected`).text();
        const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();

        if (!departmentId) {
            alert('الرجاء اختيار مركز العمل الجديد');
            return;
        }

        // Update the print section with the selected department
        $(`#print-new-department-${transferId}`).text(departmentName);

        // Update the notes in the print section
        $(`#print-notes-${transferId}`).text(notes);

        // Show loading indicator
        const saveBtn = $(button);
        const originalText = saveBtn.html();
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        saveBtn.prop('disabled', true);

        // Use simple AJAX with jQuery
        $.ajax({
            url: '{% url "home:print_transfer_letters" %}',
            type: 'POST',
            data: {
                'transfer_id': transferId,
                'department_id': departmentId,
                'notes': notes,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log("Success response:", response);

                // Update the UI to show the saved department
                saveBtn.html('<i class="fas fa-check"></i> تم الحفظ');
                setTimeout(function() {
                    saveBtn.html(originalText);
                    saveBtn.prop('disabled', false);
                }, 2000);

                // Update any UI elements that need to show the new department
                $(`#print-new-department-${transferId}`).text(departmentName);

                // Store the department name and notes in data attributes for later use
                saveBtn.attr('data-saved-department', departmentName);
                saveBtn.attr('data-saved-notes', notes);

                // Enable the print button
                $(`.print-single-letter[data-transfer-id="${transferId}"]`).prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error("AJAX Error:", status, error);
                console.log("Response:", xhr.responseText);
                alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
                saveBtn.html(originalText);
                saveBtn.prop('disabled', false);
            }
        });
    }

    function printSingleLetterDirect(transferId, button) {
        console.log("Direct print function called for transfer:", transferId);

        const departmentId = $(`select[data-transfer-id="${transferId}"]`).val();
        const departmentName = $(`select[data-transfer-id="${transferId}"] option:selected`).text();
        const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();

        if (!departmentId) {
            alert('الرجاء اختيار مركز العمل الجديد أولاً');
            return;
        }

        // Show loading indicator
        const printBtn = $(button);
        const originalText = printBtn.html();
        printBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحضير...');
        printBtn.prop('disabled', true);

        // Check if the department has been saved
        const saveBtn = $(`.save-new-department[data-transfer-id="${transferId}"]`);
        const savedDepartment = saveBtn.attr('data-saved-department');
        const savedNotes = saveBtn.attr('data-saved-notes');

        console.log("Saved department:", savedDepartment);
        console.log("Saved notes:", savedNotes);

        // If not saved or notes changed, save it first
        if (!savedDepartment || savedNotes !== notes) {
            console.log("Department or notes not saved, saving first");

            // Use AJAX to save the department and notes
            $.ajax({
                url: '{% url "home:print_transfer_letters" %}',
                type: 'POST',
                data: {
                    'transfer_id': transferId,
                    'department_id': departmentId,
                    'notes': notes,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log("Success response:", response);

                    // Store the department name and notes in data attributes for later use
                    saveBtn.attr('data-saved-department', departmentName);
                    saveBtn.attr('data-saved-notes', notes);

                    // Navigate to the single transfer letter page
                    navigateToSingleTransferLetter(transferId);
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    console.log("Response:", xhr.responseText);
                    alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
                    printBtn.html(originalText);
                    printBtn.prop('disabled', false);
                }
            });
        } else {
            // Department already saved, proceed with printing
            console.log("Department already saved, printing directly");

            // Navigate to the single transfer letter page
            navigateToSingleTransferLetter(transferId);

            // Reset the button after a delay
            setTimeout(function() {
                printBtn.html(originalText);
                printBtn.prop('disabled', false);
            }, 1000);
        }
    }

    // Helper function to navigate to the single transfer letter page
    function navigateToSingleTransferLetter(transferId) {
        // Create a form to submit
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = `/internal-transfer-print-letter/${transferId}/`;
        form.style.display = 'none';
        document.body.appendChild(form);
        form.submit();
    }

    // Esta función ha sido eliminada ya que no la necesitamos más

    $(document).ready(function() {
        console.log("Document ready");

        // Handle print summary button
        $('#print-summary-btn').on('click', function(e) {
            e.preventDefault();

            // Create a form to submit
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = '{% url "home:print_transfer_summary" %}';
            form.style.display = 'none';
            document.body.appendChild(form);
            form.submit();
        });

        // Set initial state for save buttons and print buttons based on selected departments
        $('.new-department-select').each(function() {
            const select = $(this);
            const transferId = select.attr('data-transfer-id');
            const departmentId = select.val();
            const departmentName = select.find('option:selected').text();
            const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();

            if (departmentId) {
                console.log("Found pre-selected department for transfer:", transferId, departmentName);
                console.log("Notes for transfer:", transferId, notes);

                // Update the print section with the selected department
                $(`#print-new-department-${transferId}`).text(departmentName);

                // Update the print section with the notes
                $(`#print-notes-${transferId}`).text(notes || '-');

                // Store the department name and notes in data attributes for later use
                const saveBtn = $(`.save-new-department[data-transfer-id="${transferId}"]`);
                saveBtn.attr('data-saved-department', departmentName);
                saveBtn.attr('data-saved-notes', notes);

                // Enable the print button
                $(`.print-single-letter[data-transfer-id="${transferId}"]`).prop('disabled', false);
            }
        });

        // Initialize DataTable
        try {
            $('#transferLettersTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json"
                },
                "order": [[3, "asc"], [4, "desc"]], // Sort by specialization (asc) and actual service (desc)
                "paging": false,
                "info": false
            });
            console.log("DataTable initialized");
        } catch (error) {
            console.error("Error initializing DataTable:", error);
        }

        // Initialize Select2 for department selection
        try {
            $('.new-department-select').select2({
                placeholder: "اختر مركز العمل الجديد",
                allowClear: true,
                width: '100%',
                language: "ar",
                dir: "rtl"
            });
            console.log("Select2 initialized");
        } catch (error) {
            console.error("Error initializing Select2:", error);
        }

        // Log all save buttons
        console.log("Save buttons:", $('.save-new-department').length);

        // Log all print buttons
        console.log("Print buttons:", $('.print-single-letter').length);

        // Direct save function
        function saveNewDepartment(transferId, departmentId, departmentName, saveBtn) {
            console.log("Saving department:", transferId, departmentId, departmentName);

            // Get notes
            const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();
            console.log("Notes to save:", notes);

            // Update the print section with the selected department
            $(`#print-new-department-${transferId}`).text(departmentName);

            // Update the notes in the print section
            $(`#print-notes-${transferId}`).text(notes);

            // Show loading indicator
            const originalText = saveBtn.html();
            saveBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
            saveBtn.prop('disabled', true);

            // Use simple AJAX with jQuery
            $.ajax({
                url: '{% url "home:print_transfer_letters" %}',
                type: 'POST',
                data: {
                    'transfer_id': transferId,
                    'department_id': departmentId,
                    'notes': notes,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log("Success response:", response);

                    // Update the UI to show the saved department
                    saveBtn.html('<i class="fas fa-check"></i> تم الحفظ');
                    setTimeout(function() {
                        saveBtn.html(originalText);
                        saveBtn.prop('disabled', false);
                    }, 2000);

                    // Update any UI elements that need to show the new department
                    $(`#print-new-department-${transferId}`).text(departmentName);

                    // Update any UI elements that need to show the notes
                    $(`#print-notes-${transferId}`).text(notes);

                    // Store the department name and notes in data attributes for later use
                    saveBtn.attr('data-saved-department', departmentName);
                    saveBtn.attr('data-saved-notes', notes);

                    // Enable the print button
                    $(`.print-single-letter[data-transfer-id="${transferId}"]`).prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    console.log("Response:", xhr.responseText);
                    alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
                    saveBtn.html(originalText);
                    saveBtn.prop('disabled', false);
                }
            });
        }



        // Attach click handlers directly
        $('.save-new-department').each(function() {
            const saveBtn = $(this);
            const transferId = saveBtn.attr('data-transfer-id');

            console.log("Attaching save handler for transfer:", transferId);

            saveBtn.on('click', function(e) {
                e.preventDefault();
                console.log("Save button clicked for transfer:", transferId);

                const departmentId = $(`select[data-transfer-id="${transferId}"]`).val();
                const departmentName = $(`select[data-transfer-id="${transferId}"] option:selected`).text();
                const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();

                if (!departmentId) {
                    alert('الرجاء اختيار مركز العمل الجديد');
                    return;
                }

                console.log("Calling saveNewDepartment with notes:", notes);
                saveNewDepartment(transferId, departmentId, departmentName, saveBtn);
            });
        });

        $('.print-single-letter').each(function() {
            const printBtn = $(this);
            const transferId = printBtn.attr('data-transfer-id');

            console.log("Attaching print handler for transfer:", transferId);

            // Initially disable if no department selected
            const departmentId = $(`select[data-transfer-id="${transferId}"]`).val();
            if (!departmentId) {
                printBtn.prop('disabled', true);
            }

            printBtn.on('click', function(e) {
                e.preventDefault();
                console.log("Print button clicked for transfer:", transferId);

                const departmentId = $(`select[data-transfer-id="${transferId}"]`).val();
                const departmentName = $(`select[data-transfer-id="${transferId}"] option:selected`).text();
                const notes = $(`.notes-input[data-transfer-id="${transferId}"]`).val();

                if (!departmentId) {
                    alert('الرجاء اختيار مركز العمل الجديد أولاً');
                    return;
                }

                // Show loading indicator
                const originalText = printBtn.html();
                printBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحضير...');
                printBtn.prop('disabled', true);

                // Check if the department has been saved
                const saveBtn = $(`.save-new-department[data-transfer-id="${transferId}"]`);
                const savedDepartment = saveBtn.attr('data-saved-department');
                const savedNotes = saveBtn.attr('data-saved-notes');

                console.log("Saved department:", savedDepartment);
                console.log("Saved notes:", savedNotes);
                console.log("Current notes:", notes);

                // If not saved or notes changed, save it first
                if (!savedDepartment || savedNotes !== notes) {
                    console.log("Department not saved or notes changed, saving first");

                    // Use AJAX to save the department and notes
                    $.ajax({
                        url: '{% url "home:print_transfer_letters" %}',
                        type: 'POST',
                        data: {
                            'transfer_id': transferId,
                            'department_id': departmentId,
                            'notes': notes,
                            'csrfmiddlewaretoken': '{{ csrf_token }}'
                        },
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        success: function(response) {
                            console.log("Success response:", response);

                            // Store the department name and notes in data attributes for later use
                            saveBtn.attr('data-saved-department', departmentName);
                            saveBtn.attr('data-saved-notes', notes);

                            // Navigate to the single transfer letter page using a form
                            navigateToSingleTransferLetter(transferId);
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Error:", status, error);
                            console.log("Response:", xhr.responseText);
                            alert('حدث خطأ أثناء الاتصال بالخادم: ' + error);
                            printBtn.html(originalText);
                            printBtn.prop('disabled', false);
                        }
                    });
                } else {
                    // Department already saved, proceed with printing
                    console.log("Department already saved, printing directly");

                    // Navigate to the single transfer letter page using a form
                    navigateToSingleTransferLetter(transferId);

                    // Reset the button after a delay
                    setTimeout(function() {
                        printBtn.html(originalText);
                        printBtn.prop('disabled', false);
                    }, 1000);
                }
            });
        });
    });
</script>
{% endblock %}
