// Service Purchase JavaScript Functions

$(document).ready(function() {
    
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Auto-submit search form with debounce
    let searchTimeout;
    $('#searchForm input, #searchForm select').on('input change', function() {
        clearTimeout(searchTimeout);
        
        if ($(this).is('input[type="text"]')) {
            // Add delay for text inputs
            searchTimeout = setTimeout(function() {
                $('#searchForm').submit();
            }, 500);
        } else {
            // Immediate submit for select elements
            $('#searchForm').submit();
        }
    });
    
    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        const employeeName = $(this).data('employee-name');
        const deleteUrl = $(this).attr('href');
        
        if (confirm(`هل أنت متأكد من حذف شراء الخدمة للموظف "${employeeName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            window.location.href = deleteUrl;
        }
    });
    
    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Animate new entries
    $('.service-purchase-new-entry').each(function(index) {
        $(this).delay(index * 100).animate({
            opacity: 1,
            transform: 'translateY(0)'
        }, 500);
    });
    
    // Export functionality with loading state
    $('.btn-export').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التصدير...');
        $btn.prop('disabled', true);
        
        // Re-enable button after 3 seconds
        setTimeout(function() {
            $btn.html(originalText);
            $btn.prop('disabled', false);
        }, 3000);
    });
    
    // Service period calculation
    function calculateServicePeriod(years, months, days) {
        const totalDays = (parseFloat(years) || 0) * 365 + (parseInt(months) || 0) * 30 + (parseInt(days) || 0);
        
        const calculatedYears = Math.floor(totalDays / 365);
        const calculatedMonths = Math.floor((totalDays % 365) / 30);
        const calculatedDays = Math.floor(totalDays % 30);
        
        let periodText = '';
        if (calculatedYears > 0) periodText += calculatedYears + ' سنة ';
        if (calculatedMonths > 0) periodText += calculatedMonths + ' شهر ';
        if (calculatedDays > 0) periodText += calculatedDays + ' يوم';
        
        return periodText.trim() || '0 أيام';
    }
    
    // Update service period display in forms
    $('#id_service_years, #id_service_months, #id_service_days').on('input change', function() {
        const years = $('#id_service_years').val();
        const months = $('#id_service_months').val();
        const days = $('#id_service_days').val();
        
        const totalPeriod = calculateServicePeriod(years, months, days);
        $('#totalServicePeriod').text(totalPeriod);
    });
    
    // Form validation
    $('#servicePurchaseForm').on('submit', function(e) {
        let isValid = true;
        let errorMessages = [];
        
        // Validate employee selection (for new records)
        if ($('#id_employee').length && !$('#id_employee').val()) {
            isValid = false;
            errorMessages.push('الرجاء اختيار الموظف');
        }
        
        // Validate purchase date
        if (!$('#id_purchase_date').val()) {
            isValid = false;
            errorMessages.push('الرجاء إدخال تاريخ الشراء');
        }
        
        // Validate purchase amount
        const purchaseAmount = parseFloat($('#id_purchase_amount').val());
        if (!purchaseAmount || purchaseAmount <= 0) {
            isValid = false;
            errorMessages.push('الرجاء إدخال مبلغ شراء صحيح');
        }
        
        // Validate service period
        const years = parseFloat($('#id_service_years').val()) || 0;
        const months = parseInt($('#id_service_months').val()) || 0;
        const days = parseInt($('#id_service_days').val()) || 0;
        
        if (years === 0 && months === 0 && days === 0) {
            isValid = false;
            errorMessages.push('الرجاء إدخال فترة خدمة صحيحة');
        }
        
        // Validate months and days ranges
        if (months < 0 || months > 11) {
            isValid = false;
            errorMessages.push('عدد الأشهر يجب أن يكون بين 0 و 11');
        }
        
        if (days < 0 || days > 30) {
            isValid = false;
            errorMessages.push('عدد الأيام يجب أن يكون بين 0 و 30');
        }
        
        if (!isValid) {
            e.preventDefault();
            
            // Show error messages
            const errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                '<strong>يرجى تصحيح الأخطاء التالية:</strong><br>' +
                errorMessages.join('<br>') +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
            
            $('.service-purchase-form').prepend(errorHtml);
            
            // Scroll to top to show errors
            $('html, body').animate({
                scrollTop: $('.service-purchase-form').offset().top - 100
            }, 500);
            
            return false;
        }
    });
    
    // Real-time search highlighting
    function highlightSearchTerms() {
        const searchTerm = $('#searchForm input[name="search"]').val();
        if (searchTerm && searchTerm.length > 2) {
            $('.service-purchase-table tbody td').each(function() {
                const text = $(this).text();
                const highlightedText = text.replace(
                    new RegExp(searchTerm, 'gi'),
                    '<mark>$&</mark>'
                );
                if (text !== highlightedText) {
                    $(this).html(highlightedText);
                }
            });
        }
    }
    
    // Apply search highlighting
    highlightSearchTerms();
    
    // Table row click to expand details (if needed)
    $('.service-purchase-table tbody tr').on('click', function(e) {
        if (!$(e.target).closest('.btn').length) {
            $(this).toggleClass('table-active');
        }
    });
    
    // Initialize Select2 for better dropdowns
    if ($.fn.select2) {
        $('.form-select').select2({
            theme: 'bootstrap-5',
            placeholder: 'اختر...',
            allowClear: true
        });
    }
    
    // Print functionality
    $('.btn-print').on('click', function() {
        window.print();
    });
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+N for new service purchase
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            window.location.href = $('#addServicePurchaseBtn').attr('href');
        }
        
        // Ctrl+E for export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            $('.btn-export')[0].click();
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            $('#searchForm input[type="text"]').val('').trigger('input');
        }
    });
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-IQ');
}

// Export functions for global use
window.ServicePurchase = {
    formatCurrency: formatCurrency,
    formatDate: formatDate
};