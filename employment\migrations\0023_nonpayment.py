# Generated by Django 5.2 on 2025-05-28 16:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0022_add_job_to_btec_teacher'),
    ]

    operations = [
        migrations.CreateModel(
            name='NonPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('days_count', models.PositiveIntegerField(verbose_name='عدد الأيام')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='الملاحظات')),
                ('is_transferred', models.BooleanField(default=False, verbose_name='تم الترحيل')),
                ('transfer_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الترحيل')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='non_payments', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'عدم صرف',
                'verbose_name_plural': 'عدم الصرف',
                'ordering': ['-date', '-created_at'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
