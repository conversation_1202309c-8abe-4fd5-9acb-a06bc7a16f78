# Generated by Django 5.2 on 2025-04-20 05:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0004_department_school_gender_department_school_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeePosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_obtained', models.DateField(verbose_name='تاريخ الحصول عليه')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='employees.employee', verbose_name='الموظف')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_positions', to='employment.position', verbose_name='المسمى الوظيفي')),
            ],
            options={
                'verbose_name': 'المسمى الوظيفي للموظف',
                'verbose_name_plural': 'المسميات الوظيفية للموظفين',
                'ordering': ['-date_obtained'],
            },
        ),
    ]
