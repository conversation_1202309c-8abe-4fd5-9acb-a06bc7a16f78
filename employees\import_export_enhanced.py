#!/usr/bin/env python3
"""
📊 نظام استيراد وتصدير الموظفين المحسن
Enhanced Employee Import/Export System - HR System
"""

import pandas as pd
from io import BytesIO
from django.http import HttpResponse
from django.contrib import messages
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from datetime import datetime, date
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

# استيراد مكتبات PDF مع معالجة الأخطاء
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PDF_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: مكتبات PDF غير متاحة: {e}")
    PDF_AVAILABLE = False

from .models import Employee, RetiredEmployee, ExternalTransfer, MaternityLeave, InternalTransfer
from employment.models import Employment, EmployeePosition, Department, Position

def get_all_employee_fields():
    """الحصول على جميع حقول الموظف مع البيانات المرتبطة"""
    return {
        # الحقول الأساسية
        'الرقم الوزاري': 'ministry_number',
        'الرقم الوطني': 'national_id', 
        'الاسم الكامل': 'full_name',
        'الجنس': 'gender',
        'تاريخ الميلاد': 'birth_date',
        'العنوان': 'address',
        'رقم الهاتف': 'phone_number',
        'تاريخ التعيين': 'hire_date',
        'القسم الحالي': 'school',
        
        # المؤهلات العلمية
        'المؤهل العلمي': 'qualification',
        'دبلوم عالي': 'post_graduate_diploma',
        'ماجستير': 'masters_degree',
        'دكتوراه': 'phd_degree',
        'التخصص': 'specialization',
        
        # بيانات التوظيف
        'المنصب الحالي': 'current_position',
        'تاريخ المنصب': 'position_date',
        'نوع التوظيف': 'employment_status',
        'صفة التعيين': 'appointment_type',
        'تاريخ بداية التوظيف': 'employment_start_date',
        
        # الحالة الوظيفية
        'حالة الموظف': 'employee_status',
        'تاريخ التقاعد': 'retirement_date',
        'سبب التقاعد': 'retirement_reason',
        'تاريخ النقل الخارجي': 'transfer_date',
        'جهة النقل': 'transfer_destination',
        'سبب النقل': 'transfer_reason',
        
        # إجازة الأمومة
        'في إجازة أمومة': 'on_maternity_leave',
        'تاريخ بداية إجازة الأمومة': 'maternity_start_date',
        'تاريخ نهاية إجازة الأمومة': 'maternity_end_date',
        
        # النقل الداخلي
        'آخر نقل داخلي': 'last_internal_transfer',
        'من قسم': 'transfer_from_department',
        'إلى قسم': 'transfer_to_department',
        'تاريخ النقل الداخلي': 'internal_transfer_date',
        
        # تواريخ النظام
        'تاريخ الإنشاء': 'created_at',
        'تاريخ التحديث': 'updated_at'
    }

def get_employee_comprehensive_data(employee):
    """الحصول على البيانات الشاملة للموظف"""
    data = {}
    
    # الحقول الأساسية
    data['الرقم الوزاري'] = employee.ministry_number
    data['الرقم الوطني'] = employee.national_id
    data['الاسم الكامل'] = employee.full_name
    data['الجنس'] = 'ذكر' if employee.gender == 'male' else 'أنثى' if employee.gender == 'female' else ''
    data['تاريخ الميلاد'] = employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else ''
    data['العنوان'] = employee.address or ''
    data['رقم الهاتف'] = employee.phone_number or ''
    data['تاريخ التعيين'] = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else ''
    data['القسم الحالي'] = employee.school or ''
    
    # المؤهلات العلمية
    data['المؤهل العلمي'] = employee.qualification or ''
    data['دبلوم عالي'] = employee.post_graduate_diploma or ''
    data['ماجستير'] = employee.masters_degree or ''
    data['دكتوراه'] = employee.phd_degree or ''
    data['التخصص'] = employee.specialization or ''
    
    # بيانات التوظيف
    current_employment = Employment.objects.filter(employee=employee, is_current=True).first()
    if current_employment:
        data['المنصب الحالي'] = current_employment.position.name
        data['نوع التوظيف'] = current_employment.status.get_name_display()
        data['صفة التعيين'] = current_employment.appointment_type.name if current_employment.appointment_type else ''
        data['تاريخ بداية التوظيف'] = current_employment.start_date.strftime('%Y-%m-%d')
    else:
        data['المنصب الحالي'] = employee.get_latest_position()
        data['نوع التوظيف'] = ''
        data['صفة التعيين'] = ''
        data['تاريخ بداية التوظيف'] = ''
    
    # آخر منصب من تاريخ الحراك الوظيفي
    latest_position = EmployeePosition.objects.filter(employee=employee).order_by('-date_obtained').first()
    if latest_position:
        data['تاريخ المنصب'] = latest_position.date_obtained.strftime('%Y-%m-%d')
    else:
        data['تاريخ المنصب'] = ''
    
    # حالة الموظف
    if hasattr(employee, 'retirement'):
        data['حالة الموظف'] = 'متقاعد'
        data['تاريخ التقاعد'] = employee.retirement.retirement_date.strftime('%Y-%m-%d')
        data['سبب التقاعد'] = employee.retirement.retirement_reason
    elif hasattr(employee, 'external_transfer'):
        data['حالة الموظف'] = 'منقول خارجياً'
        data['تاريخ النقل الخارجي'] = employee.external_transfer.transfer_date.strftime('%Y-%m-%d')
        data['جهة النقل'] = employee.external_transfer.destination_directorate
        data['سبب النقل'] = employee.external_transfer.transfer_reason
    else:
        data['حالة الموظف'] = 'نشط'
        data['تاريخ التقاعد'] = ''
        data['سبب التقاعد'] = ''
        data['تاريخ النقل الخارجي'] = ''
        data['جهة النقل'] = ''
        data['سبب النقل'] = ''
    
    # إجازة الأمومة
    active_maternity = MaternityLeave.objects.filter(employee=employee, is_active=True).first()
    if active_maternity:
        data['في إجازة أمومة'] = 'نعم'
        data['تاريخ بداية إجازة الأمومة'] = active_maternity.start_date.strftime('%Y-%m-%d')
        data['تاريخ نهاية إجازة الأمومة'] = active_maternity.end_date.strftime('%Y-%m-%d')
    else:
        data['في إجازة أمومة'] = 'لا'
        data['تاريخ بداية إجازة الأمومة'] = ''
        data['تاريخ نهاية إجازة الأمومة'] = ''
    
    # النقل الداخلي
    last_internal_transfer = InternalTransfer.objects.filter(employee=employee).order_by('-transfer_date').first()
    if last_internal_transfer:
        data['آخر نقل داخلي'] = 'نعم'
        data['من قسم'] = last_internal_transfer.previous_department
        data['إلى قسم'] = last_internal_transfer.new_department
        data['تاريخ النقل الداخلي'] = last_internal_transfer.transfer_date.strftime('%Y-%m-%d')
    else:
        data['آخر نقل داخلي'] = 'لا'
        data['من قسم'] = ''
        data['إلى قسم'] = ''
        data['تاريخ النقل الداخلي'] = ''
    
    # تواريخ النظام
    data['تاريخ الإنشاء'] = employee.created_at.strftime('%Y-%m-%d %H:%M') if employee.created_at else ''
    data['تاريخ التحديث'] = employee.updated_at.strftime('%Y-%m-%d %H:%M') if employee.updated_at else ''
    
    return data

@login_required
def export_employees_excel_enhanced(request):
    """تصدير بيانات الموظفين إلى Excel مع جميع الحقول"""
    
    # الحصول على الموظفين النشطين فقط (حسب الطلب)
    include_all = request.GET.get('include_all', 'false') == 'true'
    
    if include_all:
        employees = Employee.objects.all()
        filename = 'جميع_الموظفين_شامل.xlsx'
    else:
        # استبعاد المتقاعدين والمنقولين خارجياً
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        excluded_ids = list(retired_ids) + list(transferred_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)
        filename = 'الموظفين_النشطين_شامل.xlsx'
    
    # ترتيب الموظفين حسب الرقم الوزاري
    from .utils import order_employees_by_ministry_number
    employees = order_employees_by_ministry_number(employees)
    
    # إنشاء البيانات
    data_rows = []
    for employee in employees:
        employee_data = get_employee_comprehensive_data(employee)
        data_rows.append(employee_data)
    
    # إنشاء DataFrame
    df = pd.DataFrame(data_rows)
    
    # إنشاء ملف Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='بيانات الموظفين الشاملة')
        
        # تنسيق الورقة
        workbook = writer.book
        worksheet = writer.sheets['بيانات الموظفين الشاملة']
        
        # تعيين اتجاه القراءة من اليمين لليسار
        worksheet.sheet_view.rightToLeft = True
        
        # تنسيق الخلايا
        header_font = Font(bold=True, size=12, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # تطبيق التنسيق على رأس الجدول
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # تطبيق الحدود على جميع الخلايا
        for row in worksheet.iter_rows():
            for cell in row:
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # تعديل عرض الأعمدة
        column_widths = {
            'A': 15,  # الرقم الوزاري
            'B': 15,  # الرقم الوطني
            'C': 25,  # الاسم الكامل
            'D': 10,  # الجنس
            'E': 15,  # تاريخ الميلاد
            'F': 30,  # العنوان
            'G': 15,  # رقم الهاتف
            'H': 15,  # تاريخ التعيين
            'I': 25,  # القسم الحالي
            'J': 20,  # المؤهل العلمي
            'K': 20,  # دبلوم عالي
            'L': 20,  # ماجستير
            'M': 20,  # دكتوراه
            'N': 20,  # التخصص
            'O': 20,  # المنصب الحالي
            'P': 15,  # تاريخ المنصب
            'Q': 15,  # نوع التوظيف
            'R': 15,  # صفة التعيين
            'S': 15,  # تاريخ بداية التوظيف
            'T': 15,  # حالة الموظف
            'U': 15,  # تاريخ التقاعد
            'V': 20,  # سبب التقاعد
            'W': 15,  # تاريخ النقل الخارجي
            'X': 25,  # جهة النقل
            'Y': 20,  # سبب النقل
            'Z': 15,  # في إجازة أمومة
            'AA': 15, # تاريخ بداية إجازة الأمومة
            'AB': 15, # تاريخ نهاية إجازة الأمومة
            'AC': 15, # آخر نقل داخلي
            'AD': 20, # من قسم
            'AE': 20, # إلى قسم
            'AF': 15, # تاريخ النقل الداخلي
            'AG': 20, # تاريخ الإنشاء
            'AH': 20, # تاريخ التحديث
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
    
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

@login_required
def export_employees_pdf_enhanced(request):
    """تصدير بيانات الموظفين إلى PDF مع جميع الحقول"""
    
    if not PDF_AVAILABLE:
        messages.error(request, 'مكتبات PDF غير متاحة. يرجى استخدام تصدير Excel.')
        return redirect('employees:employee_import_export')
    
    # الحصول على الموظفين
    include_all = request.GET.get('include_all', 'false') == 'true'
    
    if include_all:
        employees = Employee.objects.all()
        title = 'تقرير شامل لجميع الموظفين'
    else:
        retired_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        excluded_ids = list(retired_ids) + list(transferred_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)
        title = 'تقرير شامل للموظفين النشطين'
    
    # ترتيب الموظفين
    try:
        from .utils import order_employees_by_ministry_number
        employees = order_employees_by_ministry_number(employees)
    except ImportError:
        employees = employees.order_by('ministry_number')
    
    # إنشاء ملف PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="employees_comprehensive_report.pdf"'
    
    # إنشاء المستند
    doc = SimpleDocTemplate(
        response,
        pagesize=landscape(A4),
        rightMargin=0.5*inch,
        leftMargin=0.5*inch,
        topMargin=0.5*inch,
        bottomMargin=0.5*inch
    )
    
    # تسجيل الخط العربي
    try:
        import os
        font_path = os.path.join(os.path.dirname(__file__), '..', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
        if os.path.exists(font_path):
            pdfmetrics.registerFont(TTFont('Arabic', font_path))
            arabic_font = 'Arabic'
        else:
            arabic_font = 'Helvetica'
    except:
        arabic_font = 'Helvetica'
    
    # إنشاء الأنماط
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'ArabicTitle',
        parent=styles['Title'],
        fontName=arabic_font,
        fontSize=16,
        alignment=1,  # Center
        spaceAfter=20
    )
    
    # إنشاء المحتوى
    story = []
    
    # العنوان
    title_text = arabic_reshaper.reshape(title)
    title_text = get_display(title_text)
    story.append(Paragraph(title_text, title_style))
    story.append(Spacer(1, 20))
    
    # إنشاء البيانات للجدول (الحقول الأساسية فقط للـ PDF)
    table_data = []
    
    # رأس الجدول
    headers = [
        'الرقم الوزاري', 'الاسم الكامل', 'الجنس', 'المؤهل العلمي', 
        'التخصص', 'القسم الحالي', 'المنصب الحالي', 'حالة الموظف'
    ]
    
    # تطبيق التشكيل العربي على الرؤوس
    formatted_headers = []
    for header in headers:
        reshaped_header = arabic_reshaper.reshape(header)
        formatted_headers.append(get_display(reshaped_header))
    
    table_data.append(formatted_headers)
    
    # بيانات الموظفين
    for employee in employees:
        employee_data = get_employee_comprehensive_data(employee)
        
        row = [
            employee_data['الرقم الوزاري'],
            arabic_reshaper.reshape(employee_data['الاسم الكامل']) if employee_data['الاسم الكامل'] else '',
            arabic_reshaper.reshape(employee_data['الجنس']) if employee_data['الجنس'] else '',
            arabic_reshaper.reshape(employee_data['المؤهل العلمي']) if employee_data['المؤهل العلمي'] else '',
            arabic_reshaper.reshape(employee_data['التخصص']) if employee_data['التخصص'] else '',
            arabic_reshaper.reshape(employee_data['القسم الحالي']) if employee_data['القسم الحالي'] else '',
            arabic_reshaper.reshape(employee_data['المنصب الحالي']) if employee_data['المنصب الحالي'] else '',
            arabic_reshaper.reshape(employee_data['حالة الموظف']) if employee_data['حالة الموظف'] else '',
        ]
        
        # تطبيق get_display على النصوص العربية
        formatted_row = []
        for i, cell in enumerate(row):
            if i == 0:  # الرقم الوزاري - رقم
                formatted_row.append(str(cell))
            else:  # النصوص العربية
                formatted_row.append(get_display(cell) if cell else '')
        
        table_data.append(formatted_row)
    
    # إنشاء الجدول
    table = Table(table_data, repeatRows=1)
    
    # تنسيق الجدول
    table.setStyle(TableStyle([
        # تنسيق الرأس
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), arabic_font),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        
        # تنسيق البيانات
        ('FONTNAME', (0, 1), (-1, -1), arabic_font),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.white]),
    ]))
    
    story.append(table)
    
    # إضافة معلومات إضافية
    story.append(Spacer(1, 20))
    
    info_text = f"تم إنشاء التقرير في: {timezone.now().strftime('%Y-%m-%d %H:%M')}"
    info_text += f" | عدد الموظفين: {len(employees)}"
    
    info_text = arabic_reshaper.reshape(info_text)
    info_text = get_display(info_text)
    
    info_style = ParagraphStyle(
        'Info',
        parent=styles['Normal'],
        fontName=arabic_font,
        fontSize=10,
        alignment=1
    )
    
    story.append(Paragraph(info_text, info_style))
    
    # بناء المستند
    doc.build(story)
    
    return response

def import_employees_enhanced(request, uploaded_file):
    """استيراد الموظفين مع دعم الحقول الجديدة"""
    
    try:
        # قراءة الملف
        if uploaded_file.name.endswith('.xlsx') or uploaded_file.name.endswith('.xls'):
            df = pd.read_excel(uploaded_file)
        elif uploaded_file.name.endswith('.csv'):
            df = pd.read_csv(uploaded_file)
        else:
            messages.error(request, 'نوع الملف غير مدعوم. يرجى استخدام Excel أو CSV.')
            return False
        
        # تنظيف البيانات
        df = df.dropna(how='all')
        
        # تعيين الأعمدة
        field_mapping = get_all_employee_fields()
        column_mapping = {}
        
        # ربط الأعمدة الموجودة بالحقول المطلوبة
        for arabic_name, field_name in field_mapping.items():
            for col in df.columns:
                if str(col).strip() == arabic_name:
                    column_mapping[field_name] = col
                    break
        
        # التحقق من وجود الحقول الأساسية
        required_fields = ['ministry_number', 'full_name']
        missing_fields = [field for field in required_fields if field not in column_mapping]
        
        if missing_fields:
            messages.error(request, f'الحقول المطلوبة مفقودة: {missing_fields}')
            return False
        
        # معالجة البيانات
        imported_count = 0
        skipped_count = 0
        errors = []
        
        for index, row in df.iterrows():
            try:
                # الحصول على القيم
                def get_value(field_name, default=''):
                    if field_name in column_mapping:
                        value = row.get(column_mapping[field_name], default)
                        if pd.isna(value):
                            return default
                        return str(value).strip()
                    return default
                
                # التحقق من الرقم الوزاري
                ministry_number = get_value('ministry_number')
                if not ministry_number:
                    skipped_count += 1
                    errors.append(f'صف {index + 2}: الرقم الوزاري مفقود')
                    continue
                
                # التحقق من وجود الموظف
                if Employee.objects.filter(ministry_number=ministry_number).exists():
                    skipped_count += 1
                    errors.append(f'الرقم الوزاري {ministry_number}: موجود بالفعل')
                    continue
                
                # إنشاء الموظف
                employee_data = {
                    'ministry_number': ministry_number,
                    'national_id': get_value('national_id'),
                    'full_name': get_value('full_name'),
                    'gender': 'male' if get_value('gender') == 'ذكر' else 'female',
                    'qualification': get_value('qualification'),
                    'post_graduate_diploma': get_value('post_graduate_diploma'),
                    'masters_degree': get_value('masters_degree'),
                    'phd_degree': get_value('phd_degree'),
                    'specialization': get_value('specialization'),
                    'school': get_value('school'),
                    'address': get_value('address'),
                    'phone_number': get_value('phone_number'),
                }
                
                # معالجة التواريخ
                def parse_date(date_str):
                    if not date_str:
                        return None
                    try:
                        return datetime.strptime(date_str, '%Y-%m-%d').date()
                    except:
                        try:
                            return datetime.strptime(date_str, '%d/%m/%Y').date()
                        except:
                            return None
                
                hire_date = parse_date(get_value('hire_date'))
                birth_date = parse_date(get_value('birth_date'))
                
                if hire_date:
                    employee_data['hire_date'] = hire_date
                if birth_date:
                    employee_data['birth_date'] = birth_date
                
                # إنشاء الموظف
                employee = Employee.objects.create(**employee_data)
                imported_count += 1
                
                # إضافة بيانات التوظيف إذا كانت متوفرة
                position_name = get_value('current_position')
                if position_name:
                    try:
                        position, created = Position.objects.get_or_create(name=position_name)
                        department, created = Department.objects.get_or_create(name=employee.school)
                        
                        # إنشاء التوظيف
                        Employment.objects.create(
                            employee=employee,
                            department=department,
                            position=position,
                            start_date=hire_date or timezone.now().date(),
                            is_current=True
                        )
                    except Exception as e:
                        print(f"خطأ في إنشاء التوظيف للموظف {ministry_number}: {e}")
                
            except Exception as e:
                skipped_count += 1
                errors.append(f'صف {index + 2}: خطأ في المعالجة - {str(e)}')
        
        # رسائل النتائج
        if imported_count > 0:
            messages.success(request, f'تم استيراد {imported_count} موظف بنجاح.')
        
        if skipped_count > 0:
            messages.warning(request, f'تم تخطي {skipped_count} موظف.')
            
        if errors:
            error_msg = 'الأخطاء:\n' + '\n'.join(errors[:10])  # أول 10 أخطاء فقط
            if len(errors) > 10:
                error_msg += f'\n... و {len(errors) - 10} خطأ آخر'
            messages.error(request, error_msg)
        
        return imported_count > 0
        
    except Exception as e:
        messages.error(request, f'خطأ في قراءة الملف: {str(e)}')
        return False

def create_employee_template_enhanced():
    """إنشاء قالب Excel محسن لاستيراد الموظفين"""
    
    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "قالب الموظفين"
    
    # تعيين اتجاه القراءة
    ws.sheet_view.rightToLeft = True
    
    # الحصول على جميع الحقول
    field_mapping = get_all_employee_fields()
    headers = list(field_mapping.keys())
    
    # إضافة الرؤوس
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, size=12, color='FFFFFF')
        cell.fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # إضافة صف مثال
    example_data = {
        'الرقم الوزاري': '123456',
        'الرقم الوطني': '987654321',
        'الاسم الكامل': 'أحمد محمد علي',
        'الجنس': 'ذكر',
        'تاريخ الميلاد': '1985-01-15',
        'العنوان': 'عمان - الأردن',
        'رقم الهاتف': '0791234567',
        'تاريخ التعيين': '2010-09-01',
        'القسم الحالي': 'مدرسة الأمل الأساسية',
        'المؤهل العلمي': 'بكالوريوس',
        'دبلوم عالي': 'دبلوم تربية',
        'ماجستير': '',
        'دكتوراه': '',
        'التخصص': 'رياضيات',
        'المنصب الحالي': 'معلم',
        'تاريخ المنصب': '2010-09-01',
        'نوع التوظيف': 'دائم',
        'صفة التعيين': 'تعيين جديد',
        'تاريخ بداية التوظيف': '2010-09-01',
        'حالة الموظف': 'نشط',
        'تاريخ التقاعد': '',
        'سبب التقاعد': '',
        'تاريخ النقل الخارجي': '',
        'جهة النقل': '',
        'سبب النقل': '',
        'في إجازة أمومة': 'لا',
        'تاريخ بداية إجازة الأمومة': '',
        'تاريخ نهاية إجازة الأمومة': '',
        'آخر نقل داخلي': 'لا',
        'من قسم': '',
        'إلى قسم': '',
        'تاريخ النقل الداخلي': '',
        'تاريخ الإنشاء': '',
        'تاريخ التحديث': ''
    }
    
    for col, header in enumerate(headers, 1):
        value = example_data.get(header, '')
        cell = ws.cell(row=2, column=col, value=value)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # تعديل عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = 20
    
    # حفظ الملف
    output = BytesIO()
    wb.save(output)
    output.seek(0)
    
    return output