from django.urls import path
from . import views

app_name = 'announcements'

urlpatterns = [
    # Main announcements management
    path('', views.announcements_list, name='announcements_list'),
    path('create/', views.announcement_create, name='announcement_create'),
    path('<int:pk>/', views.announcement_detail, name='announcement_detail'),
    path('<int:pk>/edit/', views.announcement_update, name='announcement_update'),
    path('<int:pk>/delete/', views.announcement_delete, name='announcement_delete'),
    
    # Public view (no login required)
    path('view/<int:pk>/', views.announcement_public_view, name='announcement_public_view'),
    
    # AJAX endpoints
    path('<int:pk>/toggle-status/', views.announcement_toggle_status, name='announcement_toggle_status'),
    path('<int:pk>/track-click/', views.announcement_click_tracking, name='announcement_click_tracking'),
    path('api/homepage/', views.get_homepage_announcements, name='get_homepage_announcements'),
    path('api/stats/', views.get_announcements_stats, name='get_announcements_stats'),
]