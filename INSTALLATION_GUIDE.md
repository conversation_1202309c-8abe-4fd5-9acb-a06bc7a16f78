# دليل تثبيت نظام شؤون الموظفين
# HR Management System Installation Guide

## نظرة عامة | Overview

هذا الدليل يوضح كيفية تثبيت وإعداد نظام شؤون الموظفين على جهاز جديد.
This guide explains how to install and setup the HR Management System on a new machine.

## المتطلبات الأساسية | Prerequisites

### 1. Python
- **الإصدار المطلوب | Required Version**: Python 3.9 أو أحدث | Python 3.9 or newer
- **التحميل | Download**: [python.org](https://www.python.org/downloads/)

### 2. قاعدة البيانات | Database (اختياري | Optional)
- **SQLite**: مدمج مع Python | Built-in with Python
- **MySQL**: للاستخدام المتقدم | For advanced usage
- **PostgreSQL**: للاستخدام المتقدم | For advanced usage

## طرق التثبيت | Installation Methods

### الطريقة الأولى: التثبيت التلقائي | Method 1: Automatic Installation

#### على Windows:
1. تأكد من تثبيت Python | Make sure Python is installed
2. انقر نقراً مزدوجاً على | Double-click on: `install_requirements.bat`
3. اتبع التعليمات على الشاشة | Follow on-screen instructions

#### على Linux/Mac:
1. افتح Terminal | Open Terminal
2. انتقل إلى مجلد المشروع | Navigate to project folder:
   ```bash
   cd /path/to/hr-system
   ```
3. اجعل الملف قابلاً للتنفيذ | Make file executable:
   ```bash
   chmod +x install_requirements.sh
   ```
4. شغل الملف | Run the file:
   ```bash
   ./install_requirements.sh
   ```

### الطريقة الثانية: التثبيت اليدوي | Method 2: Manual Installation

#### 1. إنشاء البيئة الافتراضية | Create Virtual Environment
```bash
# إنشاء البيئة الافتراضية | Create virtual environment
python -m venv hr_system_env

# تفعيل البيئة الافتراضية | Activate virtual environment
# على Windows | On Windows:
hr_system_env\Scripts\activate

# على Linux/Mac | On Linux/Mac:
source hr_system_env/bin/activate
```

#### 2. تحديث pip | Upgrade pip
```bash
python -m pip install --upgrade pip
```

#### 3. تثبيت المكتبات | Install Requirements
```bash
pip install -r requirements.txt
```

## إعداد النظام | System Setup

### 1. إنشاء قاعدة البيانات | Create Database
```bash
python manage.py migrate
```

### 2. إنشاء مستخدم مدير | Create Admin User
```bash
python manage.py createsuperuser
```

### 3. جمع الملفات الثابتة | Collect Static Files
```bash
python manage.py collectstatic
```

### 4. تشغيل الخادم | Run Server
```bash
python manage.py runserver
```

### 5. فتح النظام | Open System
افتح المتصفح واذهب إلى | Open browser and go to:
```
http://127.0.0.1:8000
```

## المكتبات المثبتة | Installed Libraries

### المكتبات الأساسية | Core Libraries
- **Django 5.2**: إطار العمل الأساسي | Main framework
- **Pillow**: معالجة الصور | Image processing
- **openpyxl**: ملفات Excel | Excel files
- **reportlab**: إنشاء PDF | PDF generation
- **WeasyPrint**: تحويل HTML إلى PDF | HTML to PDF conversion

### مكتبات البيانات | Data Libraries
- **pandas**: تحليل البيانات | Data analysis
- **numpy**: الحوسبة العلمية | Scientific computing
- **python-dateutil**: معالجة التواريخ | Date processing

### مكتبات واجهة المستخدم | UI Libraries
- **django-crispy-forms**: نماذج محسنة | Enhanced forms
- **crispy-bootstrap5**: تصميم Bootstrap | Bootstrap styling
- **django-widget-tweaks**: تخصيص الحقول | Field customization

### مكتبات الأمان | Security Libraries
- **django-allauth**: المصادقة المتقدمة | Advanced authentication
- **django-guardian**: صلاحيات الكائنات | Object permissions

### مكتبات التطوير | Development Libraries
- **django-debug-toolbar**: أدوات التطوير | Development tools
- **black**: تنسيق الكود | Code formatting
- **flake8**: فحص جودة الكود | Code quality checking

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

#### 1. خطأ في تثبيت المكتبات | Package Installation Error
```bash
# حل المشكلة | Solution:
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt --force-reinstall
```

#### 2. خطأ في قاعدة البيانات | Database Error
```bash
# حذف قاعدة البيانات وإعادة إنشائها | Delete and recreate database:
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
```

#### 3. خطأ في الملفات الثابتة | Static Files Error
```bash
# إعادة جمع الملفات الثابتة | Recollect static files:
python manage.py collectstatic --clear
```

#### 4. خطأ في الصلاحيات (Linux/Mac) | Permission Error (Linux/Mac)
```bash
# إعطاء صلاحيات التنفيذ | Give execution permissions:
chmod +x install_requirements.sh
chmod +x start_server.sh
```

### رسائل خطأ محددة | Specific Error Messages

#### "Python not found"
- تأكد من تثبيت Python | Make sure Python is installed
- أضف Python إلى PATH | Add Python to PATH
- أعد تشغيل Terminal/Command Prompt | Restart Terminal/Command Prompt

#### "pip not found"
```bash
# تثبيت pip يدوياً | Install pip manually:
python -m ensurepip --upgrade
```

#### "Permission denied"
```bash
# على Linux/Mac | On Linux/Mac:
sudo chmod +x install_requirements.sh

# أو تشغيل بصلاحيات المدير | Or run with admin privileges:
sudo ./install_requirements.sh
```

## الملفات المهمة | Important Files

- `requirements.txt`: قائمة المكتبات المطلوبة | Required packages list
- `install_requirements.bat`: ملف التثبيت لـ Windows | Windows installation file
- `install_requirements.sh`: ملف التثبيت لـ Linux/Mac | Linux/Mac installation file
- `start_server.bat/sh`: ملف تشغيل الخادم | Server startup file
- `manage.py`: ملف إدارة Django | Django management file

## نصائح مهمة | Important Tips

1. **استخدم البيئة الافتراضية دائماً** | Always use virtual environment
2. **احتفظ بنسخة احتياطية من قاعدة البيانات** | Keep database backups
3. **حدث المكتبات بانتظام** | Update packages regularly
4. **اقرأ رسائل الخطأ بعناية** | Read error messages carefully
5. **استخدم Python 3.9 أو أحدث** | Use Python 3.9 or newer

## الدعم | Support

إذا واجهت أي مشاكل، يرجى:
If you encounter any issues, please:

1. التحقق من هذا الدليل أولاً | Check this guide first
2. البحث عن الخطأ في Google | Search for the error on Google
3. التواصل مع فريق التطوير | Contact the development team

## معلومات إضافية | Additional Information

- **الموقع الرسمي للمشروع** | Project Official Website: [Link]
- **التوثيق الفني** | Technical Documentation: [Link]
- **مجتمع المطورين** | Developer Community: [Link]
