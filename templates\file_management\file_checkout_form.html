{% extends 'base.html' %}
{% load static %}

{% block title %}تسجيل خروج ملف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تسجيل خروج ملف</h1>
        <a href="{% url 'file_management:file_movement_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تسجيل خروج ملف موظف</h6>
        </div>
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}

                <input type="hidden" name="employee" id="id_employee">
                <input type="hidden" name="employee_id" id="id_employee_id">

                <div class="mb-3">
                    <label for="{{ form.ministry_number.id_for_label }}" class="form-label">الرقم الوزاري</label>
                    <div class="input-group">
                        {{ form.ministry_number }}
                        <button class="btn btn-secondary" type="button" id="search_employee_btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                    {% if form.ministry_number.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.ministry_number.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.employee_name.id_for_label }}" class="form-label">اسم الموظف</label>
                    {{ form.employee_name }}
                    {% if form.employee_name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee_name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- File field removed to avoid database errors -->

                <div class="mb-3">
                    <label for="{{ form.checkout_date.id_for_label }}" class="form-label">تاريخ خروج الملف</label>
                    {{ form.checkout_date }}
                    {% if form.checkout_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.checkout_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.notes.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-file-export"></i> تسجيل خروج الملف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form fields
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        // Employee search functionality
        const ministryNumberInput = document.getElementById('ministry_number_input');
        const employeeNameDisplay = document.getElementById('employee_name_display');
        const employeeIdInput = document.getElementById('id_employee_id');
        const searchButton = document.getElementById('search_employee_btn');

        // Function to search for employee
        function searchEmployee() {
            const ministryNumber = ministryNumberInput.value.trim();
            if (!ministryNumber) {
                alert('الرجاء إدخال الرقم الوزاري');
                return;
            }

            // Show loading indicator
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchButton.disabled = true;

            // Make AJAX request
            fetch(`/files/get-employee/?ministry_number=${ministryNumber}`)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || 'حدث خطأ أثناء البحث');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);

                    // Check if data is successful and employee data exists
                    if (!data.success) {
                        throw new Error(data.error || 'بيانات الموظف غير متوفرة');
                    }

                    if (!data.employee) {
                        throw new Error('بيانات الموظف غير متوفرة');
                    }

                    // Update form fields
                    employeeNameDisplay.value = data.employee.full_name;
                    employeeIdInput.value = data.employee.id;

                    // Update select field (hidden)
                    const selectElement = document.getElementById('id_employee');
                    if (selectElement && selectElement.options) {
                        // Check if option exists
                        let optionExists = false;
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value == data.employee.id) {
                                selectElement.options[i].selected = true;
                                optionExists = true;
                                break;
                            }
                        }

                        // If option doesn't exist, create it
                        if (!optionExists) {
                            const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                            selectElement.appendChild(newOption);
                        }
                    } else if (selectElement) {
                        // If options is undefined but selectElement exists, create a new option
                        const newOption = new Option(data.employee.full_name, data.employee.id, true, true);
                        selectElement.appendChild(newOption);
                    }
                })
                .catch(error => {
                    alert(error.message);
                    employeeNameDisplay.value = '';
                    employeeIdInput.value = '';
                })
                .finally(() => {
                    // Reset button
                    searchButton.innerHTML = '<i class="fas fa-search"></i> بحث';
                    searchButton.disabled = false;
                });
        }

        // Add event listeners
        if (searchButton) {
            searchButton.addEventListener('click', searchEmployee);
        }

        // Allow searching by pressing Enter in the ministry number field
        if (ministryNumberInput) {
            ministryNumberInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    searchEmployee();
                }
            });
        }

        // If we have a pre-filled ministry number (edit mode), trigger search
        if (ministryNumberInput && ministryNumberInput.value && employeeNameDisplay && !employeeNameDisplay.value) {
            searchEmployee();
        }

        // Add form submission handler
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            console.log('Form submitted');

            // Check if employee is selected
            if (!employeeIdInput.value) {
                e.preventDefault();
                alert('الرجاء اختيار موظف أولاً');
                return false;
            }

            // File selection is optional now

            // Check if checkout date is selected
            const checkoutDateInput = document.getElementById('id_checkout_date');
            if (!checkoutDateInput.value) {
                e.preventDefault();
                alert('الرجاء اختيار تاريخ خروج الملف');
                return false;
            }

            // Make sure the employee ID is set in the form
            const selectElement = document.getElementById('id_employee');
            if (selectElement && employeeIdInput.value) {
                selectElement.value = employeeIdInput.value;
            }

            return true;
        });
    });
</script>
{% endblock %}
