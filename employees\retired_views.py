from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import Employee, RetiredEmployee
from .forms import RetiredEmployeeForm, RetireEmployeeForm
from system_logs.models import SystemLog


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@login_required
def retired_employees_list(request):
    """View to display list of retired employees"""
    search_query = request.GET.get('search', '')
    
    # Get retired employees
    retired_employees = RetiredEmployee.objects.select_related('employee').all()
    
    # Apply search filter
    if search_query:
        retired_employees = retired_employees.filter(
            Q(employee__ministry_number__icontains=search_query) |
            Q(employee__national_id__icontains=search_query) |
            Q(employee__full_name__icontains=search_query) |
            Q(retirement_reason__icontains=search_query)
        )
    
    # Handle POST request for retiring an employee
    if request.method == 'POST':
        form = RetireEmployeeForm(request.POST)
        if form.is_valid():
            employee = form.cleaned_data['employee']
            
            # Check if employee is already retired
            if hasattr(employee, 'retirement'):
                messages.error(request, 'هذا الموظف متقاعد بالفعل.')
                return redirect('employees:retired_employees_list')
            
            # Create retirement record
            retired_employee = RetiredEmployee.objects.create(
                employee=employee,
                retirement_date=form.cleaned_data['retirement_date'],
                retirement_reason=form.cleaned_data['retirement_reason'],
                notes=form.cleaned_data['notes']
            )
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.EMPLOYEES,
                action=SystemLog.CREATE,
                page='retired_employees_list',
                object_id=str(retired_employee.id),
                object_repr=f'{employee.full_name} ({employee.ministry_number})',
                description=f'تم تقاعد الموظف: {employee.full_name} - الرقم الوزاري: {employee.ministry_number}'
            )
            
            messages.success(request, f'تم تقاعد الموظف {employee.full_name} (الرقم الوزاري: {employee.ministry_number}) بنجاح.')
            return redirect('employees:retired_employees_list')
        else:
            # Display form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{form.fields[field].label}: {error}')
    else:
        form = RetireEmployeeForm()
    
    context = {
        'retired_employees': retired_employees,
        'search_query': search_query,
        'form': form,
    }
    
    return render(request, 'employees/retired_employees_list.html', context)


@login_required
def retired_employee_detail(request, pk):
    """View to display retired employee details"""
    retired_employee = get_object_or_404(RetiredEmployee, pk=pk)
    
    context = {
        'retired_employee': retired_employee,
    }
    
    return render(request, 'employees/retired_employee_detail.html', context)


@login_required
def retired_employee_update(request, pk):
    """View to update retired employee information"""
    retired_employee = get_object_or_404(RetiredEmployee, pk=pk)
    
    if request.method == 'POST':
        form = RetiredEmployeeForm(request.POST, instance=retired_employee)
        if form.is_valid():
            form.save()
            
            # Log the action
            SystemLog.objects.create(
                user=request.user,
                ip_address=get_client_ip(request),
                module=SystemLog.EMPLOYEES,
                action=SystemLog.UPDATE,
                page='retired_employee_update',
                object_id=str(retired_employee.id),
                object_repr=f'{retired_employee.employee.full_name} ({retired_employee.employee.ministry_number})',
                description=f'تم تحديث بيانات المتقاعد: {retired_employee.employee.full_name}'
            )
            
            messages.success(request, 'تم تحديث بيانات المتقاعد بنجاح.')
            return redirect('employees:retired_employee_detail', pk=pk)
    else:
        form = RetiredEmployeeForm(instance=retired_employee)
    
    context = {
        'form': form,
        'retired_employee': retired_employee,
    }
    
    return render(request, 'employees/retired_employee_form.html', context)


@login_required
def retired_employee_delete(request, pk):
    """View to delete (restore) retired employee"""
    retired_employee = get_object_or_404(RetiredEmployee, pk=pk)
    
    if request.method == 'POST':
        employee_name = retired_employee.employee.full_name
        
        # Log the action
        SystemLog.objects.create(
            user=request.user,
            ip_address=get_client_ip(request),
            module=SystemLog.EMPLOYEES,
            action=SystemLog.DELETE,
            page='retired_employee_delete',
            object_id=str(retired_employee.id),
            object_repr=f'{employee_name} ({retired_employee.employee.ministry_number})',
            description=f'تم إلغاء تقاعد الموظف: {employee_name}'
        )
        
        # Delete the retirement record (this will restore the employee to active status)
        retired_employee.delete()
        
        messages.success(request, f'تم إلغاء تقاعد الموظف {employee_name} وإعادته للخدمة بنجاح.')
        return redirect('employees:retired_employees_list')
    
    context = {
        'retired_employee': retired_employee,
    }
    
    return render(request, 'employees/retired_employee_confirm_delete.html', context)


@login_required
def search_employees_for_retirement(request):
    """AJAX view to search for employees available for retirement"""
    search_term = request.GET.get('term', '').strip()
    
    if len(search_term) < 2:
        return JsonResponse({'results': []})
    
    # Get employees who are not retired
    retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
    employees = Employee.objects.exclude(id__in=retired_employee_ids)
    
    # Filter by search term (ministry number or name)
    employees = employees.filter(
        Q(ministry_number__icontains=search_term) |
        Q(full_name__icontains=search_term)
    ).order_by('full_name')[:20]  # Limit to 20 results
    
    results = []
    for employee in employees:
        results.append({
            'id': employee.id,
            'text': f"{employee.full_name} ({employee.ministry_number})",
            'ministry_number': employee.ministry_number,
            'full_name': employee.full_name,
            'national_id': employee.national_id,
            'school': employee.school
        })
    
    return JsonResponse({'results': results})