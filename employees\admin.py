from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from .models import Employee, AnnualReport, Penalty, RetiredEmployee, ExternalTransfer, MaternityLeave, InternalTransfer

@admin.register(Employee)
class EmployeeAdmin(ImportExportModelAdmin):
    list_display = ('ministry_number', 'national_id', 'full_name', 'qualification', 'specialization', 'hire_date', 'school')
    list_filter = ('qualification', 'hire_date')
    search_fields = ('ministry_number', 'national_id', 'full_name', 'school')
    date_hierarchy = 'hire_date'

@admin.register(AnnualReport)
class AnnualReportAdmin(admin.ModelAdmin):
    list_display = ('employee', 'year', 'score')
    list_filter = ('year',)
    search_fields = ('employee__full_name', 'employee__ministry_number')

@admin.register(Penalty)
class PenaltyAdmin(admin.ModelAdmin):
    list_display = ('employee', 'date', 'description')
    list_filter = ('date',)
    search_fields = ('employee__full_name', 'employee__ministry_number', 'description')
    date_hierarchy = 'date'

@admin.register(RetiredEmployee)
class RetiredEmployeeAdmin(admin.ModelAdmin):
    list_display = ('employee', 'retirement_date', 'retirement_reason', 'created_at')
    list_filter = ('retirement_date', 'retirement_reason', 'created_at')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'employee__national_id', 'retirement_reason')
    date_hierarchy = 'retirement_date'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('معلومات التقاعد', {
            'fields': ('retirement_date', 'retirement_reason', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(ExternalTransfer)
class ExternalTransferAdmin(admin.ModelAdmin):
    list_display = ('employee', 'destination_directorate', 'transfer_date', 'transfer_reason', 'created_at')
    list_filter = ('transfer_date', 'destination_directorate', 'transfer_reason', 'created_at')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'employee__national_id', 'destination_directorate', 'transfer_reason')
    date_hierarchy = 'transfer_date'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('معلومات النقل', {
            'fields': ('transfer_date', 'destination_directorate', 'transfer_reason', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(MaternityLeave)
class MaternityLeaveAdmin(admin.ModelAdmin):
    list_display = ('employee', 'start_date', 'end_date', 'is_active', 'created_at')
    list_filter = ('is_active', 'start_date', 'end_date', 'created_at')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'employee__national_id')
    date_hierarchy = 'start_date'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات الموظفة', {
            'fields': ('employee',)
        }),
        ('معلومات الإجازة', {
            'fields': ('start_date', 'end_date', 'is_active', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee')


@admin.register(InternalTransfer)
class InternalTransferAdmin(admin.ModelAdmin):
    list_display = ('employee', 'previous_department', 'new_department', 'transfer_date', 'start_date', 'created_at')
    list_filter = ('transfer_date', 'start_date', 'previous_department', 'new_department', 'created_at')
    search_fields = ('employee__full_name', 'employee__ministry_number', 'employee__national_id', 'previous_department', 'new_department')
    date_hierarchy = 'transfer_date'
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('employee',)
        }),
        ('معلومات النقل', {
            'fields': ('previous_department', 'new_department', 'transfer_date', 'start_date', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee')
