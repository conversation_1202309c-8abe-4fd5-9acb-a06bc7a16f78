from django.core.management.base import BaseCommand
from employment.models import Department

class Command(BaseCommand):
    help = 'Add grade data to school departments'

    def handle(self, *args, **options):
        # Get school departments
        school_departments = Department.objects.filter(workplace='school')

        if not school_departments.exists():
            self.stdout.write(self.style.WARNING('No school departments found.'))
            return

        # Sample grade data for different school types
        grade_data = [
            {
                'name_contains': 'ابتدائي',
                'highest_grade': 'grade6',
                'lowest_grade': 'grade1'
            },
            {
                'name_contains': 'اساسي',
                'highest_grade': 'grade9',
                'lowest_grade': 'grade1'
            },
            {
                'name_contains': 'ثانوي',
                'highest_grade': 'grade12',
                'lowest_grade': 'grade10'
            },
            {
                'name_contains': 'روضة',
                'highest_grade': 'kg',
                'lowest_grade': 'kg'
            },
        ]

        updated_count = 0

        for dept in school_departments:
            # Skip if already has grade data
            if dept.highest_grade and dept.lowest_grade:
                continue

            # Find matching grade data
            for grade_info in grade_data:
                if grade_info['name_contains'] in dept.name:
                    dept.highest_grade = grade_info['highest_grade']
                    dept.lowest_grade = grade_info['lowest_grade']
                    dept.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Updated {dept.name}: {dept.get_lowest_grade_display()} - {dept.get_highest_grade_display()}'
                        )
                    )
                    break
            else:
                # Default for schools without specific type
                if dept.school_type == 'primary':
                    dept.highest_grade = 'grade9'
                    dept.lowest_grade = 'grade1'
                elif dept.school_type == 'secondary':
                    dept.highest_grade = 'grade12'
                    dept.lowest_grade = 'grade10'
                else:
                    # Mixed school
                    dept.highest_grade = 'grade12'
                    dept.lowest_grade = 'kg'

                dept.save()
                updated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Updated {dept.name} (default): {dept.get_lowest_grade_display()} - {dept.get_highest_grade_display()}'
                    )
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully updated {updated_count} departments with grade data.')
        )
