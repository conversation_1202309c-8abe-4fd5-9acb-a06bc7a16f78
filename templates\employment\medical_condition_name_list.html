{% extends 'base.html' %}
{% load static %}

{% block title %}أسماء الحالات المرضية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    
    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>أسماء الحالات المرضية</h2>
    <div>
        <a href="{% url 'employment:medical_condition_name_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة اسم حالة مرضية جديدة
        </a>
        <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
            <i class="fas fa-list"></i> قائمة الحالات المرضية
        </a>
    </div>
</div>

<!-- Medical Condition Names Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">قائمة أسماء الحالات المرضية</h6>
        <span class="badge bg-info">إجمالي العدد: {{ condition_names.count }}</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>اسم الحالة المرضية</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for condition_name in condition_names %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ condition_name.name }}</td>
                        <td>{{ condition_name.description|default:"لا يوجد وصف"|truncatechars:100 }}</td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'employment:medical_condition_name_update' condition_name.pk %}" class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'employment:medical_condition_name_delete' condition_name.pk %}" class="btn btn-danger btn-sm" title="حذف">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">لا توجد أسماء حالات مرضية مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
