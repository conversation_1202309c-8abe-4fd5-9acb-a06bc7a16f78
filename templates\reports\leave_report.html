{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير الإجازات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تقرير الإجازات</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية</label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.start_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.end_date.id_for_label }}" class="form-label">تاريخ النهاية</label>
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.end_date.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.department.id_for_label }}" class="form-label">القسم (اختياري)</label>
                    {{ form.department }}
                    {% if form.department.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.department.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">اترك هذا الحقل فارغاً لإنشاء تقرير لجميع الأقسام</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.leave_type.id_for_label }}" class="form-label">نوع الإجازة (اختياري)</label>
                    {{ form.leave_type }}
                    {% if form.leave_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.leave_type.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="form-text">اترك هذا الحقل فارغاً لإنشاء تقرير لجميع أنواع الإجازات</div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-dark">
                    <i class="fas fa-file-export"></i> إنشاء التقرير
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
    </div>
    <div class="card-body">
        <p>يقوم هذا التقرير بإنشاء ملف Excel يحتوي على معلومات عن الإجازات خلال الفترة المحددة.</p>
        <p>يمكنك تحديد قسم معين أو نوع إجازة معين لتصفية النتائج.</p>
        <p>يتضمن التقرير المعلومات التالية:</p>
        <ul>
            <li>الرقم الوزاري للموظف</li>
            <li>اسم الموظف</li>
            <li>نوع الإجازة</li>
            <li>تاريخ بداية الإجازة</li>
            <li>تاريخ نهاية الإجازة</li>
            <li>عدد أيام الإجازة</li>
            <li>سبب الإجازة</li>
            <li>حالة الإجازة</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });
    });
</script>
{% endblock %}
