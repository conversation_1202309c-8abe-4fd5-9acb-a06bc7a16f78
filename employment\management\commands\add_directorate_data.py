from django.core.management.base import BaseCommand
from employment.models import Department

class Command(BaseCommand):
    help = 'Add directorate departments with directorate_type data'

    def handle(self, *args, **options):
        # Sample directorate departments
        directorate_departments = [
            {
                'name': 'قسم الشؤون الإدارية',
                'description': 'قسم مسؤول عن الشؤون الإدارية والمالية',
                'workplace': 'directorate',
                'directorate_type': 'administrative'
            },
            {
                'name': 'قسم التخطيط والمتابعة',
                'description': 'قسم مسؤول عن التخطيط ومتابعة تنفيذ الخطط',
                'workplace': 'directorate',
                'directorate_type': 'educational'
            },
            {
                'name': 'مكتب المدير',
                'description': 'مكتب مدير التربية والتعليم',
                'workplace': 'directorate',
                'directorate_type': 'manager'
            },
            {
                'name': 'قسم الموارد البشرية',
                'description': 'قسم مسؤول عن شؤون الموظفين',
                'workplace': 'directorate',
                'directorate_type': 'administrative'
            },
            {
                'name': 'قسم المناهج والتطوير',
                'description': 'قسم مسؤول عن تطوير المناهج',
                'workplace': 'directorate',
                'directorate_type': 'educational'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for dept_data in directorate_departments:
            department, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults=dept_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء قسم جديد: {department.name}')
                )
            else:
                # Update existing department
                department.description = dept_data['description']
                department.workplace = dept_data['workplace']
                department.directorate_type = dept_data['directorate_type']
                department.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'تم تحديث قسم موجود: {department.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'تم إنشاء {created_count} قسم جديد وتحديث {updated_count} قسم موجود.'
            )
        )
