from django.contrib import admin
from .models import InternalTransfer, SystemSettings, ImportantLink

@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    """Admin for system settings"""
    fieldsets = (
        ('إعدادات النقل الداخلي', {
            'fields': ('internal_transfer_enabled', 'internal_transfer_message')
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance of SystemSettings
        return SystemSettings.objects.count() == 0

    def has_delete_permission(self, request, obj=None):
        # Don't allow deleting the settings
        return False

@admin.register(ImportantLink)
class ImportantLinkAdmin(admin.ModelAdmin):
    """Admin for important links"""
    list_display = ('name', 'url', 'order', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'url', 'description')
    list_editable = ('order', 'is_active')
    ordering = ('order', 'name')
    fieldsets = (
        ('معلومات الرابط', {
            'fields': ('name', 'url', 'description')
        }),
        ('إعدادات العرض', {
            'fields': ('favicon_url', 'order', 'is_active')
        }),
    )

@admin.register(InternalTransfer)
class InternalTransferAdmin(admin.ModelAdmin):
    list_display = ('employee_name', 'ministry_number', 'employee_id', 'current_department',
                    'first_choice', 'created_at', 'status')
    list_filter = ('status', 'created_at', 'current_department', 'gender')
    search_fields = ('employee_name', 'ministry_number', 'employee_id', 'current_department',
                     'first_choice', 'second_choice', 'third_choice', 'reason')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('معلومات الموظف', {
            'fields': ('ministry_number', 'employee_name', 'employee_id', 'gender')
        }),
        ('معلومات الوظيفة', {
            'fields': ('current_department', 'hire_date', 'last_position',
                      'qualification', 'specialization', 'last_rank', 'address')
        }),
        ('خيارات النقل', {
            'fields': ('first_choice', 'second_choice', 'third_choice', 'reason')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone_number', 'email')
        }),
        ('حالة الطلب', {
            'fields': ('status', 'notes', 'created_at', 'updated_at', 'edit_token')
        }),
    )
