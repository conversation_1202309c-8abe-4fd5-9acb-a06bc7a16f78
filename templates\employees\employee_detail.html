{% extends 'base.html' %}
{% load static %}

{% block title %}{{ employee.full_name }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>بيانات الموظف: {{ employee.full_name }}</h2>
    <div>
        <a href="{% url 'employees:employee_update' employee.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">البيانات الشخصية</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">الرقم الوزاري</th>
                        <td>{{ employee.ministry_number }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الرقم الوطني</th>
                        <td>{{ employee.national_id }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الاسم الكامل</th>
                        <td>{{ employee.full_name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الميلاد</th>
                        <td>{{ employee.birth_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">العنوان</th>
                        <td>{{ employee.address }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">رقم الهاتف</th>
                        <td>{{ employee.phone_number }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">البيانات الوظيفية</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">المؤهل العلمي (بكالوريس / دبلوم)</th>
                        <td>
                            {% if employee.qualification %}
                                <span class="badge bg-primary">{{ employee.qualification }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">المؤهل العلمي (دبلوم بعد البكالوريس)</th>
                        <td>
                            {% if employee.post_graduate_diploma %}
                                <span class="badge bg-success">{{ employee.post_graduate_diploma }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">المؤهل العلمي (ماجستير)</th>
                        <td>
                            {% if employee.masters_degree %}
                                <span class="badge bg-info">{{ employee.masters_degree }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">المؤهل العلمي (دكتوراه)</th>
                        <td>
                            {% if employee.phd_degree %}
                                <span class="badge bg-warning text-dark">{{ employee.phd_degree }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">التخصص</th>
                        <td>{{ employee.specialization }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ التعيين</th>
                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">صفة التعيين</th>
                        <td>
                            {% if current_employment and current_employment.appointment_type %}
                                {{ current_employment.appointment_type.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                            <button type="button" class="btn btn-sm btn-outline-primary float-end" data-bs-toggle="modal" data-bs-target="#appointmentTypeModal">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">المسمى الوظيفي</th>
                        <td>
                            {% if current_employment and current_employment.position %}
                                {{ current_employment.position.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">القسم</th>
                        <td>{{ employee.school }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">التقارير السنوية</h6>
                <button type="button" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#addReportModal">
                    <i class="fas fa-plus"></i> إضافة تقرير
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>السنة</th>
                                <th>الدرجة</th>
                                <th>ملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in annual_reports %}
                            <tr>
                                <td>{{ report.year }}</td>
                                <td>{{ report.score }}</td>
                                <td>{{ report.comments|default:"-" }}</td>
                                <td>
                                    <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#editReportModal{{ report.id }}">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">لا يوجد تقارير</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">العقوبات</h6>
                <a href="{% url 'disciplinary:penalty_create' %}?employee_id={{ employee.id }}" class="btn btn-sm btn-dark">
                    <i class="fas fa-plus"></i> إضافة عقوبة
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>نوع العقوبة</th>
                                <th>التاريخ</th>
                                <th>رقم القرار</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for penalty in penalties %}
                            <tr>
                                <td>{{ penalty.penalty_type.name }}</td>
                                <td>{{ penalty.date|date:"Y-m-d" }}</td>
                                <td>{{ penalty.decision_number|default:"-" }}</td>
                                <td>
                                    <a href="{% url 'disciplinary:penalty_detail' penalty.pk %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{% url 'disciplinary:penalty_update' penalty.pk %}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'disciplinary:penalty_delete' penalty.pk %}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">لا يوجد عقوبات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Add Appointment Type Modal -->
<div class="modal fade" id="appointmentTypeModal" tabindex="-1" aria-labelledby="appointmentTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appointmentTypeModalLabel">تعديل صفة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employees:employee_detail' employee.pk %}">
                    {% csrf_token %}
                    <input type="hidden" name="appointment_type_form" value="1">

                    <div class="mb-3">
                        <label for="appointment_type" class="form-label">صفة التعيين</label>
                        <select name="appointment_type" id="appointment_type" class="form-control">
                            <option value="">- اختر صفة التعيين -</option>
                            {% for apt in appointment_types %}
                                <option value="{{ apt.id }}" {% if current_employment and current_employment.appointment_type and current_employment.appointment_type.id == apt.id %}selected{% endif %}>{{ apt.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Report Modal -->
<div class="modal fade" id="addReportModal" tabindex="-1" aria-labelledby="addReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addReportModalLabel">إضافة تقرير سنوي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employees:employee_detail' employee.pk %}">
                    {% csrf_token %}
                    <input type="hidden" name="report_form" value="1">
                    <input type="hidden" name="employee_id" value="{{ employee.id }}">

                    <div class="mb-3">
                        <label for="year" class="form-label">السنة</label>
                        <input type="number" name="year" id="year" class="form-control" min="2000" max="2100" value="{{ current_year }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="score" class="form-label">الدرجة</label>
                        <input type="number" name="score" id="score" class="form-control" min="0" max="100" step="0.01" required>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Internal Transfers Section -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-arrows-alt"></i>
                    حركات النقل الداخلي
                </h6>
                <small class="text-muted">
                    إجمالي حركات النقل: {{ employee.internal_transfers.count }}
                </small>
            </div>
            <div class="card-body">
                {% if employee.internal_transfers.exists %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr class="bg-light">
                                <th class="text-center" width="5%">#</th>
                                <th class="text-center" width="25%">من</th>
                                <th class="text-center" width="25%">إلى</th>
                                <th class="text-center" width="15%">تاريخ النقل</th>
                                <th class="text-center" width="15%">تاريخ المباشرة</th>
                                <th class="text-center" width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in employee.internal_transfers.all %}
                            <tr>
                                <td class="text-center">{{ forloop.counter }}</td>
                                <td>
                                    <span class="badge bg-warning text-dark">{{ transfer.previous_department }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ transfer.new_department }}</span>
                                </td>
                                <td class="text-center">
                                    <small>{{ transfer.transfer_date }}</small>
                                </td>
                                <td class="text-center">
                                    <small>{{ transfer.start_date }}</small>
                                </td>
                                <td class="text-center">
                                    <a href="{% url 'employees:internal_transfer_detail' transfer.pk %}" 
                                       class="btn btn-info btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <a href="{% url 'employees:internal_transfers_list' %}?search={{ employee.ministry_number }}" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-list"></i> عرض جميع حركات النقل لهذا الموظف
                    </a>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-arrows-alt fa-3x text-muted mb-3"></i>
                    <h5>لا توجد حركات نقل</h5>
                    <p class="text-muted">لم يتم تسجيل أي حركات نقل داخلي لهذا الموظف حتى الآن.</p>
                    <small class="text-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم تسجيل حركات النقل تلقائياً عند تحديث قسم الموظف
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            if (!element.classList.contains('btn')) {
                element.classList.add('form-control');
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
