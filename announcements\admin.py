from django.contrib import admin
from django.utils.html import format_html
from .models import Announcement


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'announcement_type_display', 'priority_display', 
        'is_active_display', 'show_on_homepage', 'created_by', 
        'views_count', 'clicks_count', 'created_at'
    )
    list_filter = (
        'announcement_type', 'priority', 'is_active', 'show_on_homepage',
        'created_at', 'start_date', 'end_date'
    )
    search_fields = ('title', 'content', 'created_by__username')
    readonly_fields = ('views_count', 'clicks_count', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('معلومات الإعلان', {
            'fields': ('title', 'content', 'announcement_type', 'priority')
        }),
        ('إعدادات العرض', {
            'fields': ('is_active', 'show_on_homepage', 'start_date', 'end_date')
        }),
        ('الرابط', {
            'fields': ('link_url', 'link_text'),
            'classes': ('collapse',)
        }),
        ('الإحصائيات', {
            'fields': ('views_count', 'clicks_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def announcement_type_display(self, obj):
        colors = {
            'info': '#0d6efd',
            'warning': '#fd7e14', 
            'success': '#198754',
            'danger': '#dc3545'
        }
        color = colors.get(obj.announcement_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_announcement_type_display()
        )
    announcement_type_display.short_description = 'نوع الإعلان'
    
    def priority_display(self, obj):
        colors = {
            'low': '#6c757d',
            'medium': '#0d6efd',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_priority_display()
        )
    priority_display.short_description = 'الأولوية'
    
    def is_active_display(self, obj):
        if obj.is_active:
            return format_html(
                '<span style="color: #198754; font-weight: bold;">✓ مفعل</span>'
            )
        else:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">✗ غير مفعل</span>'
            )
    is_active_display.short_description = 'الحالة'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
