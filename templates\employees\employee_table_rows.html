{% for employee in employees %}
<tr>
    <td class="text-center fw-bold text-primary">
        {% if page_obj %}
            {{ forloop.counter0|add:page_obj.start_index }}
        {% else %}
            {{ forloop.counter }}
        {% endif %}
    </td>
    <td>{{ employee.ministry_number }}</td>
    <td>{{ employee.national_id }}</td>
    <td>
        <a href="{% url 'employees:employee_detail' employee.pk %}" class="text-decoration-none fw-bold text-primary">
            {{ employee.full_name }}
        </a>
    </td>
    <td>
        {% if employee.gender == 'male' %}
            <span class="badge bg-primary">{{ employee.get_gender_display }}</span>
        {% else %}
            <span class="badge bg-danger">{{ employee.get_gender_display }}</span>
        {% endif %}
    </td>
    <td>
        {% if employee.birth_date %}
        <span class="text-primary fw-bold">
            <i class="fas fa-calendar me-1"></i>
            {{ employee.birth_date|date:"d/m/Y" }}
        </span>
        <br>
        <small class="text-muted">
            {{ employee.birth_date|date:"l" }}
        </small>
        {% else %}
        -
        {% endif %}
    </td>
    <td>{{ employee.qualification|default:"-" }}</td>
    <td>{{ employee.post_graduate_diploma|default:"-" }}</td>
    <td>{{ employee.masters_degree|default:"-" }}</td>
    <td>{{ employee.phd_degree|default:"-" }}</td>
    <td>{{ employee.specialization|default:"-" }}</td>
    <td>
        <span class="text-primary fw-bold">
            <i class="fas fa-calendar me-1"></i>
            {{ employee.hire_date|date:"d/m/Y" }}
        </span>
        <br>
        <small class="text-muted">
            {{ employee.hire_date|date:"l" }}
        </small>
    </td>
    <td>
        {% with current_employment=employee.employments.all|first %}
            {% if current_employment and current_employment.appointment_type %}
                {{ current_employment.appointment_type.name }}
            {% else %}
                -
            {% endif %}
        {% endwith %}
    </td>
    <td>{{ employee.school }}</td>
    <td>{{ employee.get_latest_position_with_date }}</td>
    <td>
        {% if employee.phone_number %}
            <a href="tel:{{ employee.phone_number }}" class="text-decoration-none">
                <i class="fas fa-phone-alt text-primary me-1"></i>
                {{ employee.phone_number }}
            </a>
        {% else %}
            -
        {% endif %}
    </td>
    <td>
        {% if employee.address %}
            <i class="fas fa-map-marker-alt text-primary me-1"></i>
            {{ employee.address }}
        {% else %}
            -
        {% endif %}
    </td>
    <td>
        <div class="btn-group">
            <a href="{% url 'employees:employee_detail' employee.pk %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </a>
            <a href="{% url 'employees:employee_update' employee.pk %}" class="btn btn-warning btn-sm" title="تعديل">
                <i class="fas fa-edit"></i>
            </a>
            <a href="{% url 'employees:employee_delete' employee.pk %}" class="btn btn-danger btn-sm" title="حذف">
                <i class="fas fa-trash"></i>
            </a>
        </div>
    </td>
</tr>
{% empty %}
<tr>
    <td colspan="18" class="text-center text-muted">لا توجد بيانات موظفين</td>
</tr>
{% endfor %}
