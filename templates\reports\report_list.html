{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة التقارير - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة التقارير</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع التقارير</h6>
        <form class="d-flex" method="get">
            <input class="form-control me-2" type="search" placeholder="بحث..." name="search">
            <button class="btn btn-outline-primary" type="submit">بحث</button>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>تاريخ الإنشاء</th>
                        <th>بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in reports %}
                    <tr>
                        <td>{{ report.title }}</td>
                        <td>{{ report.get_report_type_display }}</td>
                        <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                        <td>{{ report.created_by|default:"-" }}</td>
                        <td>
                            {% if report.file %}
                            <a href="{{ report.file.url }}" class="btn btn-success btn-sm" download>
                                <i class="fas fa-download"></i> تنزيل
                            </a>
                            {% else %}
                            <span class="badge bg-secondary">لا يوجد ملف</span>
                            {% endif %}
                            <a href="#" class="btn btn-danger btn-sm delete-report" data-id="{{ report.pk }}">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا يوجد تقارير</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add event listeners for delete buttons
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-report');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
                    // Implement delete functionality
                    window.location.href = `/reports/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}
