{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الحالة المرضية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .detail-section {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .detail-section h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }

    .detail-item {
        margin-bottom: 15px;
    }

    .detail-label {
        font-weight: bold;
        color: #5a5c69;
    }

    .detail-value {
        color: #3a3b45;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تفاصيل الحالة المرضية</h2>
    <div class="action-buttons">
        <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى القائمة
        </a>
        <a href="{% url 'employment:medical_condition_update' medical_condition.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employment:medical_condition_delete' medical_condition.pk %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <!-- Employee Information Section -->
        <div class="detail-section">
            <h4>معلومات الموظف</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">الرقم الوزاري</div>
                        <div class="detail-value">{{ medical_condition.employee.ministry_number }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">اسم الموظف</div>
                        <div class="detail-value">{{ medical_condition.employee.full_name }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">القسم</div>
                        <div class="detail-value">
                            {% with current_employment=medical_condition.employee.employments.filter|first %}
                                {% if current_employment and current_employment.department %}
                                    {{ current_employment.department.name }}
                                {% elif medical_condition.employee.school %}
                                    {{ medical_condition.employee.school.name }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">المسمى الوظيفي</div>
                        <div class="detail-value">
                            {% if medical_condition.employee.positions.all %}
                                {% with latest_position=medical_condition.employee.positions.all|dictsort:"date_obtained"|last %}
                                    {{ latest_position.position.name }}
                                {% endwith %}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medical Condition Information Section -->
        <div class="detail-section">
            <h4>معلومات الحالة المرضية</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">الحالة المرضية</div>
                        <div class="detail-value">{{ medical_condition.condition.name }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">تاريخ التقرير الطبي</div>
                        <div class="detail-value">{{ medical_condition.medical_report_date|date:"Y-m-d" }}</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="detail-item">
                        <div class="detail-label">وصف الحالة</div>
                        <div class="detail-value">{{ medical_condition.description|linebreaks }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="detail-section">
            <h4>ملاحظات</h4>
            <div class="row">
                <div class="col-md-12">
                    <div class="detail-item">
                        <div class="detail-value">
                            {% if medical_condition.notes %}
                                {{ medical_condition.notes|linebreaks }}
                            {% else %}
                                لا توجد ملاحظات
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metadata Section -->
        <div class="detail-section">
            <h4>بيانات النظام</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">تاريخ الإنشاء</div>
                        <div class="detail-value">{{ medical_condition.created_at|date:"Y-m-d H:i" }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">تاريخ آخر تحديث</div>
                        <div class="detail-value">{{ medical_condition.updated_at|date:"Y-m-d H:i" }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
