# دليل سريع - نظام إجازات الأمومة

## كيفية الوصول للنظام

### 1. تشغيل الخادم
```bash
# استخدم أحد الملفات التالية:
تشغيل_السيرفر.bat
# أو
start_server_maternity.bat
```

### 2. الوصول للنظام
1. افتح المتصفح واذهب إلى: `http://127.0.0.1:8000`
2. سجل دخولك بحساب المدير
3. من القائمة الجانبية، اختر **"إجازات الأمومة"** (تحت قسم الكادر)

## الميزات الرئيسية

### ✅ إضافة إجازة أمومة جديدة
1. اضغط على **"إضافة إجازة أمومة"**
2. ابحث عن الموظفة بالرقم الوزاري أو الاسم
3. حدد تاريخ بداية الإجازة
4. سيحسب النظام تاريخ الانتهاء تلقائياً (90 يوم)
5. أضف ملاحظات إن أردت
6. احفظ الإجازة

### ✅ عرض قائمة الإجازات
- جدول شامل بجميع إجازات الأمومة
- البحث والتصفية
- عرض الحالة (نشطة/منتهية)

### ✅ إدارة الإجازات
- **عرض التفاصيل**: معلومات كاملة عن الإجازة
- **تعديل**: تغيير التواريخ والملاحظات
- **حذف**: إزالة الإجازة من النظام

### ✅ تصدير البيانات
- تصدير الإجازات النشطة فقط إلى Excel
- ملف منسق ومنظم
- معلومات شاملة

## القواعد المهمة

### 🔒 قيود النظام
- ✅ الإجازة للموظفات الإناث فقط
- ✅ إجازة واحدة نشطة لكل موظفة
- ✅ مدة الإجازة 90 يوماً ثابتة
- ✅ الحساب من اليوم التالي لتاريخ البداية

### 📊 الحسابات التلقائية
- **تاريخ البداية**: يدخله المستخدم
- **تاريخ الانتهاء**: تاريخ البداية + 90 يوم
- **الحالة**: تتحدث تلقائياً عند انتهاء الإجازة

## مثال عملي

### إضافة إجازة أمومة:
```
الموظفة: فاطمة أحمد محمد
الرقم الوزاري: 12345
تاريخ البداية: 2024-01-15
تاريخ الانتهاء: 2024-04-14 (تلقائي)
الحالة: نشطة
```

### البحث:
```
- ابحث بـ "فاطمة" أو "12345"
- النتائج ستظهر فوراً
- اختر الموظفة المطلوبة
```

## استكشاف الأخطاء

### ❌ لا يمكن إضافة إجازة
**السبب المحتمل**: الموظفة لديها إجازة نشطة بالفعل
**الحل**: تحقق من الإجازات الحالية أو أنهِ الإجازة السابقة

### ❌ لا تظهر الموظفة في البحث
**السبب المحتمل**: الموظفة ذكر أو غير موجودة
**الحل**: تأكد من جنس الموظفة في بياناتها الأساسية

### ❌ خطأ في التاريخ
**السبب المحتمل**: تاريخ غير صحيح
**الحل**: استخدم تنسيق التاريخ الصحيح (YYYY-MM-DD)

## الدعم التقني

### 📁 ملفات النظام
- `employees/models.py` - نموذج البيانات
- `employees/maternity_views.py` - منطق العمل
- `templates/employees/maternity_*` - واجهات المستخدم

### 🔧 قاعدة البيانات
- جدول: `employees_maternityleave`
- Migration: `0006_maternityleave.py`

### 🌐 المسارات
- القائمة: `/employees/maternity-leaves/`
- إضافة: `/employees/maternity-leaves/add/`
- تفاصيل: `/employees/maternity-leaves/<id>/`
- تعديل: `/employees/maternity-leaves/<id>/edit/`
- حذف: `/employees/maternity-leaves/<id>/delete/`

---

**ملاحظة**: تأكد من تشغيل `python manage.py migrate` قبل استخدام النظام لأول مرة.