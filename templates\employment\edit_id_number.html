{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل رقم الهوية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل رقم الهوية</h1>
        <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تعديل رقم الهوية للموظف: {{ identification.employee.full_name }}</h6>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            معلومات الموظف
                        </div>
                        <div class="card-body">
                            <p><strong>الرقم الوزاري:</strong> {{ identification.ministry_number }}</p>
                            <p><strong>الاسم الكامل:</strong> {{ identification.employee.full_name }}</p>
                            <p><strong>الرقم الوطني:</strong> {{ identification.national_id }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="id_number">رقم الهوية</label>
                    <input type="text" class="form-control" id="id_number" name="id_number" value="{{ identification.id_number }}" required>
                    <small class="form-text text-muted">أدخل رقم الهوية الجديد للموظف</small>
                    {% if error %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endif %}
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
