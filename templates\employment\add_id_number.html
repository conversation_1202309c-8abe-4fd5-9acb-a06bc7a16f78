{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة رقم هوية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة رقم هوية</h1>
        <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إضافة رقم هوية للموظف</h6>
                </div>
                <div class="card-body">
                    {% if employee %}
                    <div class="alert alert-info mb-4">
                        <h5>بيانات الموظف</h5>
                        <p><strong>الرقم الوزاري:</strong> {{ employee.ministry_number }}</p>
                        <p><strong>الاسم:</strong> {{ employee.full_name }}</p>
                        <p><strong>القسم:</strong> {{ employee.school }}</p>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="ministry_number" value="{{ employee.ministry_number }}">
                        
                        <div class="form-group">
                            <label for="id_number">رقم الهوية</label>
                            <input type="text" class="form-control" id="id_number" name="id_number" required>
                            <small class="form-text text-muted">أدخل رقم الهوية للموظف</small>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> خطأ</h5>
                        <p>{{ error_message|default:"لم يتم العثور على الموظف" }}</p>
                    </div>
                    <a href="{% url 'employment:employee_identification_list' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
