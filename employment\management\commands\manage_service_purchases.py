"""
Management command for service purchases
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from employment.models import ServicePurchase
from employees.models import Employee
from decimal import Decimal
from datetime import date


class Command(BaseCommand):
    help = 'Manage service purchases - list, create, update, delete'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['list', 'create', 'update', 'delete', 'stats'],
            help='Action to perform'
        )
        parser.add_argument(
            '--employee-id',
            type=int,
            help='Employee ID for create/update/delete operations'
        )
        parser.add_argument(
            '--service-purchase-id',
            type=int,
            help='Service Purchase ID for update/delete operations'
        )
        parser.add_argument(
            '--years',
            type=float,
            default=0,
            help='Service years'
        )
        parser.add_argument(
            '--months',
            type=int,
            default=0,
            help='Service months'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=0,
            help='Service days'
        )
        parser.add_argument(
            '--amount',
            type=float,
            help='Purchase amount'
        )
        parser.add_argument(
            '--notes',
            type=str,
            default='',
            help='Notes'
        )
        parser.add_argument(
            '--date',
            type=str,
            help='Purchase date (YYYY-MM-DD format)'
        )

    def handle(self, *args, **options):
        action = options['action']

        if action == 'list':
            self.list_service_purchases()
        elif action == 'create':
            self.create_service_purchase(options)
        elif action == 'update':
            self.update_service_purchase(options)
        elif action == 'delete':
            self.delete_service_purchase(options)
        elif action == 'stats':
            self.show_statistics()

    def list_service_purchases(self):
        """List all active service purchases"""
        service_purchases = ServicePurchase.objects.filter(is_active=True).select_related('employee')
        
        if not service_purchases:
            self.stdout.write(
                self.style.WARNING('لا يوجد شراء خدمات نشط')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'إجمالي شراء الخدمات النشط: {service_purchases.count()}')
        )
        self.stdout.write('-' * 80)
        
        for sp in service_purchases:
            self.stdout.write(
                f'ID: {sp.id} | '
                f'الموظف: {sp.employee.full_name} | '
                f'الرقم الوزاري: {sp.employee.ministry_number} | '
                f'فترة الخدمة: {sp.total_service_period} | '
                f'المبلغ: {sp.purchase_amount} د.ع | '
                f'التاريخ: {sp.purchase_date}'
            )

    def create_service_purchase(self, options):
        """Create a new service purchase"""
        employee_id = options.get('employee_id')
        if not employee_id:
            self.stdout.write(
                self.style.ERROR('الرجاء تحديد معرف الموظف باستخدام --employee-id')
            )
            return

        try:
            employee = Employee.objects.get(id=employee_id)
        except Employee.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'الموظف بالمعرف {employee_id} غير موجود')
            )
            return

        # Check if employee already has active service purchase
        if ServicePurchase.objects.filter(employee=employee, is_active=True).exists():
            self.stdout.write(
                self.style.ERROR(f'الموظف {employee.full_name} لديه شراء خدمة نشط مسبقاً')
            )
            return

        amount = options.get('amount')
        if not amount:
            self.stdout.write(
                self.style.ERROR('الرجاء تحديد مبلغ الشراء باستخدام --amount')
            )
            return

        purchase_date = options.get('date')
        if purchase_date:
            try:
                purchase_date = date.fromisoformat(purchase_date)
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD')
                )
                return
        else:
            purchase_date = date.today()

        try:
            with transaction.atomic():
                service_purchase = ServicePurchase.objects.create(
                    employee=employee,
                    purchase_date=purchase_date,
                    service_years=Decimal(str(options.get('years', 0))),
                    service_months=options.get('months', 0),
                    service_days=options.get('days', 0),
                    purchase_amount=Decimal(str(amount)),
                    notes=options.get('notes', '')
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'تم إنشاء شراء خدمة جديد للموظف {employee.full_name} '
                        f'بمعرف {service_purchase.id}'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في إنشاء شراء الخدمة: {str(e)}')
            )

    def update_service_purchase(self, options):
        """Update an existing service purchase"""
        sp_id = options.get('service_purchase_id')
        if not sp_id:
            self.stdout.write(
                self.style.ERROR('الرجاء تحديد معرف شراء الخدمة باستخدام --service-purchase-id')
            )
            return

        try:
            service_purchase = ServicePurchase.objects.get(id=sp_id)
        except ServicePurchase.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'شراء الخدمة بالمعرف {sp_id} غير موجود')
            )
            return

        # Update fields if provided
        updated_fields = []
        
        if options.get('years') is not None:
            service_purchase.service_years = Decimal(str(options['years']))
            updated_fields.append('service_years')
        
        if options.get('months') is not None:
            service_purchase.service_months = options['months']
            updated_fields.append('service_months')
        
        if options.get('days') is not None:
            service_purchase.service_days = options['days']
            updated_fields.append('service_days')
        
        if options.get('amount') is not None:
            service_purchase.purchase_amount = Decimal(str(options['amount']))
            updated_fields.append('purchase_amount')
        
        if options.get('notes') is not None:
            service_purchase.notes = options['notes']
            updated_fields.append('notes')
        
        if options.get('date'):
            try:
                service_purchase.purchase_date = date.fromisoformat(options['date'])
                updated_fields.append('purchase_date')
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD')
                )
                return

        if updated_fields:
            try:
                service_purchase.save(update_fields=updated_fields + ['updated_at'])
                self.stdout.write(
                    self.style.SUCCESS(
                        f'تم تحديث شراء الخدمة {sp_id} للموظف {service_purchase.employee.full_name}'
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'خطأ في تحديث شراء الخدمة: {str(e)}')
                )
        else:
            self.stdout.write(
                self.style.WARNING('لم يتم تحديد أي حقول للتحديث')
            )

    def delete_service_purchase(self, options):
        """Delete a service purchase"""
        sp_id = options.get('service_purchase_id')
        if not sp_id:
            self.stdout.write(
                self.style.ERROR('الرجاء تحديد معرف شراء الخدمة باستخدام --service-purchase-id')
            )
            return

        try:
            service_purchase = ServicePurchase.objects.get(id=sp_id)
        except ServicePurchase.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'شراء الخدمة بالمعرف {sp_id} غير موجود')
            )
            return

        employee_name = service_purchase.employee.full_name
        
        try:
            service_purchase.delete()
            self.stdout.write(
                self.style.SUCCESS(
                    f'تم حذف شراء الخدمة للموظف {employee_name}'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في حذف شراء الخدمة: {str(e)}')
            )

    def show_statistics(self):
        """Show service purchase statistics"""
        total_active = ServicePurchase.objects.filter(is_active=True).count()
        total_inactive = ServicePurchase.objects.filter(is_active=False).count()
        
        if total_active == 0:
            self.stdout.write(
                self.style.WARNING('لا يوجد شراء خدمات نشط')
            )
            return

        active_purchases = ServicePurchase.objects.filter(is_active=True)
        
        total_amount = sum(sp.purchase_amount for sp in active_purchases)
        average_amount = total_amount / total_active if total_active > 0 else 0
        
        total_years = sum(float(sp.service_years) for sp in active_purchases)
        average_years = total_years / total_active if total_active > 0 else 0

        self.stdout.write(
            self.style.SUCCESS('إحصائيات شراء الخدمات')
        )
        self.stdout.write('-' * 40)
        self.stdout.write(f'إجمالي شراء الخدمات النشط: {total_active}')
        self.stdout.write(f'إجمالي شراء الخدمات غير النشط: {total_inactive}')
        self.stdout.write(f'إجمالي المبالغ: {total_amount:,.2f} د.ع')
        self.stdout.write(f'متوسط المبلغ: {average_amount:,.2f} د.ع')
        self.stdout.write(f'إجمالي السنوات المشتراة: {total_years:.2f} سنة')
        self.stdout.write(f'متوسط السنوات المشتراة: {average_years:.2f} سنة')