{% extends 'base.html' %}
{% load static %}

{% block title %}النسخ الاحتياطية - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>النسخ الاحتياطية</h2>
        <div>
            <a href="{% url 'backup:create_backup' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
            </a>
            <a href="{% url 'backup:upload_backup' %}" class="btn btn-success">
                <i class="fas fa-upload"></i> رفع نسخة احتياطية
            </a>
            <a href="{% url 'backup:restore_backup' %}" class="btn btn-warning">
                <i class="fas fa-undo"></i> استعادة نسخة احتياطية
            </a>
            <a href="{% url 'backup:toggle_maintenance' %}" class="btn btn-info">
                <i class="fas fa-tools"></i> إدارة الصيانة
            </a>
        </div>
    </div>

    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة النسخ الاحتياطية</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>الحجم</th>
                            <th>تاريخ الإنشاء</th>
                            <th>بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in backups %}
                        <tr>
                            <td>{{ backup.name }}</td>
                            <td>{{ backup.description|default:"-" }}</td>
                            <td>{{ backup.size|filesizeformat }}</td>
                            <td>{{ backup.created_at }}</td>
                            <td>{{ backup.created_by }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'backup:download_backup' backup.pk %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-download"></i> تنزيل
                                    </a>
                                    <a href="{% url 'backup:delete_backup' backup.pk %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد نسخ احتياطية</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
