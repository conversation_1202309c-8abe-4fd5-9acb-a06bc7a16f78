{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير موظف - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تقرير موظف</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.employee.id_for_label }}" class="form-label">الموظف</label>
                {{ form.employee }}
                {% if form.employee.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.employee.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <h6>محتويات التقرير</h6>
                <div class="form-check">
                    {{ form.include_personal_info }}
                    <label class="form-check-label" for="{{ form.include_personal_info.id_for_label }}">
                        تضمين المعلومات الشخصية
                    </label>
                </div>
                <div class="form-check">
                    {{ form.include_employment_info }}
                    <label class="form-check-label" for="{{ form.include_employment_info.id_for_label }}">
                        تضمين معلومات التوظيف
                    </label>
                </div>
                <div class="form-check">
                    {{ form.include_leaves }}
                    <label class="form-check-label" for="{{ form.include_leaves.id_for_label }}">
                        تضمين الإجازات
                    </label>
                </div>
                <div class="form-check">
                    {{ form.include_performance }}
                    <label class="form-check-label" for="{{ form.include_performance.id_for_label }}">
                        تضمين تقييم الأداء
                    </label>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-export"></i> إنشاء التقرير
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
    </div>
    <div class="card-body">
        <p>يقوم هذا التقرير بإنشاء ملف Excel يحتوي على معلومات شاملة عن الموظف المحدد.</p>
        <p>يمكنك تحديد أنواع المعلومات التي ترغب في تضمينها في التقرير.</p>
        <p>يتضمن التقرير المعلومات التالية (حسب اختيارك):</p>
        <ul>
            <li>المعلومات الشخصية: الرقم الوزاري، الرقم الوطني، الاسم الكامل، المؤهل العلمي (بكالوريس / دبلوم)، المؤهل العلمي (دبلوم بعد البكالوريس)، المؤهل العلمي (ماجستير)، المؤهل العلمي (دكتوراه)، التخصص، تاريخ التعيين، المدرسة، تاريخ الميلاد، العنوان، رقم الهاتف</li>
            <li>معلومات التوظيف: القسم، المنصب، حالة التوظيف، تاريخ التعيين، تاريخ الانتهاء</li>
            <li>الإجازات: نوع الإجازة، تاريخ البداية، تاريخ النهاية، عدد الأيام، السبب، الحالة</li>
            <li>تقييم الأداء: السنة، الدرجة، الدرجة القصوى، النسبة المئوية، المقيم، الملاحظات</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(function(element) {
            element.classList.add('form-check-input');
        });
    });
</script>
{% endblock %}
