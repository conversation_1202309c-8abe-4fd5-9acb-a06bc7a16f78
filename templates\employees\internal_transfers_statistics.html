{% extends 'base.html' %}
{% load static %}

{% block title %}إحصائيات حركات النقل الداخلي - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    .stats-card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: all 0.3s;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    .trend-positive {
        color: #28a745;
    }
    .trend-negative {
        color: #dc3545;
    }
    .chart-container {
        position: relative;
        height: 400px;
    }
    .department-item {
        padding: 0.75rem 1rem;
        border-left: 4px solid #007bff;
        margin-bottom: 0.5rem;
        background: #f8f9fc;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }
    .department-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    .department-item:nth-child(even) {
        border-left-color: #28a745;
    }
    .department-item:nth-child(3n) {
        border-left-color: #ffc107;
    }
    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }
    .badge-success {
        background-color: #28a745;
        color: white;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    
    /* تحسينات إضافية */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }
    
    .animate__fadeInUp {
        animation: fadeInUp 0.6s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .border-start {
        border-left: 4px solid !important;
    }
    
    .rounded-3 {
        border-radius: 0.5rem !important;
    }
    
    .shadow {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    
    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .dashboard-card {
            margin-bottom: 1rem;
        }
        
        .h3 {
            font-size: 1.5rem;
        }
        
        .department-item {
            padding: 0.5rem;
        }
        
        .chart-container {
            height: 300px;
        }
        
        .border-end {
            border-right: none !important;
            border-bottom: 1px solid #dee2e6 !important;
            margin-bottom: 1rem;
        }
    }
    
    /* تحسينات للطباعة */
    @media print {
        .dashboard-card:hover {
            transform: none;
            box-shadow: none;
        }
        
        .btn {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-primary fw-bold">
            <i class="fas fa-chart-bar me-2"></i>إحصائيات حركات النقل الداخلي
        </h1>
        <div class="d-flex gap-2">
            <a href="{% url 'employees:internal_transfers_list' %}" class="btn btn-primary btn-sm shadow-sm">
                <i class="fas fa-list me-1"></i> قائمة حركات النقل
            </a>
            <a href="{% url 'employees:export_internal_transfers_excel' %}" class="btn btn-success btn-sm shadow-sm">
                <i class="fas fa-file-excel me-1"></i> تصدير Excel
            </a>
            <button id="refreshStats" class="btn btn-outline-primary btn-sm shadow-sm">
                <i class="fas fa-sync-alt me-1"></i> تحديث البيانات
            </button>
            <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary btn-sm shadow-sm">
                <i class="fas fa-arrow-left me-1"></i> العودة لإدارة الكادر
            </a>
        </div>
    </div>

    <!-- Summary Statistics Cards -->
    <div class="row mb-4">
        <!-- Total Transfers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي حركات النقل</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ stats.total_transfers }}</div>
                            <div class="text-muted small mt-2">جميع حركات النقل المسجلة</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                <i class="fas fa-arrows-alt fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employees with Transfers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">الموظفون المنقولون</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ stats.total_employees_with_transfers }}</div>
                            <div class="text-muted small mt-2">عدد الموظفين الذين تم نقلهم</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Transfers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-info shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">نقل هذا الشهر</div>
                            <div class="h3 mb-0 fw-bold text-gray-800 d-flex align-items-center">
                                {{ stats.transfers_this_month }}
                                {% if stats.transfers_last_month %}
                                    {% if stats.transfers_this_month > stats.transfers_last_month %}
                                    <span class="ms-2 fs-6 trend-positive">
                                        <i class="fas fa-arrow-up me-1"></i>+{{ stats.transfers_this_month|add:stats.transfers_last_month|floatformat:0 }}%
                                    </span>
                                    {% elif stats.transfers_this_month < stats.transfers_last_month %}
                                    <span class="ms-2 fs-6 trend-negative">
                                        <i class="fas fa-arrow-down me-1"></i>-{{ stats.transfers_last_month|add:stats.transfers_this_month|floatformat:0 }}%
                                    </span>
                                    {% endif %}
                                {% endif %}
                            </div>
                            <div class="text-muted small mt-2">
                                {% if stats.transfers_last_month %}
                                    مقارنة بـ {{ stats.transfers_last_month }} الشهر الماضي
                                {% else %}
                                    حركات النقل الشهرية
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                <i class="fas fa-calendar-month fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Yearly Transfers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-start border-4 border-warning shadow h-100 py-2 rounded-3 dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">نقل هذا العام</div>
                            <div class="h3 mb-0 fw-bold text-gray-800">{{ stats.transfers_this_year }}</div>
                            <div class="text-muted small mt-2">إجمالي حركات النقل السنوية</div>
                        </div>
                        <div class="col-auto">
                            <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                                <i class="fas fa-calendar-year fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-clock me-2"></i>
                        النشاط الأخير (آخر 30 يوم)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border-end border-2">
                                <div class="p-3">
                                    <div class="rounded-circle bg-primary bg-opacity-10 p-3 mx-auto mb-3" style="width: fit-content;">
                                        <i class="fas fa-exchange-alt fa-2x text-primary"></i>
                                    </div>
                                    <div class="h3 fw-bold text-primary mb-1">{{ stats.recent_transfers }}</div>
                                    <div class="text-muted small">حركات نقل حديثة</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end border-2">
                                <div class="p-3">
                                    <div class="rounded-circle bg-success bg-opacity-10 p-3 mx-auto mb-3" style="width: fit-content;">
                                        <i class="fas fa-arrow-down fa-2x text-success"></i>
                                    </div>
                                    <div class="h3 fw-bold text-success mb-1">
                                        {% if stats.most_transfers_to %}
                                            {{ stats.most_transfers_to.count }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </div>
                                    <div class="text-muted small mb-2">أكثر قسم استقبالاً</div>
                                    {% if stats.most_transfers_to %}
                                    <div class="badge bg-success rounded-pill">{{ stats.most_transfers_to.new_department }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3">
                                <div class="rounded-circle bg-warning bg-opacity-10 p-3 mx-auto mb-3" style="width: fit-content;">
                                    <i class="fas fa-arrow-up fa-2x text-warning"></i>
                                </div>
                                <div class="h3 fw-bold text-warning mb-1">
                                    {% if stats.most_transfers_from %}
                                        {{ stats.most_transfers_from.count }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </div>
                                <div class="text-muted small mb-2">أكثر قسم نقلاً</div>
                                {% if stats.most_transfers_from %}
                                <div class="badge bg-warning rounded-pill">{{ stats.most_transfers_from.previous_department }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Monthly Trend Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه حركات النقل الشهرية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Employee -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-header bg-gradient-info text-white py-3">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-user-star me-2"></i>
                        الموظف الأكثر نقلاً
                    </h6>
                </div>
                <div class="card-body text-center">
                    {% if stats.employee_most_transfers %}
                    <div class="mb-4">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-4 mx-auto mb-3" style="width: fit-content;">
                            <i class="fas fa-user-circle fa-3x text-primary"></i>
                        </div>
                        <h5 class="fw-bold text-gray-800 mb-2">{{ stats.employee_most_transfers.employee__full_name }}</h5>
                        <p class="text-muted mb-3">
                            <i class="fas fa-id-badge me-1"></i>
                            {{ stats.employee_most_transfers.employee__ministry_number }}
                        </p>
                    </div>
                    <div class="bg-light rounded-3 p-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="rounded-circle bg-warning bg-opacity-20 p-2 me-3">
                                <i class="fas fa-arrows-alt text-warning"></i>
                            </div>
                            <div>
                                <div class="h4 fw-bold text-warning mb-0">{{ stats.employee_most_transfers.count }}</div>
                                <div class="small text-muted">حركة نقل</div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="py-5">
                        <div class="rounded-circle bg-light p-4 mx-auto mb-3" style="width: fit-content;">
                            <i class="fas fa-user-slash fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted mb-0">لا توجد بيانات كافية</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-header bg-gradient-warning text-white py-3">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-building me-2"></i>
                        الأقسام الأكثر نقلاً منها
                    </h6>
                </div>
                <div class="card-body">
                    {% if department_summary.from_departments %}
                    <div class="list-group list-group-flush">
                        {% for dept in department_summary.from_departments|slice:":10" %}
                        <div class="department-item rounded-3 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-warning bg-opacity-10 p-2 me-3">
                                        <i class="fas fa-arrow-up text-warning"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold text-gray-800">{{ dept.previous_department }}</div>
                                        <div class="small text-muted">قسم مصدر</div>
                                    </div>
                                </div>
                                <div>
                                    <span class="badge bg-warning rounded-pill px-3 py-2">
                                        <i class="fas fa-exchange-alt me-1"></i>
                                        {{ dept.count }} نقل
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="rounded-circle bg-light p-4 mx-auto mb-3" style="width: fit-content;">
                            <i class="fas fa-building fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted mb-0">لا توجد بيانات متاحة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-header bg-gradient-success text-white py-3">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-building me-2"></i>
                        الأقسام الأكثر استقبالاً
                    </h6>
                </div>
                <div class="card-body">
                    {% if department_summary.to_departments %}
                    <div class="list-group list-group-flush">
                        {% for dept in department_summary.to_departments|slice:":10" %}
                        <div class="department-item rounded-3 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-success bg-opacity-10 p-2 me-3">
                                        <i class="fas fa-arrow-down text-success"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold text-gray-800">{{ dept.new_department }}</div>
                                        <div class="small text-muted">قسم مستقبل</div>
                                    </div>
                                </div>
                                <div>
                                    <span class="badge bg-success rounded-pill px-3 py-2">
                                        <i class="fas fa-user-plus me-1"></i>
                                        {{ dept.count }} استقبال
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="rounded-circle bg-light p-4 mx-auto mb-3" style="width: fit-content;">
                            <i class="fas fa-building fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted mb-0">لا توجد بيانات متاحة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Footer -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow rounded-3 stats-card">
                <div class="card-body text-center py-4">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="rounded-circle bg-info bg-opacity-10 p-2 me-3">
                            <i class="fas fa-clock text-info"></i>
                        </div>
                        <div>
                            <div class="small text-muted mb-1">آخر تحديث للإحصائيات</div>
                            <div class="fw-bold text-gray-800">{{ stats.statistics_date|default:"الآن" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Monthly Trend Chart
    const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
    
    // Prepare data from Django context
    const monthlyData = [
        {% for trend in trends %}
        {
            month: '{{ trend.month_name }}',
            count: {{ trend.count }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'حركات النقل',
                data: monthlyData.map(item => item.count),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    cornerRadius: 10,
                    callbacks: {
                        label: function(context) {
                            return 'عدد حركات النقل: ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        stepSize: 1
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#007bff'
                }
            }
        }
    });

    // Add animation and interaction effects
    document.addEventListener('DOMContentLoaded', function() {
        // Animate stats cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.stats-card').forEach(card => {
            observer.observe(card);
        });

        // Refresh data button
        document.getElementById('refreshStats').addEventListener('click', function() {
            const btn = this;
            const originalText = btn.innerHTML;
            
            // Show loading state
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';
            btn.disabled = true;
            
            // Simulate refresh (reload page)
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Add hover effects to department items
        document.querySelectorAll('.department-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(10px)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });
    });
</script>
{% endblock %}