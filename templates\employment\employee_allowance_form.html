{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 15px 15px;
    }

    .form-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-section {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: #f8f9fa;
    }

    .form-section h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 10px 12px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .search-section {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .employee-info {
        background: #f1f8e9;
        border: 1px solid #c8e6c9;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        display: none;
    }

    .allowances-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .allowance-item {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
    }

    .allowance-item label {
        display: block;
        margin-bottom: 10px;
        font-weight: 600;
        color: #495057;
    }

    .search-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-money-bill-wave me-3"></i>
                    {{ title }}
                </h1>
                <p class="mb-0 mt-2">إدارة علاوات الموظفين</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'employment:employee_allowance_list' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="form-container">
        <form method="post" id="allowanceForm">
            {% csrf_token %}
            
            <!-- Employee Search Section -->
            {% if not allowance %}
            <div class="search-section">
                <h5><i class="fas fa-search me-2"></i>البحث عن الموظف</h5>
                <div class="row">
                    <div class="col-md-8">
                        <label for="{{ form.ministry_number.id_for_label }}" class="form-label">{{ form.ministry_number.label }}</label>
                        {{ form.ministry_number }}
                    </div>
                    <div class="col-md-4">
                        <label>&nbsp;</label>
                        <div>
                            <button type="button" class="btn search-btn w-100" id="searchEmployeeBtn">
                                <i class="fas fa-search me-2"></i>
                                بحث عن الموظف
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Info Display -->
                <div class="employee-info" id="employeeInfo">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="{{ form.employee_name.id_for_label }}" class="form-label">{{ form.employee_name.label }}</label>
                            {{ form.employee_name }}
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Display employee info for editing -->
            <div class="form-section">
                <h5><i class="fas fa-user me-2"></i>معلومات الموظف</h5>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">الرقم الوزاري</label>
                        <input type="text" class="form-control" value="{{ allowance.ministry_number }}" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">اسم الموظف</label>
                        <input type="text" class="form-control" value="{{ allowance.employee.full_name }}" readonly>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Hidden employee field -->
            {{ form.employee }}

            <!-- Allowances Section -->
            <div class="form-section">
                <h5><i class="fas fa-money-bill-wave me-2"></i>العلاوات</h5>
                <div class="allowances-grid">
                    <div class="allowance-item">
                        <label for="{{ form.education_allowance.id_for_label }}">{{ form.education_allowance.label }}</label>
                        {{ form.education_allowance }}
                    </div>
                    <div class="allowance-item">
                        <label for="{{ form.adjustment_allowance.id_for_label }}">{{ form.adjustment_allowance.label }}</label>
                        {{ form.adjustment_allowance }}
                    </div>
                    <div class="allowance-item">
                        <label for="{{ form.transportation_allowance.id_for_label }}">{{ form.transportation_allowance.label }}</label>
                        {{ form.transportation_allowance }}
                    </div>
                    <div class="allowance-item">
                        <label for="{{ form.supervisory_allowance.id_for_label }}">{{ form.supervisory_allowance.label }}</label>
                        {{ form.supervisory_allowance }}
                    </div>
                    <div class="allowance-item">
                        <label for="{{ form.technical_allowance.id_for_label }}">{{ form.technical_allowance.label }}</label>
                        {{ form.technical_allowance }}
                    </div>
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>
                    {% if allowance %}تحديث العلاوات{% else %}حفظ العلاوات{% endif %}
                </button>
                <a href="{% url 'employment:employee_allowance_list' %}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('searchEmployeeBtn');
    const ministryNumberInput = document.getElementById('{{ form.ministry_number.id_for_label }}');
    const employeeNameInput = document.getElementById('{{ form.employee_name.id_for_label }}');
    const employeeHiddenInput = document.getElementById('{{ form.employee.id_for_label }}');
    const employeeInfo = document.getElementById('employeeInfo');

    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const ministryNumber = ministryNumberInput.value.trim();
            
            if (!ministryNumber) {
                alert('يرجى إدخال الرقم الوزاري');
                return;
            }

            // Show loading
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...';
            searchBtn.disabled = true;

            // Make AJAX request to find employee
            fetch(`/employment/get-employee/?ministry_number=${encodeURIComponent(ministryNumber)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Fill employee data
                        employeeNameInput.value = data.employee.full_name;
                        employeeHiddenInput.value = data.employee.id;
                        employeeInfo.style.display = 'block';
                    } else {
                        alert(data.error || 'لم يتم العثور على الموظف');
                        employeeInfo.style.display = 'none';
                        employeeNameInput.value = '';
                        employeeHiddenInput.value = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث');
                })
                .finally(() => {
                    // Reset button
                    searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>بحث عن الموظف';
                    searchBtn.disabled = false;
                });
        });
    }
});
</script>
{% endblock %}
