from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Gets an item from a dictionary using the key.
    """
    if dictionary is None:
        return {}
    return dictionary.get(key, {})

@register.filter
def multiply(value, arg):
    """
    Multiplies the value by the argument.
    """
    return value * arg

@register.filter
def add(value, arg):
    """
    Adds the argument to the value.
    """
    return value + arg
