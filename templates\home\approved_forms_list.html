{% extends 'home/base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card shadow main-card">
                <div class="card-header text-white">
                    <h3 class="mb-0"><i class="fas fa-file-alt me-2"></i> {{ title }}</h3>
                </div>
                <div class="card-body">
                    <p class="lead">فيما يلي مجموعة من النماذج المعتمدة التي يمكنك تحميلها واستخدامها:</p>

                    <div class="row mt-4">
                        {% for form in forms %}
                        <div class="col-md-4 mb-4 animate-card" style="animation-delay: {{ forloop.counter0|add:1|divisibleby:3 }}0ms;">
                            <div class="card h-100 hover-shadow">
                                <div class="card-body text-center d-flex flex-column justify-content-between">
                                    <div class="logo-container mb-3">
                                        <div class="file-icon-wrapper">
                                            {% if form.file_type == 'pdf' %}
                                                <i class="fas fa-file-pdf fa-4x file-pdf"></i>
                                            {% elif form.file_type == 'doc' or form.file_type == 'docx' %}
                                                <i class="fas fa-file-word fa-4x file-word"></i>
                                            {% elif form.file_type == 'xls' or form.file_type == 'xlsx' %}
                                                <i class="fas fa-file-excel fa-4x file-excel"></i>
                                            {% elif form.file_type == 'ppt' or form.file_type == 'pptx' %}
                                                <i class="fas fa-file-powerpoint fa-4x file-powerpoint"></i>
                                            {% elif form.file_type == 'zip' or form.file_type == 'rar' %}
                                                <i class="fas fa-file-archive fa-4x file-archive"></i>
                                            {% elif form.file_type == 'txt' %}
                                                <i class="fas fa-file-alt fa-4x file-text"></i>
                                            {% else %}
                                                <i class="fas fa-file fa-4x file-default"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <h4 class="card-title">{{ form.title }}</h4>
                                    <p class="card-text text-muted small">
                                        {{ form.description|default:"" }}
                                    </p>
                                    <p class="card-text"><small class="text-muted">{{ form.file_type|upper }} - {{ form.file.size|filesizeformat }}</small></p>
                                    <div class="mt-3">
                                        <a href="{% url 'home:approved_form_download' form.id %}" class="btn btn-primary btn-download">
                                            <i class="fas fa-download me-1"></i> تحميل الملف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد نماذج متاحة حالياً.
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        ملاحظة: هذه النماذج معتمدة للاستخدام الرسمي. يرجى استخدامها وفقاً للتعليمات والإجراءات المعمول بها.
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Page Background */
    body {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="rgba(0,0,0,0.02)"/></svg>');
        background-attachment: fixed;
    }

    /* Enhanced Card Styles */
    .hover-shadow {
        transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        z-index: 1;
        background-color: white;
        overflow: hidden;
    }

    .hover-shadow::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
        background-size: 200% 200%;
        animation: gradientAnimation 5s ease infinite;
        z-index: -1;
        border-radius: var(--border-radius-lg);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .hover-shadow:hover::before {
        opacity: 1;
    }

    .hover-shadow::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: calc(var(--border-radius-lg) - 2px);
        z-index: -1;
    }

    .hover-shadow:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    @keyframes gradientAnimation {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Enhanced Logo Container */
    .logo-container {
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        padding: 10px;
        position: relative;
    }

    /* Enhanced File Icon Wrapper */
    .file-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
        transition: all 0.5s ease;
        width: 100%;
        height: 100%;
        position: relative;
    }

    .file-icon-wrapper::before {
        content: '';
        position: absolute;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
        border-radius: 50%;
        opacity: 0;
        transition: all 0.5s ease;
        z-index: -1;
    }

    .hover-shadow:hover .file-icon-wrapper {
        transform: scale(1.1) rotate(5deg);
    }

    .hover-shadow:hover .file-icon-wrapper::before {
        opacity: 1;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.2); opacity: 0.8; }
        100% { transform: scale(1); opacity: 0.5; }
    }

    /* Enhanced File Icons with 3D Effect */
    .file-pdf, .file-word, .file-excel, .file-powerpoint, .file-archive, .file-text, .file-default {
        filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
        transition: all 0.5s ease;
    }

    .hover-shadow:hover .file-pdf,
    .hover-shadow:hover .file-word,
    .hover-shadow:hover .file-excel,
    .hover-shadow:hover .file-powerpoint,
    .hover-shadow:hover .file-archive,
    .hover-shadow:hover .file-text,
    .hover-shadow:hover .file-default {
        filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.3));
    }

    /* Enhanced File Type Colors */
    .file-pdf {
        color: #dc3545;
        background: linear-gradient(135deg, #dc3545, #a71d2a);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-word {
        color: #0d6efd;
        background: linear-gradient(135deg, #0d6efd, #0a58ca);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-excel {
        color: #198754;
        background: linear-gradient(135deg, #198754, #0f5132);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-powerpoint {
        color: #fd7e14;
        background: linear-gradient(135deg, #fd7e14, #c96a0d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-archive {
        color: #6c757d;
        background: linear-gradient(135deg, #6c757d, #495057);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-text {
        color: #6c757d;
        background: linear-gradient(135deg, #6c757d, #495057);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .file-default {
        color: #6c757d;
        background: linear-gradient(135deg, #6c757d, #495057);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /* Enhanced Card Title */
    .card-title {
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .hover-shadow:hover .card-title {
        color: var(--secondary-color);
    }

    /* Enhanced Download Button */
    .btn-download {
        position: relative;
        border: none;
        transition: all 0.4s ease;
        overflow: hidden;
        z-index: 1;
        padding: 0.6rem 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-download::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
        z-index: -1;
    }

    .btn-download:hover::before {
        transform: translateX(100%);
    }

    .btn-download::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
        background-size: 200% 200%;
        z-index: -2;
        animation: gradientAnimation 3s ease infinite;
        border-radius: 6px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-download:hover::after {
        opacity: 1;
    }

    .btn-download:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        color: white;
    }

    .btn-download i {
        transition: all 0.3s ease;
    }

    .btn-download:hover i {
        transform: translateY(-3px);
    }

    /* Animation for cards */
    .animate-card {
        animation: fadeInUp 0.8s ease-out both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced Alert Styles */
    .alert-warning, .alert-info {
        border: none;
        border-radius: var(--border-radius-lg);
        position: relative;
        overflow: hidden;
    }

    .alert-warning::before, .alert-info::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
    }

    .alert-warning::before {
        background: linear-gradient(to bottom, var(--warning-color), var(--warning-dark));
    }

    .alert-info::before {
        background: linear-gradient(to bottom, var(--info-color), var(--info-dark));
    }

    /* Main card enhancement */
    .main-card {
        border: none;
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .main-card .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border-bottom: none;
        padding: 1.5rem;
    }

    .main-card .card-header h3 {
        font-weight: 700;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .main-card .card-body {
        padding: 2rem;
    }
</style>
{% endblock %}
