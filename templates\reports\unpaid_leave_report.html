{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير الإجازات بدون راتب - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الإجازات بدون راتب</h1>
        <div>
            <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتقارير
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
        </div>
        <div class="card-body">
            <form method="get" class="mb-4">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label for="department" class="form-label">القسم</label>
                        <select class="form-control" id="department" name="department">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department_id == dept.id|stringformat:"i" %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-search"></i> عرض التقرير
                            </button>
                            {% if leaves %}
                            <button type="submit" name="export" value="excel" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="submit" name="export" value="pdf" class="btn btn-danger">
                                <i class="fas fa-file-pdf"></i> معاينة PDF
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </form>

            {% if leaves %}

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الرقم الوزاري</th>
                            <th>اسم الموظف</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leaves %}
                        <tr>
                            <td>{{ leave.employee.ministry_number }}</td>
                            <td>
                                <a href="{% url 'employees:employee_detail' leave.employee.pk %}">
                                    {{ leave.employee.full_name }}
                                </a>
                            </td>
                            <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                            <td>{{ leave.days_count }}</td>
                            <td>
                                {% if leave.status == 'approved' %}
                                <span class="badge bg-success">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'pending' %}
                                <span class="badge bg-warning text-dark">{{ leave.get_status_display }}</span>
                                {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">{{ leave.get_status_display }}</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ leave.get_status_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'leaves:unpaid_leave_detail' leave.pk %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> معاينة
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد إجازات بدون راتب تطابق معايير البحث.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
