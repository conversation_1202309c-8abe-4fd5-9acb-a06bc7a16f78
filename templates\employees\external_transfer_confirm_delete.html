{% extends 'base.html' %}
{% load static %}

{% block title %}إلغاء النقل الخارجي - {{ external_transfer.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .confirm-header {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    
    .warning-card {
        border-radius: 15px;
        border: 3px solid #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
    }
    
    .employee-info {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        border-left: 5px solid #ffc107;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    
    .info-value {
        color: #212529;
        font-weight: 500;
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        margin: 10px;
    }
    
    .btn-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #ffc107;
        margin-bottom: 20px;
    }
    
    .danger-zone {
        background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
        border: 2px solid #e91e63;
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Confirmation Header -->
    <div class="confirm-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-exclamation-triangle fa-4x"></i>
            </div>
            <div class="col-md-8">
                <h1 class="mb-2">تأكيد إلغاء النقل الخارجي</h1>
                <h4 class="mb-0">{{ external_transfer.employee.full_name }}</h4>
            </div>
            <div class="col-md-2">
                <span class="badge bg-light text-dark fs-5">
                    {{ external_transfer.employee.ministry_number }}
                </span>
            </div>
        </div>
    </div>

    <!-- Warning Message -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card warning-card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h3 class="text-warning mb-3">تحذير مهم!</h3>
                    <p class="lead text-dark mb-4">
                        أنت على وشك إلغاء النقل الخارجي لهذا الموظف وإعادته للخدمة الفعلية.
                        <br>
                        هذا الإجراء سيؤثر على النظام بالطرق التالية:
                    </p>
                    
                    <div class="row text-start">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success"></i>
                                    سيعود الموظف لقائمة الموظفين النشطين
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success"></i>
                                    ستتمكن من تعديل بياناته مرة أخرى
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-times-circle text-danger"></i>
                                    ستحذف بيانات النقل الخارجي نهائياً
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times-circle text-danger"></i>
                                    لا يمكن التراجع عن هذا الإجراء
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Information -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-8">
            <div class="employee-info">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-user"></i> بيانات الموظف المراد إلغاء نقله
                </h5>
                
                <div class="info-row">
                    <span class="info-label">الاسم الكامل:</span>
                    <span class="info-value">{{ external_transfer.employee.full_name }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الرقم الوزاري:</span>
                    <span class="info-value">
                        <span class="badge bg-secondary">{{ external_transfer.employee.ministry_number }}</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">التخصص:</span>
                    <span class="info-value">{{ external_transfer.employee.specialization }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">القسم السابق:</span>
                    <span class="info-value">{{ external_transfer.employee.school }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المديرية المنقول إليها:</span>
                    <span class="info-value">
                        <span class="badge bg-warning text-dark">{{ external_transfer.destination_directorate }}</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ النقل:</span>
                    <span class="info-value">
                        <span class="badge bg-info">{{ external_transfer.transfer_date }}</span>
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">سبب النقل:</span>
                    <span class="info-value">{{ external_transfer.transfer_reason }}</span>
                </div>
                
                {% if external_transfer.notes %}
                <div class="info-row">
                    <span class="info-label">ملاحظات:</span>
                    <span class="info-value">{{ external_transfer.notes }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="danger-zone">
                <h5 class="text-danger mb-3 text-center">
                    <i class="fas fa-skull-crossbones"></i> منطقة الخطر
                </h5>
                <p class="text-center mb-4">
                    بالضغط على "تأكيد الإلغاء" ستقوم بحذف بيانات النقل الخارجي نهائياً وإعادة الموظف للخدمة الفعلية.
                </p>
                
                <form method="post" class="text-center">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-center">
                        <a href="{% url 'employees:external_transfer_detail' external_transfer.pk %}" 
                           class="btn btn-outline-secondary btn-action">
                            <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                        </a>
                        
                        <button type="submit" class="btn btn-danger btn-action" 
                                onclick="return confirm('هل أنت متأكد 100% من إلغاء نقل هذا الموظف؟\n\nهذا الإجراء لا يمكن التراجع عنه!')">
                            <i class="fas fa-undo"></i> تأكيد إلغاء النقل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{% url 'employees:external_transfers_list' %}" class="btn btn-outline-primary btn-action">
                <i class="fas fa-list"></i> العودة لقائمة المنقولين خارجي
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add extra confirmation
        $('form').on('submit', function(e) {
            const employeeName = '{{ external_transfer.employee.full_name }}';
            const ministryNumber = '{{ external_transfer.employee.ministry_number }}';
            const destination = '{{ external_transfer.destination_directorate }}';
            
            const confirmed = confirm(
                `تأكيد أخير!\n\n` +
                `سيتم إلغاء النقل الخارجي للموظف:\n` +
                `الاسم: ${employeeName}\n` +
                `الرقم الوزاري: ${ministryNumber}\n` +
                `من: ${destination}\n\n` +
                `هل أنت متأكد من المتابعة؟`
            );
            
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        });
    });
</script>
{% endblock %}