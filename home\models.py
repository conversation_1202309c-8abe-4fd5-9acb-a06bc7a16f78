from django.db import models
from django.utils.translation import gettext_lazy as _
import os
from django.utils import timezone
from django.urls import reverse

class SystemSettings(models.Model):
    """Model for system settings"""
    internal_transfer_enabled = models.BooleanField(_('تفعيل صفحة النقل الداخلي'), default=True)
    internal_transfer_message = models.TextField(
        _('رسالة صفحة النقل الداخلي عند التعطيل'),
        default='لقد انتهت فترة تقديم طلبات النقل الداخلي'
    )

    class Meta:
        verbose_name = _('إعدادات النظام')
        verbose_name_plural = _('إعدادات النظام')

    @classmethod
    def get_settings(cls):
        """Get or create system settings"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings

class InternalTransfer(models.Model):
    """Model for internal transfer requests"""
    GENDER_CHOICES = [
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    ]

    # Employee information
    ministry_number = models.CharField(_('الرقم الوزاري'), max_length=20)
    employee_name = models.CharField(_('اسم الموظف'), max_length=100)
    employee_id = models.CharField(_('الرقم الوطني'), max_length=20)
    gender = models.CharField(_('الجنس'), max_length=10, choices=GENDER_CHOICES, default='male')

    # Employment information
    current_department = models.CharField(_('القسم الحالي'), max_length=100)
    hire_date = models.DateField(_('تاريخ التعيين'), null=True, blank=True)
    actual_service = models.CharField(_('الخدمة الفعلية'), max_length=50, blank=True, null=True)
    last_position = models.CharField(_('آخر مسمى وظيفي'), max_length=100, blank=True, null=True)
    qualification = models.CharField(_('المؤهل العلمي'), max_length=100, blank=True, null=True)
    specialization = models.CharField(_('التخصص'), max_length=100, blank=True, null=True)
    last_rank = models.CharField(_('آخر رتبة'), max_length=100, blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)

    # Transfer request information
    first_choice = models.CharField(_('الخيار الأول'), max_length=100)
    second_choice = models.CharField(_('الخيار الثاني'), max_length=100, blank=True, null=True)
    third_choice = models.CharField(_('الخيار الثالث'), max_length=100, blank=True, null=True)
    reason = models.TextField(_('سبب النقل'))

    # Contact information
    phone_number = models.CharField(_('رقم الهاتف'), max_length=20)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)

    # Request status
    created_at = models.DateTimeField(_('تاريخ الطلب'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)
    status = models.CharField(_('حالة الطلب'), max_length=20, default='pending', choices=[
        ('pending', _('قيد الانتظار')),
        ('approved', _('تمت الموافقة')),
        ('rejected', _('مرفوض')),
    ])
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)



    # Token for editing the request
    edit_token = models.CharField(_('رمز التعديل'), max_length=50, blank=True, null=True)

    class Meta:
        verbose_name = _('طلب نقل داخلي')
        verbose_name_plural = _('طلبات النقل الداخلي')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee_name} - {self.current_department} إلى {self.first_choice}"


class EmployeeTransfer(models.Model):
    """Model for managing employee transfers with detailed information"""
    TRANSFER_TYPE_CHOICES = [
        ('internal_transfer', _('النقل الداخلي')),
        ('temporary_assignment', _('التكليف المؤقت')),
    ]

    ENDORSEMENT_CHOICES = [
        ('admin_financial_manager', _('تنسيب مدير الشؤون الادارية والمالية')),
        ('hr_committee', _('تنسيب لجنة الموارد البشرية')),
    ]

    # Employee information
    ministry_number = models.CharField(_('الرقم الوزاري'), max_length=20)
    employee_name = models.CharField(_('اسم الموظف'), max_length=100)
    specialization = models.CharField(_('التخصص'), max_length=100, blank=True, null=True)
    actual_service = models.CharField(_('الخدمة الفعلية'), max_length=50, blank=True, null=True)
    current_department = models.CharField(_('القسم الحالي'), max_length=100)

    # Transfer details
    new_department = models.ForeignKey(
        'employment.Department',
        on_delete=models.CASCADE,
        verbose_name=_('القسم الجديد'),
        related_name='incoming_transfers'
    )
    transfer_type = models.CharField(
        _('صفة النقل'),
        max_length=30,
        choices=TRANSFER_TYPE_CHOICES,
        default='internal_transfer'
    )
    endorsement = models.CharField(
        _('التنسيب'),
        max_length=50,
        choices=ENDORSEMENT_CHOICES,
        default='admin_financial_manager'
    )
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نقل موظف')
        verbose_name_plural = _('نقل الموظفين')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee_name} - {self.current_department} إلى {self.new_department.name}"


def get_file_upload_path(instance, filename):
    """Generate file path for uploaded files"""
    # Get the current date
    now = timezone.now()
    # Format the date as YYYY/MM/DD
    date_path = now.strftime('%Y/%m/%d')
    # Return the full path
    return os.path.join('approved_forms', date_path, filename)


class ApprovedForm(models.Model):
    """Model for storing approved forms that can be downloaded by employees"""
    title = models.CharField(_('عنوان النموذج'), max_length=100)
    description = models.TextField(_('وصف النموذج'), blank=True, null=True)
    file = models.FileField(_('الملف'), upload_to=get_file_upload_path)
    file_type = models.CharField(_('نوع الملف'), max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإضافة'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    order = models.PositiveIntegerField(_('الترتيب'), default=0)

    class Meta:
        verbose_name = _('نموذج معتمد')
        verbose_name_plural = _('النماذج المعتمدة')
        ordering = ['order', '-created_at']

    def __str__(self):
        return self.title

    def filename(self):
        """Return the filename of the uploaded file"""
        return os.path.basename(self.file.name)

    def file_extension(self):
        """Return the file extension"""
        name, extension = os.path.splitext(self.file.name)
        return extension.lower()[1:] if extension else ""

    def save(self, *args, **kwargs):
        """Override save method to set file_type automatically"""
        # Set file_type based on file extension if not provided
        if not self.file_type and self.file:
            self.file_type = self.file_extension()
        super().save(*args, **kwargs)


class ImportantLink(models.Model):
    """Model for storing important links"""
    name = models.CharField(_('اسم الرابط'), max_length=100)
    url = models.URLField(_('الرابط'))
    description = models.TextField(_('الوصف'), blank=True, null=True)
    favicon_url = models.URLField(_('رابط الأيقونة'), blank=True, null=True)
    image = models.ImageField(_('صورة الرابط'), upload_to='important_links/', blank=True, null=True,
                             help_text=_('صورة اختيارية للرابط (يفضل 64x64 بكسل)'))
    order = models.PositiveIntegerField(_('الترتيب'), default=0)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('رابط مهم')
        verbose_name_plural = _('الروابط المهمة')
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def get_icon_url(self):
        """Get the icon URL - prioritize uploaded image over favicon_url"""
        if self.image:
            return self.image.url
        elif self.favicon_url:
            return self.favicon_url
        return None

    def has_icon(self):
        """Check if the link has an icon (either uploaded image or favicon_url)"""
        return bool(self.image or self.favicon_url)


class TechnicalTransfer(models.Model):
    """Model for technical transfers"""
    # Employee information
    ministry_number = models.CharField(_('الرقم الوزاري'), max_length=20)
    employee_name = models.CharField(_('اسم الموظف'), max_length=100)
    current_department = models.CharField(_('القسم الحالي'), max_length=100)

    # Transfer information
    new_department = models.CharField(_('مركز العمل الجديد'), max_length=100)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نقل فني')
        verbose_name_plural = _('النقل الفني')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee_name} - {self.current_department} إلى {self.new_department}"

    def get_absolute_url(self):
        return reverse('home:technical_transfer_print', kwargs={'pk': self.pk})
