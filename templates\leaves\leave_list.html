{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الإجازات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة الإجازات</h2>
    <div class="d-flex">
        <a href="{% url 'leaves:leave_create' %}" class="btn btn-primary me-2">
            <i class="fas fa-plus"></i> إضافة إجازة جديدة
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-success btn-lg px-4 py-2" style="font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.2); color: white;" data-bs-toggle="dropdown" aria-expanded="false">
                <span style="font-size: 20px; display: inline-block; margin-right: 10px;">خيارات</span>
                <i class="fas fa-caret-down"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item" href="{% url 'leaves:leave_type_list' %}">
                        <i class="fas fa-list-ul me-2 text-primary"></i> أنواع الإجازات
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{% url 'leaves:leave_balance_list' %}">
                        <i class="fas fa-balance-scale me-2 text-success"></i> أرصدة الإجازات
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{% url 'leaves:departure_list' %}">
                        <i class="fas fa-sign-out-alt me-2 text-warning"></i> المغادرات
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="{% url 'leaves:leave_reports' %}">
                        <i class="fas fa-chart-bar me-2 text-info"></i> تقارير الإجازات
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{% url 'leaves:unpaid_leave_list' %}">
                        <i class="fas fa-money-bill-wave me-2 text-danger"></i> الإجازات بدون راتب
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع الإجازات</h6>
        <form class="d-flex" method="get">
            <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
            <button class="btn btn-outline-primary" type="submit">بحث</button>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> الموظف</th>
                        <th><i class="fas fa-calendar-check me-1"></i> نوع الإجازة</th>
                        <th><i class="fas fa-calendar-plus me-1"></i> من تاريخ</th>
                        <th><i class="fas fa-calendar-minus me-1"></i> إلى تاريخ</th>
                        <th><i class="fas fa-calculator me-1"></i> عدد الأيام</th>
                        <th><i class="fas fa-flag me-1"></i> الحالة</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in leaves %}
                    <tr class="text-center">
                        <td>
                            <strong>{{ leave.employee.ministry_number }}</strong>
                        </td>
                        <td>
                            <a href="{% url 'employees:employee_detail' leave.employee.pk %}">
                                {{ leave.employee.full_name }}
                            </a>
                        </td>
                        <td>{{ leave.leave_type.get_name_display }}</td>
                        <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                        <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                        <td>{{ leave.days_count }}</td>
                        <td>
                            {% if leave.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status == 'approved' %}
                                <span class="badge bg-success">موافق عليها</span>
                            {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوضة</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'leaves:leave_detail' leave.pk %}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a href="{% url 'leaves:leave_update' leave.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'leaves:leave_delete' leave.pk %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                            <button class="btn btn-success btn-sm send-whatsapp" data-employee-id="{{ leave.employee.id }}" title="إرسال رصيد الإجازات عبر واتس اب">
                                <i class="fab fa-whatsapp"></i> واتس اب
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا يوجد إجازات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle WhatsApp button click
        const whatsappButtons = document.querySelectorAll('.send-whatsapp');
        whatsappButtons.forEach(button => {
            button.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-employee-id');

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                this.disabled = true;

                // Make AJAX request to get WhatsApp URL
                fetch(`/leaves/get-leave-balance-whatsapp/${employeeId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Reset button state
                        this.innerHTML = '<i class="fab fa-whatsapp"></i> واتس اب';
                        this.disabled = false;

                        if (data.success) {
                            // Encode the message for URL
                            const encodedMessage = encodeURIComponent(data.message);
                            const whatsappUrl = `https://wa.me/${data.phone}?text=${encodedMessage}`;

                            // Open WhatsApp in a new tab
                            window.open(whatsappUrl, '_blank');
                        } else {
                            // Show error message
                            alert(data.error || 'حدث خطأ أثناء إنشاء رابط واتس اب');
                        }
                    })
                    .catch(error => {
                        // Reset button state and show error
                        this.innerHTML = '<i class="fab fa-whatsapp"></i> واتس اب';
                        this.disabled = false;
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                        console.error('Error:', error);
                    });
            });
        });
    });
</script>
{% endblock %}