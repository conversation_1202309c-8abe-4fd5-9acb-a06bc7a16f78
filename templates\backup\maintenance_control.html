{% extends 'base.html' %}

{% block title %}إدارة وضع الصيانة{% endblock %}

{% block extra_css %}
<style>
    .maintenance-card {
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .maintenance-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    }

    .status-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 10px;
        animation: pulse 2s infinite;
    }

    .status-active {
        background-color: #dc3545;
    }

    .status-inactive {
        background-color: #28a745;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .maintenance-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .btn-maintenance {
        border-radius: 25px;
        padding: 10px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-maintenance:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .form-control-custom {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control-custom:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Ensure modal works properly */
    .modal {
        z-index: 1050;
    }
    
    .modal-backdrop {
        z-index: 1040;
    }
    
    /* Debug styles */
    #enableMaintenanceBtn {
        position: relative;
    }
    
    #enableMaintenanceBtn:hover {
        transform: scale(1.05);
    }
    
    /* Fallback modal styles */
    .modal.show {
        display: block !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tools"></i> إدارة وضع الصيانة
        </h1>
        <a href="{% url 'backup:backup_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للنسخ الاحتياطية
        </a>
    </div>

    <!-- Current Status Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card maintenance-card mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> حالة النظام الحالية
                    </h6>
                    <div class="dropdown no-arrow">
                        <button class="btn btn-link btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if is_maintenance %}
                        <div class="maintenance-info">
                            <h5>
                                <i class="fas fa-exclamation-triangle"></i>
                                النظام في وضع الصيانة
                                <span class="status-indicator status-active"></span>
                            </h5>
                            {% if maintenance_info %}
                                <div class="mt-3">
                                    <p><strong>نوع العملية:</strong> {{ maintenance_info.operation_type }}</p>
                                    {% if maintenance_info.start_time %}
                                        <p><strong>بدء العملية:</strong> {{ maintenance_info.start_time }}</p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i>
                            <strong>تنبيه:</strong> النظام حالياً غير متاح للمستخدمين العاديين. 
                            فقط المديرون يمكنهم الوصول لصفحات النسخ الاحتياطي.
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <h5>
                                <i class="fas fa-check-circle"></i>
                                النظام يعمل بشكل طبيعي
                                <span class="status-indicator status-inactive"></span>
                            </h5>
                            <p class="mb-0">جميع المستخدمين يمكنهم الوصول للنظام بشكل طبيعي.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card maintenance-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs"></i> إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body text-center">
                    {% if is_maintenance %}
                        <form method="post" style="display: inline;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="disable">
                            <button type="submit" class="btn btn-success btn-maintenance">
                                <i class="fas fa-play"></i> تفعيل النظام
                            </button>
                        </form>
                    {% else %}
                        <button type="button" class="btn btn-warning btn-maintenance" data-bs-toggle="modal" data-bs-target="#enableMaintenanceModal" id="enableMaintenanceBtn">
                            <i class="fas fa-pause"></i> تفعيل وضع الصيانة
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Manual Control Card -->
    <div class="row">
        <div class="col-12">
            <div class="card maintenance-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-hand-paper"></i> التحكم اليدوي في وضع الصيانة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-info"></i> متى تستخدم وضع الصيانة؟</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> عند إنشاء نسخة احتياطية</li>
                                <li><i class="fas fa-check text-success"></i> عند استعادة نسخة احتياطية</li>
                                <li><i class="fas fa-check text-success"></i> عند تحديث النظام</li>
                                <li><i class="fas fa-check text-success"></i> عند إجراء صيانة دورية</li>
                                <li><i class="fas fa-check text-success"></i> عند حل مشاكل تقنية</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-exclamation-triangle text-warning"></i> تنبيهات مهمة</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation text-warning"></i> سيتم منع وصول جميع المستخدمين</li>
                                <li><i class="fas fa-exclamation text-warning"></i> فقط المديرون يمكنهم الوصول</li>
                                <li><i class="fas fa-exclamation text-warning"></i> تأكد من إنهاء العملية بسرعة</li>
                                <li><i class="fas fa-exclamation text-warning"></i> أعلم المستخدمين مسبقاً</li>
                                <li><i class="fas fa-exclamation text-warning"></i> لا تنس إلغاء وضع الصيانة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enable Maintenance Modal -->
<div class="modal fade" id="enableMaintenanceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tools"></i> تفعيل وضع الصيانة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" name="action" value="enable">
                    
                    <div class="form-group">
                        <label for="operation_type">نوع العملية:</label>
                        <select name="operation_type" id="operation_type" class="form-control form-control-custom">
                            <option value="صيانة يدوية">صيانة يدوية</option>
                            <option value="تحديث النظام">تحديث النظام</option>
                            <option value="إصلاح مشكلة تقنية">إصلاح مشكلة تقنية</option>
                            <option value="صيانة دورية">صيانة دورية</option>
                            <option value="تحديث قاعدة البيانات">تحديث قاعدة البيانات</option>
                            <option value="صيانة الخادم">صيانة الخادم</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> سيتم منع وصول جميع المستخدمين للنظام فوراً.
                        تأكد من إنهاء العملية بأسرع وقت ممكن.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-tools"></i> تفعيل وضع الصيانة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Auto refresh every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
    
    // Show confirmation for maintenance actions
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const actionInput = form.querySelector('input[name="action"]');
                if (actionInput && actionInput.value === 'disable') {
                    if (!confirm('هل أنت متأكد من إلغاء وضع الصيانة؟')) {
                        e.preventDefault();
                    }
                }
            });
        });
        
        // Debug: Check if modal button exists and add click listener
        const modalButton = document.getElementById('enableMaintenanceBtn');
        if (modalButton) {
            console.log('✅ Modal button found');
            modalButton.addEventListener('click', function() {
                console.log('🔧 Modal button clicked - opening modal');
            });
        } else {
            console.log('❌ Modal button not found');
        }
        
        // Alternative method to open modal if Bootstrap doesn't work
        const alternativeButton = document.querySelector('[data-bs-target="#enableMaintenanceModal"]');
        if (alternativeButton) {
            alternativeButton.addEventListener('click', function(e) {
                console.log('🔄 Alternative click handler triggered');
                // Fallback: manually show modal
                const modal = document.getElementById('enableMaintenanceModal');
                if (modal) {
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');
                    
                    // Create backdrop
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'modal-backdrop';
                    document.body.appendChild(backdrop);
                    
                    console.log('📱 Modal opened manually');
                }
            });
        }
        
        // Close modal handlers
        const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(function(btn) {
            btn.addEventListener('click', function() {
                console.log('❌ Closing modal');
                const modal = document.getElementById('enableMaintenanceModal');
                const backdrop = document.getElementById('modal-backdrop');
                
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');
                }
                
                if (backdrop) {
                    backdrop.remove();
                }
            });
        });
    });
</script>
{% endblock %}