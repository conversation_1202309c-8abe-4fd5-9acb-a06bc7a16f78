@echo off
echo ===================================
echo تشغيل نظام الموارد البشرية باستخدام py
echo ===================================
echo.

REM محاولة تشغيل النظام باستخدام py بدلاً من python
echo محاولة تشغيل النظام باستخدام py...

py -3 manage.py runserver

if %ERRORLEVEL% NEQ 0 (
    echo فشل تشغيل النظام باستخدام py.
    echo يرجى تثبيت بايثون أولاً أو استخدام ملف آخر مثل run_with_full_path.bat
)

echo.
echo انتهى التنفيذ. اضغط أي مفتاح للخروج...
pause > nul
