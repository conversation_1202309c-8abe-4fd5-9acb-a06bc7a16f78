{% extends 'base.html' %}
{% load static %}

{% block title %}الملف الشخصي - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الملف الشخصي</h2>
    <div>
        <a href="{% url 'accounts:instructions' %}" class="btn btn-info me-2">
            <i class="fas fa-book"></i> الدليل الإرشادي
        </a>
        <a href="{% url 'accounts:change_password' %}" class="btn btn-primary">
            <i class="fas fa-key"></i> تغيير كلمة المرور
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">معلومات الحساب</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">اسم المستخدم</th>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الاسم الأول</th>
                        <td>{{ user.first_name|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الاسم الأخير</th>
                        <td>{{ user.last_name|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">البريد الإلكتروني</th>
                        <td>{{ user.email|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">رقم الهاتف</th>
                        <td>{{ user.phone_number|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">العنوان</th>
                        <td>{{ user.address|default:"-" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الصلاحيات</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">نوع الحساب</th>
                        <td>
                            {% if user.is_superuser %}
                                مدير النظام
                            {% elif user.is_staff %}
                                مشرف
                            {% elif user.is_admin %}
                                مدير
                            {% else %}
                                مستخدم عادي
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الانضمام</th>
                        <td>{{ user.date_joined|date:"Y-m-d" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">آخر تسجيل دخول</th>
                        <td>{{ user.last_login|date:"Y-m-d H:i"|default:"-" }}</td>
                    </tr>
                </table>

                {% if user.custom_permissions.exists %}
                <h6 class="mt-4 mb-2">الصلاحيات المخصصة</h6>
                <table class="table table-bordered table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>الوحدة</th>
                            <th>عرض</th>
                            <th>إضافة</th>
                            <th>تعديل</th>
                            <th>حذف</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for perm in user.custom_permissions.all %}
                        <tr>
                            <td>{{ perm.module_name }}</td>
                            <td class="text-center">{% if perm.can_view %}<i class="fas fa-check text-success"></i>{% else %}<i class="fas fa-times text-danger"></i>{% endif %}</td>
                            <td class="text-center">{% if perm.can_add %}<i class="fas fa-check text-success"></i>{% else %}<i class="fas fa-times text-danger"></i>{% endif %}</td>
                            <td class="text-center">{% if perm.can_edit %}<i class="fas fa-check text-success"></i>{% else %}<i class="fas fa-times text-danger"></i>{% endif %}</td>
                            <td class="text-center">{% if perm.can_delete %}<i class="fas fa-check text-success"></i>{% else %}<i class="fas fa-times text-danger"></i>{% endif %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
