{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل بيانات المتقاعد - {{ retired_employee.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
    }
    
    .form-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 5px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .employee-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid #bbdefb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Form Header -->
    <div class="form-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-edit fa-3x"></i>
            </div>
            <div class="col-md-8">
                <h2 class="mb-2">تعديل بيانات المتقاعد</h2>
                <h4 class="mb-0">{{ retired_employee.employee.full_name }}</h4>
            </div>
            <div class="col-md-2">
                <span class="badge bg-light text-dark fs-6">
                    {{ retired_employee.employee.ministry_number }}
                </span>
            </div>
        </div>
    </div>

    <!-- Employee Information Display -->
    <div class="employee-info">
        <h5 class="text-primary mb-3">
            <i class="fas fa-user"></i> معلومات الموظف
        </h5>
        <div class="row">
            <div class="col-md-3">
                <strong>الاسم:</strong> {{ retired_employee.employee.full_name }}
            </div>
            <div class="col-md-3">
                <strong>الرقم الوزاري:</strong> {{ retired_employee.employee.ministry_number }}
            </div>
            <div class="col-md-3">
                <strong>الرقم الوطني:</strong> {{ retired_employee.employee.national_id }}
            </div>
            <div class="col-md-3">
                <strong>القسم:</strong> {{ retired_employee.employee.school }}
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card form-card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-clock"></i> تحديث بيانات التقاعد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.retirement_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar text-danger"></i> {{ form.retirement_date.label }}
                                    </label>
                                    {{ form.retirement_date }}
                                    {% if form.retirement_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.retirement_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.retirement_reason.id_for_label }}" class="form-label">
                                        <i class="fas fa-clipboard-list text-info"></i> {{ form.retirement_reason.label }}
                                    </label>
                                    {{ form.retirement_reason }}
                                    {% if form.retirement_reason.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.retirement_reason.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note text-secondary"></i> {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-warning btn-action">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{% url 'employees:retired_employee_detail' retired_employee.pk %}" class="btn btn-secondary btn-action">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{% url 'employees:retired_employees_list' %}" class="btn btn-outline-primary btn-action">
                <i class="fas fa-list"></i> العودة للقائمة
            </a>
            <a href="{% url 'employees:retired_employee_detail' retired_employee.pk %}" class="btn btn-outline-info btn-action">
                <i class="fas fa-eye"></i> عرض التفاصيل
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add current date as default if retirement date is empty
        const retirementDateInput = $('#{{ form.retirement_date.id_for_label }}');
        if (!retirementDateInput.val()) {
            const today = new Date().toISOString().split('T')[0];
            retirementDateInput.val(today);
        }
        
        // Form validation
        $('form').on('submit', function(e) {
            const retirementDate = retirementDateInput.val();
            const retirementReason = $('#{{ form.retirement_reason.id_for_label }}').val();
            
            if (!retirementDate) {
                e.preventDefault();
                alert('الرجاء إدخال تاريخ التقاعد');
                retirementDateInput.focus();
                return false;
            }
            
            if (!retirementReason.trim()) {
                e.preventDefault();
                alert('الرجاء إدخال سبب التقاعد');
                $('#{{ form.retirement_reason.id_for_label }}').focus();
                return false;
            }
        });
    });
</script>
{% endblock %}