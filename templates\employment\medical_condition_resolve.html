{% extends 'base.html' %}
{% load static %}

{% block title %}تحديث حالة المرض - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .resolve-info {
        background-color: #f8f9fc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .resolve-info h4 {
        margin-bottom: 20px;
        color: #4e73df;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }
    
    .info-item {
        margin-bottom: 15px;
    }
    
    .info-label {
        font-weight: bold;
        color: #5a5c69;
    }
    
    .info-value {
        color: #3a3b45;
    }
    
    .resolve-form {
        background-color: #e8f4ff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .resolve-form h4 {
        margin-bottom: 20px;
        color: #1cc88a;
        border-bottom: 1px solid #e3e6f0;
        padding-bottom: 10px;
    }
    
    .required-field label:after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تحديث حالة المرض إلى "تم علاجها"</h2>
    <a href="{% url 'employment:medical_condition_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى القائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="resolve-info">
            <h4>معلومات الحالة المرضية</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">الرقم الوزاري</div>
                        <div class="info-value">{{ medical_condition.employee.ministry_number }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">اسم الموظف</div>
                        <div class="info-value">{{ medical_condition.employee.full_name }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">اسم الحالة المرضية</div>
                        <div class="info-value">{{ medical_condition.condition_name }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">نوع الحالة</div>
                        <div class="info-value">{{ medical_condition.get_condition_type_display }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">تاريخ التشخيص</div>
                        <div class="info-value">{{ medical_condition.diagnosis_date|date:"Y-m-d" }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">الحالة الحالية</div>
                        <div class="info-value">{{ medical_condition.get_status_display }}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="resolve-form">
            <h4>تحديث الحالة إلى "تم علاجها"</h4>
            <form method="post">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6 required-field">
                        <div class="mb-3">
                            <label for="id_resolution_date">{{ form.resolution_date.label }}</label>
                            {{ form.resolution_date }}
                            {% if form.resolution_date.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.resolution_date.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_notes">{{ form.notes.label }}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.notes.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Form Errors -->
                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between mt-3">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check-circle"></i> تأكيد تحديث الحالة
                    </button>
                    <a href="{% url 'employment:medical_condition_detail' medical_condition.pk %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
