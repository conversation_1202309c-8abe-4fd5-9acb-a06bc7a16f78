# Generated by Django 5.2 on 2025-05-14 21:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0010_excessemployee'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalCondition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('condition_name', models.CharField(max_length=255, verbose_name='اسم الحالة المرضية')),
                ('condition_type', models.CharField(choices=[('chronic', 'مزمنة'), ('temporary', 'مؤقتة'), ('disability', 'إعاقة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الحالة')),
                ('severity', models.Char<PERSON>ield(choices=[('mild', 'خفيفة'), ('moderate', 'متوسطة'), ('severe', 'شديدة')], max_length=20, verbose_name='شدة الحالة')),
                ('diagnosis_date', models.DateField(verbose_name='تاريخ التشخيص')),
                ('description', models.TextField(verbose_name='وصف الحالة')),
                ('treatment', models.TextField(blank=True, null=True, verbose_name='العلاج')),
                ('doctor_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الطبيب')),
                ('hospital', models.CharField(blank=True, max_length=255, null=True, verbose_name='المستشفى/العيادة')),
                ('status', models.CharField(choices=[('active', 'نشطة'), ('inactive', 'غير نشطة'), ('resolved', 'تم علاجها')], default='active', max_length=20, verbose_name='حالة المرض')),
                ('resolution_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشفاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medical_conditions', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حالة مرضية',
                'verbose_name_plural': 'الحالات المرضية',
                'ordering': ['-diagnosis_date'],
            },
        ),
    ]
