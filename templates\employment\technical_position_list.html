{% extends 'base.html' %}
{% load static %}

{% block title %}الموقف الفني - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .specialization-first-row {
        border-top: 2px solid #4e73df;
    }

    .badge.bg-success {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
    }

    .table td[rowspan] {
        vertical-align: middle;
        background-color: rgba(78, 115, 223, 0.05);
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>الموقف الفني</h2>
    <div>
        <a href="{% url 'employment:zero_schools_list' %}" class="btn btn-warning me-2">
            <i class="fas fa-school"></i> المدارس الصفرية
        </a>
        <a href="{% url 'employment:export_technical_positions_excel' %}{% if search_query %}?search={{ search_query }}{% endif %}" class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> تصدير إلى إكسل
        </a>
        <a href="{% url 'employment:technical_position_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة موقف فني جديد
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">قائمة المواقف الفنية</h6>
        <form class="d-flex" method="get">
            <input class="form-control me-2" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
            <button class="btn btn-outline-primary" type="submit">بحث</button>
        </form>
    </div>
    <div class="card-body">
        <!-- إجمالي الشواغر -->
        <div class="alert alert-info mb-4">
            <div class="row">
                <div class="col-md-12 text-center">
                    <h5 class="mb-0">إجمالي عدد الشواغر: <span class="badge bg-primary">{{ total_vacancies }}</span></h5>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>التخصص</th>
                        <th>القسم</th>
                        <th>الجنس</th>
                        <th>عدد الشواغر</th>
                        <th>ملاحظات (المبرر)</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if grouped_positions %}
                        {% for specialization, data in grouped_positions.items %}
                            {% for position in data.positions %}
                                <tr {% if forloop.first %}class="specialization-first-row"{% endif %}>
                                    {% if forloop.first %}
                                        <td rowspan="{{ data.positions|length }}">{{ specialization }}</td>
                                    {% endif %}
                                    <td>{{ position.department.name|default:"-" }}</td>
                                    <td>{{ position.get_gender_display }}</td>
                                    {% if forloop.first %}
                                        <td rowspan="{{ data.positions|length }}">
                                            <span class="badge bg-success">{{ data.total_vacancies }}</span>
                                        </td>
                                    {% endif %}
                                    <td>{{ position.notes }}</td>
                                    <td>
                                        <button class="btn btn-warning btn-sm edit-position" data-id="{{ position.pk }}">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-danger btn-sm delete-position" data-id="{{ position.pk }}">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">لا يوجد مواقف فنية</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add event listeners for edit and delete buttons
    document.addEventListener('DOMContentLoaded', function() {
        const editButtons = document.querySelectorAll('.edit-position');
        const deleteButtons = document.querySelectorAll('.delete-position');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                window.location.href = `/employment/technical-positions/${id}/edit/`;
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذا الموقف الفني؟')) {
                    window.location.href = `/employment/technical-positions/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}
