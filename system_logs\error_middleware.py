"""
Middleware لتسجيل أخطاء النظام تلقائياً
"""

import traceback
import sys
from django.utils.deprecation import MiddlewareMixin
from django.http import Http404
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import IntegrityError, OperationalError
from .views import log_system_error
from .models import SystemError


class ErrorLoggingMiddleware(MiddlewareMixin):
    """
    Middleware لتسجيل الأخطاء في قاعدة البيانات
    """
    
    def process_exception(self, request, exception):
        """
        معالجة الاستثناءات وتسجيلها في قاعدة البيانات
        """
        
        # تجاهل بعض الأخطاء الشائعة التي لا نريد تسجيلها
        ignored_exceptions = (
            Http404,  # صفحة غير موجودة
        )
        
        if isinstance(exception, ignored_exceptions):
            return None
        
        try:
            # تحديد نوع الخطأ
            error_type = self._get_error_type(exception)
            
            # الحصول على معلومات الخطأ
            error_message = str(exception)
            stack_trace = traceback.format_exc()
            
            # الحصول على معلومات إضافية من stack trace
            tb = sys.exc_info()[2]
            file_path = None
            line_number = None
            function_name = None
            
            if tb:
                frame = tb.tb_frame
                file_path = frame.f_code.co_filename
                line_number = tb.tb_lineno
                function_name = frame.f_code.co_name
            
            # تحديد الوحدة من URL
            module = self._get_module_from_path(request.path)
            
            # تسجيل الخطأ
            log_system_error(
                request=request,
                error_type=error_type,
                error_message=error_message,
                file_path=file_path,
                line_number=line_number,
                function_name=function_name,
                stack_trace=stack_trace,
                module=module,
                severity=self._get_error_severity(exception)
            )
            
        except Exception as e:
            # في حالة فشل تسجيل الخطأ، اطبع رسالة في الكونسول
            print(f"Error logging middleware failed: {str(e)}")
        
        # إرجاع None للسماح للمعالجات الأخرى بالعمل
        return None
    
    def _get_error_type(self, exception):
        """تحديد نوع الخطأ بناءً على نوع الاستثناء"""
        
        error_mapping = {
            SyntaxError: SystemError.SYNTAX_ERROR,
            TypeError: SystemError.TYPE_ERROR,
            ValueError: SystemError.VALUE_ERROR,
            AttributeError: SystemError.ATTRIBUTE_ERROR,
            KeyError: SystemError.KEY_ERROR,
            IndexError: SystemError.INDEX_ERROR,
            NameError: SystemError.NAME_ERROR,
            ImportError: SystemError.IMPORT_ERROR,
            ModuleNotFoundError: SystemError.IMPORT_ERROR,
            PermissionDenied: SystemError.PERMISSION_ERROR,
            IntegrityError: SystemError.DATABASE_ERROR,
            OperationalError: SystemError.DATABASE_ERROR,
            ValidationError: SystemError.VALIDATION_ERROR,
        }
        
        # البحث عن نوع الخطأ المطابق
        for exc_type, error_type in error_mapping.items():
            if isinstance(exception, exc_type):
                return error_type
        
        # إذا لم يتم العثور على نوع مطابق، استخدم "خطأ آخر"
        return SystemError.OTHER_ERROR
    
    def _get_error_severity(self, exception):
        """تحديد مستوى خطورة الخطأ"""
        
        critical_errors = (
            IntegrityError,
            OperationalError,
            ImportError,
            ModuleNotFoundError,
        )
        
        high_errors = (
            SyntaxError,
            NameError,
            PermissionDenied,
        )
        
        low_errors = (
            ValidationError,
        )
        
        if isinstance(exception, critical_errors):
            return SystemError.CRITICAL
        elif isinstance(exception, high_errors):
            return SystemError.HIGH
        elif isinstance(exception, low_errors):
            return SystemError.LOW
        else:
            return SystemError.MEDIUM
    
    def _get_module_from_path(self, path):
        """استخراج اسم الوحدة من مسار URL"""
        
        if not path or path == '/':
            return 'home'
        
        # إزالة الشرطة المائلة في البداية والنهاية
        path = path.strip('/')
        
        # استخراج الجزء الأول من المسار
        parts = path.split('/')
        if parts:
            module = parts[0]
            
            # تحويل بعض الأسماء الشائعة
            module_mapping = {
                'admin': 'accounts',
                'api': 'api',
                'static': 'static',
                'media': 'media',
            }
            
            return module_mapping.get(module, module)
        
        return 'unknown'


class CustomErrorHandler:
    """
    معالج مخصص للأخطاء يمكن استخدامه في أي مكان في التطبيق
    """
    
    @staticmethod
    def log_error(request, exception, **kwargs):
        """
        تسجيل خطأ يدوياً
        
        Args:
            request: Django request object
            exception: Exception object
            **kwargs: معلومات إضافية
        """
        
        middleware = ErrorLoggingMiddleware()
        middleware.process_exception(request, exception)
    
    @staticmethod
    def log_custom_error(request, error_type, error_message, **kwargs):
        """
        تسجيل خطأ مخصص
        
        Args:
            request: Django request object
            error_type: نوع الخطأ من SystemError.ERROR_TYPE_CHOICES
            error_message: رسالة الخطأ
            **kwargs: معلومات إضافية
        """
        
        try:
            log_system_error(
                request=request,
                error_type=error_type,
                error_message=error_message,
                **kwargs
            )
        except Exception as e:
            print(f"Failed to log custom error: {str(e)}")


# دالة مساعدة للاستخدام السريع
def log_error_quickly(request, error_message, error_type=None, **kwargs):
    """
    دالة مساعدة لتسجيل الأخطاء بسرعة
    
    Args:
        request: Django request object
        error_message: رسالة الخطأ
        error_type: نوع الخطأ (اختياري)
        **kwargs: معلومات إضافية
    """
    
    if not error_type:
        error_type = SystemError.OTHER_ERROR
    
    CustomErrorHandler.log_custom_error(
        request=request,
        error_type=error_type,
        error_message=error_message,
        **kwargs
    )