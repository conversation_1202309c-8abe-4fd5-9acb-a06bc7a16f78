from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from notifications.utils import *

User = get_user_model()

class Command(BaseCommand):
    help = 'Create test notifications for demonstration'

    def handle(self, *args, **options):
        # Get the first user (usually admin)
        try:
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting user: {e}'))
            return

        # Create various test notifications
        notifications_data = [
            {
                'func': notify_user,
                'args': [user, "تحديث البيانات", "تم تحديث بيانات الموظف بنجاح", "success", "fa-edit"]
            },
            {
                'func': notify_user,
                'args': [user, "تنبيه مهم", "يرجى مراجعة طلبات الإجازات المعلقة", "warning", "fa-exclamation-triangle"]
            },
            {
                'func': notify_user,
                'args': [user, "نسخة احتياطية", "تم إنشاء النسخة الاحتياطية اليومية", "success", "fa-database"]
            },
            {
                'func': notify_user,
                'args': [user, "خطأ في النظام", "فشل في تحميل بعض البيانات", "error", "fa-times-circle"]
            },
            {
                'func': notify_all_users,
                'args': ["إعلان عام", "سيتم إجراء صيانة للنظام غداً من الساعة 2-4 صباحاً", "info", "fa-tools"]
            },
            {
                'func': notify_user,
                'args': [user, "تقرير شهري", "تم إنشاء التقرير الشهري للموظفين", "info", "fa-file-alt"]
            },
            {
                'func': notify_user,
                'args': [user, "مهمة مكتملة", "تم تحديث جميع بيانات الرواتب", "success", "fa-check-circle"]
            },
            {
                'func': notify_admins,
                'args': ["تحديث أمني", "تم تطبيق التحديثات الأمنية الجديدة", "info", "fa-shield-alt"]
            }
        ]

        created_count = 0
        for notification_data in notifications_data:
            try:
                func = notification_data['func']
                args = notification_data['args']
                func(*args)
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created notification: {args[1] if len(args) > 1 else args[0]}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating notification: {e}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} test notifications')
        )
