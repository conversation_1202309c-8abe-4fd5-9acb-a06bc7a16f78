{% load static %}
{% load permission_tags %}

<div class="permission-management">
    <!-- User Info -->
    <div class="alert alert-info">
        <div class="d-flex align-items-center">
            <div class="avatar-sm me-3">
                <div class="avatar-title bg-primary rounded-circle">
                    {{ user.username|first|upper }}
                </div>
            </div>
            <div>
                <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                <small class="text-muted">
                    {% if user.is_superuser %}
                        مدير النظام الرئيسي
                    {% elif user.is_full_admin %}
                        مدير كامل
                    {% elif user.is_admin %}
                        مدير
                    {% elif user.is_staff %}
                        مشرف
                    {% else %}
                        مستخدم عادي
                    {% endif %}
                </small>
            </div>
        </div>
    </div>

    <!-- Permission Presets -->
    <div class="mb-4">
        <h6 class="text-primary">
            <i class="fas fa-magic"></i>
            قوالب الصلاحيات السريعة
        </h6>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-danger btn-sm preset-btn" data-preset="full_admin">
                <i class="fas fa-crown"></i>
                مدير كامل
            </button>
            <button type="button" class="btn btn-outline-primary btn-sm preset-btn" data-preset="admin">
                <i class="fas fa-user-shield"></i>
                مدير
            </button>
            <button type="button" class="btn btn-outline-warning btn-sm preset-btn" data-preset="supervisor">
                <i class="fas fa-user-tie"></i>
                مشرف
            </button>
            <button type="button" class="btn btn-outline-info btn-sm preset-btn" data-preset="user">
                <i class="fas fa-user"></i>
                مستخدم
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm preset-btn" data-preset="readonly">
                <i class="fas fa-eye"></i>
                قراءة فقط
            </button>
            <button type="button" class="btn btn-outline-dark btn-sm preset-btn" data-preset="clear">
                <i class="fas fa-ban"></i>
                بلا صلاحيات
            </button>
        </div>
    </div>

    <!-- Modules Permissions -->
    <div class="row">
        {% for module_name, module_data in modules.items %}
        <div class="col-md-6 mb-4">
            <div class="card permission-module" data-module="{{ module_name }}">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check form-switch module-switch-container">
                            <input class="form-check-input module-toggle" type="checkbox" 
                                   id="module_{{ module_name }}" 
                                   data-module="{{ module_name }}"
                                   {% with user_permissions|get_permission:module_name as module_perms %}
                                   {% if module_perms %}checked{% endif %}
                                   {% endwith %}>
                            <label class="form-check-label module-switch-label" for="module_{{ module_name }}">
                                <span class="switch-text">تفعيل الوحدة</span>
                            </label>
                        </div>
                        <h6 class="mb-0 module-title">
                            <i class="fas fa-cube text-primary"></i>
                            {{ module_data.name }}
                        </h6>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Basic Permissions -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label fw-bold">الصلاحيات الأساسية:</label>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input can-view" type="checkbox" 
                                       id="{{ module_name }}_view"
                                       {% with user_permissions|get_permission:module_name as module_perms %}
                                       {% if module_perms.can_view %}checked{% endif %}>
                                <label class="form-check-label" for="{{ module_name }}_view">
                                    <i class="fas fa-eye text-info"></i>
                                    عرض
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input can-add" type="checkbox" 
                                       id="{{ module_name }}_add"
                                       {% if module_perms.can_add %}checked{% endif %}>
                                <label class="form-check-label" for="{{ module_name }}_add">
                                    <i class="fas fa-plus text-success"></i>
                                    إضافة
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input can-edit" type="checkbox" 
                                       id="{{ module_name }}_edit"
                                       {% if module_perms.can_edit %}checked{% endif %}>
                                <label class="form-check-label" for="{{ module_name }}_edit">
                                    <i class="fas fa-edit text-warning"></i>
                                    تعديل
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input can-delete" type="checkbox" 
                                       id="{{ module_name }}_delete"
                                       {% if module_perms.can_delete %}checked{% endif %}>
                                <label class="form-check-label" for="{{ module_name }}_delete">
                                    <i class="fas fa-trash text-danger"></i>
                                    حذف
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Specific Pages -->
                    <div class="pages-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">الصفحات المتاحة:</label>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-success btn-xs select-all-pages" 
                                        data-module="{{ module_name }}">
                                    <i class="fas fa-check-double"></i>
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-xs clear-all-pages" 
                                        data-module="{{ module_name }}">
                                    <i class="fas fa-times"></i>
                                    إلغاء الكل
                                </button>
                            </div>
                        </div>
                        <div class="pages-list" style="max-height: 200px; overflow-y: auto;">
                            {% for page in module_data.pages %}
                            <div class="form-check form-check-sm">
                                <input class="form-check-input page-checkbox" type="checkbox" 
                                       value="{{ page.url }}" 
                                       id="page_{{ page.url|slugify }}"
                                       {% if page.url in module_perms.visible_pages %}checked{% endif %}>
                                <label class="form-check-label" for="page_{{ page.url|slugify }}">
                                    <small>{{ page.name }}</small>
                                </label>
                            </div>
                            {% endfor %}
                        {% endwith %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<style>
.permission-module {
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.permission-module:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.permission-module.active {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.pages-list {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.form-check-sm .form-check-input {
    margin-top: 0.1rem;
}

/* Module switch styling */
.module-switch-container {
    order: 1;
    margin-bottom: 0;
}

.module-title {
    order: 2;
    text-align: right;
}

.module-switch-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
    margin-right: 0.75rem;
    margin-left: 0.5rem;
    cursor: pointer;
}

.switch-text {
    white-space: nowrap;
    padding-right: 1rem;
    display: inline-block;
}

.form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
    background-color: #6c757d;
    border: none;
    border-radius: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
    border-color: #198754;
}

.form-switch .form-check-input:hover {
    opacity: 0.8;
}

/* Module card states */
.permission-module.module-enabled {
    border-color: #198754;
    background-color: #f8fff9;
}

.permission-module.module-enabled .card-header {
    background-color: #d1e7dd;
    border-bottom-color: #198754;
}

.permission-module.module-disabled {
    border-color: #6c757d;
    background-color: #f8f9fa;
    opacity: 0.7;
}

.permission-module.module-disabled .card-header {
    background-color: #e9ecef;
    border-bottom-color: #6c757d;
}

.permission-module.module-disabled .card-body {
    opacity: 0.5;
    pointer-events: none;
}

/* Improved spacing and layout */
.module-switch-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input.module-toggle {
    margin: 0;
    flex-shrink: 0;
}

.module-switch-label {
    margin: 0;
    display: flex;
    align-items: center;
}

.switch-text {
    margin-right: 1rem;
    font-weight: 500;
}

/* Ensure proper spacing in header */
.card-header .d-flex {
    gap: 1rem;
}

/* Better visual feedback for toggle state */
.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
    border-color: #198754;
}

.form-switch .form-check-input:checked:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* Ensure toggle is always clickable */
.module-toggle {
    cursor: pointer !important;
    pointer-events: auto !important;
}

.module-switch-label {
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* Animation for module state change */
.permission-module {
    transition: all 0.4s ease;
}

.module-toggle {
    transition: all 0.3s ease;
}

.module-toggle:checked {
    transform: scale(1.05);
    animation: togglePulse 0.3s ease;
}

@keyframes togglePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
}

.form-check-sm .form-check-label {
    font-size: 0.875rem;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
}
</style>

<script>
// Permission preset configurations
const permissionPresets = {
    'full_admin': {
        description: 'صلاحيات كاملة لجميع الوحدات',
        permissions: 'all'
    },
    'admin': {
        description: 'صلاحيات إدارية (عرض، إضافة، تعديل)',
        permissions: ['can_view', 'can_add', 'can_edit']
    },
    'supervisor': {
        description: 'صلاحيات إشرافية (عرض، إضافة، تعديل)',
        permissions: ['can_view', 'can_add', 'can_edit'],
        excludeModules: ['accounts', 'backup', 'system_logs']
    },
    'user': {
        description: 'صلاحيات مستخدم (عرض، إضافة)',
        permissions: ['can_view', 'can_add'],
        excludeModules: ['accounts', 'backup', 'system_logs', 'disciplinary']
    },
    'readonly': {
        description: 'صلاحيات قراءة فقط',
        permissions: ['can_view'],
        excludeModules: ['accounts', 'backup', 'system_logs']
    },
    'none': {
        description: 'بلا صلاحيات',
        permissions: []
    }
};

function applyPermissionPreset(presetName) {
    const preset = permissionPresets[presetName];
    if (!preset) return;
    
    if (!confirm(`هل تريد تطبيق قالب "${preset.description}"؟\nسيتم استبدال جميع الصلاحيات الحالية.`)) {
        return;
    }
    
    // Reset all permissions first
    document.querySelectorAll('.permission-module input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    if (preset.permissions === 'all') {
        // Grant all permissions to all modules
        document.querySelectorAll('.permission-module').forEach(module => {
            const moduleName = module.dataset.module;
            module.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = true;
            });
        });
    } else if (preset.permissions.length > 0) {
        // Apply specific permissions
        document.querySelectorAll('.permission-module').forEach(module => {
            const moduleName = module.dataset.module;
            
            // Skip excluded modules
            if (preset.excludeModules && preset.excludeModules.includes(moduleName)) {
                return;
            }
            
            // Apply permissions
            preset.permissions.forEach(permission => {
                const checkbox = module.querySelector(`.${permission.replace('_', '-')}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            // Select all pages if view permission is granted
            if (preset.permissions.includes('can_view')) {
                module.querySelectorAll('.page-checkbox').forEach(pageCheckbox => {
                    pageCheckbox.checked = true;
                });
            }
        });
    }
    
    // Update module toggles
    updateModuleToggles();
}

function updateModuleToggles() {
    document.querySelectorAll('.permission-module').forEach(module => {
        const moduleToggle = module.querySelector('.module-toggle');
        const hasAnyPermission = Array.from(module.querySelectorAll('input[type="checkbox"]:not(.module-toggle)')).some(cb => cb.checked);
        moduleToggle.checked = hasAnyPermission;
    });
}

// Initialize module toggles on load
document.addEventListener('DOMContentLoaded', function() {
    updateModuleToggles();
});
</script>