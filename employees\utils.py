"""
Utility functions for employees app
"""
from django.db.models.functions import Cast
from django.db.models import IntegerField


def order_employees_by_ministry_number(queryset):
    """
    Order employees queryset by ministry number in numeric order.
    This ensures proper numeric sorting (e.g., 2 comes before 10).
    
    Args:
        queryset: Employee queryset to order
        
    Returns:
        Ordered queryset
    """
    try:
        # Check if queryset is already sliced
        if queryset._result_cache is not None or queryset.query.low_mark is not None or queryset.query.high_mark is not None:
            # If sliced, convert to list and sort in Python
            employees_list = list(queryset)
            try:
                # Try to sort numerically
                employees_list.sort(key=lambda x: int(x.ministry_number) if x.ministry_number.isdigit() else float('inf'))
            except (ValueError, AttributeError):
                # Fallback to string sorting
                employees_list.sort(key=lambda x: x.ministry_number or '')
            return employees_list
        else:
            # If not sliced, use database sorting
            return queryset.annotate(
                ministry_number_int=Cast('ministry_number', IntegerField())
            ).order_by('ministry_number_int')
    except Exception:
        # Fallback to string sorting if casting fails
        try:
            return queryset.order_by('ministry_number')
        except Exception:
            # If even string sorting fails, return as is
            return queryset