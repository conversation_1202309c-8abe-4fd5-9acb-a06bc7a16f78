# Generated by Django 5.2 on 2025-07-18 11:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0029_remove_servicepurchase_purchase_amount_and_more'),
        ('home', '0009_importantlink_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ministry_number', models.Char<PERSON>ield(max_length=20, verbose_name='الرقم الوزاري')),
                ('employee_name', models.CharField(max_length=100, verbose_name='اسم الموظف')),
                ('specialization', models.CharField(blank=True, max_length=100, null=True, verbose_name='التخصص')),
                ('actual_service', models.CharField(blank=True, max_length=50, null=True, verbose_name='الخدمة الفعلية')),
                ('current_department', models.CharField(max_length=100, verbose_name='القسم الحالي')),
                ('transfer_type', models.CharField(choices=[('internal_transfer', 'النقل الداخلي'), ('temporary_assignment', 'التكليف المؤقت')], default='internal_transfer', max_length=30, verbose_name='صفة النقل')),
                ('endorsement', models.CharField(choices=[('admin_financial_manager', 'تنسيب مدير الشؤون الادارية والمالية'), ('hr_committee', 'تنسيب لجنة الموارد البشرية')], default='admin_financial_manager', max_length=50, verbose_name='التنسيب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('new_department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='employment.department', verbose_name='القسم الجديد')),
            ],
            options={
                'verbose_name': 'نقل موظف',
                'verbose_name_plural': 'نقل الموظفين',
                'ordering': ['-created_at'],
            },
        ),
    ]
