from django import forms
from .models import FileMovement, File
from employees.models import Employee

class FileMovementForm(forms.Form):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # File field
    file = forms.ModelChoiceField(
        queryset=File.objects.all(),
        label='الملف',
        required=True,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Other fields
    checkout_date = forms.DateField(
        label='تاريخ خروج الملف',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=True
    )
    return_date = forms.DateField(
        label='تاريخ عودة الملف',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=False
    )
    action_taken = forms.CharField(
        label='الإجراء المتخذ',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False
    )
    notes = forms.CharField(
        label='ملاحظات',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False
    )

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')
        checkout_date = cleaned_data.get('checkout_date')
        return_date = cleaned_data.get('return_date')

        # Validate employee
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        # Try to get employee
        try:
            if employee_id:
                employee = Employee.objects.get(id=employee_id)
            elif ministry_number:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee_id'] = employee.id
            cleaned_data['employee'] = employee
        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        # Check if return date is after checkout date
        if return_date and checkout_date and return_date < checkout_date:
            raise forms.ValidationError('تاريخ عودة الملف يجب أن يكون بعد تاريخ خروج الملف')

        return cleaned_data

    def save(self):
        # Get data from cleaned_data
        employee = self.cleaned_data['employee']
        file = self.cleaned_data['file']
        checkout_date = self.cleaned_data['checkout_date']
        return_date = self.cleaned_data.get('return_date')
        action_taken = self.cleaned_data.get('action_taken', '')
        notes = self.cleaned_data.get('notes', '')

        # Create new file movement
        file_movement = FileMovement(
            employee=employee,
            file=file,
            checkout_date=checkout_date,
            return_date=return_date,
            action_taken=action_taken,
            notes=notes
        )

        # Set status based on return date
        if return_date:
            file_movement.status = 'returned'
        else:
            file_movement.status = 'out'

        # Save to database
        file_movement.save()

        return file_movement

class FileCheckoutForm(forms.Form):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # File field
    file = forms.ModelChoiceField(
        queryset=File.objects.all(),
        label='الملف',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Set default checkout date to today
    checkout_date = forms.DateField(
        label='تاريخ خروج الملف',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=True
    )

    notes = forms.CharField(
        label='ملاحظات',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False
    )

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')

        # Validate employee
        if not employee_id and not ministry_number:
            raise forms.ValidationError('الرجاء إدخال الرقم الوزاري للموظف')

        # Try to get employee
        try:
            if employee_id:
                employee = Employee.objects.get(id=employee_id)
            elif ministry_number:
                employee = Employee.objects.get(ministry_number=ministry_number)
                cleaned_data['employee_id'] = employee.id
            cleaned_data['employee'] = employee

            # Check if employee already has an active file checkout
            active_checkouts = FileMovement.objects.filter(
                employee=employee,
                status='out',
                return_date__isnull=True
            )

            if active_checkouts.exists():
                active_checkout = active_checkouts.first()
                file_info = f" (ملف: {active_checkout.file.title})" if active_checkout.file else ""
                raise forms.ValidationError(
                    f'الموظف لديه ملف خارج بالفعل منذ {active_checkout.checkout_date}{file_info}'
                )

        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        return cleaned_data

    def save(self):
        # Get data from cleaned_data
        employee = self.cleaned_data['employee']
        file = self.cleaned_data['file']
        checkout_date = self.cleaned_data['checkout_date']
        notes = self.cleaned_data.get('notes', '')

        # Create new file movement
        file_movement = FileMovement(
            employee=employee,
            file=file,
            checkout_date=checkout_date,
            notes=notes,
            status='out'
        )

        # Save to database
        file_movement.save()

        return file_movement

class FileReturnForm(forms.Form):
    return_date = forms.DateField(
        label='تاريخ عودة الملف',
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=True
    )
    action_taken = forms.CharField(
        label='الإجراء المتخذ',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=True
    )
    notes = forms.CharField(
        label='ملاحظات',
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        required=False
    )

    def __init__(self, *args, **kwargs):
        self.file_movement = kwargs.pop('file_movement', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        return_date = cleaned_data.get('return_date')

        # Check if return date is after checkout date
        if return_date and self.file_movement and return_date < self.file_movement.checkout_date:
            raise forms.ValidationError('تاريخ عودة الملف يجب أن يكون بعد تاريخ خروج الملف')

        return cleaned_data

class FileForm(forms.ModelForm):
    class Meta:
        model = File
        fields = ['file_number', 'title', 'employee', 'status', 'description']
        widgets = {
            'file_number': forms.TextInput(attrs={'class': 'form-control'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'employee': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }
