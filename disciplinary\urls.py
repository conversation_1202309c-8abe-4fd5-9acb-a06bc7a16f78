from django.urls import path
from . import views

app_name = 'disciplinary'

urlpatterns = [
    path('penalties/', views.penalty_list, name='penalty_list'),
    path('penalties/add/', views.penalty_create, name='penalty_create'),
    path('penalties/<int:pk>/', views.penalty_detail, name='penalty_detail'),
    path('penalties/<int:pk>/edit/', views.penalty_update, name='penalty_update'),
    path('penalties/<int:pk>/delete/', views.penalty_delete, name='penalty_delete'),
    path('penalty-types/', views.penalty_type_list, name='penalty_type_list'),
    path('penalty-types/add/', views.penalty_type_create, name='penalty_type_create'),
    path('penalty-types/<int:pk>/edit/', views.penalty_type_update, name='penalty_type_update'),
    path('penalty-types/<int:pk>/delete/', views.penalty_type_delete, name='penalty_type_delete'),
    path('get-employee/', views.get_employee_by_ministry_number, name='get_employee_by_ministry_number'),
]
