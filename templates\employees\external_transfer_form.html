{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل بيانات النقل الخارجي - {{ external_transfer.employee.full_name }}{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
    }
    
    .form-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #17a2b8;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 5px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .employee-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid #bbdefb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Form Header -->
    <div class="form-header text-center">
        <div class="row align-items-center">
            <div class="col-md-2">
                <i class="fas fa-edit fa-3x"></i>
            </div>
            <div class="col-md-8">
                <h2 class="mb-2">تعديل بيانات النقل الخارجي</h2>
                <h4 class="mb-0">{{ external_transfer.employee.full_name }}</h4>
            </div>
            <div class="col-md-2">
                <span class="badge bg-light text-dark fs-6">
                    {{ external_transfer.employee.ministry_number }}
                </span>
            </div>
        </div>
    </div>

    <!-- Employee Information Display -->
    <div class="employee-info">
        <h5 class="text-primary mb-3">
            <i class="fas fa-user"></i> معلومات الموظف
        </h5>
        <div class="row">
            <div class="col-md-3">
                <strong>الاسم:</strong> {{ external_transfer.employee.full_name }}
            </div>
            <div class="col-md-3">
                <strong>الرقم الوزاري:</strong> {{ external_transfer.employee.ministry_number }}
            </div>
            <div class="col-md-3">
                <strong>التخصص:</strong> {{ external_transfer.employee.specialization }}
            </div>
            <div class="col-md-3">
                <strong>القسم السابق:</strong> {{ external_transfer.employee.school }}
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card form-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt"></i> تحديث بيانات النقل الخارجي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.transfer_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar text-warning"></i> {{ form.transfer_date.label }}
                                    </label>
                                    {{ form.transfer_date }}
                                    {% if form.transfer_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.transfer_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.destination_directorate.id_for_label }}" class="form-label">
                                        <i class="fas fa-building text-info"></i> {{ form.destination_directorate.label }}
                                    </label>
                                    {{ form.destination_directorate }}
                                    {% if form.destination_directorate.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.destination_directorate.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.transfer_reason.id_for_label }}" class="form-label">
                                <i class="fas fa-clipboard-list text-secondary"></i> {{ form.transfer_reason.label }}
                            </label>
                            {{ form.transfer_reason }}
                            {% if form.transfer_reason.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.transfer_reason.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note text-muted"></i> {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-info btn-action">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{% url 'employees:external_transfer_detail' external_transfer.pk %}" class="btn btn-secondary btn-action">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{% url 'employees:external_transfers_list' %}" class="btn btn-outline-primary btn-action">
                <i class="fas fa-list"></i> العودة للقائمة
            </a>
            <a href="{% url 'employees:external_transfer_detail' external_transfer.pk %}" class="btn btn-outline-info btn-action">
                <i class="fas fa-eye"></i> عرض التفاصيل
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Form validation
        $('form').on('submit', function(e) {
            const transferDate = $('#{{ form.transfer_date.id_for_label }}').val();
            const destinationDirectorate = $('#{{ form.destination_directorate.id_for_label }}').val();
            
            if (!transferDate) {
                e.preventDefault();
                alert('الرجاء إدخال تاريخ النقل');
                $('#{{ form.transfer_date.id_for_label }}').focus();
                return false;
            }
            
            if (!destinationDirectorate.trim()) {
                e.preventDefault();
                alert('الرجاء إدخال المديرية المنقول إليها');
                $('#{{ form.destination_directorate.id_for_label }}').focus();
                return false;
            }
        });
    });
</script>
{% endblock %}