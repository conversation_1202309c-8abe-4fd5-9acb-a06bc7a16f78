{% extends 'base.html' %}
{% load static %}

{% block title %}التقارير السنوية - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Card Styles */
    .dashboard-card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
    }

    .dashboard-card .text-xs {
        font-size: 0.85rem;
        letter-spacing: 0.05em;
    }

    .dashboard-card .h3 {
        font-size: 2rem;
        font-weight: 700;
    }

    .rounded-circle {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تحسين المسافة بين الأزرار */
    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }

    /* تحسين عرض عمود الإجراءات */
    .actions-column {
        min-width: 280px;
    }

    /* تنسيق الجدول مع حدود واضحة */
    .table {
        border: 1px solid #000 !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        text-align: center;
        font-weight: bold;
    }

    .table thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* تكبير الأرقام */
    .table td:nth-child(3),
    .table td:nth-child(4) {
        font-size: 16px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>التقارير السنوية</h2>
    <div>
        <a href="{% url 'employees:annual_report_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة تقرير سنوي جديد
        </a>
        <a href="{% url 'performance:performance_import' %}" class="btn btn-success text-white">
            <i class="fas fa-file-import"></i> استيراد من Excel
        </a>
        <a href="{% url 'performance:performance_list' %}?export=excel{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}" class="btn btn-success text-white">
            <i class="fas fa-file-export"></i> تصدير إلى Excel
        </a>
        <a href="{% url 'performance:performance_list' %}?export=pdf_preview{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_year %}&year={{ selected_year }}{% endif %}" class="btn btn-danger text-white">
            <i class="fas fa-file-pdf"></i> معاينة PDF
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-4 border-primary shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي التقارير</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ total_evaluations_count }}</div>
                        <div class="text-muted small mt-2">العدد الكلي لجميع التقارير السنوية</div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-chart-line fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-4 border-success shadow h-100 py-2 rounded-3 dashboard-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">تقارير {{ current_year }}</div>
                        <div class="h3 mb-0 fw-bold text-gray-800">{{ current_year_evaluations_count }}</div>
                        <div class="text-muted small mt-2">عدد التقارير للعام الحالي {{ current_year }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-calendar-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">جميع التقارير السنوية</h6>
            <span class="badge bg-primary ms-2 fs-6 p-2">عدد الموظفين: {{ employee_count }}</span>
        </div>
        <form class="d-flex" method="get">
            <div class="d-flex">
                <div class="input-group me-2">
                    <input class="form-control" type="search" placeholder="بحث..." name="search" value="{{ search_query }}">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <select class="form-select" name="year" id="yearFilter" style="max-width: 150px;">
                    <option value="all" {% if not selected_year %}selected{% endif %}>جميع السنوات</option>
                    {% for year in years %}
                    <option value="{{ year }}" {% if selected_year == year|stringformat:"i" %}selected{% endif %}>{{ year }}</option>
                    {% endfor %}
                </select>
                {% if selected_year or search_query %}
                <a href="{% url 'performance:performance_list' %}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-redo"></i>
                </a>
                {% endif %}
            </div>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                        <th><i class="fas fa-user me-1"></i> الاسم الرباعي</th>
                        <th><i class="fas fa-star me-1"></i> العلامة</th>
                        <th><i class="fas fa-calendar-alt me-1"></i> السنة</th>
                        <th class="actions-column"><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for evaluation in evaluations %}
                    <tr class="text-center">
                        <td>{{ evaluation.employee.ministry_number }}</td>
                        <td>
                            <a href="{% url 'employees:employee_detail' evaluation.employee.pk %}">
                                {{ evaluation.employee.full_name }}
                            </a>
                        </td>
                        <td>{{ evaluation.score }}</td>
                        <td>{{ evaluation.year }}</td>
                        <td>
                            <a href="{% url 'performance:performance_detail' evaluation.pk %}" class="btn btn-info btn-sm" style="width: 80px;">
                                <i class="fas fa-eye"></i> معاينة
                            </a>
                            <a href="{% url 'performance:performance_update' evaluation.pk %}" class="btn btn-warning btn-sm" style="width: 80px;">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'performance:performance_delete' evaluation.pk %}" class="btn btn-danger btn-sm" style="width: 80px;">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr class="text-center">
                        <td colspan="5">لا يوجد تقارير سنوية</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
