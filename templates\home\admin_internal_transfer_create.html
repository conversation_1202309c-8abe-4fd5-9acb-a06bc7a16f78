{% extends 'base.html' %}
{% load static %}

{% block title %}تقديم طلب نقل داخلي - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .search-card .card-body {
        padding: 2rem;
    }

    .search-input {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        font-size: 16px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: border-color 0.3s ease;
    }

    .search-input:focus {
        border-color: #007bff;
        box-shadow: 0 4px 6px rgba(0, 123, 255, 0.25);
    }

    .search-input.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 4px 6px rgba(220, 53, 69, 0.25);
    }

    .search-btn {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .result-card {
        display: none;
        animation: fadeInUp 0.5s ease-in-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .employee-info {
        background: #f8f9fa;
        color: #333;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .employee-info h4 {
        color: #2c3e50 !important;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .employee-info h5 {
        color: #34495e !important;
        font-weight: 600;
    }

    .employee-info p {
        color: #2c3e50 !important;
        margin-bottom: 0.5rem;
    }

    .employee-info strong {
        color: #2c3e50 !important;
    }

    .employee-info .row {
        align-items: center;
    }

    .employee-info .text-primary {
        color: #007bff !important;
    }

    .employee-info .text-success {
        color: #28a745 !important;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-primary {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        box-shadow: 0 4px 6px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
    }

    .btn-secondary {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
    }

    .card {
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #dee2e6;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .loading-spinner {
        display: none;
    }

    .loading-spinner.show {
        display: inline-block;
    }

    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .search-card .card-body {
            padding: 1.5rem;
        }
        
        .search-input {
            font-size: 14px;
            padding: 10px 15px;
        }
        
        .search-btn {
            padding: 10px 20px;
            font-size: 14px;
        }
        
        .employee-info {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus text-info me-2"></i>
                تقديم طلب نقل داخلي
            </h1>
            <p class="text-muted mb-0">إضافة طلب نقل داخلي جديد للموظفين</p>
        </div>
        <div>
            <a href="{% url 'home:internal_transfer_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة لقائمة الطلبات
            </a>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-card">
                <div class="card-body text-center">
                    <h3 class="mb-4">
                        <i class="fas fa-search me-2"></i>
                        البحث عن الموظف
                    </h3>
                    <p class="mb-4">ابحث عن الموظف باستخدام الرقم الوزاري أو الاسم</p>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="input-group mb-3">
                                <input type="text" 
                                       id="searchInput" 
                                       class="form-control search-input" 
                                       placeholder="أدخل الرقم الوزاري أو الاسم..."
                                       autocomplete="off">
                                <button class="btn btn-light search-btn" type="button" id="searchBtn">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                    <span class="loading-spinner ms-2">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </button>
                            </div>
                            <div id="searchError" class="alert alert-danger" style="display: none;"></div>
                            <div id="multipleResults" class="alert alert-info" style="display: none;">
                                <h6><i class="fas fa-users me-2"></i>تم العثور على عدة موظفين:</h6>
                                <div id="employeesList"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Info and Form Section -->
    <div class="row">
        <div class="col-12">
            <div class="card result-card" id="resultCard">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        تقديم طلب النقل الداخلي
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Employee Information -->
                    <div id="employeeInfo" class="employee-info" style="display: none;">
                        <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
                    </div>

                    <!-- Transfer Form -->
                    <form method="post" id="transferForm" style="display: none;">
                        {% csrf_token %}
                        
                        <!-- Hidden fields for employee data -->
                        {{ form.ministry_number }}
                        {{ form.employee_id }}
                        {{ form.employee_name }}
                        {{ form.current_department }}
                        {{ form.specialization }}
                        {{ form.actual_service }}

                        <div class="row">
                            <!-- First Choice -->
                            <div class="col-md-4 mb-3">
                                <label for="id_first_choice" class="form-label">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    الخيار الأول <span class="text-danger">*</span>
                                </label>
                                {{ form.first_choice }}
                                {% if form.first_choice.errors %}
                                    <div class="text-danger small mt-1">{{ form.first_choice.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Second Choice -->
                            <div class="col-md-4 mb-3">
                                <label for="id_second_choice" class="form-label">
                                    <i class="fas fa-star-half-alt text-info me-1"></i>
                                    الخيار الثاني
                                </label>
                                {{ form.second_choice }}
                                {% if form.second_choice.errors %}
                                    <div class="text-danger small mt-1">{{ form.second_choice.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- Third Choice -->
                            <div class="col-md-4 mb-3">
                                <label for="id_third_choice" class="form-label">
                                    <i class="fas fa-star text-secondary me-1"></i>
                                    الخيار الثالث
                                </label>
                                {{ form.third_choice }}
                                {% if form.third_choice.errors %}
                                    <div class="text-danger small mt-1">{{ form.third_choice.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="id_notes" class="form-label">
                                    <i class="fas fa-sticky-note text-info me-1"></i>
                                    ملاحظات إضافية
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small mt-1">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary me-3">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    تقديم طلب النقل الداخلي
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-redo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const searchError = document.getElementById('searchError');
    const resultCard = document.getElementById('resultCard');
    const employeeInfo = document.getElementById('employeeInfo');
    const transferForm = document.getElementById('transferForm');
    const loadingSpinner = document.querySelector('.loading-spinner');

    // Search functionality
    function performSearch() {
        const searchTerm = searchInput.value.trim();
        
        if (!searchTerm) {
            showError('يرجى إدخال الرقم الوزاري أو الاسم للبحث');
            return;
        }

        // Show loading
        loadingSpinner.classList.add('show');
        searchBtn.disabled = true;
        hideError();

        // Perform search
        fetch('{% url "home:admin_internal_transfer_create" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `search_term=${encodeURIComponent(searchTerm)}`
        })
        .then(response => response.json())
        .then(data => {
            loadingSpinner.classList.remove('show');
            searchBtn.disabled = false;

            if (data.success) {
                displayEmployeeInfo(data.employee);
                populateForm(data.employee);
                showResultCard();
            } else if (data.multiple_results) {
                showMultipleResults(data.employees);
                showError(data.error);
            } else {
                showError(data.error || 'حدث خطأ أثناء البحث');
            }
        })
        .catch(error => {
            loadingSpinner.classList.remove('show');
            searchBtn.disabled = false;
            showError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            console.error('Search error:', error);
        });
    }

    // Event listeners
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch();
        }
    });

    // Helper functions
    function showError(message) {
        searchError.textContent = message;
        searchError.style.display = 'block';
        searchInput.classList.add('is-invalid');
        hideMultipleResults();
    }

    function hideError() {
        searchError.style.display = 'none';
        searchInput.classList.remove('is-invalid');
    }

    function showMultipleResults(employees) {
        const multipleResults = document.getElementById('multipleResults');
        const employeesList = document.getElementById('employeesList');
        
        let listHtml = '<div class="list-group">';
        employees.forEach(emp => {
            listHtml += `
                <button type="button" class="list-group-item list-group-item-action" 
                        onclick="selectEmployee('${emp.ministry_number}')">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${emp.full_name}</h6>
                        <small class="text-primary">${emp.ministry_number}</small>
                    </div>
                    <p class="mb-1">${emp.current_department}</p>
                </button>
            `;
        });
        listHtml += '</div>';
        
        employeesList.innerHTML = listHtml;
        multipleResults.style.display = 'block';
    }

    function hideMultipleResults() {
        const multipleResults = document.getElementById('multipleResults');
        multipleResults.style.display = 'none';
    }

    // Function to select employee from multiple results
    window.selectEmployee = function(ministryNumber) {
        searchInput.value = ministryNumber;
        hideMultipleResults();
        performSearch();
    };

    function showResultCard() {
        resultCard.style.display = 'block';
        resultCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    function displayEmployeeInfo(employee) {
        const infoHtml = `
            <h4><i class="fas fa-user me-2"></i>معلومات الموظف</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الاسم الكامل:</strong> <span class="text-primary">${employee.full_name}</span></p>
                    <p><strong>الرقم الوزاري:</strong> <span class="text-success">${employee.ministry_number}</span></p>
                    <p><strong>القسم الحالي:</strong> ${employee.current_department || 'غير محدد'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>التخصص:</strong> ${employee.specialization || 'غير محدد'}</p>
                    <p><strong>الخدمة الفعلية:</strong> ${employee.actual_service || 'غير محدد'}</p>
                    <p><strong>تاريخ التعيين:</strong> ${employee.hire_date || 'غير محدد'}</p>
                </div>
            </div>
        `;
        employeeInfo.innerHTML = infoHtml;
        employeeInfo.style.display = 'block';
    }

    function populateForm(employee) {
        // Find the hidden input fields by name attribute
        const ministryField = document.querySelector('input[name="ministry_number"]');
        const employeeIdField = document.querySelector('input[name="employee_id"]');
        const employeeNameField = document.querySelector('input[name="employee_name"]');
        const departmentField = document.querySelector('input[name="current_department"]');
        const specializationField = document.querySelector('input[name="specialization"]');
        const serviceField = document.querySelector('input[name="actual_service"]');
        
        if (ministryField) ministryField.value = employee.ministry_number;
        if (employeeIdField) employeeIdField.value = employee.employee_id || '';
        if (employeeNameField) employeeNameField.value = employee.full_name;
        if (departmentField) departmentField.value = employee.current_department || '';
        if (specializationField) specializationField.value = employee.specialization || '';
        if (serviceField) serviceField.value = employee.actual_service || '';
        
        transferForm.style.display = 'block';
    }

    // Reset form function
    window.resetForm = function() {
        searchInput.value = '';
        resultCard.style.display = 'none';
        employeeInfo.style.display = 'none';
        transferForm.style.display = 'none';
        hideError();
        hideMultipleResults();
        searchInput.focus();
    };

    // Focus on search input
    searchInput.focus();
});
</script>
{% endblock %}