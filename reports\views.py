from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
import pandas as pd
from io import BytesIO
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from .models import Report
from .forms import ReportForm, AttendanceReportForm, EmploymentReportForm, EmployeeReportForm, DepartmentReportForm, LeaveReportForm, StaffReportForm, TechnicalPositionReportForm, PositionReportForm, SpecializationReportForm
from employees.models import Employee
from employment.models import Department, Employment, Position, TechnicalPosition
from leaves.models import Leave, LeaveBalance, LeaveType
from performance.models import PerformanceEvaluation
import datetime

@login_required
def report_dashboard(request):
    # Get counts for dashboard
    employee_count = Employee.objects.count()
    department_count = Department.objects.count()
    leave_count = Leave.objects.count()
    evaluation_count = PerformanceEvaluation.objects.count()

    # Get recent reports with current time
    recent_reports = Report.objects.all().order_by('-created_at')[:5]

    # Update created_at to current time for display purposes
    for report in recent_reports:
        report.display_time = timezone.localtime(report.created_at).strftime('%Y-%m-%d %H:%M:%S')

    return render(request, 'reports/report_dashboard.html', {
        'employee_count': employee_count,
        'department_count': department_count,
        'leave_count': leave_count,
        'evaluation_count': evaluation_count,
        'recent_reports': recent_reports,
    })

@login_required
def attendance_report(request):
    if request.method == 'POST':
        form = AttendanceReportForm(request.POST)
        if form.is_valid():
            start_date = form.cleaned_data['start_date']
            end_date = form.cleaned_data['end_date']
            department = form.cleaned_data['department']

            # Get leaves in the date range
            leaves_query = Leave.objects.filter(
                start_date__lte=end_date,
                end_date__gte=start_date,
                status='approved'
            )

            if department:
                # Filter by department
                employees_in_dept = Employment.objects.filter(
                    department=department,
                    is_current=True
                ).values_list('employee_id', flat=True)
                leaves_query = leaves_query.filter(employee_id__in=employees_in_dept)

            leaves = leaves_query.select_related('employee', 'leave_type')

            # Create report
            report = Report.objects.create(
                title=f'تقرير الحضور من {start_date} إلى {end_date}',
                report_type=Report.ATTENDANCE,
                parameters={
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'department_id': department.id if department else None
                },
                created_by=request.user.username
            )

            # Export to Excel
            data = {
                'الرقم الوزاري': [leave.employee.ministry_number for leave in leaves],
                'الرقم الوطني': [leave.employee.national_id for leave in leaves],
                'اسم الموظف': [leave.employee.full_name for leave in leaves],
                'الجنس': [leave.employee.get_gender_display() for leave in leaves],
                'القسم': [leave.employee.get_current_department() for leave in leaves],
                'المسمى الوظيفي الحالي': [leave.employee.get_latest_position() for leave in leaves],
                'نوع الإجازة': [leave.leave_type.get_name_display() for leave in leaves],
                'من تاريخ': [leave.start_date for leave in leaves],
                'إلى تاريخ': [leave.end_date for leave in leaves],
                'عدد الأيام': [leave.days_count for leave in leaves],
                'السبب': [leave.reason for leave in leaves],
                'الحالة': [leave.get_status_display() for leave in leaves],
            }
            df = pd.DataFrame(data)

            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='تقرير الحضور')

            # Save file to report
            output.seek(0)
            report.file.save(f'attendance_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = AttendanceReportForm()

    return render(request, 'reports/attendance_report.html', {'form': form})

@login_required
def employment_report(request):
    if request.method == 'POST':
        form = EmploymentReportForm(request.POST)
        if form.is_valid():
            department = form.cleaned_data['department']
            status = form.cleaned_data['status']
            is_current = form.cleaned_data['is_current']

            # Get employments
            employments_query = Employment.objects.all()

            if department:
                employments_query = employments_query.filter(department=department)

            if status:
                employments_query = employments_query.filter(status__name=status)

            if is_current:
                employments_query = employments_query.filter(is_current=True)

            employments = employments_query.select_related('employee', 'department', 'position', 'status', 'appointment_type')

            # Create report
            report = Report.objects.create(
                title='تقرير الكادر',
                report_type=Report.EMPLOYMENT,
                parameters={
                    'department_id': department.id if department else None,
                    'status': status,
                    'is_current': is_current
                },
                created_by=request.user.username
            )

            # Export to Excel
            data = {
                'الرقم الوزاري': [emp.employee.ministry_number for emp in employments],
                'الرقم الوطني': [emp.employee.national_id for emp in employments],
                'اسم الموظف': [emp.employee.full_name for emp in employments],
                'الجنس': [emp.employee.get_gender_display() for emp in employments],
                'القسم': [emp.department.name for emp in employments],
                'المسمى الوظيفي': [emp.position.name for emp in employments],
                'الحالة': [emp.status.get_name_display() for emp in employments],
                'صفة التعيين': [emp.appointment_type.name if emp.appointment_type else '' for emp in employments],
                'تاريخ التعيين': [emp.start_date for emp in employments],
                'تاريخ الانتهاء': [emp.end_date if emp.end_date else '' for emp in employments],
                'تاريخ الميلاد': [emp.employee.birth_date for emp in employments],
                'رقم الهاتف': [emp.employee.phone_number for emp in employments],
                'العنوان': [emp.employee.address for emp in employments],
                'المؤهل العلمي': [emp.employee.qualification or '' for emp in employments],
                'التخصص': [emp.employee.specialization for emp in employments],
                'حالي': ['نعم' if emp.is_current else 'لا' for emp in employments],
            }
            df = pd.DataFrame(data)

            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='تقرير الكادر')

            # Save file to report
            output.seek(0)
            report.file.save(f'employment_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = EmploymentReportForm()

    return render(request, 'reports/employment_report.html', {'form': form})

@login_required
def employee_selection(request):
    # Show employee selection form with search functionality
    search_query = request.GET.get('search', '')
    
    employees = Employee.objects.all()
    
    if search_query:
        employees = employees.filter(
            Q(full_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(qualification__icontains=search_query) |
            Q(post_graduate_diploma__icontains=search_query) |
            Q(masters_degree__icontains=search_query) |
            Q(phd_degree__icontains=search_query) |
            Q(specialization__icontains=search_query)
        )
    
    employees = employees.order_by('full_name')
    
    context = {
        'employees': employees,
        'search_query': search_query,
    }
    
    return render(request, 'reports/employee_selection.html', context)

@login_required
def employee_report(request, pk):

    # Generate report for specific employee
    employee = get_object_or_404(Employee, pk=pk)

    # Get employee data
    employments = Employment.objects.filter(employee=employee).select_related('department', 'position', 'status', 'appointment_type')
    leaves = Leave.objects.filter(employee=employee).select_related('leave_type')
    evaluations = PerformanceEvaluation.objects.filter(employee=employee)
    
    # Get additional data
    from ranks.models import EmployeeRank
    from file_management.models import File, FileMovement
    from disciplinary.models import Penalty, UnpaidLeave
    from employment.models import EmployeePosition
    
    ranks = EmployeeRank.objects.filter(employee=employee).select_related('rank_type')
    files = File.objects.filter(employee=employee)
    file_movements = FileMovement.objects.filter(employee=employee).select_related('file')
    penalties = Penalty.objects.filter(employee=employee).select_related('penalty_type')
    unpaid_leaves = UnpaidLeave.objects.filter(employee=employee)
    positions = EmployeePosition.objects.filter(employee=employee).select_related('position')

    # Create report
    report = Report.objects.create(
        title=f'تقرير الموظف: {employee.full_name}',
        report_type=Report.EMPLOYEE,
        parameters={
            'employee_id': employee.id,
            'include_personal_info': True,
            'include_employment_info': True,
            'include_leaves': True,
            'include_performance': True
        },
        created_by=request.user.username if request.user.is_authenticated else 'النظام'
    )

    # Export to Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # Personal info sheet
        personal_data = {
            'الرقم الوزاري': [employee.ministry_number],
            'الرقم الوطني': [employee.national_id],
            'الاسم الكامل': [employee.full_name],
            'الجنس': [employee.get_gender_display()],
            'المؤهل العلمي (بكالوريس / دبلوم)': [employee.qualification or ''],
            'المؤهل العلمي (دبلوم بعد البكالوريس)': [employee.post_graduate_diploma or ''],
            'المؤهل العلمي (ماجستير)': [employee.masters_degree or ''],
            'المؤهل العلمي (دكتوراه)': [employee.phd_degree or ''],
            'التخصص': [employee.specialization],
            'تاريخ التعيين': [employee.hire_date],
            'المدرسة': [employee.school],
            'تاريخ الميلاد': [employee.birth_date],
            'العنوان': [employee.address],
            'رقم الهاتف': [employee.phone_number],
        }
        pd.DataFrame(personal_data).to_excel(writer, index=False, sheet_name='البيانات الشخصية')

        # Employment info sheet
        if employments.exists():
            employment_data = {
                'القسم': [emp.department.name for emp in employments],
                'المسمى الوظيفي': [emp.position.name for emp in employments],
                'الحالة': [emp.status.get_name_display() for emp in employments],
                'صفة التعيين': [emp.appointment_type.name if emp.appointment_type else '' for emp in employments],
                'تاريخ التعيين': [emp.start_date for emp in employments],
                'تاريخ الانتهاء': [emp.end_date if emp.end_date else '' for emp in employments],
                'حالي': ['نعم' if emp.is_current else 'لا' for emp in employments],
            }
            pd.DataFrame(employment_data).to_excel(writer, index=False, sheet_name='بيانات التوظيف')

        # Leaves sheet
        if leaves.exists():
            leave_data = {
                'نوع الإجازة': [leave.leave_type.get_name_display() for leave in leaves],
                'من تاريخ': [leave.start_date for leave in leaves],
                'إلى تاريخ': [leave.end_date for leave in leaves],
                'عدد الأيام': [leave.days_count for leave in leaves],
                'السبب': [leave.reason for leave in leaves],
                'الحالة': [leave.get_status_display() for leave in leaves],
            }
            pd.DataFrame(leave_data).to_excel(writer, index=False, sheet_name='الإجازات')

        # Performance sheet
        if evaluations.exists():
            performance_data = {
                'السنة': [eval.year for eval in evaluations],
                'العلامة': [eval.score for eval in evaluations],
                'الدرجة القصوى': [eval.max_score for eval in evaluations],
                'النسبة المئوية': [eval.percentage for eval in evaluations],
                'المقيم': [eval.evaluator for eval in evaluations],
                'الملاحظات': [eval.comments for eval in evaluations],
            }
            pd.DataFrame(performance_data).to_excel(writer, index=False, sheet_name='التقارير السنوية')

        # Employee Positions (الحراك الوظيفي) sheet
        if positions.exists():
            positions_data = {
                'المسمى الوظيفي': [pos.position.name for pos in positions],
                'تاريخ الحصول عليه': [pos.date_obtained for pos in positions],
                'المرحلة': [pos.get_school_level_display() if pos.school_level else '' for pos in positions],
                'ملاحظات': [pos.notes or '' for pos in positions],
            }
            pd.DataFrame(positions_data).to_excel(writer, index=False, sheet_name='الحراك الوظيفي')

        # Ranks sheet
        if ranks.exists():
            ranks_data = {
                'نوع الرتبة': [rank.rank_type.name for rank in ranks],
                'تاريخ الحصول عليها': [rank.date_obtained for rank in ranks],
                'ملاحظات': [rank.notes or '' for rank in ranks],
            }
            pd.DataFrame(ranks_data).to_excel(writer, index=False, sheet_name='الرتب')

        # Files sheet
        if files.exists():
            files_data = {
                'رقم الملف': [file.file_number for file in files],
                'عنوان الملف': [file.title for file in files],
                'حالة الملف': [file.get_status_display() for file in files],
                'وصف الملف': [file.description or '' for file in files],
                'تاريخ الإنشاء': [file.created_at.date() for file in files],
            }
            pd.DataFrame(files_data).to_excel(writer, index=False, sheet_name='الملفات')

        # File Movements sheet
        if file_movements.exists():
            movements_data = {
                'رقم الملف': [mov.file.file_number if mov.file else '' for mov in file_movements],
                'تاريخ خروج الملف': [mov.checkout_date for mov in file_movements],
                'تاريخ عودة الملف': [mov.return_date if mov.return_date else '' for mov in file_movements],
                'الإجراء المتخذ': [mov.action_taken or '' for mov in file_movements],
                'الحالة': [mov.get_status_display() for mov in file_movements],
                'ملاحظات': [mov.notes or '' for mov in file_movements],
            }
            pd.DataFrame(movements_data).to_excel(writer, index=False, sheet_name='حركات الملفات')

        # Penalties sheet
        if penalties.exists():
            penalties_data = {
                'نوع العقوبة': [penalty.penalty_type.name for penalty in penalties],
                'التاريخ': [penalty.date for penalty in penalties],
                'الوصف': [penalty.description for penalty in penalties],
                'رقم القرار': [penalty.decision_number or '' for penalty in penalties],
                'تاريخ القرار': [penalty.decision_date if penalty.decision_date else '' for penalty in penalties],
            }
            pd.DataFrame(penalties_data).to_excel(writer, index=False, sheet_name='العقوبات')

        # Unpaid Leaves sheet
        if unpaid_leaves.exists():
            unpaid_data = {
                'تاريخ البداية': [leave.start_date for leave in unpaid_leaves],
                'تاريخ النهاية': [leave.end_date for leave in unpaid_leaves],
                'المدة (أيام)': [leave.duration for leave in unpaid_leaves],
                'سبب الإجازة': [leave.reason or '' for leave in unpaid_leaves],
                'رقم القرار': [leave.decision_number or '' for leave in unpaid_leaves],
                'تاريخ القرار': [leave.decision_date if leave.decision_date else '' for leave in unpaid_leaves],
            }
            pd.DataFrame(unpaid_data).to_excel(writer, index=False, sheet_name='إجازات بدون راتب')

    # Save file to report
    output.seek(0)
    report.file.save(f'employee_report_{employee.id}.xlsx', output)

    messages.success(request, 'تم إنشاء التقرير بنجاح.')
    return redirect('reports:report_dashboard')

@login_required
def department_selection(request):
    # Show department selection form
    departments = Department.objects.all().order_by('name')
    return render(request, 'reports/department_selection.html', {'departments': departments})

@login_required
def department_report(request, pk):
    # Generate report for specific department
    department = get_object_or_404(Department, pk=pk)

    # Get employees in department
    employments = Employment.objects.filter(department=department, is_current=True).select_related('employee', 'position', 'status', 'appointment_type')

    # Create report
    report = Report.objects.create(
        title=f'تقرير القسم: {department.name}',
        report_type=Report.DEPARTMENT,
        parameters={
            'department_id': department.id
        },
        created_by=request.user.username
    )

    # Export to Excel
    data = {
        'الرقم الوزاري': [emp.employee.ministry_number for emp in employments],
        'الرقم الوطني': [emp.employee.national_id for emp in employments],
        'اسم الموظف': [emp.employee.full_name for emp in employments],
        'الجنس': [emp.employee.get_gender_display() for emp in employments],
        'المسمى الوظيفي': [emp.position.name for emp in employments],
        'حالة التوظيف': [emp.status.get_name_display() for emp in employments],
        'صفة التعيين': [emp.appointment_type.name if emp.appointment_type else '' for emp in employments],
        'تاريخ التعيين': [emp.start_date for emp in employments],
        'المؤهل العلمي (بكالوريس / دبلوم)': [emp.employee.qualification or '' for emp in employments],
        'المؤهل العلمي (دبلوم بعد البكالوريس)': [emp.employee.post_graduate_diploma or '' for emp in employments],
        'المؤهل العلمي (ماجستير)': [emp.employee.masters_degree or '' for emp in employments],
        'المؤهل العلمي (دكتوراه)': [emp.employee.phd_degree or '' for emp in employments],
        'التخصص': [emp.employee.specialization for emp in employments],
        'تاريخ الميلاد': [emp.employee.birth_date for emp in employments],
        'رقم الهاتف': [emp.employee.phone_number for emp in employments],
        'المسمى الوظيفي الحالي': [emp.employee.get_latest_position() for emp in employments],
    }
    df = pd.DataFrame(data)

    # Create Excel file
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=f'قسم {department.name}')

    # Save file to report
    output.seek(0)
    report.file.save(f'department_report_{department.id}.xlsx', output)

    messages.success(request, 'تم إنشاء التقرير بنجاح.')
    return redirect('reports:report_dashboard')

@login_required
def staff_report(request):
    if request.method == 'POST':
        form = StaffReportForm(request.POST)
        if form.is_valid():
            gender = form.cleaned_data['gender']
            department = form.cleaned_data['department']

            # Get employees based on filters
            employees_query = Employee.objects.all()

            if gender:
                employees_query = employees_query.filter(gender=gender)

            if department:
                # Get employees in the department
                employees_in_dept = Employment.objects.filter(
                    department=department,
                    is_current=True
                ).values_list('employee_id', flat=True)
                employees_query = employees_query.filter(id__in=employees_in_dept)

            employees = employees_query.order_by('full_name')

            # Create report
            title = 'تقرير الكادر'
            if gender:
                gender_display = 'ذكور' if gender == 'male' else 'إناث'
                title += f' - {gender_display}'
            if department:
                title += f' - {department.name}'

            report = Report.objects.create(
                title=title,
                report_type=Report.STAFF,
                parameters={
                    'gender': gender,
                    'department_id': department.id if department else None
                },
                created_by=request.user.username
            )

            # Export to Excel
            data = {
                'الرقم الوزاري': [emp.ministry_number for emp in employees],
                'الرقم الوطني': [emp.national_id for emp in employees],
                'الاسم الكامل': [emp.full_name for emp in employees],
                'الجنس': [emp.get_gender_display() for emp in employees],
                'المؤهل العلمي (بكالوريس / دبلوم)': [emp.qualification or '' for emp in employees],
                'المؤهل العلمي (دبلوم بعد البكالوريس)': [emp.post_graduate_diploma or '' for emp in employees],
                'المؤهل العلمي (ماجستير)': [emp.masters_degree or '' for emp in employees],
                'المؤهل العلمي (دكتوراه)': [emp.phd_degree or '' for emp in employees],
                'التخصص': [emp.specialization for emp in employees],
                'تاريخ التعيين': [emp.hire_date for emp in employees],
                'القسم': [emp.school for emp in employees],
                'تاريخ الميلاد': [emp.birth_date for emp in employees],
                'العنوان': [emp.address for emp in employees],
                'رقم الهاتف': [emp.phone_number for emp in employees],
                'المسمى الوظيفي الحالي': [emp.get_latest_position() for emp in employees],
            }
            df = pd.DataFrame(data)

            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='تقرير الكادر')

            # Save file to report
            output.seek(0)
            report.file.save(f'staff_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = StaffReportForm()

    return render(request, 'reports/staff_report.html', {'form': form})

@login_required
def leave_report(request):
    if request.method == 'POST':
        form = LeaveReportForm(request.POST)
        if form.is_valid():
            start_date = form.cleaned_data['start_date']
            end_date = form.cleaned_data['end_date']
            department = form.cleaned_data['department']
            leave_type = form.cleaned_data['leave_type']

            # Get leaves in the date range
            leaves_query = Leave.objects.filter(
                start_date__gte=start_date,
                end_date__lte=end_date
            )

            if department:
                # Filter by department
                employees_in_dept = Employment.objects.filter(
                    department=department,
                    is_current=True
                ).values_list('employee_id', flat=True)
                leaves_query = leaves_query.filter(employee_id__in=employees_in_dept)

            if leave_type:
                leaves_query = leaves_query.filter(leave_type__name=leave_type)

            leaves = leaves_query.select_related('employee', 'leave_type')

            # Create report
            report = Report.objects.create(
                title=f'تقرير الإجازات من {start_date} إلى {end_date}',
                report_type=Report.LEAVE,
                parameters={
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'department_id': department.id if department else None,
                    'leave_type': leave_type
                },
                created_by=request.user.username
            )

            # Export to Excel
            data = {
                'الرقم الوزاري': [leave.employee.ministry_number for leave in leaves],
                'الرقم الوطني': [leave.employee.national_id for leave in leaves],
                'اسم الموظف': [leave.employee.full_name for leave in leaves],
                'الجنس': [leave.employee.get_gender_display() for leave in leaves],
                'القسم': [leave.employee.get_current_department() for leave in leaves],
                'المسمى الوظيفي الحالي': [leave.employee.get_latest_position() for leave in leaves],
                'نوع الإجازة': [leave.leave_type.get_name_display() for leave in leaves],
                'من تاريخ': [leave.start_date for leave in leaves],
                'إلى تاريخ': [leave.end_date for leave in leaves],
                'عدد الأيام': [leave.days_count for leave in leaves],
                'السبب': [leave.reason for leave in leaves],
                'الحالة': [leave.get_status_display() for leave in leaves],
                'تاريخ الطلب': [leave.created_at.date() for leave in leaves],
            }
            df = pd.DataFrame(data)

            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='تقرير الإجازات')

            # Save file to report
            output.seek(0)
            report.file.save(f'leave_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = LeaveReportForm()

    return render(request, 'reports/leave_report.html', {'form': form})


@login_required
def technical_position_report(request):
    if request.method == 'POST':
        form = TechnicalPositionReportForm(request.POST)
        if form.is_valid():
            gender = form.cleaned_data.get('gender')

            # Get technical positions
            technical_positions_query = TechnicalPosition.objects.all().order_by('specialization')

            # Filter by gender if specified
            if gender:
                technical_positions_query = technical_positions_query.filter(gender=gender)

            # Create report
            report = Report.objects.create(
                title='تقرير الموقف الفني',
                report_type=Report.TECHNICAL_POSITION,
                parameters={
                    'gender': gender,
                },
                created_by=request.user.username
            )

            # Create Excel file
            output = BytesIO()
            writer = pd.ExcelWriter(output, engine='openpyxl')

            # Prepare data
            data = {
                'التخصص': [tp.specialization for tp in technical_positions_query],
                'القسم': [tp.department.name if tp.department else '-' for tp in technical_positions_query],
                'عدد الشواغر': [tp.vacancies for tp in technical_positions_query],
                'الجنس': [tp.get_gender_display() for tp in technical_positions_query],
                'ملاحظات (المبرر)': [tp.notes or '' for tp in technical_positions_query],
                'تاريخ الإنشاء': [tp.created_at.date() for tp in technical_positions_query],
                'آخر تحديث': [tp.updated_at.date() for tp in technical_positions_query],
            }

            # Create DataFrame and write to Excel
            df = pd.DataFrame(data)
            df.to_excel(writer, index=False, sheet_name='الموقف الفني')

            # Save file
            writer.close()
            output.seek(0)
            report.file.save(f'technical_position_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = TechnicalPositionReportForm()

    return render(request, 'reports/technical_position_report.html', {'form': form})


@login_required
def position_report(request):
    if request.method == 'POST':
        form = PositionReportForm(request.POST)
        if form.is_valid():
            position = form.cleaned_data.get('position')
            gender = form.cleaned_data.get('gender')

            # Get employees with the specified position
            employments_query = Employment.objects.filter(is_current=True).select_related('employee', 'position', 'department', 'status', 'appointment_type')

            if position:
                employments_query = employments_query.filter(position=position)

            # Filter by gender if specified
            if gender:
                employments_query = employments_query.filter(employee__gender=gender)

            # Create report
            report_title = 'تقرير المسميات الوظيفية'
            if position:
                report_title += f': {position.name}'

            report = Report.objects.create(
                title=report_title,
                report_type=Report.POSITION,
                parameters={
                    'position_id': position.id if position else None,
                    'gender': gender,
                },
                created_by=request.user.username
            )

            # Create Excel file
            output = BytesIO()
            writer = pd.ExcelWriter(output, engine='openpyxl')

            # Prepare data
            data = {
                'الرقم الوزاري': [emp.employee.ministry_number for emp in employments_query],
                'الرقم الوطني': [emp.employee.national_id for emp in employments_query],
                'الاسم الكامل': [emp.employee.full_name for emp in employments_query],
                'الجنس': [emp.employee.get_gender_display() for emp in employments_query],
                'المسمى الوظيفي': [emp.position.name for emp in employments_query],
                'القسم': [emp.department.name for emp in employments_query],
                'حالة التوظيف': [emp.status.get_name_display() for emp in employments_query],
                'صفة التعيين': [emp.appointment_type.name if emp.appointment_type else '' for emp in employments_query],
                'تاريخ التعيين': [emp.start_date for emp in employments_query],
                'تاريخ الميلاد': [emp.employee.birth_date for emp in employments_query],
                'رقم الهاتف': [emp.employee.phone_number for emp in employments_query],
                'المؤهل العلمي': [emp.employee.qualification or '' for emp in employments_query],
                'التخصص': [emp.employee.specialization for emp in employments_query],
            }

            # Create DataFrame and write to Excel
            df = pd.DataFrame(data)
            df.to_excel(writer, index=False, sheet_name='المسميات الوظيفية')

            # Save file
            writer.close()
            output.seek(0)
            report.file.save(f'position_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = PositionReportForm()

    return render(request, 'reports/position_report.html', {'form': form})


@login_required
def specialization_report(request):
    if request.method == 'POST':
        form = SpecializationReportForm(request.POST)
        if form.is_valid():
            specialization = form.cleaned_data.get('specialization')
            gender = form.cleaned_data.get('gender')

            # Get employees with the specified specialization
            employees_query = Employee.objects.filter(specialization=specialization)

            # Filter by gender if specified
            if gender:
                employees_query = employees_query.filter(gender=gender)

            # Create report
            report = Report.objects.create(
                title=f'تقرير التخصص: {specialization}',
                report_type=Report.SPECIALIZATION,
                parameters={
                    'specialization': specialization,
                    'gender': gender,
                },
                created_by=request.user.username
            )

            # Create Excel file
            output = BytesIO()
            writer = pd.ExcelWriter(output, engine='openpyxl')

            # Prepare data
            data = {
                'الرقم الوزاري': [emp.ministry_number for emp in employees_query],
                'الرقم الوطني': [emp.national_id for emp in employees_query],
                'الاسم الكامل': [emp.full_name for emp in employees_query],
                'الجنس': [emp.get_gender_display() for emp in employees_query],
                'التخصص': [emp.specialization for emp in employees_query],
                'المؤهل العلمي (بكالوريس / دبلوم)': [emp.qualification or '' for emp in employees_query],
                'المؤهل العلمي (دبلوم بعد البكالوريس)': [emp.post_graduate_diploma or '' for emp in employees_query],
                'المؤهل العلمي (ماجستير)': [emp.masters_degree or '' for emp in employees_query],
                'المؤهل العلمي (دكتوراه)': [emp.phd_degree or '' for emp in employees_query],
                'القسم الحالي': [emp.get_current_department() for emp in employees_query],
                'المسمى الوظيفي الحالي': [emp.get_latest_position() for emp in employees_query],
                'المدرسة': [emp.school for emp in employees_query],
                'تاريخ التعيين': [emp.hire_date for emp in employees_query],
                'تاريخ الميلاد': [emp.birth_date for emp in employees_query],
                'رقم الهاتف': [emp.phone_number for emp in employees_query],
                'العنوان': [emp.address for emp in employees_query],
            }

            # Get current position for each employee
            positions = []
            for emp in employees_query:
                current_employment = emp.employments.filter(is_current=True).first()
                position_name = current_employment.position.name if current_employment and current_employment.position else '-'
                positions.append(position_name)

            data['المسمى الوظيفي'] = positions

            # Create DataFrame and write to Excel
            df = pd.DataFrame(data)
            df.to_excel(writer, index=False, sheet_name='التخصص')

            # Save file
            writer.close()
            output.seek(0)
            report.file.save(f'specialization_report_{report.id}.xlsx', output)

            messages.success(request, 'تم إنشاء التقرير بنجاح.')
            return redirect('reports:report_dashboard')
    else:
        form = SpecializationReportForm()

    return render(request, 'reports/specialization_report.html', {'form': form})


@login_required
def report_list(request):
    """View for listing all reports"""
    # Get search parameter
    search = request.GET.get('search', '')

    # Filter reports
    if search:
        reports = Report.objects.filter(
            Q(title__icontains=search) |
            Q(created_by__icontains=search)
        ).order_by('-created_at')
    else:
        reports = Report.objects.all().order_by('-created_at')

    # Update created_at to current time for display purposes
    for report in reports:
        report.display_time = timezone.localtime(report.created_at).strftime('%Y-%m-%d %H:%M:%S')

    return render(request, 'reports/report_list.html', {'reports': reports})


@login_required
def report_delete(request, pk):
    """View for deleting a report"""
    report = get_object_or_404(Report, pk=pk)

    if request.method == 'POST':
        # Delete the report
        report.delete()
        messages.success(request, 'تم حذف التقرير بنجاح.')
        return redirect('reports:report_dashboard')

    return render(request, 'reports/report_confirm_delete.html', {'report': report})


@login_required
def ranks_report(request):
    """Generate ranks report"""
    if request.method == 'POST':
        from ranks.models import EmployeeRank, RankType
        
        # Get all employee ranks
        ranks = EmployeeRank.objects.all().select_related('employee', 'rank_type').order_by('-date_obtained')
        
        # Create report
        report = Report.objects.create(
            title='تقرير الرتب',
            report_type='ranks',
            parameters={},
            created_by=request.user.username
        )
        
        # Export to Excel
        if ranks.exists():
            data = {
                'الرقم الوزاري': [rank.employee.ministry_number for rank in ranks],
                'اسم الموظف': [rank.employee.full_name for rank in ranks],
                'نوع الرتبة': [rank.rank_type.name for rank in ranks],
                'تاريخ الحصول عليها': [rank.date_obtained for rank in ranks],
                'ملاحظات': [rank.notes or '' for rank in ranks],
            }
            df = pd.DataFrame(data)
            
            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='تقرير الرتب')
            
            # Save file to report
            output.seek(0)
            report.file.save(f'ranks_report_{report.id}.xlsx', output)
        
        messages.success(request, 'تم إنشاء تقرير الرتب بنجاح.')
        return redirect('reports:report_dashboard')
    
    return render(request, 'reports/ranks_report.html')


@login_required
def files_report(request):
    """Generate files report"""
    if request.method == 'POST':
        from file_management.models import File, FileMovement
        
        # Get all files
        files = File.objects.all().select_related('employee').order_by('file_number')
        
        # Create report
        report = Report.objects.create(
            title='تقرير الملفات',
            report_type='files',
            parameters={},
            created_by=request.user.username
        )
        
        # Export to Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            if files.exists():
                # Files sheet
                files_data = {
                    'رقم الملف': [file.file_number for file in files],
                    'عنوان الملف': [file.title for file in files],
                    'الرقم الوزاري': [file.employee.ministry_number for file in files],
                    'اسم الموظف': [file.employee.full_name for file in files],
                    'حالة الملف': [file.get_status_display() for file in files],
                    'وصف الملف': [file.description or '' for file in files],
                    'تاريخ الإنشاء': [file.created_at.date() for file in files],
                }
                pd.DataFrame(files_data).to_excel(writer, index=False, sheet_name='الملفات')
            
            # File movements sheet
            movements = FileMovement.objects.all().select_related('file', 'employee').order_by('-checkout_date')
            if movements.exists():
                movements_data = {
                    'رقم الملف': [mov.file.file_number if mov.file else '' for mov in movements],
                    'الرقم الوزاري': [mov.employee.ministry_number for mov in movements],
                    'اسم الموظف': [mov.employee.full_name for mov in movements],
                    'تاريخ خروج الملف': [mov.checkout_date for mov in movements],
                    'تاريخ عودة الملف': [mov.return_date if mov.return_date else '' for mov in movements],
                    'الإجراء المتخذ': [mov.action_taken or '' for mov in movements],
                    'الحالة': [mov.get_status_display() for mov in movements],
                    'ملاحظات': [mov.notes or '' for mov in movements],
                }
                pd.DataFrame(movements_data).to_excel(writer, index=False, sheet_name='حركات الملفات')
        
        # Save file to report
        output.seek(0)
        report.file.save(f'files_report_{report.id}.xlsx', output)
        
        messages.success(request, 'تم إنشاء تقرير الملفات بنجاح.')
        return redirect('reports:report_dashboard')
    
    return render(request, 'reports/files_report.html')


@login_required
def penalties_report(request):
    """Generate penalties report"""
    if request.method == 'POST':
        from disciplinary.models import Penalty, UnpaidLeave
        
        # Get all penalties
        penalties = Penalty.objects.all().select_related('employee', 'penalty_type').order_by('-date')
        
        # Create report
        report = Report.objects.create(
            title='تقرير العقوبات والإجازات بدون راتب',
            report_type='penalties',
            parameters={},
            created_by=request.user.username
        )
        
        # Export to Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            if penalties.exists():
                # Penalties sheet
                penalties_data = {
                    'الرقم الوزاري': [penalty.employee.ministry_number for penalty in penalties],
                    'اسم الموظف': [penalty.employee.full_name for penalty in penalties],
                    'نوع العقوبة': [penalty.penalty_type.name for penalty in penalties],
                    'التاريخ': [penalty.date for penalty in penalties],
                    'الوصف': [penalty.description for penalty in penalties],
                    'رقم القرار': [penalty.decision_number or '' for penalty in penalties],
                    'تاريخ القرار': [penalty.decision_date if penalty.decision_date else '' for penalty in penalties],
                }
                pd.DataFrame(penalties_data).to_excel(writer, index=False, sheet_name='العقوبات')
            
            # Unpaid leaves sheet
            unpaid_leaves = UnpaidLeave.objects.all().select_related('employee').order_by('-start_date')
            if unpaid_leaves.exists():
                unpaid_data = {
                    'الرقم الوزاري': [leave.employee.ministry_number for leave in unpaid_leaves],
                    'اسم الموظف': [leave.employee.full_name for leave in unpaid_leaves],
                    'تاريخ البداية': [leave.start_date for leave in unpaid_leaves],
                    'تاريخ النهاية': [leave.end_date for leave in unpaid_leaves],
                    'المدة (أيام)': [leave.duration for leave in unpaid_leaves],
                    'سبب الإجازة': [leave.reason or '' for leave in unpaid_leaves],
                    'رقم القرار': [leave.decision_number or '' for leave in unpaid_leaves],
                    'تاريخ القرار': [leave.decision_date if leave.decision_date else '' for leave in unpaid_leaves],
                }
                pd.DataFrame(unpaid_data).to_excel(writer, index=False, sheet_name='إجازات بدون راتب')
        
        # Save file to report
        output.seek(0)
        report.file.save(f'penalties_report_{report.id}.xlsx', output)
        
        messages.success(request, 'تم إنشاء تقرير العقوبات بنجاح.')
        return redirect('reports:report_dashboard')
    
    return render(request, 'reports/penalties_report.html')


@login_required
def positions_movement_report(request):
    """Generate positions movement report (الحراك الوظيفي)"""
    if request.method == 'POST':
        from employment.models import EmployeePosition
        
        # Get all employee positions
        positions = EmployeePosition.objects.all().select_related('employee', 'position').order_by('-date_obtained')
        
        # Create report
        report = Report.objects.create(
            title='تقرير الحراك الوظيفي',
            report_type='positions_movement',
            parameters={},
            created_by=request.user.username
        )
        
        # Export to Excel
        if positions.exists():
            data = {
                'الرقم الوزاري': [pos.employee.ministry_number for pos in positions],
                'اسم الموظف': [pos.employee.full_name for pos in positions],
                'المسمى الوظيفي': [pos.position.name for pos in positions],
                'تاريخ الحصول عليه': [pos.date_obtained for pos in positions],
                'المرحلة': [pos.get_school_level_display() if pos.school_level else '' for pos in positions],
                'ملاحظات': [pos.notes or '' for pos in positions],
            }
            df = pd.DataFrame(data)
            
            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='الحراك الوظيفي')
            
            # Save file to report
            output.seek(0)
            report.file.save(f'positions_movement_report_{report.id}.xlsx', output)
        
        messages.success(request, 'تم إنشاء تقرير الحراك الوظيفي بنجاح.')
        return redirect('reports:report_dashboard')
    
    return render(request, 'reports/positions_movement_report.html')