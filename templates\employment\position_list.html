{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المسميات الوظيفية - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قائمة المسميات الوظيفية</h2>
    <div>
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
            <i class="fas fa-plus"></i> إضافة مسمى وظيفي جديد
        </a>
        <a href="{% url 'employment:employment_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للكادر
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع المسميات الوظيفية</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الاسم</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for position in positions %}
                    <tr>
                        <td>{{ position.name }}</td>
                        <td>{{ position.description|default:"-" }}</td>
                        <td>
                            <button class="btn btn-warning btn-sm edit-position" data-id="{{ position.pk }}" data-name="{{ position.name }}" data-description="{{ position.description|default:'' }}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger btn-sm delete-position" data-id="{{ position.pk }}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center">لا يوجد مسميات وظيفية</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1" aria-labelledby="addPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPositionModalLabel">إضافة مسمى وظيفي جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employment:position_create' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Position Modal -->
<div class="modal fade" id="editPositionModal" tabindex="-1" aria-labelledby="editPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPositionModalLabel">تعديل مسمى وظيفي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employment:position_update' 0 %}" id="editPositionForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit position
        const editButtons = document.querySelectorAll('.edit-position');
        const editForm = document.getElementById('editPositionForm');
        const editNameInput = document.getElementById('edit_name');
        const editDescriptionInput = document.getElementById('edit_description');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');

                // Update form action URL
                editForm.action = editForm.action.replace(/\/\d+\/$/, `/${id}/`);

                // Set form values
                editNameInput.value = name;
                editDescriptionInput.value = description;

                // Show modal
                const editModal = new bootstrap.Modal(document.getElementById('editPositionModal'));
                editModal.show();
            });
        });

        // Delete position
        const deleteButtons = document.querySelectorAll('.delete-position');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذا المسمى الوظيفي؟')) {
                    window.location.href = `/employment/positions/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}
