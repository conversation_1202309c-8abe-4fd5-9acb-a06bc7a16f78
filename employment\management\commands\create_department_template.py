from django.core.management.base import BaseCommand
import pandas as pd
import os
from django.conf import settings

class Command(BaseCommand):
    help = 'Create department import template Excel file'

    def handle(self, *args, **options):
        # Create sample data with all possible columns
        data = {
            'الاسم': [
                'مدرسة الأمل الأساسية للبنين',
                'مدرسة النور الثانوية للبنات', 
                'روضة الطفولة السعيدة',
                'قسم الشؤون الإدارية',
                'قسم التخطيط والمتابعة'
            ],
            'الوصف': [
                'مدرسة أساسية للذكور',
                'مدرسة ثانوية للإناث',
                'روضة أطفال مختلطة',
                'قسم إداري في المديرية',
                'قسم تعليمي في المديرية'
            ],
            'مكان العمل': [
                'المدارس',
                'المدارس',
                'المدارس', 
                'المديرية',
                'المديرية'
            ],
            'تصنيف المدرسة': [
                'أساسي',
                'ثانوي',
                '',
                '',
                ''
            ],
            'جنس المدرسة': [
                'ذكور',
                'إناث',
                'مختلط',
                '',
                ''
            ],
            'أعلى صف': [
                'grade9',
                'grade12',
                'kg',
                '',
                ''
            ],
            'أدنى صف': [
                'grade1',
                'grade10',
                'kg',
                '',
                ''
            ],
            'يتبع لـ': [
                '',
                '',
                '',
                'الأقسام الإدارية',
                'الأقسام التعليمية'
            ]
        }
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Create static directory if it doesn't exist
        static_dir = os.path.join(settings.BASE_DIR, 'static', 'templates')
        os.makedirs(static_dir, exist_ok=True)
        
        # Save to Excel file
        file_path = os.path.join(static_dir, 'department_template.xlsx')
        df.to_excel(file_path, index=False, engine='openpyxl')
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء ملف النموذج بنجاح: {file_path}')
        )
        
        # Also create an empty template
        empty_data = {col: [''] for col in data.keys()}
        empty_df = pd.DataFrame(empty_data)
        
        empty_file_path = os.path.join(static_dir, 'department_template_empty.xlsx')
        empty_df.to_excel(empty_file_path, index=False, engine='openpyxl')
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء ملف النموذج الفارغ بنجاح: {empty_file_path}')
        )
