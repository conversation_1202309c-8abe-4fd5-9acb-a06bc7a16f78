# Generated by Django 5.2 on 2025-05-25 10:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
        ('employment', '0018_update_kindergarten_grades'),
    ]

    operations = [
        migrations.CreateModel(
            name='BtecField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='اسم الحقل')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحقل')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'حقل BTEC',
                'verbose_name_plural': 'حقول BTEC',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BtecTeacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='btec_records', to='employees.employee', verbose_name='الموظف')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teachers', to='employment.btecfield', verbose_name='الحقل')),
            ],
            options={
                'verbose_name': 'معلم BTEC',
                'verbose_name_plural': 'معلمي BTEC',
                'ordering': ['employee__full_name'],
                'unique_together': {('employee', 'field')},
            },
        ),
    ]
