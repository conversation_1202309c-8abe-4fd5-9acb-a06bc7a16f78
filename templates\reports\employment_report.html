{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>تقرير الكادر</h2>
    <a href="{% url 'reports:report_dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة للتقارير
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معايير التقرير</h6>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.department.id_for_label }}" class="form-label">القسم (اختياري)</label>
                {{ form.department }}
                {% if form.department.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.department.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">اترك هذا الحقل فارغاً لإنشاء تقرير لجميع الأقسام</div>
            </div>

            <div class="mb-3">
                <label for="{{ form.status.id_for_label }}" class="form-label">حالة التوظيف (اختياري)</label>
                {{ form.status }}
                {% if form.status.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.status.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">اترك هذا الحقل فارغاً لإنشاء تقرير لجميع حالات التوظيف</div>
            </div>

            <div class="mb-3 form-check">
                {{ form.is_current }}
                <label class="form-check-label" for="{{ form.is_current.id_for_label }}">
                    الموظفين الحاليين فقط
                </label>
                {% if form.is_current.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.is_current.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-dark">
                    <i class="fas fa-file-export"></i> إنشاء التقرير
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">معلومات عن التقرير</h6>
    </div>
    <div class="card-body">
        <p>يقوم هذا التقرير بإنشاء ملف Excel يحتوي على معلومات عن كادر الموظفين حسب المعايير المحددة.</p>
        <p>يمكنك تحديد قسم معين أو حالة توظيف معينة لتصفية النتائج.</p>
        <p>يتضمن التقرير المعلومات التالية:</p>
        <ul>
            <li>الرقم الوزاري للموظف</li>
            <li>اسم الموظف</li>
            <li>الجنس</li>
            <li>القسم</li>
            <li>المسمى الوظيفي</li>
            <li>حالة التوظيف</li>
            <li>تاريخ التعيين</li>
            <li>تاريخ الانتهاء (إن وجد)</li>
            <li>حالة التوظيف الحالية</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formControls = document.querySelectorAll('input:not([type="checkbox"]), select, textarea');
        formControls.forEach(function(element) {
            element.classList.add('form-control');
        });

        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(function(element) {
            element.classList.add('form-check-input');
        });
    });
</script>
{% endblock %}
