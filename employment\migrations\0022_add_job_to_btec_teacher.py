# Generated manually

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employment', '0021_add_btec_job_step1'),
    ]

    operations = [
        # Remove old unique constraint
        migrations.AlterUniqueTogether(
            name='btecteacher',
            unique_together=set(),
        ),
        # Add job field to BtecTeacher
        migrations.AddField(
            model_name='btecteacher',
            name='job',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='teachers', to='employment.btecjob', verbose_name='الوظيفة'),
        ),
        # Add new unique constraint
        migrations.AlterUniqueTogether(
            name='btecteacher',
            unique_together={('employee', 'field', 'job')},
        ),
    ]
