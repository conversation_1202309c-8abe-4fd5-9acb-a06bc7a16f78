{% extends 'base.html' %}
{% load static %}

{% block title %}معاينة PDF لرتب الموظفين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    @page {
        size: landscape;
        margin: 0.25in;
    }

    body {
        direction: rtl;
        text-align: right;
        font-family: 'Noto Sans Arabic', sans-serif;
    }

    .report-title {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .report-date, .report-count {
        font-size: 12px;
    }

    .report-count {
        font-weight: bold;
        color: #007bff;
    }

    .table th {
        background-color: #f2f2f2 !important;
        color: #000 !important;
        font-weight: bold;
    }

    .table-bordered th, .table-bordered td {
        border: 1px solid #000 !important;
    }

    .table th, .table td {
        text-align: center;
        vertical-align: middle;
        padding: 8px;
        font-size: 14px;
    }

    /* Enhanced styles for better PDF quality */
    .table {
        width: 100%;
        border-collapse: collapse;
        box-shadow: none;
    }

    .table-responsive {
        overflow: visible;
        border: none;
        box-shadow: none;
    }

    .even-row {
        background-color: #f9f9f9;
    }

    .btn-toolbar {
        margin-bottom: 20px;
    }

    @media print {
        .btn-toolbar, .no-print {
            display: none !important;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
        }

        .card {
            border: none;
            box-shadow: none;
        }

        .card-body {
            padding: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="btn-toolbar justify-content-between mb-3">
    <div class="btn-group">
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
    <div class="btn-group">
        <button id="captureAndSavePDF" class="btn btn-danger">
            <i class="fas fa-file-pdf"></i> تصدير PDF
        </button>
        <button onclick="printTable()" class="btn btn-primary">
            <i class="fas fa-print"></i> طباعة
        </button>
    </div>
</div>

<div class="card shadow">
    <div class="card-body">
        <div class="report-title">رتب الموظفين</div>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="report-date">تاريخ التقرير: {% now "Y-m-d" %}</div>
            <div class="report-count">عدد الموظفين: {{ employee_count }}</div>
        </div>

        {% if selected_year or selected_rank_type %}
        <div class="report-filters mb-3">
            <strong>الفلاتر المطبقة:</strong>
            {% if selected_year %}
            <span class="badge bg-info me-2">السنة: {{ selected_year }}</span>
            {% endif %}
            {% if selected_rank_type %}
            <span class="badge bg-info me-2">نوع الرتبة: {{ selected_rank_type }}</span>
            {% endif %}
        </div>
        {% endif %}

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>الرقم الوزاري</th>
                        <th>اسم الموظف</th>
                        <th>نوع الرتبة</th>
                        <th>تاريخ الحصول عليها</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee_rank in employee_ranks %}
                    <tr class="{% cycle '' 'even-row' %}">
                        <td>{{ employee_rank.employee.ministry_number }}</td>
                        <td>{{ employee_rank.employee.full_name }}</td>
                        <td>{{ employee_rank.rank_type.name }}</td>
                        <td>{{ employee_rank.date_obtained|date:"Y-m-d" }}</td>
                        <td>{{ employee_rank.notes|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا يوجد رتب للموظفين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="text-left mt-3">
            <small>صفحة <span class="page-number">1</span></small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include required libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
    // Add page numbers when printing
    window.onbeforeprint = function() {
        document.querySelectorAll('.page-number').forEach(function(el) {
            el.textContent = '1';
        });
    };

    // Function to handle PDF download - using the same approach as directorate_employees_pdf_preview.html
    document.addEventListener('DOMContentLoaded', function() {
        const captureButton = document.getElementById('captureAndSavePDF');
        if (captureButton) {
            captureButton.addEventListener('click', function() {
                // Show loading message
                alert('جاري إنشاء ملف PDF، يرجى الانتظار...');

                // Get the table container
                var element = document.querySelector('.card-body');

                // Use html2canvas to capture the table as an image
                html2canvas(element, {
                    scale: 2,  // Higher scale for better quality
                    useCORS: true,
                    backgroundColor: '#ffffff',
                    logging: false,
                    allowTaint: true,
                    foreignObjectRendering: false
                }).then(function(canvas) {
                    try {
                        // Get the jsPDF class from the loaded library
                        var { jsPDF } = window.jspdf;

                        // Create a new PDF document in landscape orientation
                        var pdf = new jsPDF({
                            orientation: 'landscape',
                            unit: 'mm',
                            format: 'a4'
                        });

                        // Get canvas as image
                        var imgData = canvas.toDataURL('image/jpeg', 1.0);

                        // Calculate dimensions to fit the page
                        var pdfWidth = pdf.internal.pageSize.getWidth();
                        var pdfHeight = pdf.internal.pageSize.getHeight();

                        var imgWidth = pdfWidth - 20;  // 10mm margin on each side
                        var imgHeight = (canvas.height * imgWidth) / canvas.width;

                        // If image is too tall, scale it to fit the page height
                        if (imgHeight > pdfHeight - 20) {
                            imgHeight = pdfHeight - 20;  // 10mm margin on top and bottom
                            imgWidth = (canvas.width * imgHeight) / canvas.height;
                        }

                        // Calculate position to center the image
                        var x = (pdfWidth - imgWidth) / 2;
                        var y = (pdfHeight - imgHeight) / 2;

                        // Add the image to the PDF
                        pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

                        // Add metadata
                        pdf.setProperties({
                            title: 'رتب الموظفين',
                            subject: 'تقرير رتب الموظفين',
                            creator: 'نظام شؤون الموظفين',
                            author: 'نظام شؤون الموظفين'
                        });

                        // Save the PDF
                        pdf.save('رتب_الموظفين.pdf');

                        // Show success message
                        alert('تم إنشاء ملف PDF بنجاح!');
                    } catch (error) {
                        // Show error message
                        console.error('Error creating PDF:', error);
                        alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
                    }
                }).catch(function(error) {
                    // Show error message
                    console.error('Error capturing image:', error);
                    alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
                });
            });
        }
    });

    // Function to handle printing
    function printTable() {
        // Show loading message
        alert('جاري تجهيز الصفحة للطباعة، يرجى الانتظار...');

        // Get the table container
        var element = document.querySelector('.card-body');

        // Use html2canvas to capture the table as an image
        html2canvas(element, {
            scale: 2,  // Higher scale for better quality
            useCORS: true,
            backgroundColor: '#ffffff',
            logging: false,
            allowTaint: true,
            foreignObjectRendering: false
        }).then(function(canvas) {
            try {
                // Create a new window
                var printWindow = window.open('', '_blank');

                // Write HTML content to the new window
                printWindow.document.write('<html><head><title>رتب الموظفين</title>');
                printWindow.document.write('<style>body { margin: 0; padding: 20px; direction: rtl; } img { max-width: 100%; height: auto; }</style>');
                printWindow.document.write('</head><body>');

                // Add the canvas as an image
                printWindow.document.write('<img src="' + canvas.toDataURL('image/jpeg', 1.0) + '" />');

                printWindow.document.write('</body></html>');

                // Close the document
                printWindow.document.close();

                // Wait for the image to load before printing
                printWindow.onload = function() {
                    printWindow.print();
                };
            } catch (error) {
                // Show error message
                console.error('Error preparing for print:', error);
                alert('حدث خطأ أثناء تجهيز الصفحة للطباعة: ' + error.message);
            }
        }).catch(function(error) {
            // Show error message
            console.error('Error capturing image:', error);
            alert('حدث خطأ أثناء التقاط صورة الجدول: ' + error.message);
        });
    }
</script>
{% endblock %}
